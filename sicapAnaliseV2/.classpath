<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry including="**/*.java" kind="src" output="target/classes" path="src/main/java">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.jboss.ide.eclipse.as.core.server.runtime.runtimeTarget/WildFly 23.0 Runtime">
		<attributes>
			<attribute name="owner.project.facets" value="jst.jsf;jst.web;jpt.jpa"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/classmate-0.8.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-beanutils-1.9.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-collections-3.1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-collections-3.2.2.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-collections4-4.1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-digester-2.1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-fileupload-1.3.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-io-2.4.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-lang-2.6.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-lang3-3.5.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/commons-logging-1.1.1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/comumUtil.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/fonts.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/gson-2.3.1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/hibernate-core-5.4.10.Final.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/itext-2.1.7.js5.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jasperreports-6.16.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jasperreports-fonts-6.3.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jcommon-1.0.23.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jersey-client-2.25.1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jfreechart-1.0.19.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/poi-3.16-beta1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/poi-3.17.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/poi-ooxml-3.17.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/poi-ooxml-schemas-3.17.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/primefaces-7.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/primefaces-extensions-7.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/resources-ckeditor-3.2.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/resources-codemirror-3.2.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/slf4j-api-1.7.12.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/uaiCriteria-4.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/xmlbeans-2.6.0.jar"/>
	<classpathentry kind="src" output="target/test-classes" path="src/test/java">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry excluding="**" kind="src" output="target/test-classes" path="src/test/resources">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.m2e.MAVEN2_CLASSPATH_CONTAINER">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" path="target/generated-sources/annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="target/test-classes" path="target/generated-test-sources/test-annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="target/classes"/>
</classpath>
