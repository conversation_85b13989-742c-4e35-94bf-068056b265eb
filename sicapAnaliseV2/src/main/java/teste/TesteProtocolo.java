package teste;

import com.google.gson.Gson;

import br.gov.ac.tce.sicapanalise.auditoria.processo.ArquivoWS;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ArquivoWSContainer;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ResponsavelSimplesPrestacao;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ResponsavelSimplesPrestacaoContainer;

public class TesteProtocolo {

	public static void main(String[] args) {
		// TODO Auto-generated method stub

		Gson gson = new Gson();
		
		ArquivoWS arquivo1 = new ArquivoWS();
		arquivo1.setOrdem(1);
		arquivo1.setNome("Historico Funcional");
		arquivo1.setTipoArquivo("application/pdf");
		arquivo1.setTipoDocumento("declaração");
		arquivo1.setUrlArquivo("/opt/repositorio/processo/tempPrevi/historico_funcional.pdf");
		
		ArquivoWS arquivo2 = new ArquivoWS();
		arquivo2.setOrdem(1);
		arquivo2.setNome("Certidão de tempo de contribuição");
		arquivo2.setTipoArquivo("application/pdf");
		arquivo2.setTipoDocumento("certidão");
		arquivo2.setUrlArquivo("/opt/repositorio/processo/tempPrevi/certidao_tempo_contribuicao.pdf");
		
		ArquivoWSContainer arquivos = new ArquivoWSContainer();
		arquivos.getArquivos().add(arquivo1);
		arquivos.getArquivos().add(arquivo2);
		
		String arq = gson.toJson(arquivos);
		
		System.out.println(arq);
		
		System.out.println("===============================================================");
	
		ResponsavelSimplesPrestacao responsavel1 = new ResponsavelSimplesPrestacao();
		responsavel1.setCpfCnpj("123.456.789-09");
		responsavel1.setNome("Responsavel teste 1");
		responsavel1.setEmail("<EMAIL>");
		
		ResponsavelSimplesPrestacao responsavel2 = new ResponsavelSimplesPrestacao();
		responsavel1.setCpfCnpj("123.555.999-09");
		responsavel1.setNome("Responsavel teste 2");
		responsavel1.setEmail("<EMAIL>");
		
		ResponsavelSimplesPrestacaoContainer resContainer = new ResponsavelSimplesPrestacaoContainer();
		resContainer.getResponsaveis().add(responsavel1);
		resContainer.getResponsaveis().add(responsavel2);
		
		String resp = gson.toJson(resContainer);
		
		System.out.println(resp);
		
	}

}
