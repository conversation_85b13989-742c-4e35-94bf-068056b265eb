package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.cargo.Escolaridade;
import br.gov.ac.tce.sicapweb.modelo.cargo.TipoAcumulavel;
import br.gov.ac.tce.sicapweb.modelo.cargo.TipoCargo;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.repositorio.RemessaEventualRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.CargoRepositorio;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Named
@ViewScoped
public class CargoBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private Cargo cargo;
	@Inject
	private RemessaEventual remessaEventual;
	@Inject
	private FormatUtil formatUtil;

	@Inject
	private RemessaEventualRepositorio remessaEventualRepositorio;
	@Inject
	private CargoRepositorio cargoRepositorio;

	private Collection<Cargo> listaCargos;
	private Collection<RemessaEventual> listaRemessaEventual;

	private Integer tipoCargo;
	private Integer tipoEscolaridade;
	private Integer tipoAcumulavel;
	private Integer situacao;
	private String nome;

	private int currentLevel;

	@PostConstruct
	public void init() {
		this.setCurrentLevel(1);
		if (this.loginBean.existeEntidadeSelecionada("/dados/entidade/cargo/cargos.xhtml?faces-redirect=true")) {
			// 0 = todos os registros
			this.tipoCargo = 0;
			this.tipoEscolaridade = 0;
			this.tipoAcumulavel = 0;
			this.situacao = 0;
			this.nome = "";
			this.remessaEventual.setId(Long.valueOf(0));
			try {
				this.listaRemessaEventual = this.remessaEventualRepositorio.listarPorEntidadeTipoSituacao(
						this.loginBean.getEntidade(), TypeArquivo.CARGO, SituacaoRemessa.PROCESSADA);
				this.listaCargos = this.cargoRepositorio.pesquisaCargos(this.loginBean.getEntidade(),
						this.remessaEventual, this.tipoCargo, this.tipoEscolaridade, tipoAcumulavel, this.situacao,
						this.nome);
			} catch (RepositorioException e) {
				Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as remesas de cargo.");
			}
		}
	}

	public void pesquisar() {
		try {
			this.listaCargos.clear();
			this.remessaEventual.setId(Long.valueOf(0));
			this.listaCargos = this.cargoRepositorio.pesquisaCargos(this.loginBean.getEntidade(), this.remessaEventual,
					this.tipoCargo, this.tipoEscolaridade, this.tipoAcumulavel, this.situacao, this.nome);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar nenhum cargo.");
		}
	}

	public void buscarDadosCargo(Cargo cargo) {
		try {
			this.cargo = this.cargoRepositorio.buscaCargoPorId(this.loginBean.getEntidade(), cargo);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível detalhar os dados do servidor.");
		}
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public Collection<RemessaEventual> getListaRemessaEventual() {
		return listaRemessaEventual;
	}

	public void setListaRemessaEventual(Collection<RemessaEventual> listaRemessaEventual) {
		this.listaRemessaEventual = listaRemessaEventual;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public Cargo getCargo() {
		return cargo;
	}

	public void setCargo(Cargo cargo) {
		this.cargo = cargo;
	}

	public Collection<Cargo> getListaCargos() {
		return listaCargos;
	}

	public void setListaCargos(Collection<Cargo> listaCargos) {
		this.listaCargos = listaCargos;
	}

	public TipoCargo[] getListaTipoCargo() {
		return TipoCargo.values();
	}

	public Escolaridade[] getListaEscolaridade() {
		return Escolaridade.values();
	}

	public TipoAcumulavel[] getListaTipoAcumulavel() {
		return TipoAcumulavel.values();
	}

	public Integer getTipoCargo() {
		return tipoCargo;
	}

	public void setTipoCargo(Integer tipoCargo) {
		this.tipoCargo = tipoCargo;
	}

	public Integer getTipoAcumulavel() {
		return tipoAcumulavel;
	}

	public void setTipoAcumulavel(Integer tipoAcumulavel) {
		this.tipoAcumulavel = tipoAcumulavel;
	}

	public Integer getTipoEscolaridade() {
		return tipoEscolaridade;
	}

	public void setTipoEscolaridade(Integer tipoEscolaridade) {
		this.tipoEscolaridade = tipoEscolaridade;
	}

	public Integer getSituacao() {
		return situacao;
	}

	public void setSituacao(Integer situacao) {
		this.situacao = situacao;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

}
