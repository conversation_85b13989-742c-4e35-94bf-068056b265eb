package br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel;

import java.io.Serializable;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "vw_Servidor", schema = "auditoria")
@NamedQueries({
		@NamedQuery(name = "usuario.listaUsuariosAtivos", query = "SELECT u FROM Usuario u where u.ativo = true"),
		@NamedQuery(name = "usuario.listaUsuarioPorLogin", query = "SELECT u FROM Usuario u where u.login = :login") })
public class Usuario implements Serializable {

	private static final long serialVersionUID = -8088580282077999880L;
	@Id
	private Long id;
	private String cpf;
	private String nome;
	private Integer matricula;
	private String login;
	private String email;
	private Boolean ativo;
	@Column(name = "setor_id")
	private Long setorId;
	@Column(name = "setor_nome")
	private String setorNome;
	@Column(name = "setor_telefone")
	private String setorTelefone;

	@Column(name = "cargo_id")
	private Long cargoId;
	@Column(name = "cargo_nome")
	private String cargoNome;

	@OneToMany(mappedBy = "usuario")
	private Collection<UsuarioGrupo> listaUsuarioGrupo;
	

	public void setId(Long id) {
		this.id = id;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public void setMatricula(Integer matricula) {
		this.matricula = matricula;
	}

	public void setLogin(String login) {
		this.login = login;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public void setAtivo(Boolean ativo) {
		this.ativo = ativo;
	}

	public void setSetorId(Long setorId) {
		this.setorId = setorId;
	}

	public void setSetorNome(String setorNome) {
		this.setorNome = setorNome;
	}

	public void setSetorTelefone(String setorTelefone) {
		this.setorTelefone = setorTelefone;
	}

	public void setCargoId(Long cargoId) {
		this.cargoId = cargoId;
	}

	public void setCargoNome(String cargoNome) {
		this.cargoNome = cargoNome;
	}

	public void setListaUsuarioGrupo(Collection<UsuarioGrupo> listaUsuarioGrupo) {
		this.listaUsuarioGrupo = listaUsuarioGrupo;
	}

	public Long getId() {
		return id;
	}

	public String getCpf() {
		return cpf;
	}

	public String getNome() {
		return nome;
	}

	public Integer getMatricula() {
		return matricula;
	}

	public String getLogin() {
		return login;
	}

	public String getEmail() {
		return email;
	}

	public Boolean getAtivo() {
		return ativo;
	}

	public Long getSetorId() {
		return setorId;
	}

	public String getSetorNome() {
		return setorNome;
	}

	public String getSetorTelefone() {
		return setorTelefone;
	}

	public Long getCargoId() {
		return cargoId;
	}

	public String getCargoNome() {
		return cargoNome;
	}

	public Collection<UsuarioGrupo> getListaUsuarioGrupo() {
		return listaUsuarioGrupo;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Usuario other = (Usuario) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}


	

}
