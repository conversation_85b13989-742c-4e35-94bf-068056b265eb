package br.gov.ac.tce.sicapanalise.controle.bean;

import java.io.IOException;
import java.io.Serializable;
import java.text.Normalizer;
import java.util.Collection;
import java.util.Optional;

import javax.annotation.PostConstruct;
import javax.enterprise.context.SessionScoped;
import javax.faces.context.ExternalContext;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.PerfilGrupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.UsuarioGrupo;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.UsuarioAuditoriaRepositorio;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;

@Named
@SessionScoped
public class LoginBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private Entidade entidade;
	@Inject
	private EntidadeRepositorio entidadeRepositorio;
	@Inject
	private UsuarioAuditoriaRepositorio servidorTceRepositorio;
//	@Inject
//	private UsuarioAutorizacaoRepositorio usuarioAutorizacaoRepositorio;

	@Inject
	private FacesContext facesContext;
	@Inject
	private HttpServletRequest request;

	private String login;
	private String senha;
//	private String nome;
//	private Long idUsuario;
	private Boolean logado;
	private Collection<Entidade> listaEntidades;
	private String paginaSolicitada;

	private Usuario usuario;

//
//	private Collection<Object[]> listaUsuariosAutorizados;

	@PostConstruct
	public void init() {
		this.login = null;
		this.senha = null;
		this.paginaSolicitada = null;
		this.setLogado(false);
		this.listaEntidades = this.entidadeRepositorio.lista();
	}

	public String efetuarLogin() {
//		HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext()
//				.getRequest();

		setLogado(false);
		try {
			request.login(this.login, this.senha);
			setLogado(true);
//			this.nome = this.usuarioInternoRespositorio.buscarNomePorLogin(this.login);
//			this.setIdUsuario(this.usuarioInternoRespositorio.buscarIdUsuarioPorLogin(this.login));
			this.usuario = this.servidorTceRepositorio.retornaServidorPorLogin(this.login);

//			this.listaUsuariosAutorizados = this.usuarioAutorizacaoRepositorio.listaUsuariosAutorizados();
			return "/principal.xhtml?faces-redirect=true";
		} catch (ServletException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Usuário/senha inválidos.");
		} catch (Exception e1) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Usuário/senha inválidos.");
		}
		return "";
	}

	public String logoff() {
		this.logado = false;
//		FacesContext.getCurrentInstance().getExternalContext().invalidateSession();
		facesContext.getExternalContext().invalidateSession();
		return "/login?faces-redirect=true";
	}

	public boolean existeEntidadeSelecionada(String paginaSolicitada) {
		if (this.entidade.getIdEntidadeCjur() == null) {
			try {
				this.paginaSolicitada = paginaSolicitada;
				ExternalContext contextoExterno = FacesContext.getCurrentInstance().getExternalContext();
				contextoExterno.redirect(
						contextoExterno.getApplicationContextPath() + "/selecionarEntidade.xhtml?faces-redirect=true");
			} catch (IOException e) {
				e.printStackTrace();
			}
			return false;
		} else {
			return true;
		}
	}

	public void selecaoEntidade() {
		if (this.entidade.getIdEntidadeCjur() != null) {
			try {
				if (this.paginaSolicitada == null) {
					this.paginaSolicitada = "/principal.xhtml?faces-redirect=true";
				}
				ExternalContext contextoExterno = FacesContext.getCurrentInstance().getExternalContext();
				contextoExterno.redirect(contextoExterno.getApplicationContextPath() + this.paginaSolicitada);
				this.paginaSolicitada = "";
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	public Boolean isADM() {
		// if (this.listaUsuariosAutorizados.contains(this.login)) {
		return true;
		// } else {
		// return false;
		// }
	}

	public String removeAcentos(String str) {
		// System.out.println("txt: " + str);
		str = Normalizer.normalize(str, Normalizer.Form.NFD);
		str = str.replaceAll("\\p{InCombiningDiacriticalMarks}+", "");
		// System.out.println("txt alterado: " + str);
		return "teste";
	}

	public String getLogin() {
		return login;
	}

	public void setLogin(String login) {
		this.login = login;
	}

	public String getSenha() {
		return senha;
	}

	public void setSenha(String senha) {
		this.senha = senha;
	}

	public Boolean isLogado() {
		return logado;
	}

	public void setLogado(Boolean logado) {
		this.logado = logado;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Collection<Entidade> getListaEntidades() {
		return listaEntidades;
	}

	public void setListaEntidades(Collection<Entidade> listaEntidades) {
		this.listaEntidades = listaEntidades;
	}

	public Usuario getUsuario() {
		return usuario;
	}

	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
	}

	public boolean getPermissao(String role) {
		if (request.isUserInRole(role))
			return true;
		return false;
	}

	public boolean getPerfilDistribuicao() {
		Optional<UsuarioGrupo> usuarioGrupoDistribuicao = this.usuario.getListaUsuarioGrupo().stream()
				.filter(ug -> ug.getAtivo() && ug.getGrupo().getAtivo()
						&& ug.getGrupo().getPerfil().equals(PerfilGrupo.DISTRIBUICAO))
				.findAny();
		
		if (usuarioGrupoDistribuicao.isPresent()) {
			return true;
		}
		return false;
	}

	public boolean getPerfilAuditoria() {
		Optional<UsuarioGrupo> usuarioGrupoAuditoria = this.usuario.getListaUsuarioGrupo().stream()
				.filter(ug -> ug.getAtivo() && ug.getGrupo().getAtivo()
						&& ug.getGrupo().getPerfil().equals(PerfilGrupo.AUDITORIA))
				.findAny();
		if (usuarioGrupoAuditoria.isPresent()) {
			return true;
		}
		return false;
	}

//	public String getNome() {
//		return nome;
//	}
//
//	public void setNome(String nome) {
//		this.nome = nome;
//	}
//
//	public Long getIdUsuario() {
//		return idUsuario;
//	}
//
//	public void setIdUsuario(Long idUsuario) {
//		this.idUsuario = idUsuario;
//	}

}
