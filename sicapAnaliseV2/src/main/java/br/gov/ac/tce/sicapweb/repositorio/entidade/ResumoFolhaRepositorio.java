package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.List;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import br.gov.ac.tce.sicapanalise.auditoria.dto.FolhaCompetenciaDTO;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoFolha;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;

@Stateless
public class ResumoFolhaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	@SuppressWarnings("unchecked")
	public List<Object[]> listaResumoGeral(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.entityManager.createNativeQuery("SELECT rf.DESCRICAO_TIPO_FOLHA, " + "rf.SITUACAO, "
					+ "rf.DESCRICAO_CARGO, " + "SUM(rf.QUANTIDADE) AS qtd, " + "SUM(rf.DESCONTO) AS des, "
					+ "SUM(rf.VENCIMENTO) AS ven, tipoCargo " + "FROM auditoria.vw_resumoFolhaGeral rf "
					+ "WHERE rf.REMESSA = ? "
					+ "GROUP BY rf.DESCRICAO_TIPO_FOLHA, rf.SITUACAO, rf.DESCRICAO_CARGO, tipoCargo "
					+ "ORDER BY rf.DESCRICAO_TIPO_FOLHA, rf.SITUACAO");

			query.setParameter(1, remessa.getId());
			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.entityManager.createNativeQuery("SELECT rf.DESCRICAO_TIPO_FOLHA, " + "rf.SITUACAO, "
					+ "rf.DESCRICAO_CARGO, " + "SUM(rf.QUANTIDADE) as qtd, " + "SUM(rf.DESCONTO) AS des, "
					+ "SUM(rf.VENCIMENTO) as ven, tipoCargo " + "FROM auditoria.vw_resumoFolhaGeral rf "
					+ "WHERE rf.REMESSA = ? AND rf.ID_TIPO_FOLHA = ? "
					+ "GROUP BY rf.DESCRICAO_TIPO_FOLHA, rf.SITUACAO, rf.DESCRICAO_CARGO, tipoCargo "
					+ "ORDER BY rf.DESCRICAO_TIPO_FOLHA, rf.SITUACAO");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());
			return (List<Object[]>) query.getResultList();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> listaResumoPorTipoFolha(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.entityManager.createNativeQuery("SELECT rt.DESCRICAO_TIPO_FOLHA,   "
					+ "SUM(rt.QUANTIDADE) as qtd, " + "SUM(rt.total_descontos_verbas) as tdv, "
					+ "SUM(rt.total_vencimentos_verbas) as tvv, rt.SITUACAO, rt.tipoCargo "
					+ "FROM auditoria.vw_resumoPorTipoFolha rt " + "WHERE rt.REMESSA = ? "
					+ "GROUP BY rt.DESCRICAO_TIPO_FOLHA, rt.SITUACAO, rt.tipoCargo "
					+ " ORDER BY rt.DESCRICAO_TIPO_FOLHA");

			query.setParameter(1, remessa.getId());

			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.entityManager.createNativeQuery("SELECT rt.DESCRICAO_TIPO_FOLHA,   "
					+ "SUM(rt.QUANTIDADE) as qtd, " + "SUM(rt.total_descontos_verbas) as tdv, "
					+ "SUM(rt.total_vencimentos_verbas) as tvv, rt.SITUACAO, rt.tipoCargo "
					+ "FROM auditoria.vw_resumoPorTipoFolha rt " + "WHERE rt.REMESSA = ? AND rt.ID_TIPO_FOLHA = ? "
					+ "GROUP BY rt.DESCRICAO_TIPO_FOLHA, rt.SITUACAO, tipoCargo "
					+ " ORDER BY rt.DESCRICAO_TIPO_FOLHA");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> listaResumoPorSituacaoBeneficiario(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.entityManager.createNativeQuery("SELECT rb.SITUACAO, "
					+ "SUM(rb.QUANTIDADE) AS QUANTIDADE, " + "SUM(rb.descontos_verbas) AS DESCONTO, "
					+ "SUM(rb.vencimentos_verbas) AS VENCIMENTO, rb.tipoCargo "
					+ "FROM auditoria.vw_resumoPorTipoBeneficiario rb " + "WHERE rb.REMESSA = ? "
					+ "GROUP BY rb.SITUACAO, rb.tipoCargo " + "ORDER BY rb.SITUACAO");

			query.setParameter(1, remessa.getId());

			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.entityManager.createNativeQuery("SELECT rb.SITUACAO, "
					+ "SUM(rb.QUANTIDADE) AS QUANTIDADE, " + "SUM(rb.descontos_verbas) AS DESCONTO, "
					+ "SUM(rb.vencimentos_verbas) AS VENCIMENTO, rb.tipoCargo "
					+ "FROM auditoria.vw_resumoPorTipoBeneficiario rb "
					+ "WHERE rb.REMESSA = ? AND rb.ID_TIPO_FOLHA = ? " + "GROUP BY rb.SITUACAO, rb.tipoCargo "
					+ "ORDER BY rb.SITUACAO");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> listaResumoPorCargo(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.entityManager.createNativeQuery("SELECT rc.DESCRICAO_CARGO, "
					+ "SUM(rc.QUANTIDADE) AS QUANTIDADE, " + "SUM(rc.descontos_verbas) AS DESCONTOS, "
					+ "SUM(rc.vencimentos_verbas) AS VENCIMENTOS, rc.tipoCargo "
					+ "FROM auditoria.vw_resumoPorCargo rc " + "WHERE rc.REMESSA = ? "
					+ "GROUP BY rc.DESCRICAO_CARGO, rc.tipoCargo " + "ORDER BY rc.DESCRICAO_CARGO");

			query.setParameter(1, remessa.getId());

			return ((List<Object[]>) query.getResultList());
		} else {
			Query query = this.entityManager.createNativeQuery("SELECT rc.DESCRICAO_CARGO, "
					+ "SUM(rc.QUANTIDADE) AS QUANTIDADE, " + "SUM(rc.descontos_verbas) AS DESCONTOS, "
					+ "SUM(rc.vencimentos_verbas) AS VENCIMENTOS, tipoCargo " + "FROM auditoria.vw_resumoPorCargo rc "
					+ "WHERE rc.REMESSA = ? AND rc.ID_TIPO_FOLHA = ? " + "GROUP BY rc.DESCRICAO_CARGO, rc.tipoCargo "
					+ "ORDER BY rc.DESCRICAO_CARGO");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> listaDadosFolhaPagamentoAnual(Remessa remessa) {
		if (remessa != null && remessa.getId() != null) {
			Query query = this.entityManager.createNativeQuery(
					"SELECT f.matricula, f.cpf, f.nome, f.sexo, f.dataNascimento, f.cargo, f.[dataAdmissao/inicioPensao], "
							+ "f.descricao, f.dataOcorrencia, f.tipoPensao, f.ano, f.JANEIRO, f.FEVEREIRO, f.MARCO, f.ABRIL, f.MAIO, f.JUNHO, f.JULHO, f.AGOSTO, f.SETEMBRO, "
							+ "f.OUTUBRO, f.NOVEMBRO, f.DEZEMBRO, f.DECIMO, f.valorTotal "
							+ " FROM vw_exportarFolhaPagamentoAnual f " + "WHERE f.idEntidadeCjur = ? AND f.ano = ? "
							+ "ORDER BY f.nome");

			query.setParameter(1, remessa.getEntidade().getIdEntidadeCjur());
			query.setParameter(2, remessa.getCompetencia().getAno());

			return (List<Object[]>) query.getResultList();
		}
		return null;
	}

	@SuppressWarnings("unchecked")
	public List<Object[]> listaResumoPorVerba(Remessa remessa, TipoFolha tipoFolha) {
		if (tipoFolha == null || tipoFolha.getId() == null) {
			Query query = this.entityManager.createNativeQuery(
					"SELECT rv.DESCRICAO, rv.DESCONTOS, rv.VENCIMENTOS, rv.tipoCargo FROM auditoria.vw_resumoFolhaPorVerba rv WHERE rv.idRemessa = ? ORDER BY rv.DESCRICAO");

			query.setParameter(1, remessa.getId());

			return (List<Object[]>) query.getResultList();
		} else {
			Query query = this.entityManager.createNativeQuery(
					"SELECT rv.DESCRICAO, rv.DESCONTOS, rv.VENCIMENTOS, rv.tipoCargo FROM auditoria.vw_resumoFolhaPorVerba rv WHERE rv.idRemessa = ? AND rv.idTipoFolha = ? ORDER BY rv.DESCRICAO");

			query.setParameter(1, remessa.getId());
			query.setParameter(2, tipoFolha.getId());

			return (List<Object[]>) query.getResultList();
		}
	}

	public List<FolhaCompetenciaDTO> listaResumoPorEntidade(List<Entidade> entidades, Integer ano) {
		List<FolhaCompetenciaDTO> listaResumoPorEntidade = null;
		String sql = "select distinct  " + 
				"cc.idEntidadeCjur as entidadeId, " + 
				"e.nome as entidadeNome, " + 
				"cc.idBeneficiario as beneficiarioId, " + 
				"b.matricula as beneficiarioMatricula, " + 
				"cu.cpf as beneficiarioCpf,  " + 
				"pf.nome as beneficiarioNome, " + 
				"pf.sexo as beneficiarioSexo, " + 
				"pf.dataNascimento as beneficiarioDataNascimento, " + 
				"CASE vf.tipoServidor WHEN 1 THEN 'CIVIL' WHEN 2 THEN 'MILITAR' END AS beneficiarioTipo, " + 
				"(select top(1) coalesce(ca2.nome,'PENS�O') from Cargo ca2 where ca2.idEntidadeCjur = cc.idEntidadeCjur and ca2.codigo = ca.codigo order by ca2.id desc) as beneficiarioCargo, " + 
				"CASE cc.SituacaoBeneficiario WHEN 'A' THEN 'ATIVO' WHEN 'I' THEN 'INATIVO' WHEN 'P' THEN 'PENSIONISTA' END AS beneficiarioSituacao, " + 
				"CASE ca.tipo WHEN 1 THEN 'EFETIVO' WHEN 2 THEN 'COMISSIONADO' WHEN 3 THEN 'TEMPOR�RIO' END AS beneficiarioCargoTipo, " + 
				"CASE ca.escolaridade WHEN 1 THEN 'N�VEL FUNDAMENTAL' WHEN 2 THEN 'N�VEL M�DIO' WHEN 3 THEN 'N�VEL SUPERIOR' WHEN 4 THEN 'N�VEL T�CNICO' WHEN 9 THEN 'N�O APLIC�VEL' END AS beneficiarioCargoEscolaridade,  " + 
				"ca.cargaHorariaMensal as beneficiarioCargoCargaHorariaMensal, " + 
				"UPPER(tv.descricao) as vinculoTipo, " + 
				"UPPER(sf.descricao) as situacaoFuncional, " + 
				"hf.dataOcorrencia as dataSituacaoFuncional, " + 
				"UPPER(ul.nome) as unidadeLotacao, " + 
				"UPPER(m.nome) as municipioLotacao, " + 
				"coalesce(vf.dataAdmissao,pns.dataInicio) as dataAdmissaoInicioPensao, " + 
				"CASE pns.tipoPensao when 1 then 'TEMPOR�RIA' when 2 then 'VITAL�CIA' end as pensaoTipo, " + 
				"UPPER(td.descricao) as dependenciaTipo, " + 
				"CASE vf.regimePrevidenciario WHEN 1 THEN 'RGPS' WHEN 2 THEN 'RPPS' END AS regimePrevidenciario, " + 
				"cc.ano,  " + 
				"sum(case when cc.mes = 1 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorJaneiro, " + 
				"sum(case when cc.mes = 2 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorFevereiro, " + 
				"sum(case when cc.mes = 3 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorMarco, " + 
				"sum(case when cc.mes = 4 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorAbril, " + 
				"sum(case when cc.mes = 5 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorMaio, " + 
				"sum(case when cc.mes = 6 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorJunho, " + 
				"sum(case when cc.mes = 7 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorJulho, " + 
				"sum(case when cc.mes = 8 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorAgosto, " + 
				"sum(case when cc.mes = 9 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorSetembro, " + 
				"sum(case when cc.mes = 10 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorOutubro, " + 
				"sum(case when cc.mes = 11 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorNovembro, " + 
				"sum(case when cc.mes = 12 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorDezembro, " + 
				"sum(case when cc.mes = 13 and v.natureza = 'C' then vcc.valor else 0 end) over(partition by cc.idBeneficiario,ul.nome,ca.codigo) as valorDecimo " + 
				"from ContraCheque cc " + 
				"inner join VerbasContraCheque vcc on vcc.idContraCheque = cc.id " + 
				"inner join Verba v on v.id = vcc.idVerba " + 
				"inner join Beneficiario b on b.id = cc.idBeneficiario " + 
				"inner join CadastroUnico cu on cu.id = cc.idCadastroUnico " + 
				"inner join Entidade e on cc.idEntidadeCjur = e.idEntidadeCjur " + 
				"inner join remessa.Remessa r on r.id = cc.idRemessa and r.situacao = 'CO' " + 
				"inner join PessoaFisica pf on pf.idCadastroUnico = cu.id and pf.registroAtivo = 1 " + 
				"left join VinculoFuncional vf on vf.idBeneficiario = b.id and vf.registroAtivo = 1 " + 
				"left join UnidadeLotacao ul on ul.id = cc.idUnidadeLotacao " +   
				"left join Municipio m on m.id = ul.idMunicipio " + 
				"left join TipoVinculo tv on tv.id = vf.idTipoVinculo " + 
				"left join Cargo ca on ca.id = cc.idCargoAtual " + 
				"left join Pensao pns on pns.idBeneficiarioPensionista = b.id " + 
				"left join TipoDependencia td on td.id = pns.idTipoDependencia " + 
				"left join ((select idBeneficiario, max(id) as id from HistoricoFuncional group by idBeneficiario)) hst " + 
				"on hst.idBeneficiario = b.id " + 
				"left join HistoricoFuncional hf on hf.id = hst.id " + 
				"left join SituacaoFuncional sf on sf.id = hf.idSituacaoFuncional " + 
				"where cc.ano = :pAno and cc.idEntidadeCjur in (:pEntidades)";
		
		try {
			listaResumoPorEntidade = this.entityManager.createNativeQuery(sql,"FolhaCompetenciaDTOMapping").setParameter("pAno", ano).setParameter("pEntidades", entidades).getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return listaResumoPorEntidade;
	}
}
