package br.gov.ac.tce.sicapweb.xml.validador;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.w3c.dom.Document;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

import br.gov.ac.tce.sicapweb.util.PropriedadeSistema;

public class XMLValidador implements ErrorHandler, Serializable {

	private static final long serialVersionUID = 1L;

	private List<String> listaErrosValidacao = null;

	public void validator(TypeArquivo xsdFile, File arquivoXML) {
		try {
			this.listaErrosValidacao = new ArrayList<String>();
			StringBuilder xml = new StringBuilder();
			String linha = null;
			String caminhoXsd = null;

			caminhoXsd = PropriedadeSistema.diretorioXSD + xsdFile.getArquivoXsd();

			BufferedReader bufferedReader = new BufferedReader(
					new InputStreamReader(new FileInputStream(arquivoXML), "UTF-8"));
			while ((linha = bufferedReader.readLine()) != null) {
				xml.append(linha);
			}
			bufferedReader.close();
			List<String> errosValidacao = new ArrayList<String>();
			errosValidacao = validateXml(normalizeXML(xml.toString()), caminhoXsd);
			if (!((errosValidacao != null) && (errosValidacao.size() > 0))) {
				this.listaErrosValidacao = null;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private List<String> validateXml(String xml, String xsd) throws Exception {
		DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		factory.setNamespaceAware(true);
		factory.setValidating(true);
		factory.setAttribute("http://java.sun.com/xml/jaxp/properties/schemaLanguage",
				"http://www.w3.org/2001/XMLSchema");
		factory.setAttribute("http://java.sun.com/xml/jaxp/properties/schemaSource", xsd);

		DocumentBuilder builder = null;

		try {
			builder = factory.newDocumentBuilder();
			builder.setErrorHandler(this);
		} catch (ParserConfigurationException e) {
			throw new Exception(e.toString());
		}

		try {
			Document document = null;
			document = builder.parse(new ByteArrayInputStream(xml.getBytes()));
			// Node rootNode = document.getFirstChild();

			return this.getListaErrosValidacao();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return this.getListaErrosValidacao();
	}

	private boolean isError(SAXParseException exception) {
		if (exception.getMessage().startsWith("cvc-pattern-valid")
				|| exception.getMessage().startsWith("cvc-maxLength-valid")
				|| exception.getMessage().startsWith("cvc-datatype")) {
			return false;
		}
		return true;
	}

	@Override
	public void error(SAXParseException exception) throws SAXException {
		if (isError(exception)) {
			this.listaErrosValidacao.add(exception.getMessage());
		}
	}

	@Override
	public void fatalError(SAXParseException exception) throws SAXException {
		this.listaErrosValidacao.add(exception.getMessage());
	}

	@Override
	public void warning(SAXParseException exception) throws SAXException {
		this.listaErrosValidacao.add(exception.getMessage());
	}

	private String normalizeXML(String xml) {
		if ((xml != null) && (!"".equals(xml))) {
			xml = xml.replace("\\r\\n", "");
		}
		return xml;
	}

	public List<String> getListaErrosValidacao() {
		return listaErrosValidacao;
	}
}
