package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collection;

import javax.faces.event.ActionEvent;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicapanalise.auditoria.dto.ContraChequeDTO;
import br.gov.ac.tce.sicapweb.repositorio.ContraChequeRepositorio;

@Named
@ViewScoped
public class AuditoriaContraChequeBean implements Serializable {

	private static final long serialVersionUID = -7481926058609835192L;

	@Inject
	private ContraChequeRepositorio contraChequeRepositorio;

	private Collection<ContraChequeDTO> contraCheque;
	
	private Long idBeneficiario;
	private Integer ano;
	private Integer mes;

	public Collection<ContraChequeDTO> getContraCheque() {
		return contraCheque;
	}

	public void setContraCheque(Collection<ContraChequeDTO> contraCheque) {
		this.contraCheque = contraCheque;
	}

	public void carregaContraCheque() {
		this.contraCheque = this.contraChequeRepositorio.retornaContraCheque(idBeneficiario, ano, mes);
	}
	
	public void carregaParametros(ActionEvent event) {
		idBeneficiario = (Long)event.getComponent().getAttributes().get("beneficiario");
		ano = (Integer)event.getComponent().getAttributes().get("ano");
		mes = (Integer)event.getComponent().getAttributes().get("mes");
	}
	
	public BigDecimal getValorLiquido() {
		if (contraCheque != null && !contraCheque.isEmpty()) {
			ContraChequeDTO cc = (ContraChequeDTO) contraCheque.iterator().next();
			return cc.getTotalVencimentos().subtract(cc.getTotalDescontos());
		}
		return null;
	}
}
