package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.PrimeFaces;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoSituacaoAnalise;
import br.gov.ac.tce.sicapanalise.auditoria.business.AnaliseBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.AcumulacaoAnaliseDTO;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named
@ViewScoped
public class AnaliseAcumulacaoBean implements Serializable {

	private static final long serialVersionUID = 1L;

	private TipoSituacaoAnalise tipoSituacaoAnalise;

	private List<AcumulacaoAnaliseDTO> listaAcumulacao;
	private List<AcumulacaoAnaliseDTO> listaAcumulacaoSelecionadas;

	@Inject
	private LoginBean loginBean;

	@Inject
	private AnaliseBusiness analiseBusiness;

	private int nivelAtual = 1;

	@PostConstruct
	void inicializado() {
		carregaAnalises();
	}

	public void carregaAnalises() {
		listaAcumulacao = analiseBusiness.retornaListaAnaliseAcumulacao(tipoSituacaoAnalise, loginBean.getUsuario());

	}

	public Collection<TipoSituacaoAnalise> getListaTipoSituacaoAnalise() {
		return analiseBusiness.retornaTiposSituacaoAnalise();
	}

	public int getNivelAtual() {
		return nivelAtual;
	}

	public void setNivelAtual(int nivelAtual) {
		this.nivelAtual = nivelAtual;
	}

	public TipoSituacaoAnalise getTipoSituacaoAnalise() {
		return tipoSituacaoAnalise;
	}

	public void setTipoSituacaoAnalise(TipoSituacaoAnalise tipoSituacaoAnalise) {
		this.tipoSituacaoAnalise = tipoSituacaoAnalise;
	}

	public List<AcumulacaoAnaliseDTO> getListaAcumulacao() {
		return listaAcumulacao;
	}

	public void setListaAcumulacao(List<AcumulacaoAnaliseDTO> listaAcumulacao) {
		this.listaAcumulacao = listaAcumulacao;
	}

	public List<AcumulacaoAnaliseDTO> getListaAcumulacaoSelecionadas() {
		return listaAcumulacaoSelecionadas;
	}

	public void setListaAcumulacaoSelecionadas(List<AcumulacaoAnaliseDTO> listaAcumulacaoSelecionadas) {
		this.listaAcumulacaoSelecionadas = listaAcumulacaoSelecionadas;
	}

	public void checaAcumulacoesParaNotificar() {
		checaAcumulacoesSelecionadas("Notificar", Arrays.asList("AR", "NR", "PE"),
				"Já existe existe notificação gerada para a(s) acumulação(ões) selecionada(s).");
	}

	public void checaAcumulacoesParaConcluir() {
		checaAcumulacoesSelecionadas("Concluir", Arrays.asList("RE", "ID", "IC","IL"),
				"Nenhuma acumulação apta para conclusão foi selecionada.");
	}

	private void checaAcumulacoesSelecionadas(String operacao, List<String> situacoesNaoPermitidas,
			String mensagemErro) {
		if (this.listaAcumulacaoSelecionadas == null || this.listaAcumulacaoSelecionadas.isEmpty()) {
			Mensagem.setMensagem(MensagemType.ERRO, "Nenhuma acumulação foi selecionada", "");
		} else {
			List<AcumulacaoAnaliseDTO> listaAcumulacaoEmAnalise = this.listaAcumulacaoSelecionadas.stream()
					.filter(a -> ((a.getAnaliseId() != null && operacao.equals("Concluir"))
							|| operacao.equals("Notificar") && a.getAnaliseId() == null)
							&& !situacoesNaoPermitidas.contains(a.getAcumulacaoSituacaoCodigo()))
					.collect(Collectors.toList());
			if (listaAcumulacaoEmAnalise == null || listaAcumulacaoEmAnalise.isEmpty())
				Mensagem.setMensagem(MensagemType.ERRO, mensagemErro, "");
			this.listaAcumulacaoSelecionadas = listaAcumulacaoEmAnalise;
		}
	}

	public void viewDocumentos(AcumulacaoAnaliseDTO acumulacaoAnalise) {
		try {
			Map<String, Object> options = new HashMap<String, Object>();
			options.put("modal", true);
			options.put("responsive", true);
			options.put("resizable", false);
			options.put("closeOnEscape", true);
//	        options.put("style", "margin-top: 25px;");
			options.put("fitViewport", true);
//        	options.put("position", "top");
			options.put("maximizable", true);
			options.put("showEffect", "fade");
			options.put("hideEffect", "fade");
			options.put("width", "90%");
			options.put("height", "90%");
			options.put("contentWidth", "100%");
			options.put("contentHeight", "100%");

			Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
			sessionMap.put("acumulacaoAnalise", acumulacaoAnalise);
//	        RequestContext.getCurrentInstance().openDialog("documentos", options, null);
			PrimeFaces.current().dialog().openDynamic("documentos", options, null);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

}
