package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;
import java.util.Set;
import java.util.TreeSet;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.event.FlowEvent;

import br.gov.ac.tce.sicapanalise.modelo.agrupamentos.ClasseCargos;
import br.gov.ac.tce.sicapanalise.modelo.agrupamentos.SubClasseCargoComparator;
import br.gov.ac.tce.sicapanalise.modelo.agrupamentos.SubClasseCargos;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;

@Named
@ViewScoped
public class ClasseCargosBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private ClasseCargos classeCargos;
	@Inject
	private SubClasseCargos subClasseCargos;
	@Inject
	private Entidade Entidade;

	@Inject
	private EntidadeRepositorio entidadeRepositorio;

	private Collection<Cargo> listaCargos;
	private Collection<Entidade> listaEntidade;
	private Set<SubClasseCargos> listaSubClasseCargoInsert;

	private int currentLevel;
	private Boolean possuiSubClasse;

	@PostConstruct
	public void init() {
		try {
			this.currentLevel = 1;
			this.possuiSubClasse = true;
			this.classeCargos.setPermitirAlteracao(true);
			this.listaSubClasseCargoInsert = new TreeSet<SubClasseCargos>(new SubClasseCargoComparator());
			this.setListaEntidade(this.entidadeRepositorio.listaTodasRepositorioSicap());
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao selecionar o filtro para a consulta.", "");
		}
	}

	public void adicionarSubClasse() {

		if (this.subClasseCargos.getNome().equals("") || this.subClasseCargos.getNome().isEmpty()) {
			Mensagem.setMensagem(MensagemType.ALERTA, "O nome da subClasse deve ser informado.", "");
		} else {
			if (!this.listaSubClasseCargoInsert.add(this.subClasseCargos)) {
				Mensagem.setMensagem(MensagemType.ALERTA, "Já existe uma subClasse criada com esse nome.", "");
			} else {
				this.subClasseCargos = new SubClasseCargos();
			}
		}
	}

	public void editarSubClasse(SubClasseCargos subClasseCargos) {
		if (this.listaSubClasseCargoInsert.remove(subClasseCargos)) {
			this.subClasseCargos = subClasseCargos;
		} else {
			Mensagem.setMensagem(MensagemType.ERRO, "Erro ao editar subClasse.", "");
		}
	}

	public void removerSubClasse(SubClasseCargos subClasseCargos) {
		if (this.listaSubClasseCargoInsert.remove(subClasseCargos)) {
			Mensagem.setMensagem(MensagemType.INFORMACAO, "SubClasse removida com sucesso.", "");
		} else {
			Mensagem.setMensagem(MensagemType.ERRO, "Erro ao remover subClasse.", "");
		}
	}

	public String onFlowProcess(FlowEvent event) {
		if (!this.possuiSubClasse) {
			System.out.println("sem subClasse.");
			return "tabListaCargos";
		} else {
			return event.getNewStep();
		}
	}

	public Collection<Entidade> getListaEntidade() {
		return listaEntidade;
	}

	public void setListaEntidade(Collection<Entidade> listaEntidade) {
		this.listaEntidade = listaEntidade;
	}

	public Entidade getEntidade() {
		return Entidade;
	}

	public void setEntidade(Entidade entidade) {
		Entidade = entidade;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<Cargo> getListaCargos() {
		return listaCargos;
	}

	public void setListaCargos(Collection<Cargo> listaCargos) {
		this.listaCargos = listaCargos;
	}

	public ClasseCargos getClasseCargos() {
		return classeCargos;
	}

	public void setClasseCargos(ClasseCargos classeCargos) {
		this.classeCargos = classeCargos;
	}

	public SubClasseCargos getSubClasseCargos() {
		return subClasseCargos;
	}

	public void setSubClasseCargos(SubClasseCargos subClasseCargos) {
		this.subClasseCargos = subClasseCargos;
	}

	public Boolean getPossuiSubClasse() {
		return possuiSubClasse;
	}

	public void setPossuiSubClasse(Boolean possuiSubClasse) {
		this.possuiSubClasse = possuiSubClasse;
	}

	public Set<SubClasseCargos> getListaSubClasseCargoInsert() {
		return listaSubClasseCargoInsert;
	}

	public void setListaSubClasseCargoInsert(Set<SubClasseCargos> listaSubClasseCargoInsert) {
		this.listaSubClasseCargoInsert = listaSubClasseCargoInsert;
	}
}
