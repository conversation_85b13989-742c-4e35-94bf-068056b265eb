package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.Relatorio;

@Stateless
public class RelatorioRepositorio {

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager em;

	public void salvar(Relatorio relatorio) {
		em.persist(relatorio);		
	}

	
}
