package br.gov.ac.tce.sicapweb.util;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.nio.charset.Charset;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipException;
import java.util.zip.ZipFile;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;

import org.primefaces.model.UploadedFile;

//import org.primefaces.model.UploadedFile;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;
import br.gov.ac.tce.sicapweb.xml.exception.ArquivoRemessaException;
import br.gov.ac.tce.sicapweb.xml.exception.DiretorioCreateException;
import br.gov.ac.tce.sicapweb.xml.exception.EstruturaXMLException;
import br.gov.ac.tce.sicapweb.xml.exception.NomeArquivoException;
import br.gov.ac.tce.sicapweb.xml.validador.SicapXML;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;
import br.gov.ac.tce.sicapweb.xml.validador.XMLValidador;

public class TratamentoArquivoRemessa implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 
	 * Metodo para descompactar arquivos das remessas periodicas
	 * 
	 * @param remessa
	 * @param arquivoZipDescompactar
	 * @return um objeto do tipo SicapArquivo - atributo file com o arquivo xml
	 *         e um atributo do tipo TypeArquivo identificando o tipo de arquivo
	 * @throws ZipException
	 * @throws IOException
	 * @throws DiretorioCreateException
	 * @throws ArquivoRemessaException
	 * @throws EstruturaXMLException
	 * @throws Exception
	 */
	@SuppressWarnings("finally")
	public static SicapArquivo descompactarArquivoZip(Remessa remessa, File arquivoZipDescompactar) throws ZipException,
			IOException, DiretorioCreateException, ArquivoRemessaException, EstruturaXMLException, Exception {

		ZipFile arquivoZip = null;

		File arquivo = null;
		String nomeFullArquivo = null;
		SicapArquivo sicapArquivo = null;

		XMLValidador xmlValidador = null;
		ZipEntry zipEntry = null;

		File diretorioTemporarioXML;

		try {
			diretorioTemporarioXML = new File(PropriedadeSistema.diretorioTemporarioXML);

			xmlValidador = new XMLValidador();
			sicapArquivo = new SicapArquivo();
			nomeFullArquivo = PropriedadeSistema.diretorioTemporarioXML
					+ criarNovoNomeArquivo(remessa, ExtensaoArquivo.XML);

			if (!diretorioTemporarioXML.exists() || !diretorioTemporarioXML.isDirectory()) {
				throw new DiretorioCreateException("O Diretorio para descompactar os arquivos não existe.");
			}
			arquivoZip = new ZipFile(arquivoZipDescompactar);
			@SuppressWarnings("unchecked")
			Enumeration<ZipEntry> listaArquivosDoZIP = (Enumeration<ZipEntry>) arquivoZip.entries();
			if ((listaArquivosDoZIP.hasMoreElements()) && (arquivoZip.size() == 1)) {
				zipEntry = (ZipEntry) listaArquivosDoZIP.nextElement();
				arquivo = new File(nomeFullArquivo);
				escreverXMLDisco(arquivoZip, zipEntry, new FileOutputStream(arquivo));
				xmlValidador.validator(TypeArquivo.SICAP, arquivo);
				if (xmlValidador.getListaErrosValidacao() == null) {
					sicapArquivo = carregarXML(arquivo);
				} else {
					sicapArquivo.setArquivo(arquivo);
					throw new EstruturaXMLException(
							"Erro na estrutura do arquivo XML, é impossivel determinar o tipo de arquivo xml.");
				}

			} else {
				throw new ArquivoRemessaException(
						"O arquivo " + arquivoZipDescompactar.getName() + " não possui um arquivo XML valido.");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("Não foi possível criar o arquivo XML.");
		} finally {
			arquivoZip.close();
			return sicapArquivo;
		}
	}

	/**
	 * 
	 * Metodo para descompactar arquivos das remessas eventuais
	 * 
	 * @param entidade
	 * @param arquivoZipDescompactar
	 * @param typeArquivo
	 * @return um objeto do tipo SicapArquivo - atributo file com o arquivo xml
	 *         e um atributo do tipo TypeArquivo identificando o tipo de arquivo
	 * @throws ZipException
	 * @throws IOException
	 * @throws DiretorioCreateException
	 * @throws ArquivoRemessaException
	 * @throws EstruturaXMLException
	 * @throws Exception
	 */
	@SuppressWarnings({ "finally", "unchecked" })
	public static SicapArquivo descompactarArquivoZip(Entidade entidade, File arquivoZipDescompactar,
			TypeArquivo typeArquivo) throws ZipException, IOException, DiretorioCreateException,
			ArquivoRemessaException, EstruturaXMLException, Exception {

		ZipFile arquivoZip = null;

		File arquivo = null;
		String nomeFullArquivo = null;
		SicapArquivo sicapArquivo = null;

		XMLValidador xmlValidador = null;
		ZipEntry zipEntry = null;

		File diretorioTemporarioXML;

		diretorioTemporarioXML = new File(PropriedadeSistema.diretorioTemporarioXML);

		try {
			xmlValidador = new XMLValidador();
			sicapArquivo = new SicapArquivo();
			nomeFullArquivo = PropriedadeSistema.diretorioTemporarioXML
					+ criarNovoNomeArquivo(entidade, ExtensaoArquivo.XML, typeArquivo);

			if (!diretorioTemporarioXML.exists() || !diretorioTemporarioXML.isDirectory()) {
				throw new DiretorioCreateException("O Diretorio para descompactar os arquivos não existe.");
			}
			arquivoZip = new ZipFile(arquivoZipDescompactar);
			Enumeration<ZipEntry> listaArquivosDoZIP = (Enumeration<ZipEntry>) arquivoZip.entries();
			if ((listaArquivosDoZIP.hasMoreElements()) && (arquivoZip.size() == 1)) {
				zipEntry = (ZipEntry) listaArquivosDoZIP.nextElement();
				arquivo = new File(nomeFullArquivo);
				escreverXMLDisco(arquivoZip, zipEntry, new FileOutputStream(arquivo));
				xmlValidador.validator(TypeArquivo.SICAP, arquivo);
				if (xmlValidador.getListaErrosValidacao() == null) {
					sicapArquivo = carregarXML(arquivo);
				} else {
					sicapArquivo.setArquivo(arquivo);
					throw new EstruturaXMLException(
							"Erro na estrutura do arquivo XML, é impossivel determinar o tipo de arquivo xml.");
				}

			} else {
				throw new ArquivoRemessaException(
						"O arquivo " + arquivoZipDescompactar.getName() + " não possui um arquivo XML valido.");
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("Não foi possível criar o arquivo XML.");
		} finally {
			arquivoZip.close();
			return sicapArquivo;
		}
	}

	private static void escreverXMLDisco(ZipFile zipFile, ZipEntry zipEntry, OutputStream outputStream)
			throws IOException, ZipException {
		byte[] buffer = new byte[2048];
		int bytesLido = 0;
		InputStream inputStream = null;

		inputStream = zipFile.getInputStream(zipEntry);
		if (inputStream == null) {
			throw new ZipException("Erro ao ler entrada do zip: " + zipEntry.getName());
		} else {
			while ((bytesLido = inputStream.read(buffer)) > 0) {
				outputStream.write(buffer, 0, bytesLido);
			}
		}
		inputStream.close();
		outputStream.close();
	}

	@SuppressWarnings("finally")
	public static File salvarArquivoZipDisco(Entidade entidade, UploadedFile arquivo, String nomeArquivoDestino)
			throws DiretorioCreateException, FileNotFoundException, IOException {
		File diretorioDestino = null;
		File arquivoDestino = null;
		FileOutputStream fileOutputStream = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				diretorioDestino = new File(PropriedadeSistema.diretorioZIP + entidade.getIdEntidadeCjur());
				if (!diretorioDestino.exists()) {
					if (!diretorioDestino.mkdirs()) {
						throw new DiretorioCreateException(
								"Não foi possível criar diretório de destino: " + diretorioDestino.getAbsolutePath());
					}
				}
				arquivoDestino = new File(diretorioDestino + "/" + nomeArquivoDestino);
				fileOutputStream = new FileOutputStream(arquivoDestino);
				fileOutputStream.write(arquivo.getContents());

			} else {
				throw new DiretorioCreateException(
						"Não foi possível criar diretório de destino: nenhuma entidade foi selecionada.");
			}
		} catch (FileNotFoundException e) {
			throw new FileNotFoundException("Arquivo de destino não encontrado: " + arquivoDestino.getName());
		} catch (IOException e) {
			throw new IOException("Não foi possível criar arquivo de destino:" + arquivoDestino.getName());
		} finally {
			fileOutputStream.close();
			return arquivoDestino;
		}
	}

	public static String criarNovoNomeArquivo(Remessa remessa, ExtensaoArquivo extensaoArquivo)
			throws NomeArquivoException {
		String novoNome = null;
		LocalDateTime localDateTimeAtual = null;
		try {
			if (remessa.getId() != null) {
				localDateTimeAtual = LocalDateTime.now();
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy_MM_dd_HH_mm_ss");
				novoNome = "M_" + remessa.getEntidade().getIdEntidadeCjur() + "_" + remessa.getCompetencia().getMes()
						+ "_" + remessa.getCompetencia().getAno() + "_" + localDateTimeAtual.format(formatter)
						+ extensaoArquivo.getExtensao();
			} else {
				throw new NomeArquivoException(
						"Não foi possivel criar o nome do arquivo, nenhuma remessa foi selecionada.");
			}
		} catch (Exception e) {
			throw new NomeArquivoException("Não foi possivel criar o nome do arquivo.", e.getCause());
		}
		return novoNome;
	}

	public static String criarNovoNomeArquivo(Entidade entidade, ExtensaoArquivo extensaoArquivo,
			TypeArquivo typeArquivo) throws NomeArquivoException {
		String novoNome = null;
		LocalDateTime localDateTimeAtual = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				localDateTimeAtual = LocalDateTime.now();
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy_MM_dd_HH_mm_ss");
				switch (typeArquivo.getId()) {
				case 1:
					novoNome = "E1_";
					break;
				case 2:
					novoNome = "E2_";
					break;
				case 3:
					novoNome = "E3_";
					break;
				case 4:
					novoNome = "E4_";
					break;
				case 5:
					novoNome = "E5_";
					break;
				case 6:
					novoNome = "E6_";
					break;
				case 7:
					novoNome = "E7_";
					break;
				case 8:
					novoNome = "E8_";
					break;
				default:
					novoNome = null;
					break;
				}
				novoNome = novoNome + entidade.getIdEntidadeCjur() + "_" + localDateTimeAtual.format(formatter)
						+ extensaoArquivo.getExtensao();
			} else {
				throw new NomeArquivoException(
						"Não foi possivel criar o nome do arquivo, nenhuma entidade foi selecionada.");
			}
		} catch (Exception e) {
			throw new NomeArquivoException("Não foi possivel criar o nome do arquivo.", e.getCause());
		}
		return novoNome;
	}

	/**
	 * 
	 * Metodo para definir o tipo de remessa a partir do atributo arquivo
	 * presente na tag sicap
	 * 
	 * @param arquivoXML
	 * @return um objeto do tipo SicapArquivo - atributo file com o arquivo xml
	 *         e um atributo do tipo TypeArquivo identificando o tipo de arquivo
	 */
	@SuppressWarnings("finally")
	private static SicapArquivo carregarXML(File arquivoXML) {
		JAXBContext jaxbContext;
		Unmarshaller unmarshaller;
		SicapXML sicapXML = null;
		TypeArquivo typeArquivo = null;
		SicapArquivo sicapArquivo = null;
		try {
			jaxbContext = JAXBContext.newInstance(SicapXML.class);
			unmarshaller = jaxbContext.createUnmarshaller();
			sicapXML = (SicapXML) unmarshaller.unmarshal(arquivoXML);
			sicapArquivo = new SicapArquivo();
			switch (sicapXML.getArquivo()) {
			case "cargos":
				typeArquivo = TypeArquivo.CARGO;
				break;
			case "servidores":
				typeArquivo = TypeArquivo.SERVIDOR;
				break;
			case "pensionistas":
				typeArquivo = TypeArquivo.PENSIONISTA;
				break;
			case "verbas":
				typeArquivo = TypeArquivo.VERBAS;
				break;
			case "tiposFolha":
				typeArquivo = TypeArquivo.TIPOS_FOLHA;
				break;
			case "tabelaVencimentos":
				typeArquivo = TypeArquivo.TABELAS_VENCIMENTO;
				break;
			case "unidadesLotacao":
				typeArquivo = TypeArquivo.UNIDADES_LOTACAO;
				break;
			case "historicoFuncional":
				typeArquivo = TypeArquivo.HISTORICO_FUNCIONAL;
				break;
			case "contracheque":
				typeArquivo = TypeArquivo.CONTRACHEQUE;
				break;

			default:
				typeArquivo = null;
				break;
			}
			sicapArquivo.setTypeArquivo(typeArquivo);
			sicapArquivo.setArquivo(arquivoXML);
			sicapArquivo.setMes(sicapXML.getContraChequesXML().getMes());
			sicapArquivo.setAno(sicapXML.getContraChequesXML().getAno());
		} catch (JAXBException e) {
		} finally {
			return sicapArquivo;
		}
	}

	public static Boolean arquivoExiste(String arquivoXML) {
		File arquivo = null;
		arquivo = new File(PropriedadeSistema.diretorioTemporarioXML + arquivoXML);
		return arquivo.exists();
	}

	public static String getCaminhoArquivoXML(String arquivoXML) {
		return PropriedadeSistema.diretorioTemporarioXML + arquivoXML;
	}

	public static Boolean removerArquivoXML(String arquivoXML) {

		Path arquivo = null;
		try {
			arquivo = FileSystems.getDefault().getPath(PropriedadeSistema.diretorioTemporarioXML + arquivoXML);
			return Files.deleteIfExists(arquivo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public static Boolean removerArquivoCompactado(Entidade entidade, String arquivoZIP) {

		Path arquivo = null;
		try {
			arquivo = FileSystems.getDefault()
					.getPath(PropriedadeSistema.diretorioZIP + entidade.getIdEntidadeCjur() + '/' + arquivoZIP);
			return Files.deleteIfExists(arquivo);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}

	public static String lerConteudoXML(File arquivoXML) {
		byte[] bytesArquivoXML = null;
		String conteudoXML = null;
		try {
			bytesArquivoXML = Files.readAllBytes(Paths.get(arquivoXML.getAbsolutePath(), new String[0]));
			conteudoXML = new String(bytesArquivoXML, Charset.defaultCharset());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return conteudoXML;
	}
}
