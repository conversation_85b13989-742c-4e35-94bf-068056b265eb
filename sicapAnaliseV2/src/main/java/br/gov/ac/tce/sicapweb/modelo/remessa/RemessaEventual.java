package br.gov.ac.tce.sicapweb.modelo.remessa;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Entity
@Table(schema = "remessa")
@NamedQueries({
		@NamedQuery(name = RemessaEventual.listarTodasPorEntidade, query = "select r from RemessaEventual r where r.entidade = :entidade order by r.id desc"),
		@NamedQuery(name = RemessaEventual.listarPorEntidadeTipo, query = "select r from RemessaEventual r where r.entidade = :entidade and r.tipoRemessa = :tipoRemessa"),
		@NamedQuery(name = RemessaEventual.listarPorEntidadeTipoSituacao, query = "select r from RemessaEventual r where r.entidade = :entidade and r.tipoRemessa = :tipoRemessa and r.situacao = :situacao order by r.id"),
		@NamedQuery(name = RemessaEventual.buscarPorId, query = "select r from RemessaEventual r where r.id = :id") })
public class RemessaEventual implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String listarTodasPorEntidade = "RemessaEventual.listarTodasPorEntidade";
	public static final String listarPorEntidadeTipo = "RemessaEventual.listarPorEntidadeTipo";
	public static final String listarPorEntidadeTipoSituacao = "RemessaEventual.listarPorEntidadeTipoSituacao";
	public static final String buscarPorId = "RemessaEventual.buscarPorId";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(nullable = false)
	private LocalDateTime dataEnvio;
	@Column(length = 100, nullable = false)
	private String nomeOriginalArquivoZip;
	@Column(length = 100, nullable = false)
	private String nomeAtualArquivoZip;
	@Column(length = 100, nullable = false)
	private String nomeArquivoXML;
	@Column(length = 100, nullable = false)
	private String hashArquivo;
	@Column(length = 2, nullable = false)
	private String situacao;
	@Column(length = 2, nullable = false)
	private Integer tipoRemessa;
	@Column(name = "idUsuario", nullable = false)
	private Integer usuario;
	@OneToMany(mappedBy = "remessaEventual", fetch = FetchType.LAZY)
	private Collection<ErrosProcessamentoRemessaEventual> listaErrosProcessamentoRemessaEventual;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public LocalDateTime getDataEnvio() {
		return dataEnvio;
	}

	public void setDataEnvio(LocalDateTime dataEnvio) {
		this.dataEnvio = dataEnvio;
	}

	public String getNomeOriginalArquivoZip() {
		return nomeOriginalArquivoZip;
	}

	public void setNomeOriginalArquivoZip(String nomeOriginalArquivoZip) {
		this.nomeOriginalArquivoZip = nomeOriginalArquivoZip;
	}

	public String getNomeAtualArquivoZip() {
		return nomeAtualArquivoZip;
	}

	public void setNomeAtualArquivoZip(String nomeAtualArquivoZip) {
		this.nomeAtualArquivoZip = nomeAtualArquivoZip;
	}

	public String getHashArquivo() {
		return hashArquivo;
	}

	public void setHashArquivo(String hashArquivo) {
		this.hashArquivo = hashArquivo;
	}

	public SituacaoRemessa getSituacao() {
		return SituacaoRemessa.parse(this.situacao);
	}

	public void setSituacao(SituacaoRemessa situacaoRemessa) {
		this.situacao = situacaoRemessa.getId();
	}

	public String getNomeArquivoXML() {
		return nomeArquivoXML;
	}

	public void setNomeArquivoXML(String nomeArquivoXML) {
		this.nomeArquivoXML = nomeArquivoXML;
	}

	public TypeArquivo getTipoRemessa() {
		return TypeArquivo.parse(this.tipoRemessa);
	}

	public void setTipoRemessa(TypeArquivo tipoRemessa) {
		this.tipoRemessa = tipoRemessa.getId();
	}

	public Integer getUsuario() {
		return usuario;
	}

	public void setUsuario(Integer usuario) {
		this.usuario = usuario;
	}

	public Collection<ErrosProcessamentoRemessaEventual> getListaErrosProcessamentoRemessaEventual() {
		return listaErrosProcessamentoRemessaEventual;
	}

	public void setListaErrosProcessamentoRemessaEventual(
			Collection<ErrosProcessamentoRemessaEventual> listaErrosProcessamentoRemessaEventual) {
		this.listaErrosProcessamentoRemessaEventual = listaErrosProcessamentoRemessaEventual;
	}

	public Boolean isErroProcessamento() {
		if (this.situacao.equals("EI")) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dataEnvio == null) ? 0 : dataEnvio.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((hashArquivo == null) ? 0 : hashArquivo.hashCode());
		result = prime * result + ((listaErrosProcessamentoRemessaEventual == null) ? 0
				: listaErrosProcessamentoRemessaEventual.hashCode());
		result = prime * result + ((nomeArquivoXML == null) ? 0 : nomeArquivoXML.hashCode());
		result = prime * result + ((nomeAtualArquivoZip == null) ? 0 : nomeAtualArquivoZip.hashCode());
		result = prime * result + ((nomeOriginalArquivoZip == null) ? 0 : nomeOriginalArquivoZip.hashCode());
		result = prime * result + ((situacao == null) ? 0 : situacao.hashCode());
		result = prime * result + ((tipoRemessa == null) ? 0 : tipoRemessa.hashCode());
		result = prime * result + ((usuario == null) ? 0 : usuario.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RemessaEventual other = (RemessaEventual) obj;
		if (dataEnvio == null) {
			if (other.dataEnvio != null)
				return false;
		} else if (!dataEnvio.equals(other.dataEnvio))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (hashArquivo == null) {
			if (other.hashArquivo != null)
				return false;
		} else if (!hashArquivo.equals(other.hashArquivo))
			return false;
		if (listaErrosProcessamentoRemessaEventual == null) {
			if (other.listaErrosProcessamentoRemessaEventual != null)
				return false;
		} else if (!listaErrosProcessamentoRemessaEventual.equals(other.listaErrosProcessamentoRemessaEventual))
			return false;
		if (nomeArquivoXML == null) {
			if (other.nomeArquivoXML != null)
				return false;
		} else if (!nomeArquivoXML.equals(other.nomeArquivoXML))
			return false;
		if (nomeAtualArquivoZip == null) {
			if (other.nomeAtualArquivoZip != null)
				return false;
		} else if (!nomeAtualArquivoZip.equals(other.nomeAtualArquivoZip))
			return false;
		if (nomeOriginalArquivoZip == null) {
			if (other.nomeOriginalArquivoZip != null)
				return false;
		} else if (!nomeOriginalArquivoZip.equals(other.nomeOriginalArquivoZip))
			return false;
		if (situacao == null) {
			if (other.situacao != null)
				return false;
		} else if (!situacao.equals(other.situacao))
			return false;
		if (tipoRemessa == null) {
			if (other.tipoRemessa != null)
				return false;
		} else if (!tipoRemessa.equals(other.tipoRemessa))
			return false;
		if (usuario == null) {
			if (other.usuario != null)
				return false;
		} else if (!usuario.equals(other.usuario))
			return false;
		return true;
	}

}
