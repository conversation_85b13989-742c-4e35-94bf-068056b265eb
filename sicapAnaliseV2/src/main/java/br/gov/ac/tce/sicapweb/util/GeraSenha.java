package br.gov.ac.tce.sicapweb.util;

import java.util.Random;


public class GeraSenha {
	private static final Random RANDOM = new Random();

	public static String createSenha(Level level, Integer size) {

		String chars = level.toString();
		String senha = "";

		int maxChars = chars.length();

		for (Integer j = 0; j < size; j++) {
			double i = RANDOM.nextFloat() * (maxChars - 1);
			senha += chars.charAt((int) i);
		}
		return senha;
	}
}
