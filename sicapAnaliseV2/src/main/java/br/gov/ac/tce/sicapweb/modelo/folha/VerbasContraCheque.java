package br.gov.ac.tce.sicapweb.modelo.folha;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Entity
public class VerbasContraCheque implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idContraCheque", nullable = false)
	private ContraCheque contraCheque;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idVerba", nullable = false)
	private Verba verba;
	@Column(length = 30)
	private String referencia;
	@Column(nullable = false, precision = 10, scale = 2)
	private BigDecimal valor;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public ContraCheque getContraCheque() {
		return contraCheque;
	}

	public void setContraCheque(ContraCheque contraCheque) {
		this.contraCheque = contraCheque;
	}

	public Verba getVerba() {
		return verba;
	}

	public void setVerba(Verba verba) {
		this.verba = verba;
	}

	public String getReferencia() {
		return referencia;
	}

	public void setReferencia(String referencia) {
		this.referencia = referencia;
	}

	public BigDecimal getValor() {
		return valor;
	}

	public void setValor(BigDecimal valor) {
		this.valor = valor;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((contraCheque == null) ? 0 : contraCheque.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((referencia == null) ? 0 : referencia.hashCode());
		result = prime * result + ((valor == null) ? 0 : valor.hashCode());
		result = prime * result + ((verba == null) ? 0 : verba.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		VerbasContraCheque other = (VerbasContraCheque) obj;
		if (contraCheque == null) {
			if (other.contraCheque != null)
				return false;
		} else if (!contraCheque.equals(other.contraCheque))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (referencia == null) {
			if (other.referencia != null)
				return false;
		} else if (!referencia.equals(other.referencia))
			return false;
		if (valor == null) {
			if (other.valor != null)
				return false;
		} else if (!valor.equals(other.valor))
			return false;
		if (verba == null) {
			if (other.verba != null)
				return false;
		} else if (!verba.equals(other.verba))
			return false;
		return true;
	}

}
