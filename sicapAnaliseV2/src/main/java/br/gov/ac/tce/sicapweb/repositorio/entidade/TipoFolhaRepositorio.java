package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoFolha;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Stateless
public class TipoFolhaRepositorio {

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<TipoFolha> pesquisaTiposFolha(Entidade entidade, RemessaEventual remessaEventual, String nome)
			throws RepositorioException {
		Collection<TipoFolha> listaTipoFolha = null;

		try {
			UaiCriteria<TipoFolha> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					TipoFolha.class);
			uaiCriteria.andEquals("entidade", entidade);

			if (remessaEventual.getId() != 0) {
				uaiCriteria.andEquals("remessaEventual", remessaEventual);
			}

			if ((nome != null) && (!nome.isEmpty())) {
				uaiCriteria.andStringLike("descricao", "%" + nome + "%");
			}

			listaTipoFolha = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro tipoFolhaRepositorio.pesquisaTiposFolha.", e.getCause());
		}
		return listaTipoFolha;
	}

	public TipoFolha pesquisaPorIdTipoFolha(Entidade entidade, Long id) throws RepositorioException {
		TipoFolha tipoFolha = null;

		try {
			UaiCriteria<TipoFolha> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					TipoFolha.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("id", id);

			tipoFolha = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro tipoFolhaRepositorio.pesquisaPorIdTipoFolha", e.getCause());
		}

		return tipoFolha;
	}

	public Collection<TipoFolha> listaTipoFolha(Entidade entidade) throws RepositorioException {
		Collection<TipoFolha> listaTipoFolha;

		try {
			UaiCriteria<TipoFolha> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					TipoFolha.class);
			uaiCriteria.andEquals("entidade", entidade);
			listaTipoFolha = uaiCriteria.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro tipoFolhaRepositorio.listaTipoFolha.", e.getCause());
		}
		return listaTipoFolha;
	}
}
