package br.gov.ac.tce.sicapanalise.auditoria.processo;

import java.io.Serializable;

public class ArquivoWS implements Serializable, Comparable<ArquivoWS> {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8895390762843294033L;
	private String nome;
	private String urlArquivo;
	private String tipoArquivo;
	private String tipoDocumento;
	private Integer ordem;
	
	public ArquivoWS(){}
	
	@Override
	public int compareTo(ArquivoWS o) {
		// TODO Auto-generated method stub
		return 0;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public String getUrlArquivo() {
		return urlArquivo;
	}

	public void setUrlArquivo(String urlArquivo) {
		this.urlArquivo = urlArquivo;
	}

	public String getTipoArquivo() {
		return tipoArquivo;
	}

	public void setTipoArquivo(String tipoArquivo) {
		this.tipoArquivo = tipoArquivo;
	}

	public String getTipoDocumento() {
		return tipoDocumento;
	}

	public void setTipoDocumento(String tipoDocumento) {
		this.tipoDocumento = tipoDocumento;
	}

	public Integer getOrdem() {
		return ordem;
	}

	public void setOrdem(Integer ordem) {
		this.ordem = ordem;
	}

	
}
