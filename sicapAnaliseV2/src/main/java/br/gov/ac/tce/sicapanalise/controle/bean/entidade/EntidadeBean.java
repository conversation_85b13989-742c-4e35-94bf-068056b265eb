package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicapanalise.auditoria.business.EntidadeBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.business.FolhaBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.FolhaCompetenciaDTO;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;
import br.gov.ac.tce.sicapanalise.util.StringUtil;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Named
@ViewScoped
public class EntidadeBean implements Serializable {

	private static final long serialVersionUID = -8844471887690244989L;

	@Inject
	private EntidadeBusiness entidadeBusiness;

	@Inject
	private FolhaBusiness folhaBusiness;

	private Entidade entidade;
	private List<Entidade> listaEntidade;
	private List<Entidade> listaEntidadeSelecionadas;

//	private List<ResumoFolhaDTO> listaResumoPorEntidade;
	private List<FolhaCompetenciaDTO> listaResumoPorEntidade;

	private Map<String, List<FolhaCompetenciaDTO>> resumoAgrupadoPorEntidade;

	private Integer ano;

	@PostConstruct
	void inicializado() {
		listaEntidade = entidadeBusiness.listaEntidades();
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public List<Entidade> getListaEntidade() {
		return listaEntidade;
	}

	public void setListaEntidade(List<Entidade> listaEntidade) {
		this.listaEntidade = listaEntidade;
	}

	public List<Entidade> getListaEntidadeSelecionadas() {
		return listaEntidadeSelecionadas;
	}

	public void setListaEntidadeSelecionadas(List<Entidade> listaEntidadeSelecionadas) {
		this.listaEntidadeSelecionadas = listaEntidadeSelecionadas;
	}

	public Integer getAno() {
		return ano;
	}

	public void setAno(Integer ano) {
		this.ano = ano;
	}

	public List<FolhaCompetenciaDTO> getListaResumoPorEntidade() {
		return listaResumoPorEntidade;
	}

	public void setListaResumoPorEntidade(List<FolhaCompetenciaDTO> listaResumoPorEntidade) {
		this.listaResumoPorEntidade = listaResumoPorEntidade;
	}

	public List<Integer> listaCompetenciaAno() {
		return new ArrayList<>(entidadeBusiness.listaCompetenciaAno());
	}

	public List<String> getListaResumoSintetico() {
		List<String> listaResumoSintetico = null;
		if (resumoAgrupadoPorEntidade != null && !resumoAgrupadoPorEntidade.isEmpty()) {
			listaResumoSintetico = new ArrayList<>(resumoAgrupadoPorEntidade.keySet());
		}
		return listaResumoSintetico;
	}

	public BigDecimal getValorResumoSintetico(String entidade, Integer mes) {
		return listaResumoPorEntidade.stream().filter(r -> r.getEntidadeNome().equals(entidade)).map(r -> {
			switch (mes) {
			case 1:
				return r.getValorJaneiro();
			case 2:
				return r.getValorFevereiro();
			case 3:
				return r.getValorMarco();
			case 4:
				return r.getValorAbril();
			case 5:
				return r.getValorMaio();
			case 6:
				return r.getValorJunho();
			case 7:
				return r.getValorJulho();

			case 8:
				return r.getValorAgosto();

			case 9:
				return r.getValorSetembro();

			case 10:
				return r.getValorOutubro();

			case 11:
				return r.getValorNovembro();

			case 12:
				return r.getValorDezembro();

			case 13:
				return r.getValorDecimo();

			default:
				return r.getValorTotal();

			}
		}).reduce(BigDecimal.ZERO, BigDecimal::add);
	}

	public void pesquisar() {
		if (ano == null || listaEntidadeSelecionadas == null || listaEntidadeSelecionadas.isEmpty()) {
			Mensagem.setMensagem(MensagemType.ERRO, "Selecione a competência e a entidade(s).", "");
		} else {
			listaResumoPorEntidade = folhaBusiness.listaResumoPorEntidade(listaEntidadeSelecionadas, ano);
			if (listaResumoPorEntidade != null && !listaResumoPorEntidade.isEmpty()) {
				Map<String, List<FolhaCompetenciaDTO>> agrupamentoPorEntidade = listaResumoPorEntidade.stream()
						.collect(Collectors.groupingBy(FolhaCompetenciaDTO::getEntidadeNome));
				
				resumoAgrupadoPorEntidade = new TreeMap<String, List<FolhaCompetenciaDTO>>(agrupamentoPorEntidade);
			}
		}

	}
	
	
	public boolean filtroSemAcento(Object valor, Object filtro, Locale locale) {
		boolean filtra = false;
		try {
			String strValor = (String) valor;
			String strFiltro = (String) filtro;
			
			strValor = StringUtil.unnacent(strValor.toLowerCase());
			strFiltro = StringUtil.unnacent(strFiltro.toLowerCase());
			
			if (strValor.contains(strFiltro)) {
				filtra = true;
			}
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return filtra;
}
	
//	public void teste() {
//		List<Integer> numeros = Arrays.asList(10, 20, 20, 10, 30, 40, 10, 50, 10);
//		List<Integer> numeros2 = Arrays.asList(10, 10, 20, 20, 30, 50, 20);
//		
//		Map<Integer,Long> agrupamento1 = numeros.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
//		
//		int pares;
//		for (Map.Entry<Integer, Long> entrada : agrupamento1.entrySet()) {
//			if (entrada.getValue().equals(2)) {
//				pares += 1;
//			}
//		}
//	
//	}
}