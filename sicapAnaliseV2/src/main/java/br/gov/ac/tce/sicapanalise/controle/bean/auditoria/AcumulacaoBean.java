package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.sql.Connection;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Optional;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.PerfilGrupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.UsuarioGrupo;
import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.UsuarioAuditoriaRepositorio;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;
import br.gov.ac.tce.sicapweb.modelo.SituacaoAcumulacao;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;

@Named
@ViewScoped
public class AcumulacaoBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private UsuarioAuditoriaRepositorio servidorTceRepositorio;

	@Inject
	private EntidadeRepositorio entidadeRepositorio;
	@Inject
	private RelatorioConexao relatorioConexao;
	
//	@Inject
//	private AnaliseBusiness analiseBusiness;
	
	@Inject
	private AcumulacaoBusiness acumulacaoBusiness;
	

	private Collection<Object[]> listaCompetencias;
	private Collection<Object[]> listaCargosAcumulacao;
	private Collection<Entidade> listaEntidades;

	private Collection<Acumulacao> listaAcumulacoes;

	private List<Acumulacao> acumulacoesSelecionadas;

	private String competencia;
	private String cpf;
	private String nome;
	private Integer entidade;
	private String situacao;
	private Integer cargo;

	private Acumulacao acumulacao;

	private int currentLevel;
	
	
	private String iniciarAnaliseDespacho;
	

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<Object[]> getListaCompetencias() {
		return listaCompetencias;
	}

	public void setListaCompetencias(Collection<Object[]> listaCompetencias) {
		this.listaCompetencias = listaCompetencias;
	}

	public String getCompetencia() {
		return competencia;
	}

	public void setCompetencia(String competencia) {
		this.competencia = competencia;
	}

	public String getCpf() {
		return cpf;
	}

	public String getSituacao() {
		return situacao;
	}

	public void setSituacao(String situacao) {
		this.situacao = situacao;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public List<SituacaoAcumulacao> getlistaSituacaoAcumulacao() {
		List<SituacaoAcumulacao> situacaoAcumulacoes = Arrays.asList(SituacaoAcumulacao.values());
		Collections.sort(situacaoAcumulacoes, Comparator.comparing(SituacaoAcumulacao::getDescricao));
		return situacaoAcumulacoes;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public Integer getEntidade() {
		return entidade;
	}

	public void setEntidade(Integer situacao) {
		this.entidade = situacao;
	}

	public Collection<Entidade> getListaEntidades() {
		return listaEntidades;
	}

	public Collection<Object[]> getListaCargosAcumulacao() {
		return listaCargosAcumulacao;
	}

	public void setListaCargosAcumulacao(Collection<Object[]> listaCargosAcumulacao) {
		this.listaCargosAcumulacao = listaCargosAcumulacao;
	}

	public Integer getCargo() {
		return cargo;
	}

	public void setCargo(Integer idCargo) {
		this.cargo = idCargo;
	}

	public Collection<Acumulacao> getListaAcumulacoes() {
		return listaAcumulacoes;
	}

	public void setListaAcumulacoes(Collection<Acumulacao> listaAcumulacoes) {
		this.listaAcumulacoes = listaAcumulacoes;
	}

	public List<Acumulacao> getAcumulacoesSelecionadas() {
		return acumulacoesSelecionadas;
	}

	public void setAcumulacoesSelecionadas(List<Acumulacao> acumulacoesSelecionadas) {
		this.acumulacoesSelecionadas = acumulacoesSelecionadas;
	}

	public Acumulacao getAcumulacao() {
		return acumulacao;
	}

	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
	}

	public String getIniciarAnaliseDespacho() {
		return iniciarAnaliseDespacho;
	}

	public void setIniciarAnaliseDespacho(String iniciarAnaliseDespacho) {
		this.iniciarAnaliseDespacho = iniciarAnaliseDespacho;
	}

	@PostConstruct
	public void init() {
		try {
			this.currentLevel = 1;
			this.entidade = 0;
			this.cpf = "";
			this.nome = "";
			this.listaCompetencias = this.acumulacaoBusiness.listaCompetencias();
			this.listaEntidades = this.entidadeRepositorio.lista();

			this.iniciarAnaliseDespacho = "";
			this.listaAcumulacoes = this.acumulacaoBusiness.listaAcumulacoes(this.competencia, this.situacao,
					this.cpf, this.nome, this.entidade, this.cargo);
			
			//Busca os perfis e grupo do usuário novamente para verificar se o mesmo foi adicionado em algum grupo ou perfil
			this.loginBean.setUsuario(this.servidorTceRepositorio.retornaServidorPorLogin(this.loginBean.getLogin()));
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao selecionar o filtro para a consulta.", "");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void pesquisar() {
		try {
			this.listaAcumulacoes = this.acumulacaoBusiness.listaAcumulacoes(this.competencia, this.situacao,
					this.cpf, this.nome, this.entidade, this.cargo);
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as acumulações.");
		}
	}

	public void perquisarCargos() {
		try {
			this.listaCargosAcumulacao = this.acumulacaoBusiness.listaCargosAcumulacao(this.competencia,
					this.entidade);

		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar os cargos.", "");
		}
	}
	

	public void gerarRelatorioAcumulacao() {
		String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext().getRealPath("relatorios/")
				+ "/";
		String pathRelatorio = pathRelatorioDir + "acumulacao/" + "relatorioAcumulacao.jasper";

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("cpf", this.acumulacao.getCpf()/* this.servidor[1] */);
		parameters.put("usuario", this.acumulacao.getNome()/* this.loginBean.getNome() */);
		parameters.put("exercicio", this.acumulacao.getAno()/* this.servidor[3] */);
		parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
		parameters.put("SUBREPORT_DIR", pathRelatorioDir + "acumulacao/");
		parameters.put("mes", this.acumulacao.getMes()/* this.servidor[4] */);
		String competencia = this.acumulacao.getMes() + "-" + this.acumulacao.getAno();
		parameters.put("competencia", competencia);

		JasperPrint jasperPrint;
		try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance()
					.getExternalContext().getResponse();
			httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + "RelatorioAcumulacao-"
					+ this.acumulacao.getCpf() + "-" + competencia + ".pdf");
			ServletOutputStream arquivo = httpServletResponse.getOutputStream();

			
			jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters,
					conexaoJasper);
			JasperExportManager.exportReportToPdfStream(jasperPrint, arquivo);
			arquivo.flush();
			arquivo.close();

			FacesContext.getCurrentInstance().responseComplete();

			// JRExporter exporter = new JRPdfExporter();
			// exporter.setParameter(JRExporterParameter.JASPER_PRINT,
			// jasperPrint);
			// ByteArrayOutputStream baosReport = new ByteArrayOutputStream();
			// exporter.setParameter(JRExporterParameter.OUTPUT_STREAM,
			// baosReport);
			// exporter.exportReport();
			// byte[] toReturn = baosReport.toByteArray();
			// baosReport.close();

			// InputStream inputStream = new FileInputStream(toReturn.);

			// FacesContext facesContext = FacesContext.getCurrentInstance();
			// ExternalContext externalContext =
			// facesContext.getExternalContext();
			// externalContext.responseReset();
			// externalContext.setResponseContentType("application/pdf");
			// externalContext.setResponseContentLength(toReturn.length);
			// externalContext.setResponseHeader("Content-Disposition",
			// "attachment; filename=\"" + this.cpfSelecionado +
			// "-fichaAnualVinculosRendimentos.pdf");
			// OutputStream outputStream =
			// externalContext.getResponseOutputStream();
			// outputStream.write(toReturn);
			// outputStream.flush();
			// facesContext.responseComplete();

			// JasperExportManager.exportReportToPdfFile(jasperPrint,
			// this.cpfSelecionado +
			// "-fichaAnualVinculosFuncionaisRendimentos.pdf");
			//
			// JasperViewer jasperViewer = new JasperViewer(jasperPrint, false);
			// jasperViewer.setVisible(true);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void gerarRelatorioAcumulacaoVerbas() {
		String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext()
				.getRealPath("relatorios/fichaFinanceira") + "/";
		String pathRelatorio = pathRelatorioDir + "relatorioFinanceiro.jasper";

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("cpf", this.acumulacao.getCpf());
		parameters.put("usuario", this.loginBean.getUsuario().getNome());
		parameters.put("ano", this.acumulacao.getAno());
		parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
		parameters.put("SUBREPORT_DIR", pathRelatorioDir);

		JasperPrint jasperPrint;
		try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance()
					.getExternalContext().getResponse();
			httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + "RelatorioAcumulacao-"
					+ this.acumulacao.getCpf() + "-" + competencia + ".pdf");
			ServletOutputStream arquivo = httpServletResponse.getOutputStream();

			
			jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters,
					conexaoJasper);
			JasperExportManager.exportReportToPdfStream(jasperPrint, arquivo);
			arquivo.flush();
			arquivo.close();
			
			FacesContext.getCurrentInstance().responseComplete();

			// JRExporter exporter = new JRPdfExporter();
			// exporter.setParameter(JRExporterParameter.JASPER_PRINT,
			// jasperPrint);
			// ByteArrayOutputStream baosReport = new ByteArrayOutputStream();
			// exporter.setParameter(JRExporterParameter.OUTPUT_STREAM,
			// baosReport);
			// exporter.exportReport();
			// byte[] toReturn = baosReport.toByteArray();
			// baosReport.close();

			// InputStream inputStream = new FileInputStream(toReturn.);

			// FacesContext facesContext = FacesContext.getCurrentInstance();
			// ExternalContext externalContext =
			// facesContext.getExternalContext();
			// externalContext.responseReset();
			// externalContext.setResponseContentType("application/pdf");
			// externalContext.setResponseContentLength(toReturn.length);
			// externalContext.setResponseHeader("Content-Disposition",
			// "attachment; filename=\"" + this.cpfSelecionado +
			// "-fichaAnualVinculosRendimentos.pdf");
			// OutputStream outputStream =
			// externalContext.getResponseOutputStream();
			// outputStream.write(toReturn);
			// outputStream.flush();
			// facesContext.responseComplete();

			// JasperExportManager.exportReportToPdfFile(jasperPrint,
			// this.cpfSelecionado +
			// "-fichaAnualVinculosFuncionaisRendimentos.pdf");
			//
			// JasperViewer jasperViewer = new JasperViewer(jasperPrint, false);
			// jasperViewer.setVisible(true);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	public void temAcumulacaoSelecionada() {
		if (this.acumulacoesSelecionadas == null || this.acumulacoesSelecionadas.isEmpty()) {
			Mensagem.setMensagem(MensagemType.ERRO, "Nenhuma Acumulação foi selecionada.", "");
		}
	}
	
//	@Transactional
//	public void iniciarAnalise() {
//		try {
//			this.analiseBusiness.iniciarAnalise(acumulacoesSelecionadas, this.iniciarAnaliseDespacho, this.loginBean.getUsuario());
//			
//			//Fecha o dialog após o processamento
////			RequestContext requestContext = RequestContext.getCurrentInstance();
////			requestContext.execute("PF('dlgInitAnlAcmVar').hide();");
//			PrimeFaces.current().executeScript("PF('dlgInitAnlAcmVar').hide();");
//			
//			// Limpa as acumulações selecionadas
////			requestContext.execute("PF('tblDistVar').unselectAllRows()");
//			PrimeFaces.current().executeScript("PF('tblDistVar').unselectAllRows();");
//	
//			//Mensagem de sucesso
//			Mensagem.setMensagem(MensagemType.INFORMACAO, "Análise de Acumulação", "A análise das acumulações seleciondas foi iniciada.");
//		} catch (Exception e) {
//			Mensagem.setMensagem(MensagemType.ERRO, e.getMessage(), "");
//		}
//		
//	}
	
	
	public boolean getPerfilDistribuicao() {
		
		Optional<UsuarioGrupo> usuarioGrupoDistribuicao = this.loginBean.getUsuario().getListaUsuarioGrupo().stream()
				.filter(ug -> ug.getAtivo() && ug.getGrupo().getAtivo()
						&& ug.getGrupo().getPerfil().equals(PerfilGrupo.DISTRIBUICAO))
				.findAny();
		
		if (usuarioGrupoDistribuicao.isPresent()) {
			return true;
		}
		return false;
	}

	public boolean getPerfilAuditoria() {
		Optional<UsuarioGrupo> usuarioGrupoAuditoria = this.loginBean.getUsuario().getListaUsuarioGrupo().stream()
				.filter(ug -> ug.getAtivo() && ug.getGrupo().getAtivo()
						&& ug.getGrupo().getPerfil().equals(PerfilGrupo.AUDITORIA))
				.findAny();
		if (usuarioGrupoAuditoria.isPresent()) {
			return true;
		}
		return false;
	}

}
