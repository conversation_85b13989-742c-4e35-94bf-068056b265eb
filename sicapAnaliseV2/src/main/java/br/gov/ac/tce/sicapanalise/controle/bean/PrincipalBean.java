package br.gov.ac.tce.sicapanalise.controle.bean;

import java.io.File;
import java.io.Serializable;
import java.sql.Connection;
import java.time.LocalDate;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;
import br.gov.ac.tce.sicapweb.modelo.entidade.ClassificacaoAdministrativa;
import br.gov.ac.tce.sicapweb.modelo.entidade.Ente;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.entidade.EntidadeCJUR;
import br.gov.ac.tce.sicapweb.modelo.entidade.Esfera;
import br.gov.ac.tce.sicapweb.modelo.entidade.Poder;
import br.gov.ac.tce.sicapweb.modelo.remessa.AssinaturaRemessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.PrazoEnvioRemessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.TempestividadeRemessa;
import br.gov.ac.tce.sicapweb.modelo.usuario.Usuario;
import br.gov.ac.tce.sicapweb.repositorio.AssinaturaRemessaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.PrazoEnvioRemessaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import br.gov.ac.tce.sicapweb.repositorio.RemessaPeriodicaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.UsuarioRepositorio;
import br.gov.ac.tce.sicapweb.util.FormatarTexto;
import br.gov.ac.tce.sicapweb.util.VerificaTempestividade;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;

@Named
@ViewScoped
public class PrincipalBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private EntidadeCJUR entidade;
	@Inject
	private Remessa remessa;
	@Inject
	private PrazoEnvioRemessa prazoEnvioRemessa;
	@Inject
	private Usuario usuario;
	@Inject
	private AssinaturaRemessa assinaturaRemessa;

	@Inject
	private RemessaPeriodicaRepositorio remessaPeriodicaRepositorio;
//	@Inject
//	private SituacaoRemessaRepositorio situacaoRemessaRepositorio;
	@Inject
	private EntidadeRepositorio entidadeRepositorio;
	@Inject
	private PrazoEnvioRemessaRepositorio prazoEnvioRemessaRepositorio;
	@Inject
	private UsuarioRepositorio usuarioRepositorio;
	@Inject
	private AssinaturaRemessaRepositorio assinaturaRemessaRepositorio;
	@Inject
	private RelatorioConexao relatorioConexao;

	@Inject
	private VerificaTempestividade verificaTempestividade;
	@Inject
	private FormatUtil formatUtil;

	private Collection<TempestividadeRemessa> listaSituacaoRemessas;
//	private HorizontalBarChartModel horizontalBarModel;
//	private LineChartModel lineModel;

	private String tempoAtraso;

	private Collection<Entidade> listaEntidade;
	private Collection<Integer> listaExercicio;

	private Integer exercicio;
	private Integer ente;
	private Integer poder;
	private Integer idEntidadeCjur;
	private Integer classificacaoAdministrativa;
	private Integer esfera;

	private int currentLevel;

	@PostConstruct
	public void init() {
		try {
			this.currentLevel = 1;
			this.exercicio = 0;
			this.ente = 0;
			this.poder = 0;
			this.idEntidadeCjur = 0;
			this.classificacaoAdministrativa = 0;
			this.esfera = 0;
			this.listaExercicio = this.remessaPeriodicaRepositorio.listaExercicio();
			this.listaEntidade = this.entidadeRepositorio.lista();
			this.tempoAtraso = null;
			// this.listaSituacaoRemessas =
			// this.situacaoRemessaRepositorio.consultaSituacaoRemessa(null,
			// 2016);
			// createHorizontalBarModel(listaSituacaoRemessas);
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao selecionar os filtros da pesquisa.", "");
		}
	}

	public void pesquisar() {
		try {
			this.listaSituacaoRemessas = this.remessaPeriodicaRepositorio.consultaSituacaoRemessa(this.exercicio,
					this.ente, this.poder, this.idEntidadeCjur, this.classificacaoAdministrativa, this.esfera);
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao pesquisar as remessas.", "");
		}
	}

	public void detalharRemessa(Integer idEntidadeCjur, Integer ano, Integer mes) {
		try {
			this.entidade = null;
			this.prazoEnvioRemessa = null;
			this.remessa = null;
			this.tempoAtraso = null;
			this.usuario = null;
			this.assinaturaRemessa = null;
			this.entidade = this.entidadeRepositorio.pesquisaEntidade(idEntidadeCjur);
			this.prazoEnvioRemessa = this.prazoEnvioRemessaRepositorio.pesquisarPrazo(ano, mes);
			this.remessa = this.remessaPeriodicaRepositorio.pesquisarRemessa(idEntidadeCjur, ano, mes);
			this.assinaturaRemessa = this.assinaturaRemessaRepositorio.buscarPorRemessa(this.remessa);
			if (assinaturaRemessa != null) {
				this.tempoAtraso = VerificaTempestividade.calcularTempoAtraso(
						this.assinaturaRemessa.getDataAssinatura(), this.prazoEnvioRemessa.getPrazoEnvio());
				this.usuario = this.usuarioRepositorio.buscarPorId(this.remessa.getAssinaturaRemessa().getIdUsuario(),
						this.remessa.getEntidade());
			} else {
				if (this.remessa != null) {
					this.tempoAtraso = VerificaTempestividade.calcularTempoAtraso(this.remessa.getDataConfirmacao(),
							this.prazoEnvioRemessa.getPrazoEnvio());
				} else {
					this.tempoAtraso = "";
				}
			}
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao detalhar a remessa.", "");
		}
	}

	public void gerarCertidaoEntregaRemessa(TempestividadeRemessa tempestividadeRemessa) {
		try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext().getRealPath("relatorios/") + File.separator;
			String pathRelatorio = pathRelatorioDir + "certidaoEntregaRemessa.jasper";

			LocalDate hoje = LocalDate.now();
			Integer anoAtual = hoje.getYear();
			Integer mesAtual = hoje.getMonthValue();
			
			VerificaTempestividade verificaTempestividade = new VerificaTempestividade(
					this.remessaPeriodicaRepositorio.getPrazoEnvioRemessaAtual(anoAtual, mesAtual));
			verificaTempestividade.gerarLegendaAssinaturas(tempestividadeRemessa);
			

			HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
			parameters.put("remessas", tempestividadeRemessa);
//			parameters.put("usuario", FormatarTexto.primeiraLetraCadaPalavraMaiuscula(this.loginBean.getNome()));
			parameters.put("usuario", FormatarTexto.primeiraLetraCadaPalavraMaiuscula(this.loginBean.getUsuario().getNome()));
			parameters.put("idEntidadeCjur", tempestividadeRemessa.getIdEntidadeCjur());
			parameters.put("exercicio", tempestividadeRemessa.getAno());
			parameters.put("nomeEntidade",
					FormatarTexto.primeiraLetraCadaPalavraMaiuscula(tempestividadeRemessa.getEntidade()));
			parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
			parameters.put("SUBREPORT_DIR", pathRelatorioDir);

			JasperPrint jasperPrint;

			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance()
					.getExternalContext().getResponse();
			httpServletResponse.addHeader("Content-disposition", "attachment; filename="
					+ tempestividadeRemessa.getIdEntidadeCjur() + "_CertidaoEntregaRemessa.pdf");
			ServletOutputStream arquivo = httpServletResponse.getOutputStream();

			
			jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters,
					conexaoJasper);
			JasperExportManager.exportReportToPdfStream(jasperPrint, arquivo);
			arquivo.flush();
			arquivo.close();
			
			FacesContext.getCurrentInstance().responseComplete();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

//	@SuppressWarnings("unused")
//	private void createHorizontalBarModel(Collection<Object[]> listaRemessas) {
//		horizontalBarModel = new HorizontalBarChartModel();
//		Integer contadorRemessa;
//		ChartSeries entidade = new ChartSeries();
//		entidade.setLabel("Entidades");
//		for (Object[] remessa : listaRemessas) {
//
//			contadorRemessa = 0;
//
//			if (remessa[2] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[3] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[4] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[5] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[6] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[7] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[8] == null) {
//				contadorRemessa++;
//			}
//			entidade.set(remessa[1], contadorRemessa);
//			horizontalBarModel.addSeries(entidade);
//		}
//		horizontalBarModel.setTitle("Situa��o das Remessas");
//		horizontalBarModel.setLegendPosition("ne");
//		horizontalBarModel.setLegendPlacement(LegendPlacement.OUTSIDEGRID);
//		horizontalBarModel.setStacked(true);
//
//		Axis xAxis = horizontalBarModel.getAxis(AxisType.X);
//		xAxis.setLabel("Quantidade de remessas");
//		xAxis.setMin(0);
//		xAxis.setMax(13);
//	}
//
//	private void createLineModel(Collection<Object[]> listaRemessas) {
//		lineModel = new LineChartModel();
//		Integer contadorRemessa = null;
//
//		for (Object[] remessa : listaRemessas) {
//			LineChartSeries entidade = new LineChartSeries();
//			entidade.setLabel("Entidades");
//			contadorRemessa = 0;
//
//			if (remessa[2] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[3] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[4] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[5] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[6] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[7] == null) {
//				contadorRemessa++;
//			}
//			if (remessa[8] == null) {
//				contadorRemessa++;
//			}
//			entidade.set(remessa[1], contadorRemessa);
//			horizontalBarModel.addSeries(entidade);
//		}
//		horizontalBarModel.setTitle("Situação das Remessas");
//		horizontalBarModel.setLegendPosition("ne");
//		horizontalBarModel.setLegendPlacement(LegendPlacement.OUTSIDEGRID);
//		horizontalBarModel.setStacked(true);
//
//		Axis xAxis = horizontalBarModel.getAxis(AxisType.X);
//		xAxis.setLabel("Quantidade de remessas");
//		xAxis.setMin(0);
//		xAxis.setMax(13);
//	}

	public Collection<TempestividadeRemessa> getListaSituacaoRemessas() {
		return listaSituacaoRemessas;
	}

//	public HorizontalBarChartModel getHorizontalBarModel() {
//		return horizontalBarModel;
//	}
//
//	public void setHorizontalBarModel(HorizontalBarChartModel horizontalBarModel) {
//		this.horizontalBarModel = horizontalBarModel;
//	}

	public Ente[] getListaEnte() {
		return Ente.values();
	}

	public Poder[] getListaPoder() {
		return Poder.values();
	}

	public ClassificacaoAdministrativa[] getListaClassificacaoAdministrativa() {
		return ClassificacaoAdministrativa.values();
	}

	public Esfera[] getListaEsfera() {
		return Esfera.values();
	}

	public Integer getEnte() {
		return ente;
	}

	public void setEnte(Integer ente) {
		this.ente = ente;
	}

	public Integer getPoder() {
		return poder;
	}

	public void setPoder(Integer poder) {
		this.poder = poder;
	}

	public Collection<Integer> getListaExercicio() {
		return listaExercicio;
	}

	public void setListaExercicio(Collection<Integer> listaExercicio) {
		this.listaExercicio = listaExercicio;
	}

	public Integer getClassificacaoAdministrativa() {
		return classificacaoAdministrativa;
	}

	public void setClassificacaoAdministrativa(Integer classificacaoAdministrativa) {
		this.classificacaoAdministrativa = classificacaoAdministrativa;
	}

	public Collection<Entidade> getListaEntidade() {
		return listaEntidade;
	}

	public void setListaEntidade(Collection<Entidade> listaEntidade) {
		this.listaEntidade = listaEntidade;
	}

	public EntidadeCJUR getEntidade() {
		return entidade;
	}

	public void setEntidade(EntidadeCJUR entidade) {
		this.entidade = entidade;
	}

	public Integer getEsfera() {
		return esfera;
	}

	public void setEsfera(Integer esfera) {
		this.esfera = esfera;
	}

	public Integer getExercicio() {
		return exercicio;
	}

	public void setExercicio(Integer exercicio) {
		this.exercicio = exercicio;
	}

	public Integer getIdEntidadeCjur() {
		return idEntidadeCjur;
	}

	public void setIdEntidadeCjur(Integer idEntidadeCjur) {
		this.idEntidadeCjur = idEntidadeCjur;
	}

	public VerificaTempestividade getVerificaTempestividade() {
		return verificaTempestividade;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public Remessa getRemessa() {
		return remessa;
	}

	public void setRemessa(Remessa remessa) {
		this.remessa = remessa;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public PrazoEnvioRemessa getPrazoEnvioRemessa() {
		return prazoEnvioRemessa;
	}

	public void setPrazoEnvioRemessa(PrazoEnvioRemessa prazoEnvioRemessa) {
		this.prazoEnvioRemessa = prazoEnvioRemessa;
	}

	public String getTempoAtraso() {
		return tempoAtraso;
	}

	public void setTempoAtraso(String tempoAtraso) {
		this.tempoAtraso = tempoAtraso;
	}

	public Usuario getUsuario() {
		return usuario;
	}

	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
	}

	public AssinaturaRemessa getAssinaturaRemessa() {
		return assinaturaRemessa;
	}

	public void setAssinaturaRemessa(AssinaturaRemessa assinaturaRemessa) {
		this.assinaturaRemessa = assinaturaRemessa;
	}
}
