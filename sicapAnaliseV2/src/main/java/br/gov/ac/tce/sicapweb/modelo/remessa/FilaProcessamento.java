package br.gov.ac.tce.sicapweb.modelo.remessa;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Entity
@Table(schema = "remessa")
@NamedQueries({
		@NamedQuery(name = FilaProcessamento.listarTodasPendentes, query = "select f from FilaProcessamento f where f.situacaoProcessamento = 'AG' order by f.tipoArquivo asc"),
		@NamedQuery(name = FilaProcessamento.buscarPorTipoIdRemessaEntidade, query = "select f from FilaProcessamento f where f.idRemessa = :idRemessa and f.tipoArquivo = :tipoArquivo and f.entidade = :entidade") })
public class FilaProcessamento implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String listarTodasPendentes = "FilaProcessamento.listarTodasPendentes";
	public static final String buscarPorTipoIdRemessaEntidade = "FilaProcessamento.buscarPorTipoIdRemessaEntidade";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(nullable = false)
	private Long idRemessa;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(nullable = false)
	private Integer tipoArquivo;
	@Column(length = 2, nullable = false)
	private String situacaoProcessamento;
	@Column(nullable = false)
	private LocalDateTime dataInclusao;
	private LocalDateTime inicioProcessamento;
	private LocalDateTime fimProcessamento;
	private String mensagemProcessamento;
	@Column(columnDefinition = "text")
	@Basic(fetch = FetchType.LAZY)
	private String conteudoXML;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getIdRemessa() {
		return idRemessa;
	}

	public void setIdRemessa(Long idRemessa) {
		this.idRemessa = idRemessa;
	}

	public TypeArquivo getTipoArquivo() {
		return TypeArquivo.parse(this.tipoArquivo);
	}

	public void setTipoArquivo(TypeArquivo tipoArquivo) {
		this.tipoArquivo = tipoArquivo.getId();
	}

	public SituacaoRemessa getSituacaoProcessamento() {
		return SituacaoRemessa.parse(this.situacaoProcessamento);
	}

	public void setSituacaoProcessamento(SituacaoRemessa situacaoRemessa) {
		this.situacaoProcessamento = situacaoRemessa.getId();
	}

	public LocalDateTime getInicioProcessamento() {
		return inicioProcessamento;
	}

	public void setInicioProcessamento(LocalDateTime inicioProcessamento) {
		this.inicioProcessamento = inicioProcessamento;
	}

	public LocalDateTime getFimProcessamento() {
		return fimProcessamento;
	}

	public void setFimProcessamento(LocalDateTime fimProcessamento) {
		this.fimProcessamento = fimProcessamento;
	}

	public String getMensagemProcessamento() {
		return mensagemProcessamento;
	}

	public void setMensagemProcessamento(String mensagemProcessamento) {
		this.mensagemProcessamento = mensagemProcessamento;
	}

	public LocalDateTime getDataInclusao() {
		return dataInclusao;
	}

	public void setDataInclusao(LocalDateTime dataInclusao) {
		this.dataInclusao = dataInclusao;
	}

	public String getConteudoXML() {
		return conteudoXML;
	}

	public void setConteudoXML(String conteudoXML) {
		this.conteudoXML = conteudoXML;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((conteudoXML == null) ? 0 : conteudoXML.hashCode());
		result = prime * result + ((dataInclusao == null) ? 0 : dataInclusao.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((fimProcessamento == null) ? 0 : fimProcessamento.hashCode());
		result = prime * result + ((idRemessa == null) ? 0 : idRemessa.hashCode());
		result = prime * result + ((inicioProcessamento == null) ? 0 : inicioProcessamento.hashCode());
		result = prime * result + ((mensagemProcessamento == null) ? 0 : mensagemProcessamento.hashCode());
		result = prime * result + ((situacaoProcessamento == null) ? 0 : situacaoProcessamento.hashCode());
		result = prime * result + ((tipoArquivo == null) ? 0 : tipoArquivo.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		FilaProcessamento other = (FilaProcessamento) obj;
		if (conteudoXML == null) {
			if (other.conteudoXML != null)
				return false;
		} else if (!conteudoXML.equals(other.conteudoXML))
			return false;
		if (dataInclusao == null) {
			if (other.dataInclusao != null)
				return false;
		} else if (!dataInclusao.equals(other.dataInclusao))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (fimProcessamento == null) {
			if (other.fimProcessamento != null)
				return false;
		} else if (!fimProcessamento.equals(other.fimProcessamento))
			return false;
		if (idRemessa == null) {
			if (other.idRemessa != null)
				return false;
		} else if (!idRemessa.equals(other.idRemessa))
			return false;
		if (inicioProcessamento == null) {
			if (other.inicioProcessamento != null)
				return false;
		} else if (!inicioProcessamento.equals(other.inicioProcessamento))
			return false;
		if (mensagemProcessamento == null) {
			if (other.mensagemProcessamento != null)
				return false;
		} else if (!mensagemProcessamento.equals(other.mensagemProcessamento))
			return false;
		if (situacaoProcessamento == null) {
			if (other.situacaoProcessamento != null)
				return false;
		} else if (!situacaoProcessamento.equals(other.situacaoProcessamento))
			return false;
		if (tipoArquivo == null) {
			if (other.tipoArquivo != null)
				return false;
		} else if (!tipoArquivo.equals(other.tipoArquivo))
			return false;
		return true;
	}

}
