package br.gov.ac.tce.sicap.modelo.entidade.auditoria;

public enum TipoAuditoria {

	ACUMULACAO("Acumulação");

	private final String descricao;

	private TipoAuditoria(String descricao) {
		this.descricao = descricao;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoAuditoria parse(String id) {
		TipoAuditoria tipoAuditoria = null;
//		for (TipoAuditoria item : TipoAuditoria.values()) {
//			// if (item.getId() == id) {
//			// tipoAuditoria = item;
//			// break;
//			// }
//		}
		return tipoAuditoria;
	}

}
