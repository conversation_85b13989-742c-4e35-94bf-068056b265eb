package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Entity
@Table(schema = "auditoria")
public class SolicitacaoDocumento implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAnalise", nullable = false)
	private Analise analise;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(nullable = false)
	private LocalDateTime dataSolicitacao;
	@Column(nullable = false)
	private LocalDateTime dataPrazoResposta;
	@Column(nullable = false)
	private Boolean envioLiberado;
	@Column(nullable = false)
	@Enumerated(EnumType.STRING)
	private SituacaoSolicitacaoDocumento situacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoAnalise", nullable = false)
	private TipoAnalise tipoAnalise;
	
	private String protocoloEntregaDocumentos;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Analise getAnalise() {
		return analise;
	}

	public void setAnalise(Analise analise) {
		this.analise = analise;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public LocalDateTime getDataSolicitacao() {
		return dataSolicitacao;
	}

	public void setDataSolicitacao(LocalDateTime dataSolicitacao) {
		this.dataSolicitacao = dataSolicitacao;
	}

	public LocalDateTime getDataPrazoResposta() {
		return dataPrazoResposta;
	}

	public void setDataPrazoResposta(LocalDateTime dataPrazoResposta) {
		this.dataPrazoResposta = dataPrazoResposta;
	}

	public Boolean getEnvioLiberado() {
		return envioLiberado;
	}

	public void setEnvioLiberado(Boolean envioLiberado) {
		this.envioLiberado = envioLiberado;
	}

	public SituacaoSolicitacaoDocumento getSituacao() {
		return situacao;
	}

	public void setSituacao(SituacaoSolicitacaoDocumento situacao) {
		this.situacao = situacao;
	}

	public TipoAnalise getTipoAnalise() {
		return tipoAnalise;
	}

	public void setTipoAnalise(TipoAnalise tipoAnalise) {
		this.tipoAnalise = tipoAnalise;
	}
	

	public String getProtocoloEntregaDocumentos() {
		return protocoloEntregaDocumentos;
	}

	public void setProtocoloEntregaDocumentos(String protocoloEntregaDocumentos) {
		this.protocoloEntregaDocumentos = protocoloEntregaDocumentos;
	}


	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SolicitacaoDocumento other = (SolicitacaoDocumento) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}


	

	
}
