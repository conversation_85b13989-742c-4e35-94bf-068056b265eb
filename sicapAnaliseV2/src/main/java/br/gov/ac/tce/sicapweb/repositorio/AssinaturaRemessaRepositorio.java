package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.remessa.AssinaturaRemessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;

@Stateless
public class AssinaturaRemessaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public AssinaturaRemessa buscarPorRemessa(Remessa remessa) throws RepositorioException {
		AssinaturaRemessa assinaturaRemessa = null;
		try {
			if (remessa != null) {
				UaiCriteria<AssinaturaRemessa> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
						AssinaturaRemessa.class);
				uaiCriteria.andEquals("remessa", remessa);

				assinaturaRemessa = uaiCriteria.getSingleResult();
			}
		} catch (NoResultException noResultException) {
			assinaturaRemessa = null;
			// throw new RepositorioException("Assinatura n�o encontrada.",
			// noResultException.getCause());
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscar por assinatura da remessa.", e.getCause());
		}
		return assinaturaRemessa;
	}
}
