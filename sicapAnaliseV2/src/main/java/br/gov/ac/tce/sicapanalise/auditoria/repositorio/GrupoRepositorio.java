package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Grupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.PerfilGrupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.UsuarioGrupo;

@Stateless
public class GrupoRepositorio implements Serializable {

	private static final long serialVersionUID = 2715195779508059900L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager em;

	public void salvarGrupo(Grupo grupo) {
		try {
			em.merge(grupo);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public Grupo retornaPorId(Long grupoId) {
		return em.find(Grupo.class, grupoId);
	}

	public Collection<Grupo> listaGrupos() {
		Collection<Grupo> listaGrupos = null;
		String jpql = "SELECT DISTINCT g FROM Grupo g LEFT JOIN FETCH g.listaUsuarioGrupo ug LEFT JOIN FETCH ug.usuario WHERE g.ativo = true";
		try {
			listaGrupos = em.createQuery(jpql, Grupo.class).getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaGrupos;
	}

	public Collection<UsuarioGrupo> listaUsuariosNoGrupo(Grupo grupo) {
		Collection<UsuarioGrupo> listaUsuariosNoGrupo = null;
		String jpql = "SELECT ug FROM UsuarioGrupo ug " + "JOIN FETCH ug.usuario u " + "JOIN FETCH ug.grupo g "
				+ "WHERE ug.grupo = :grupo " + "AND ug.ativo =  true " + "AND u.ativo = true " + "ORDER BY u.nome";
		try {
			listaUsuariosNoGrupo = em.createQuery(jpql, UsuarioGrupo.class).setParameter("grupo", grupo)
					.getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaUsuariosNoGrupo;
	}

	public void incluirUsuarioGrupo(Collection<Usuario> listaUsuarioIncluir, Grupo grupo) {

		try {

			listaUsuarioIncluir.forEach(u -> {
				UsuarioGrupo usuarioGrupo = retornaUsuarioGrupo(u, grupo);

				if (usuarioGrupo == null) {
					usuarioGrupo = new UsuarioGrupo(u, grupo);
					usuarioGrupo.setAtivo(true);
					em.persist(usuarioGrupo);
				} else {
					usuarioGrupo.setAtivo(true);
					em.merge(usuarioGrupo);
				}

			});

		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public void desativarGrupo(Collection<Grupo> grupos) {
		try {
			int gruposDesativados = em.createQuery("UPDATE Grupo g SET g.ativo = false WHERE g IN :grupos")
					.setParameter("grupos", grupos).executeUpdate();
			int usuariosDesativados = em
					.createQuery("UPDATE UsuarioGrupo ug SET ug.ativo = false WHERE ug.grupo IN :grupos")
					.setParameter("grupos", grupos).executeUpdate();
			System.out.println("Quantidade de grupos desativados: " + gruposDesativados);
			System.out.println("Quantidade de usuários desativados: " + usuariosDesativados);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void desativarUsuarioGrupo(Collection<UsuarioGrupo> usuariosGrupo) {
		try {
			int usuarioGrupoDesativos = em
					.createQuery("UPDATE UsuarioGrupo ug SET ug.ativo = false WHERE ug in :usuariosGrupo")
					.setParameter("usuariosGrupo", usuariosGrupo).executeUpdate();
			System.out.println("QUantidade de usuários desativados: " + usuarioGrupoDesativos);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public Collection<Usuario> listaUsuariosPorPerfil(PerfilGrupo perfil) {
		Collection<Usuario> listaUsuariosAuditores = null;

		try {
			String jpql = "Select distinct u FROM Usuario u JOIN FETCH u.listaUsuarioGrupo ug JOIN FETCH ug.grupo g WHERE g.perfil = :pPerfil AND g.ativo = true ORDER BY u.nome";

			listaUsuariosAuditores = this.em.createQuery(jpql, Usuario.class).setParameter("pPerfil", perfil)
					.getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}

		return listaUsuariosAuditores;
	}

	public UsuarioGrupo retornaUsuarioGrupo(Usuario usuario, Grupo grupo) {
		UsuarioGrupo usuarioGrupo = null;
		String jpql = "SELECT ug FROM UsuarioGrupo ug WHERE ug.usuario = :pUsuario AND ug.grupo = :pGrupo";
		try {
			usuarioGrupo = em.createQuery(jpql, UsuarioGrupo.class).setParameter("pUsuario", usuario)
					.setParameter("pGrupo", grupo).getSingleResult();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return usuarioGrupo;
	}
}
