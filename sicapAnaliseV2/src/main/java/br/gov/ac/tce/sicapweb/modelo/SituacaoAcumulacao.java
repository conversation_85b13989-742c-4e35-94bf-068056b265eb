package br.gov.ac.tce.sicapweb.modelo;

public enum SituacaoAcumulacao {
	AGUARDANDO_ANALISE("AG","Aguardando Análise"), 
	REGULAR("RE","Regular"), 
	INDEVIDA("ID","Indevida"),
	INCOMPATIVEL_CARGA_HORARIA("IC","Incompatível por Carga Horária"),
	INCOMPATIVEL_LOTACAO("IL","Incompatível por Lotação"),
	REANALISE("RA","Reanálise"), 
	EM_ANALISE("EA","Em Anlise"),
	SEM_STATUS("AM","Sem Status"),
	
	//Includas 13.5.2020 - Ma�ao
	NOTIFICADA_AGUARDANDO_RESPOSTA("AR", "Notificada e Aguardando Respostas"),
	NOTIFICACAO_RESPONDIDA("NR", "Notificação Respondida"),
	PRAZO_ENCERRADO("PE","Prazo Encerrado");

	private String id;
	private String descricao;

	private SituacaoAcumulacao(String id,String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}


	public static SituacaoAcumulacao parse(String id) {
		SituacaoAcumulacao situacaoAcumulacao = null;
		for (SituacaoAcumulacao item : SituacaoAcumulacao.values()) {
			if (item.getId().equals(id)) {
				situacaoAcumulacao = item;
				break;
			}
		}
		return situacaoAcumulacao;
	}
}
