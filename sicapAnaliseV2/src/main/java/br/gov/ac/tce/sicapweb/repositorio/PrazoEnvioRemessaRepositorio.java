package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.remessa.PrazoEnvioRemessa;

public class PrazoEnvioRemessaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public PrazoEnvioRemessa pesquisarPrazo(Integer ano, Integer mes) throws RepositorioException {
		PrazoEnvioRemessa prazoEnvioRemessa = null;

		try {
			if ((ano != null) && (mes != null)) {
				UaiCriteria<PrazoEnvioRemessa> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
						PrazoEnvioRemessa.class);
				uaiCriteria.andEquals("ano", ano);
				uaiCriteria.andEquals("mes", mes);

				prazoEnvioRemessa = uaiCriteria.getSingleResult();
			}
		} catch (NoResultException nre) {
			prazoEnvioRemessa = null;
		} catch (Exception e) {
			throw new RepositorioException("Erro prazoEnvioRemessaRepositorio.pesquisaPrazo", e.getCause());
		}

		return prazoEnvioRemessa;
	}
}
