package br.gov.ac.tce.message;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;

public class Messenger {
	
	public static void mostrarMensagem(MessageType tipo, String titulo,
			String mensagem) {
		FacesContext.getCurrentInstance().addMessage(null,
				new FacesMessage(tipo.getSeverity(), titulo, mensagem));
	}

	public static void mostrarMensagem(MessageType tipo, String mensagem) {
		FacesContext.getCurrentInstance().addMessage(null,
				new FacesMessage(tipo.getSeverity(), mensagem, null));
	}

	public static void mostrarMensagem(String titulo, String mensagem) {
		FacesContext.getCurrentInstance().addMessage(null,
				new FacesMessage(FacesMessage.SEVERITY_INFO, titulo, mensagem));
	}

	public static void mostrarMensagem(String mensagem) {
		FacesContext.getCurrentInstance().addMessage(null,
				new FacesMessage(FacesMessage.SEVERITY_INFO, mensagem, null));
	}
	
}
