package br.gov.ac.tce.sicapweb.modelo.entidade;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;

@Entity
public class Entidade implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	private Integer idEntidadeCjur;
	private String nome;
	private String ente;
	private String poder;
	@Transient
	private String esfera;
	private String classificacaoAdministrativa;
	
	public Entidade() {

	}

	public Entidade(Integer idEntidadeCjur, String nome) {
		this.idEntidadeCjur = idEntidadeCjur;
		this.nome = nome;
	}

	public String getEnte() {
		return Ente.parse(this.ente).getDescricao();
	}
	
	public Ente getEnteEnum() {
		return Ente.parse(this.ente);
	}

	public void setEnte(String ente) {
		this.ente = Ente.parse(ente).getDescricao();
	}

	public String getPoder() {
		return Poder.parse(this.poder).getDescricao();
	}

	public void setPoder(String poder) {
		this.poder = Poder.parse(poder).getDescricao();
	}

	public String getClassificacaoAdministrativa() {
		return ClassificacaoAdministrativa.parse(this.classificacaoAdministrativa).getDescricao();
	}

	public void setClassificacaoAdministrativa(String classificacaoAdministrativa) {
		this.classificacaoAdministrativa = ClassificacaoAdministrativa.parse(classificacaoAdministrativa)
				.getDescricao();
	}

	public String getEsfera() {
		return Esfera.parse(this.esfera).getNome();
	}

	public void setEsfera(String esfera) {
		this.esfera = Esfera.parse(esfera).getNome();
	}

	public Integer getIdEntidadeCjur() {
		return idEntidadeCjur;
	}

	public void setIdEntidadeCjur(Integer idEntidadeCjur) {
		this.idEntidadeCjur = idEntidadeCjur;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((classificacaoAdministrativa == null) ? 0 : classificacaoAdministrativa.hashCode());
		result = prime * result + ((ente == null) ? 0 : ente.hashCode());
		result = prime * result + ((idEntidadeCjur == null) ? 0 : idEntidadeCjur.hashCode());
		result = prime * result + ((nome == null) ? 0 : nome.hashCode());
		result = prime * result + ((poder == null) ? 0 : poder.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Entidade other = (Entidade) obj;
		if (classificacaoAdministrativa == null) {
			if (other.classificacaoAdministrativa != null)
				return false;
		} else if (!classificacaoAdministrativa.equals(other.classificacaoAdministrativa))
			return false;
		if (ente == null) {
			if (other.ente != null)
				return false;
		} else if (!ente.equals(other.ente))
			return false;
		if (idEntidadeCjur == null) {
			if (other.idEntidadeCjur != null)
				return false;
		} else if (!idEntidadeCjur.equals(other.idEntidadeCjur))
			return false;
		if (nome == null) {
			if (other.nome != null)
				return false;
		} else if (!nome.equals(other.nome))
			return false;
		if (poder == null) {
			if (other.poder != null)
				return false;
		} else if (!poder.equals(other.poder))
			return false;
		return true;
	}

}
