package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoFolha;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.repositorio.RemessaEventualRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.TipoFolhaRepositorio;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Named
@ViewScoped
public class TipoFolhaBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private TipoFolha tipoFolha;
	@Inject
	private RemessaEventual remessaEventual;
	@Inject
	private FormatUtil formatUtil;

	@Inject
	private RemessaEventualRepositorio remessaEventualRepositorio;
	@Inject
	private TipoFolhaRepositorio tipoFolhaRepositorio;

	private Collection<RemessaEventual> listaRemessaEventual;
	private Collection<TipoFolha> listaTipoFolha;

	private String nome;

	private int currentLevel;

	@PostConstruct
	public void init() {
		this.currentLevel = 1;
		if (this.loginBean.existeEntidadeSelecionada("/dados/entidade/tipoFolha/tipoFolha.xhtml?faces-redirect=true")) {
			try {
				this.nome = "";
				this.listaRemessaEventual = this.remessaEventualRepositorio.listarPorEntidadeTipoSituacao(
						this.loginBean.getEntidade(), TypeArquivo.TIPOS_FOLHA, SituacaoRemessa.PROCESSADA);
			} catch (RepositorioException e) {
				Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as remesas de tipos de folha.");
			}
		}
	}

	public void pesquisar() {
		try {
			this.remessaEventual.setId(Long.valueOf(0));
			this.listaTipoFolha = this.tipoFolhaRepositorio.pesquisaTiposFolha(this.loginBean.getEntidade(),
					this.remessaEventual, this.nome);
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar nenhum tipo de folha.");
		}
	}

	public void buscaDadosTipoFolha(TipoFolha tipoFolha) {
		try {
			this.tipoFolha = this.tipoFolhaRepositorio.pesquisaPorIdTipoFolha(this.loginBean.getEntidade(),
					tipoFolha.getId());
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível encontrar nenhum tipo de folha.");
		}
	}

	public TipoFolha getTipoFolha() {
		return tipoFolha;
	}

	public void setTipoFolha(TipoFolha tipoFolha) {
		this.tipoFolha = tipoFolha;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public void setFormatUtil(FormatUtil formatUtil) {
		this.formatUtil = formatUtil;
	}

	public Collection<RemessaEventual> getListaRemessaEventual() {
		return listaRemessaEventual;
	}

	public void setListaRemessaEventual(Collection<RemessaEventual> listaRemessaEventual) {
		this.listaRemessaEventual = listaRemessaEventual;
	}

	public Collection<TipoFolha> getListaTipoFolha() {
		return listaTipoFolha;
	}

	public void setListaTipoFolha(Collection<TipoFolha> listaTipoFolha) {
		this.listaTipoFolha = listaTipoFolha;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}
}
