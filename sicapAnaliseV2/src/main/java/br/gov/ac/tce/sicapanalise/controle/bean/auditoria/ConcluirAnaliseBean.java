package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.ServletException;
import javax.transaction.Transactional;

import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.ResultadoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoResultadoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoSituacaoAnalise;
import br.gov.ac.tce.sicapanalise.auditoria.business.AnaliseBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.AcumulacaoAnaliseDTO;

@Named
@ViewScoped
public class ConcluirAnaliseBean implements Serializable {

	private static final long serialVersionUID = -5419234440820805225L;

	@Inject
	private AnaliseBusiness analiseBusiness;
	
	@Inject
	private FacesContext contexto;
	
	
	private List<TipoResultadoAnalise> listaTipoResultadoAnalises;
	
	private List<TipoSituacaoAnalise> listaTipoSituacaoAnalises;
	
	private ResultadoAnalise resultadoAnalise;
	
	private TipoSituacaoAnalise tipoSituacaoAnaliseSelecionado;
	
//	private Part arquivoPart;
	private UploadedFile arquivoPart;


	private List<AcumulacaoAnaliseDTO> listaAcumulacaoAnalise = new ArrayList<>();

	@PostConstruct
	void inicializado() {
		resultadoAnalise = new ResultadoAnalise();
		listaTipoResultadoAnalises = new ArrayList<>(analiseBusiness.retornaTiposResultadoAnalise());
		listaTipoSituacaoAnalises = new ArrayList<>(analiseBusiness.retornaTiposSituacaoAnalise());
	}


	public List<AcumulacaoAnaliseDTO> getListaAnaliseAcumulacao() {
		return listaAcumulacaoAnalise;
	}



	public void setListaAnaliseAcumulacao(List<AcumulacaoAnaliseDTO> listaAcumulacaoAnalise) {
		this.listaAcumulacaoAnalise = listaAcumulacaoAnalise;
	}



	public List<TipoSituacaoAnalise> getListaTipoSituacaoAnalises() {
		return listaTipoSituacaoAnalises;
	}

	public List<TipoResultadoAnalise> getListaTipoResultadoAnalises() {
		return listaTipoResultadoAnalises;
	}

	


	public ResultadoAnalise getResultadoAnalise() {
		return resultadoAnalise;
	}

	public void setResultadoAnalise(ResultadoAnalise resultadoAnalise) {
		this.resultadoAnalise = resultadoAnalise;
	}

	public TipoSituacaoAnalise getTipoSituacaoAnaliseSelecionado() {
		return tipoSituacaoAnaliseSelecionado;
	}

	public void setTipoSituacaoAnaliseSelecionado(TipoSituacaoAnalise tipoSituacaoAnaliseSelecionado) {
		this.tipoSituacaoAnaliseSelecionado = tipoSituacaoAnaliseSelecionado;
	}
	

//	public UploadedFile getArquivoPart() {
//		return arquivoPart;
//	}
//
//	public void setArquivoPart(UploadedFile arquivoPart) {
//		this.arquivoPart = arquivoPart;
//	}

//	public void validaArquivo(FacesContext fc, UIComponent componente, Object objeto) throws ValidatorException {
//		Part arquivo = (Part) objeto;
//		String mensagem = "";
//		if (arquivo != null) {
//			if (!arquivo.getContentType().endsWith("pdf"))
//				mensagem = "Selecione um arquivo PDF";
//			else if (arquivo.getSize() > 5000000)
//				mensagem = "Tamanho do arquivo muito grande. O tamanho do arquivo permitido é menor ou igual a 5 MB.";
//
//			if (!mensagem.isEmpty()) {
//
//				throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR, mensagem, null));
//			}
//		}
//	}
	
	public void anexaRelatorio(FileUploadEvent evento) {
		
//		try {
//			System.out.println("Arquivo: " + new String(evento.getFile().getFileName().getBytes("ISO-8859-1"),"UTF-8"));
//		} catch (UnsupportedEncodingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//		System.out.println("Formato: " + evento.getFile().getContentType());
		arquivoPart = evento.getFile();
	}
	
	public String getArquivoNome() {
		String arquivoNome = "";
		try {
			if(arquivoPart != null)
				arquivoNome = new String(arquivoPart.getFileName().getBytes("ISO-8859-1"),"UTF-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		return arquivoNome;
	}

	
	@Transactional
	public String concluirAnalises() throws IOException, ServletException {

		if (resultadoAnalise.getTipoResultadoAnalise() != null && getTipoSituacaoAnaliseSelecionado() != null) {
			analiseBusiness.concluirAnalise(resultadoAnalise, tipoSituacaoAnaliseSelecionado, arquivoPart, listaAcumulacaoAnalise);
			
			contexto.getExternalContext().getFlash().setKeepMessages(true);
			contexto.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO,"Análise Concluída","As análises das acumulações selecionadas foram finalizadas."));
		
			return "index?faces-redirect=true";
		}
		 return null;



	}

}
