package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Entity implementation class for Entity: RelatorioAnalise
 *
 */
@Entity
@Table(schema = "auditoria")
public class RelatorioAnalise implements Serializable {

	   
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAnalise",nullable = false)
	private Analise analise;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRelatorio",nullable = false)
	private Relatorio relatorio;
	private static final long serialVersionUID = 1L;

	public RelatorioAnalise() {
	}   	
	public RelatorioAnalise(Relatorio relatorio, Analise analise) {
		this.relatorio = relatorio;
		this.analise = analise;
	}
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}   
	public Analise getAnalise() {
		return this.analise;
	}

	public void setAnalise(Analise analise) {
		this.analise = analise;
	}   
	public Relatorio getRelatorio() {
		return this.relatorio;
	}

	public void setRelatorio(Relatorio relatorio) {
		this.relatorio = relatorio;
	}
   
}
