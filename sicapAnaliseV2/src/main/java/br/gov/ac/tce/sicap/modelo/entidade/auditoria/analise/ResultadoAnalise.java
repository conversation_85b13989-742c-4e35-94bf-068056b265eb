package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;

@Entity
@Table(schema = "auditoria")
public class ResultadoAnalise implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4274429074444566444L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	private String descricao;
	private LocalDateTime data;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idUsuario", nullable = false)
	private Usuario usuario;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAnalise", nullable = false)
	private Analise analise;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoResultadoAnalise", nullable = false)
	private TipoResultadoAnalise tipoResultadoAnalise;
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getDescricao() {
		return descricao;
	}
	public void setDescricao(String descricao) {
		this.descricao = descricao;
	}
	public LocalDateTime getData() {
		return data;
	}
	public void setData(LocalDateTime data) {
		this.data = data;
	}
	public Usuario getUsuario() {
		return usuario;
	}
	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
	}
	public Analise getAnalise() {
		return analise;
	}
	public void setAnalise(Analise analise) {
		this.analise = analise;
	}
	public TipoResultadoAnalise getTipoResultadoAnalise() {
		return tipoResultadoAnalise;
	}
	public void setTipoResultadoAnalise(TipoResultadoAnalise tipoResultadoAnalise) {
		this.tipoResultadoAnalise = tipoResultadoAnalise;
	}
	
	
	
	
}
