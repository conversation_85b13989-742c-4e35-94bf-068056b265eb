package br.gov.ac.tce.sicapweb.modelo.remessa;

import java.io.Serializable;

public class TempestividadeRemessa implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer idEntidadeCjur;
	private String ente;
	private String poder;
	private String classificacao;
	private String esfera;
	private Integer ano;
	private String entidade;
	private String textoJaneiro;
	private String corJaneiro;
	private String textoFevereiro;
	private String corFevereiro;
	private String textoMarco;
	private String corMarco;
	private String textoAbril;
	private String corAbril;
	private String textoMaio;
	private String corMaio;
	private String textoJunho;
	private String corJunho;
	private String textoJulho;
	private String corJulho;
	private String textoAgosto;
	private String corAgosto;
	private String textoSetembro;
	private String corSetembro;
	private String textoOutubro;
	private String corOutrubro;
	private String textoNovembro;
	private String corNovembro;
	private String textoDezembro;
	private String corDezembro;
	private String textoDecimo;
	private String corDecimo;

	private String dataEnvioJaneiro;
	private String dataEnvioFevereiro;
	private String dataEnvioMarco;
	private String dataEnvioAbril;
	private String dataEnvioMaio;
	private String dataEnvioJunho;
	private String dataEnvioJulho;
	private String dataEnvioAgosto;
	private String dataEnvioSetembro;
	private String dataEnvioOutubro;
	private String dataEnvioNovembro;
	private String dataEnvioDezembro;
	private String dataEnvioDecimo;

	private String dataConfirmacaoJaneiro;
	private String dataConfirmacaoFevereiro;
	private String dataConfirmacaoMarco;
	private String dataConfirmacaoAbril;
	private String dataConfirmacaoMaio;
	private String dataConfirmacaoJunho;
	private String dataConfirmacaoJulho;
	private String dataConfirmacaoAgosto;
	private String dataConfirmacaoSetembro;
	private String dataConfirmacaoOutubro;
	private String dataConfirmacaoNovembro;
	private String dataConfirmacaoDezembro;
	private String dataConfirmacaoDecimo;

	private String protocoloJaneiro;
	private String protocoloFevereiro;
	private String protocoloMarco;
	private String protocoloAbril;
	private String protocoloMaio;
	private String protocoloJunho;
	private String protocoloJulho;
	private String protocoloAgosto;
	private String protocoloSetembro;
	private String protocoloOutubro;
	private String protocoloNovembro;
	private String protocoloDezembro;
	private String protocoloDecimo;

	private String assinaturaJaneiro;
	private String assinaturaFevereiro;
	private String assinaturaMarco;
	private String assinaturaAbril;
	private String assinaturaMaio;
	private String assinaturaJunho;
	private String assinaturaJulho;
	private String assinaturaAgosto;
	private String assinaturaSetembro;
	private String assinaturaOutubro;
	private String assinaturaNovembro;
	private String assinaturaDezembro;
	private String assinaturaDecimo;

	private String textoRelatorioJan;
	private String textoRelatorioFev;
	private String textoRelatorioMar;
	private String textoRelatorioAbr;
	private String textoRelatorioMai;
	private String textoRelatorioJun;
	private String textoRelatorioJul;
	private String textoRelatorioAgo;
	private String textoRelatorioSet;
	private String textoRelatorioOut;
	private String textoRelatorioNov;
	private String textoRelatorioDez;
	private String textoRelatorioDecimo;

	private Integer idUsuarioJan;
	private Integer idUsuarioFev;
	private Integer idUsuarioMar;
	private Integer idUsuarioAbr;
	private Integer idUsuarioMai;
	private Integer idUsuarioJun;
	private Integer idUsuarioJul;
	private Integer idUsuarioAgo;
	private Integer idUsuarioSet;
	private Integer idUsuarioOut;
	private Integer idUsuarioNov;
	private Integer idUsuarioDez;
	private Integer idUsuarioDecimo;

	public Integer getIdEntidadeCjur() {
		return idEntidadeCjur;
	}

	public void setIdEntidadeCjur(Integer idEntidadeCjur) {
		this.idEntidadeCjur = idEntidadeCjur;
	}

	public String getEnte() {
		return ente;
	}

	public void setEnte(String ente) {
		this.ente = ente;
	}

	public String getPoder() {
		return poder;
	}

	public void setPoder(String poder) {
		this.poder = poder;
	}

	public String getClassificacao() {
		return classificacao;
	}

	public void setClassificacao(String classificacao) {
		this.classificacao = classificacao;
	}

	public String getEsfera() {
		return esfera;
	}

	public void setEsfera(String esfera) {
		this.esfera = esfera;
	}

	public Integer getAno() {
		return ano;
	}

	public void setAno(Integer ano) {
		this.ano = ano;
	}

	public String getEntidade() {
		return entidade;
	}

	public void setEntidade(String entidade) {
		this.entidade = entidade;
	}

	public String getTextoJaneiro() {
		return textoJaneiro;
	}

	public void setTextoJaneiro(String textoJaneiro) {
		this.textoJaneiro = textoJaneiro;
	}

	public String getCorJaneiro() {
		return corJaneiro;
	}

	public void setCorJaneiro(String corJaneiro) {
		this.corJaneiro = corJaneiro;
	}

	public String getTextoFevereiro() {
		return textoFevereiro;
	}

	public void setTextoFevereiro(String textoFevereiro) {
		this.textoFevereiro = textoFevereiro;
	}

	public String getCorFevereiro() {
		return corFevereiro;
	}

	public void setCorFevereiro(String corFevereiro) {
		this.corFevereiro = corFevereiro;
	}

	public String getTextoMarco() {
		return textoMarco;
	}

	public void setTextoMarco(String textoMarco) {
		this.textoMarco = textoMarco;
	}

	public String getCorMarco() {
		return corMarco;
	}

	public void setCorMarco(String corMarco) {
		this.corMarco = corMarco;
	}

	public String getTextoAbril() {
		return textoAbril;
	}

	public void setTextoAbril(String textoAbril) {
		this.textoAbril = textoAbril;
	}

	public String getCorAbril() {
		return corAbril;
	}

	public void setCorAbril(String corAbril) {
		this.corAbril = corAbril;
	}

	public String getTextoMaio() {
		return textoMaio;
	}

	public void setTextoMaio(String textoMaio) {
		this.textoMaio = textoMaio;
	}

	public String getCorMaio() {
		return corMaio;
	}

	public void setCorMaio(String corMaio) {
		this.corMaio = corMaio;
	}

	public String getTextoJunho() {
		return textoJunho;
	}

	public void setTextoJunho(String textoJunho) {
		this.textoJunho = textoJunho;
	}

	public String getCorJunho() {
		return corJunho;
	}

	public void setCorJunho(String corJunho) {
		this.corJunho = corJunho;
	}

	public String getTextoJulho() {
		return textoJulho;
	}

	public void setTextoJulho(String textoJulho) {
		this.textoJulho = textoJulho;
	}

	public String getCorJulho() {
		return corJulho;
	}

	public void setCorJulho(String corJulho) {
		this.corJulho = corJulho;
	}

	public String getTextoAgosto() {
		return textoAgosto;
	}

	public void setTextoAgosto(String textoAgosto) {
		this.textoAgosto = textoAgosto;
	}

	public String getCorAgosto() {
		return corAgosto;
	}

	public void setCorAgosto(String corAgosto) {
		this.corAgosto = corAgosto;
	}

	public String getTextoSetembro() {
		return textoSetembro;
	}

	public void setTextoSetembro(String textoSetembro) {
		this.textoSetembro = textoSetembro;
	}

	public String getCorSetembro() {
		return corSetembro;
	}

	public void setCorSetembro(String corSetembro) {
		this.corSetembro = corSetembro;
	}

	public String getTextoOutubro() {
		return textoOutubro;
	}

	public void setTextoOutubro(String textoOutubro) {
		this.textoOutubro = textoOutubro;
	}

	public String getCorOutrubro() {
		return corOutrubro;
	}

	public void setCorOutrubro(String corOutrubro) {
		this.corOutrubro = corOutrubro;
	}

	public String getTextoNovembro() {
		return textoNovembro;
	}

	public void setTextoNovembro(String textoNovembro) {
		this.textoNovembro = textoNovembro;
	}

	public String getCorNovembro() {
		return corNovembro;
	}

	public void setCorNovembro(String corNovembro) {
		this.corNovembro = corNovembro;
	}

	public String getTextoDezembro() {
		return textoDezembro;
	}

	public void setTextoDezembro(String textoDezembro) {
		this.textoDezembro = textoDezembro;
	}

	public String getCorDezembro() {
		return corDezembro;
	}

	public void setCorDezembro(String corDezembro) {
		this.corDezembro = corDezembro;
	}

	public String getTextoDecimo() {
		return textoDecimo;
	}

	public void setTextoDecimo(String textoDecimo) {
		this.textoDecimo = textoDecimo;
	}

	public String getCorDecimo() {
		return corDecimo;
	}

	public void setCorDecimo(String corDecimo) {
		this.corDecimo = corDecimo;
	}

	public String getAssinaturaJaneiro() {
		return assinaturaJaneiro;
	}

	public void setAssinaturaJaneiro(String assinaturaJaneiro) {
		this.assinaturaJaneiro = assinaturaJaneiro;
	}

	public String getAssinaturaFevereiro() {
		return assinaturaFevereiro;
	}

	public void setAssinaturaFevereiro(String assinaturaFevereiro) {
		this.assinaturaFevereiro = assinaturaFevereiro;
	}

	public String getAssinaturaMarco() {
		return assinaturaMarco;
	}

	public void setAssinaturaMarco(String assinaturaMarco) {
		this.assinaturaMarco = assinaturaMarco;
	}

	public String getAssinaturaAbril() {
		return assinaturaAbril;
	}

	public void setAssinaturaAbril(String assinaturaAbril) {
		this.assinaturaAbril = assinaturaAbril;
	}

	public String getAssinaturaMaio() {
		return assinaturaMaio;
	}

	public void setAssinaturaMaio(String assinaturaMaio) {
		this.assinaturaMaio = assinaturaMaio;
	}

	public String getAssinaturaJunho() {
		return assinaturaJunho;
	}

	public void setAssinaturaJunho(String assinaturaJunho) {
		this.assinaturaJunho = assinaturaJunho;
	}

	public String getAssinaturaJulho() {
		return assinaturaJulho;
	}

	public void setAssinaturaJulho(String assinaturaJulho) {
		this.assinaturaJulho = assinaturaJulho;
	}

	public String getAssinaturaAgosto() {
		return assinaturaAgosto;
	}

	public void setAssinaturaAgosto(String assinaturaAgosto) {
		this.assinaturaAgosto = assinaturaAgosto;
	}

	public String getAssinaturaSetembro() {
		return assinaturaSetembro;
	}

	public void setAssinaturaSetembro(String assinaturaSetembro) {
		this.assinaturaSetembro = assinaturaSetembro;
	}

	public String getAssinaturaOutubro() {
		return assinaturaOutubro;
	}

	public void setAssinaturaOutubro(String assinaturaOutubro) {
		this.assinaturaOutubro = assinaturaOutubro;
	}

	public String getAssinaturaNovembro() {
		return assinaturaNovembro;
	}

	public void setAssinaturaNovembro(String assinaturaNovembro) {
		this.assinaturaNovembro = assinaturaNovembro;
	}

	public String getAssinaturaDezembro() {
		return assinaturaDezembro;
	}

	public void setAssinaturaDezembro(String assinaturaDezembro) {
		this.assinaturaDezembro = assinaturaDezembro;
	}

	public String getProtocoloJaneiro() {
		return protocoloJaneiro;
	}

	public void setProtocoloJaneiro(String protocoloJaneiro) {
		this.protocoloJaneiro = protocoloJaneiro;
	}

	public String getProtocoloFevereiro() {
		return protocoloFevereiro;
	}

	public void setProtocoloFevereiro(String protocoloFevereiro) {
		this.protocoloFevereiro = protocoloFevereiro;
	}

	public String getProtocoloMarco() {
		return protocoloMarco;
	}

	public void setProtocoloMarco(String protocoloMarco) {
		this.protocoloMarco = protocoloMarco;
	}

	public String getProtocoloAbril() {
		return protocoloAbril;
	}

	public void setProtocoloAbril(String protocoloAbril) {
		this.protocoloAbril = protocoloAbril;
	}

	public String getProtocoloMaio() {
		return protocoloMaio;
	}

	public void setProtocoloMaio(String protocoloMaio) {
		this.protocoloMaio = protocoloMaio;
	}

	public String getProtocoloJunho() {
		return protocoloJunho;
	}

	public void setProtocoloJunho(String protocoloJunho) {
		this.protocoloJunho = protocoloJunho;
	}

	public String getProtocoloJulho() {
		return protocoloJulho;
	}

	public void setProtocoloJulho(String protocoloJulho) {
		this.protocoloJulho = protocoloJulho;
	}

	public String getProtocoloAgosto() {
		return protocoloAgosto;
	}

	public void setProtocoloAgosto(String protocoloAgosto) {
		this.protocoloAgosto = protocoloAgosto;
	}

	public String getProtocoloSetembro() {
		return protocoloSetembro;
	}

	public void setProtocoloSetembro(String protocoloSetembro) {
		this.protocoloSetembro = protocoloSetembro;
	}

	public String getProtocoloOutubro() {
		return protocoloOutubro;
	}

	public void setProtocoloOutubro(String protocoloOutubro) {
		this.protocoloOutubro = protocoloOutubro;
	}

	public String getProtocoloNovembro() {
		return protocoloNovembro;
	}

	public void setProtocoloNovembro(String protocoloNovembro) {
		this.protocoloNovembro = protocoloNovembro;
	}

	public String getProtocoloDezembro() {
		return protocoloDezembro;
	}

	public void setProtocoloDezembro(String protocoloDezembro) {
		this.protocoloDezembro = protocoloDezembro;
	}

	public String getProtocoloDecimo() {
		return protocoloDecimo;
	}

	public void setProtocoloDecimo(String protocoloDecimo) {
		this.protocoloDecimo = protocoloDecimo;
	}

	public String getAssinaturaDecimo() {
		return assinaturaDecimo;
	}

	public void setAssinaturaDecimo(String assinaturaDecimo) {
		this.assinaturaDecimo = assinaturaDecimo;
	}

	public String getDataEnvioJaneiro() {
		return dataEnvioJaneiro;
	}

	public void setDataEnvioJaneiro(String dataEnvioJaneiro) {
		this.dataEnvioJaneiro = dataEnvioJaneiro;
	}

	public String getDataEnvioFevereiro() {
		return dataEnvioFevereiro;
	}

	public void setDataEnvioFevereiro(String dataEnvioFevereiro) {
		this.dataEnvioFevereiro = dataEnvioFevereiro;
	}

	public String getDataEnvioMarco() {
		return dataEnvioMarco;
	}

	public void setDataEnvioMarco(String dataEnvioMarco) {
		this.dataEnvioMarco = dataEnvioMarco;
	}

	public String getDataEnvioAbril() {
		return dataEnvioAbril;
	}

	public void setDataEnvioAbril(String dataEnvioAbril) {
		this.dataEnvioAbril = dataEnvioAbril;
	}

	public String getDataEnvioMaio() {
		return dataEnvioMaio;
	}

	public void setDataEnvioMaio(String dataEnvioMaio) {
		this.dataEnvioMaio = dataEnvioMaio;
	}

	public String getDataEnvioJunho() {
		return dataEnvioJunho;
	}

	public void setDataEnvioJunho(String dataEnvioJunho) {
		this.dataEnvioJunho = dataEnvioJunho;
	}

	public String getDataEnvioJulho() {
		return dataEnvioJulho;
	}

	public void setDataEnvioJulho(String dataEnvioJulho) {
		this.dataEnvioJulho = dataEnvioJulho;
	}

	public String getDataEnvioAgosto() {
		return dataEnvioAgosto;
	}

	public void setDataEnvioAgosto(String dataEnvioAgosto) {
		this.dataEnvioAgosto = dataEnvioAgosto;
	}

	public String getDataEnvioSetembro() {
		return dataEnvioSetembro;
	}

	public void setDataEnvioSetembro(String dataEnvioSetembro) {
		this.dataEnvioSetembro = dataEnvioSetembro;
	}

	public String getDataEnvioOutubro() {
		return dataEnvioOutubro;
	}

	public void setDataEnvioOutubro(String dataEnvioOutubro) {
		this.dataEnvioOutubro = dataEnvioOutubro;
	}

	public String getDataEnvioNovembro() {
		return dataEnvioNovembro;
	}

	public void setDataEnvioNovembro(String dataEnvioNovembro) {
		this.dataEnvioNovembro = dataEnvioNovembro;
	}

	public String getDataEnvioDezembro() {
		return dataEnvioDezembro;
	}

	public void setDataEnvioDezembro(String dataEnvioDezembro) {
		this.dataEnvioDezembro = dataEnvioDezembro;
	}

	public String getDataEnvioDecimo() {
		return dataEnvioDecimo;
	}

	public void setDataEnvioDecimo(String dataEnvioDecimo) {
		this.dataEnvioDecimo = dataEnvioDecimo;
	}

	public String getTextoRelatorioJan() {
		return textoRelatorioJan;
	}

	public void setTextoRelatorioJan(String textoRelatorioJan) {
		this.textoRelatorioJan = textoRelatorioJan;
	}

	public String getTextoRelatorioFev() {
		return textoRelatorioFev;
	}

	public void setTextoRelatorioFev(String textoRelatorioFev) {
		this.textoRelatorioFev = textoRelatorioFev;
	}

	public String getTextoRelatorioMar() {
		return textoRelatorioMar;
	}

	public void setTextoRelatorioMar(String textoRelatorioMar) {
		this.textoRelatorioMar = textoRelatorioMar;
	}

	public String getTextoRelatorioAbr() {
		return textoRelatorioAbr;
	}

	public void setTextoRelatorioAbr(String textoRelatorioAbr) {
		this.textoRelatorioAbr = textoRelatorioAbr;
	}

	public String getTextoRelatorioMai() {
		return textoRelatorioMai;
	}

	public void setTextoRelatorioMai(String textoRelatorioMai) {
		this.textoRelatorioMai = textoRelatorioMai;
	}

	public String getTextoRelatorioJun() {
		return textoRelatorioJun;
	}

	public void setTextoRelatorioJun(String textoRelatorioJun) {
		this.textoRelatorioJun = textoRelatorioJun;
	}

	public String getTextoRelatorioJul() {
		return textoRelatorioJul;
	}

	public void setTextoRelatorioJul(String textoRelatorioJul) {
		this.textoRelatorioJul = textoRelatorioJul;
	}

	public String getTextoRelatorioAgo() {
		return textoRelatorioAgo;
	}

	public void setTextoRelatorioAgo(String textoRelatorioAgo) {
		this.textoRelatorioAgo = textoRelatorioAgo;
	}

	public String getTextoRelatorioSet() {
		return textoRelatorioSet;
	}

	public void setTextoRelatorioSet(String textoRelatorioSet) {
		this.textoRelatorioSet = textoRelatorioSet;
	}

	public String getTextoRelatorioOut() {
		return textoRelatorioOut;
	}

	public void setTextoRelatorioOut(String textoRelatorioOut) {
		this.textoRelatorioOut = textoRelatorioOut;
	}

	public String getTextoRelatorioNov() {
		return textoRelatorioNov;
	}

	public void setTextoRelatorioNov(String textoRelatorioNov) {
		this.textoRelatorioNov = textoRelatorioNov;
	}

	public String getTextoRelatorioDez() {
		return textoRelatorioDez;
	}

	public void setTextoRelatorioDez(String textoRelatorioDez) {
		this.textoRelatorioDez = textoRelatorioDez;
	}

	public String getTextoRelatorioDecimo() {
		return textoRelatorioDecimo;
	}

	public void setTextoRelatorioDecimo(String textoRelatorioDecimo) {
		this.textoRelatorioDecimo = textoRelatorioDecimo;
	}

	public Integer getIdUsuarioJan() {
		return idUsuarioJan;
	}

	public void setIdUsuarioJan(Integer idUsuarioJan) {
		this.idUsuarioJan = idUsuarioJan;
	}

	public Integer getIdUsuarioFev() {
		return idUsuarioFev;
	}

	public void setIdUsuarioFev(Integer idUsuarioFev) {
		this.idUsuarioFev = idUsuarioFev;
	}

	public Integer getIdUsuarioMar() {
		return idUsuarioMar;
	}

	public void setIdUsuarioMar(Integer idUsuarioMar) {
		this.idUsuarioMar = idUsuarioMar;
	}

	public Integer getIdUsuarioAbr() {
		return idUsuarioAbr;
	}

	public void setIdUsuarioAbr(Integer idUsuarioAbr) {
		this.idUsuarioAbr = idUsuarioAbr;
	}

	public Integer getIdUsuarioMai() {
		return idUsuarioMai;
	}

	public void setIdUsuarioMai(Integer idUsuarioMai) {
		this.idUsuarioMai = idUsuarioMai;
	}

	public Integer getIdUsuarioJun() {
		return idUsuarioJun;
	}

	public void setIdUsuarioJun(Integer idUsuarioJun) {
		this.idUsuarioJun = idUsuarioJun;
	}

	public Integer getIdUsuarioJul() {
		return idUsuarioJul;
	}

	public void setIdUsuarioJul(Integer idUsuarioJul) {
		this.idUsuarioJul = idUsuarioJul;
	}

	public Integer getIdUsuarioAgo() {
		return idUsuarioAgo;
	}

	public void setIdUsuarioAgo(Integer idUsuarioAgo) {
		this.idUsuarioAgo = idUsuarioAgo;
	}

	public Integer getIdUsuarioSet() {
		return idUsuarioSet;
	}

	public void setIdUsuarioSet(Integer idUsuarioSet) {
		this.idUsuarioSet = idUsuarioSet;
	}

	public Integer getIdUsuarioOut() {
		return idUsuarioOut;
	}

	public void setIdUsuarioOut(Integer idUsuarioOut) {
		this.idUsuarioOut = idUsuarioOut;
	}

	public Integer getIdUsuarioNov() {
		return idUsuarioNov;
	}

	public void setIdUsuarioNov(Integer idUsuarioNov) {
		this.idUsuarioNov = idUsuarioNov;
	}

	public Integer getIdUsuarioDez() {
		return idUsuarioDez;
	}

	public void setIdUsuarioDez(Integer idUsuarioDez) {
		this.idUsuarioDez = idUsuarioDez;
	}

	public Integer getIdUsuarioDecimo() {
		return idUsuarioDecimo;
	}

	public void setIdUsuarioDecimo(Integer idUsuarioDecimo) {
		this.idUsuarioDecimo = idUsuarioDecimo;
	}

	public String getDataConfirmacaoJaneiro() {
		return dataConfirmacaoJaneiro;
	}

	public void setDataConfirmacaoJaneiro(String dataConfirmacaoJaneiro) {
		this.dataConfirmacaoJaneiro = dataConfirmacaoJaneiro;
	}

	public String getDataConfirmacaoFevereiro() {
		return dataConfirmacaoFevereiro;
	}

	public void setDataConfirmacaoFevereiro(String dataConfirmacaoFevereiro) {
		this.dataConfirmacaoFevereiro = dataConfirmacaoFevereiro;
	}

	public String getDataConfirmacaoMarco() {
		return dataConfirmacaoMarco;
	}

	public void setDataConfirmacaoMarco(String dataConfirmacaoMarco) {
		this.dataConfirmacaoMarco = dataConfirmacaoMarco;
	}

	public String getDataConfirmacaoAbril() {
		return dataConfirmacaoAbril;
	}

	public void setDataConfirmacaoAbril(String dataConfirmacaoAbril) {
		this.dataConfirmacaoAbril = dataConfirmacaoAbril;
	}

	public String getDataConfirmacaoMaio() {
		return dataConfirmacaoMaio;
	}

	public void setDataConfirmacaoMaio(String dataConfirmacaoMaio) {
		this.dataConfirmacaoMaio = dataConfirmacaoMaio;
	}

	public String getDataConfirmacaoJunho() {
		return dataConfirmacaoJunho;
	}

	public void setDataConfirmacaoJunho(String dataConfirmacaoJunho) {
		this.dataConfirmacaoJunho = dataConfirmacaoJunho;
	}

	public String getDataConfirmacaoJulho() {
		return dataConfirmacaoJulho;
	}

	public void setDataConfirmacaoJulho(String dataConfirmacaoJulho) {
		this.dataConfirmacaoJulho = dataConfirmacaoJulho;
	}

	public String getDataConfirmacaoAgosto() {
		return dataConfirmacaoAgosto;
	}

	public void setDataConfirmacaoAgosto(String dataConfirmacaoAgosto) {
		this.dataConfirmacaoAgosto = dataConfirmacaoAgosto;
	}

	public String getDataConfirmacaoSetembro() {
		return dataConfirmacaoSetembro;
	}

	public void setDataConfirmacaoSetembro(String dataConfirmacaoSetembro) {
		this.dataConfirmacaoSetembro = dataConfirmacaoSetembro;
	}

	public String getDataConfirmacaoOutubro() {
		return dataConfirmacaoOutubro;
	}

	public void setDataConfirmacaoOutubro(String dataConfirmacaoOutubro) {
		this.dataConfirmacaoOutubro = dataConfirmacaoOutubro;
	}

	public String getDataConfirmacaoNovembro() {
		return dataConfirmacaoNovembro;
	}

	public void setDataConfirmacaoNovembro(String dataConfirmacaoNovembro) {
		this.dataConfirmacaoNovembro = dataConfirmacaoNovembro;
	}

	public String getDataConfirmacaoDezembro() {
		return dataConfirmacaoDezembro;
	}

	public void setDataConfirmacaoDezembro(String dataConfirmacaoDezembro) {
		this.dataConfirmacaoDezembro = dataConfirmacaoDezembro;
	}

	public String getDataConfirmacaoDecimo() {
		return dataConfirmacaoDecimo;
	}

	public void setDataConfirmacaoDecimo(String dataConfirmacaoDecimo) {
		this.dataConfirmacaoDecimo = dataConfirmacaoDecimo;
	}

	@Override
	public String toString() {
		return "TempestividadeRemessa [idEntidadeCjur=" + idEntidadeCjur + ", ente=" + ente + ", poder=" + poder
				+ ", classificacao=" + classificacao + ", esfera=" + esfera + ", ano=" + ano + ", entidade=" + entidade
				+ ", textoJaneiro=" + textoJaneiro + ", corJaneiro=" + corJaneiro + ", textoFevereiro=" + textoFevereiro
				+ ", corFevereiro=" + corFevereiro + ", textoMarco=" + textoMarco + ", corMarco=" + corMarco
				+ ", textoAbril=" + textoAbril + ", corAbril=" + corAbril + ", textoMaio=" + textoMaio + ", corMaio="
				+ corMaio + ", textoJunho=" + textoJunho + ", corJunho=" + corJunho + ", textoJulho=" + textoJulho
				+ ", corJulho=" + corJulho + ", textoAgosto=" + textoAgosto + ", corAgosto=" + corAgosto
				+ ", textoSetembro=" + textoSetembro + ", corSetembro=" + corSetembro + ", textoOutubro=" + textoOutubro
				+ ", corOutrubro=" + corOutrubro + ", textoNovembro=" + textoNovembro + ", corNovembro=" + corNovembro
				+ ", textoDezembro=" + textoDezembro + ", corDezembro=" + corDezembro + ", textoDecimo=" + textoDecimo
				+ ", corDecimo=" + corDecimo + ", dataEnvioJaneiro=" + dataEnvioJaneiro + ", dataEnvioFevereiro="
				+ dataEnvioFevereiro + ", dataEnvioMarco=" + dataEnvioMarco + ", dataEnvioAbril=" + dataEnvioAbril
				+ ", dataEnvioMaio=" + dataEnvioMaio + ", dataEnvioJunho=" + dataEnvioJunho + ", dataEnvioJulho="
				+ dataEnvioJulho + ", dataEnvioAgosto=" + dataEnvioAgosto + ", dataEnvioSetembro=" + dataEnvioSetembro
				+ ", dataEnvioOutubro=" + dataEnvioOutubro + ", dataEnvioNovembro=" + dataEnvioNovembro
				+ ", dataEnvioDezembro=" + dataEnvioDezembro + ", dataEnvioDecimo=" + dataEnvioDecimo
				+ ", dataConfirmacaoJaneiro=" + dataConfirmacaoJaneiro + ", dataConfirmacaoFevereiro="
				+ dataConfirmacaoFevereiro + ", dataConfirmacaoMarco=" + dataConfirmacaoMarco
				+ ", dataConfirmacaoAbril=" + dataConfirmacaoAbril + ", dataConfirmacaoMaio=" + dataConfirmacaoMaio
				+ ", dataConfirmacaoJunho=" + dataConfirmacaoJunho + ", dataConfirmacaoJulho=" + dataConfirmacaoJulho
				+ ", dataConfirmacaoAgosto=" + dataConfirmacaoAgosto + ", dataConfirmacaoSetembro="
				+ dataConfirmacaoSetembro + ", dataConfirmacaoOutubro=" + dataConfirmacaoOutubro
				+ ", dataConfirmacaoNovembro=" + dataConfirmacaoNovembro + ", dataConfirmacaoDezembro="
				+ dataConfirmacaoDezembro + ", dataConfirmacaoDecimo=" + dataConfirmacaoDecimo + ", protocoloJaneiro="
				+ protocoloJaneiro + ", protocoloFevereiro=" + protocoloFevereiro + ", protocoloMarco=" + protocoloMarco
				+ ", protocoloAbril=" + protocoloAbril + ", protocoloMaio=" + protocoloMaio + ", protocoloJunho="
				+ protocoloJunho + ", protocoloJulho=" + protocoloJulho + ", protocoloAgosto=" + protocoloAgosto
				+ ", protocoloSetembro=" + protocoloSetembro + ", protocoloOutubro=" + protocoloOutubro
				+ ", protocoloNovembro=" + protocoloNovembro + ", protocoloDezembro=" + protocoloDezembro
				+ ", protocoloDecimo=" + protocoloDecimo + ", assinaturaJaneiro=" + assinaturaJaneiro
				+ ", assinaturaFevereiro=" + assinaturaFevereiro + ", assinaturaMarco=" + assinaturaMarco
				+ ", assinaturaAbril=" + assinaturaAbril + ", assinaturaMaio=" + assinaturaMaio + ", assinaturaJunho="
				+ assinaturaJunho + ", assinaturaJulho=" + assinaturaJulho + ", assinaturaAgosto=" + assinaturaAgosto
				+ ", assinaturaSetembro=" + assinaturaSetembro + ", assinaturaOutubro=" + assinaturaOutubro
				+ ", assinaturaNovembro=" + assinaturaNovembro + ", assinaturaDezembro=" + assinaturaDezembro
				+ ", assinaturaDecimo=" + assinaturaDecimo + ", textoRelatorioJan=" + textoRelatorioJan
				+ ", textoRelatorioFev=" + textoRelatorioFev + ", textoRelatorioMar=" + textoRelatorioMar
				+ ", textoRelatorioAbr=" + textoRelatorioAbr + ", textoRelatorioMai=" + textoRelatorioMai
				+ ", textoRelatorioJun=" + textoRelatorioJun + ", textoRelatorioJul=" + textoRelatorioJul
				+ ", textoRelatorioAgo=" + textoRelatorioAgo + ", textoRelatorioSet=" + textoRelatorioSet
				+ ", textoRelatorioOut=" + textoRelatorioOut + ", textoRelatorioNov=" + textoRelatorioNov
				+ ", textoRelatorioDez=" + textoRelatorioDez + ", textoRelatorioDecimo=" + textoRelatorioDecimo
				+ ", idUsuarioJan=" + idUsuarioJan + ", idUsuarioFev=" + idUsuarioFev + ", idUsuarioMar=" + idUsuarioMar
				+ ", idUsuarioAbr=" + idUsuarioAbr + ", idUsuarioMai=" + idUsuarioMai + ", idUsuarioJun=" + idUsuarioJun
				+ ", idUsuarioJul=" + idUsuarioJul + ", idUsuarioAgo=" + idUsuarioAgo + ", idUsuarioSet=" + idUsuarioSet
				+ ", idUsuarioOut=" + idUsuarioOut + ", idUsuarioNov=" + idUsuarioNov + ", idUsuarioDez=" + idUsuarioDez
				+ ", idUsuarioDecimo=" + idUsuarioDecimo + "]";
	}
	
	

}
