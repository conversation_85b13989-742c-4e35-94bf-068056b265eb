package br.gov.ac.tce.sicapanalise.auditoria.converters;

import java.util.Map;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoSituacaoAnalise;

@FacesConverter("tipoSituacaoAnaliseConverter")
public class TipoSituacaoAnaliseConverter implements Converter {
	@Override
	public Object getAsObject(FacesContext contexto, UIComponent component, String valor) {
		if (valor == null || valor.trim().isEmpty())
			return null;
//		System.out.println("Convertendo para Objeto: " + valor);

		
		return this.getAttributesFrom(component).get(valor);
		
//		try {
//			TipoSituacaoAnalise tsa = new TipoSituacaoAnalise();
//			tsa.setId(Long.valueOf(valor));
//			return tsa;
//		} catch (Exception e) {
//			e.printStackTrace();
////			System.out.println("Erro TipoSituacaoAnaliseConverter: " + e.getMessage());
//			return null;
//		}

	}

	@Override
	public String getAsString(FacesContext contexto, UIComponent component, Object objeto) {

		if (objeto == null)
			return null;
//		System.out.println("Convertendo para String: " + objeto);

		TipoSituacaoAnalise tsa = (TipoSituacaoAnalise) objeto;
		
		// adiciona objeto como atributo do componente
		this.addAttribute(component, tsa);
		
		return tsa.getId().toString();
	}
	
	
	protected void addAttribute(UIComponent component, TipoSituacaoAnalise tipoSituacaoAnalise) {
		String key = tipoSituacaoAnalise.getId().toString(); // codigo da entidade
		this.getAttributesFrom(component).put(key, tipoSituacaoAnalise);
	}
	
	protected Map<String, Object> getAttributesFrom(UIComponent component) {
		return component.getAttributes();
	}
}
