package br.gov.ac.tce.sicapanalise.auditoria.business;

import java.time.LocalDateTime;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.inject.Inject;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.AnaliseAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DetalhamentoAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.SolicitacaoDocumentoAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.Analise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.HistoricoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.SituacaoSolicitacaoDocumento;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.SolicitacaoDocumento;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoProcedimentoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoSituacaoAnalise;
import br.gov.ac.tce.sicap.modelo.entidade.notificacao.Notificacao;
import br.gov.ac.tce.sicap.modelo.entidade.notificacao.NotificacaoInformacao;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.AcumulacaoRepositorio;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.AnaliseRepositorio;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.NotificacaoRepositorio;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.SolicitacaoDocumentoRepositorio;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Stateless
public class NotificacaoBusiness {

	@Inject
	private AnaliseRepositorio analiseRepositorio;

	@Inject
	private AcumulacaoRepositorio acumulacaoRepositorio;

	@Inject
	private SolicitacaoDocumentoRepositorio solicitacaoDocumentoRepositorio;

	@Inject
	private NotificacaoRepositorio notificacaoRepositorio;

	public void notificarAnalise(Collection<AnaliseAcumulacao> analisesANotificar, NotificacaoInformacao notifInfo) {

		LocalDateTime dataSolicitacao = LocalDateTime.now();
		LocalDateTime dataPrazoResposta = dataSolicitacao.plusDays((long) notifInfo.getPrazoDias());

		TipoAnalise tipoAnalise = analiseRepositorio.retornaTipoAnalise(1L);
		TipoProcedimentoAnalise tipoProcedimentoAnalise = analiseRepositorio.retornaTipoProcedimentoAnalise(2L);
		TipoSituacaoAnalise tipoSituacaoAnalise = analiseRepositorio.retornaTipoSituacaoAnalise(8L);

		for (AnaliseAcumulacao aa : analisesANotificar) {

//			AnaliseAcumulacao aa = analiseRepositorio.retornaAnaliseAcumulacao(aaDto.getId());

			Analise analise = aa.getAnalise();
			analise.setSituacao(tipoSituacaoAnalise.getCodigo());
			analise.setSituacaoAnalise(tipoSituacaoAnalise);
			analiseRepositorio.atualizar(analise);

			HistoricoAnalise historicoAnalise = new HistoricoAnalise();
			historicoAnalise.setData(dataSolicitacao);
			historicoAnalise.setProcedimentoRealizado(tipoProcedimentoAnalise);
			historicoAnalise.setDespacho(notifInfo.getDespacho());
			historicoAnalise.setUsuario(notifInfo.getUsuario());
			historicoAnalise.setAnalise(analise);
			analiseRepositorio.inserirHistorico(historicoAnalise);

			Acumulacao acumulacao = aa.getAcumulacao();
			acumulacao.setSituacao(tipoSituacaoAnalise.getCodigo());
			acumulacaoRepositorio.atualizar(acumulacao);

			Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao = acumulacaoRepositorio
					.retornaAcumulacaoPorId(acumulacao.getId()).getListaDetalhamentoAcumulacao();
			for (DetalhamentoAcumulacao detalhamentoAcumulacao : listaDetalhamentoAcumulacao) {
				Entidade entidade = detalhamentoAcumulacao.getEntidade();

				SolicitacaoDocumento solicitacaoDocumento = new SolicitacaoDocumento();
				solicitacaoDocumento.setAnalise(analise);
				solicitacaoDocumento.setDataSolicitacao(dataSolicitacao);
				solicitacaoDocumento.setDataPrazoResposta(dataPrazoResposta);
				solicitacaoDocumento.setEntidade(entidade);
				solicitacaoDocumento.setEnvioLiberado(true);
				solicitacaoDocumento.setSituacao(SituacaoSolicitacaoDocumento.AG);
				solicitacaoDocumento.setTipoAnalise(tipoAnalise);
				solicitacaoDocumentoRepositorio.salvarSolicitacaoDocumento(solicitacaoDocumento);

				SolicitacaoDocumentoAcumulacao solicitacaoDocumentoAcumulacao = new SolicitacaoDocumentoAcumulacao();
				solicitacaoDocumentoAcumulacao.setDetalhamentoAcumulacao(detalhamentoAcumulacao);
				solicitacaoDocumentoAcumulacao.setSolicitacaoDocumento(solicitacaoDocumento);
				solicitacaoDocumentoAcumulacao.setAnaliseAcumulacao(aa);
				solicitacaoDocumentoAcumulacao.setEnvioFinalizado(false);
				solicitacaoDocumentoRepositorio.salvarSolicitacaoDocumentoAcumulacao(solicitacaoDocumentoAcumulacao);

				String competencia = detalhamentoAcumulacao.getMes().getNome() + "/" + detalhamentoAcumulacao.getAno();
				String assunto = "Notificação de possíveis acumulações durante análise da folha de pagamento da competência "
						+ competencia + ". Servidor: " + detalhamentoAcumulacao.getAcumulacao().getNome()
						+ ", Matricula: " + detalhamentoAcumulacao.getBeneficiario().getMatricula();
				String mensagem = "Foram encontrados indícios de possíveis acumulações durante análise da folha de pagamento da competência "
						+ competencia + ".";

				Notificacao notificacao = new Notificacao();
				notificacao.setDataCriacao(dataSolicitacao);
				notificacao.setLida(false);
				notificacao.setEntidade(entidade);
				notificacao.setSolicitacaoDocumento(solicitacaoDocumento);
				notificacao.setAssunto(assunto);
				notificacao.setMensagem(mensagem);
				notificacao.setExibir(true);
				notificacao.setSituacao("AG");
				notificacao.setAnalise(analise);

				notificacaoRepositorio.salvar(notificacao);
			}

		}

	}
}
