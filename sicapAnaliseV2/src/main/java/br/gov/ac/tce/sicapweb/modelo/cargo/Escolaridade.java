package br.gov.ac.tce.sicapweb.modelo.cargo;

public enum Escolaridade {
	NIVEL_FUNDAMENTAL(Integer.valueOf(1), "Nível Fundamental"), NIVEL_MEDIO(Integer.valueOf(2),
			"Nível Médio"), NIVEL_SUPERIOR(Integer.valueOf(3), "Nível Superior"), NIVEL_TECNICO(Integer.valueOf(4),
					"Nível Técnico"), NAO_APLICAVEL(Integer.valueOf(9), "Não Aplicável");

	private Integer id;
	private String descricao;

	private Escolaridade(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static Escolaridade parse(Integer id) {
		Escolaridade escolaridade = null;
		for (Escolaridade item : Escolaridade.values()) {
			if (item.getId() == id) {
				escolaridade = item;
				break;
			}
		}
		return escolaridade;
	}
}
