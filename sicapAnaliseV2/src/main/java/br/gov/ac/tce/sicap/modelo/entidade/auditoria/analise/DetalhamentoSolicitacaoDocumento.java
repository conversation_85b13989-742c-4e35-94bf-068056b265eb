package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(schema = "auditoria")
public class DetalhamentoSolicitacaoDocumento implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idSolicitacaoDocumento", nullable = false)
	private SolicitacaoDocumento solicitacaoDocumento;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoDocumento", nullable = false)
	private TipoDocumento tipoDocumento;
	
	@Column(length = 255, nullable = false)
	private String documento;
	@Column(length = 2, nullable = false)
	@Enumerated(EnumType.STRING)
	private SituacaoSolicitacaoDocumento situacao;
	private LocalDateTime dataEnvio;
	private Long idUsuarioCjur;
	@Column(length = 100)
	private String hashDocumento;
	@Column(nullable = false)
	private Boolean obrigatorio;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public SolicitacaoDocumento getSolicitacaoDocumento() {
		return solicitacaoDocumento;
	}

	public void setSolicitacaoDocumento(SolicitacaoDocumento solicitacaoDocumento) {
		this.solicitacaoDocumento = solicitacaoDocumento;
	}

	public TipoDocumento getTipoDocumento() {
		return tipoDocumento;
	}

	public void setTipoDocumento(TipoDocumento tipoDocumento) {
		this.tipoDocumento = tipoDocumento;
	}

	public String getDocumento() {
		return documento;
	}

	public void setDocumento(String documento) {
		this.documento = documento;
	}

	public SituacaoSolicitacaoDocumento getSituacao() {
		return situacao;
	}

	public void setSituacao(SituacaoSolicitacaoDocumento situacao) {
		this.situacao = situacao;
	}

	public LocalDateTime getDataEnvio() {
		return dataEnvio;
	}

	public void setDataEnvio(LocalDateTime dataEnvio) {
		this.dataEnvio = dataEnvio;
	}

	public Long getIdUsuarioCjur() {
		return idUsuarioCjur;
	}

	public void setIdUsuarioCjur(Long idUsuarioCjur) {
		this.idUsuarioCjur = idUsuarioCjur;
	}

	public String getHashDocumento() {
		return hashDocumento;
	}

	public void setHashDocumento(String hashDocumento) {
		this.hashDocumento = hashDocumento;
	}

	public Boolean getObrigatorio() {
		return obrigatorio;
	}

	public void setObrigatorio(Boolean obrigatorio) {
		this.obrigatorio = obrigatorio;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DetalhamentoSolicitacaoDocumento other = (DetalhamentoSolicitacaoDocumento) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}
	
	

//	@Override
//	public int hashCode() {
//		final int prime = 31;
//		int result = 1;
//		result = prime * result + ((dataEnvio == null) ? 0 : dataEnvio.hashCode());
//		result = prime * result + ((documento == null) ? 0 : documento.hashCode());
//		result = prime * result + ((hashDocumento == null) ? 0 : hashDocumento.hashCode());
//		result = prime * result + ((idUsuarioCjur == null) ? 0 : idUsuarioCjur.hashCode());
//		result = prime * result + ((obrigatorio == null) ? 0 : obrigatorio.hashCode());
//		result = prime * result + ((situacao == null) ? 0 : situacao.hashCode());
//		result = prime * result + ((solicitacaoDocumento == null) ? 0 : solicitacaoDocumento.hashCode());
//		result = prime * result + ((tipoDocumento == null) ? 0 : tipoDocumento.hashCode());
//		return result;
//	}
//
//	@Override
//	public boolean equals(Object obj) {
//		if (this == obj)
//			return true;
//		if (obj == null)
//			return false;
//		if (getClass() != obj.getClass())
//			return false;
//		DetalhamentoSolicitacaoDocumento other = (DetalhamentoSolicitacaoDocumento) obj;
//		if (dataEnvio == null) {
//			if (other.dataEnvio != null)
//				return false;
//		} else if (!dataEnvio.equals(other.dataEnvio))
//			return false;
//		if (documento == null) {
//			if (other.documento != null)
//				return false;
//		} else if (!documento.equals(other.documento))
//			return false;
//		if (hashDocumento == null) {
//			if (other.hashDocumento != null)
//				return false;
//		} else if (!hashDocumento.equals(other.hashDocumento))
//			return false;
//		if (idUsuarioCjur == null) {
//			if (other.idUsuarioCjur != null)
//				return false;
//		} else if (!idUsuarioCjur.equals(other.idUsuarioCjur))
//			return false;
//		if (obrigatorio == null) {
//			if (other.obrigatorio != null)
//				return false;
//		} else if (!obrigatorio.equals(other.obrigatorio))
//			return false;
//		if (situacao != other.situacao)
//			return false;
//		if (solicitacaoDocumento == null) {
//			if (other.solicitacaoDocumento != null)
//				return false;
//		} else if (!solicitacaoDocumento.equals(other.solicitacaoDocumento))
//			return false;
//		if (tipoDocumento == null) {
//			if (other.tipoDocumento != null)
//				return false;
//		} else if (!tipoDocumento.equals(other.tipoDocumento))
//			return false;
//		return true;
//	}

}
