package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.folha.ContraCheque;
import br.gov.ac.tce.sicapweb.modelo.pensionista.TipoDependencia;
import br.gov.ac.tce.sicapweb.modelo.pensionista.TipoPensao;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.repositorio.RemessaEventualRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.ContraChequeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.PensionistaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.TipoDependenciaRepositorio;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Named
@ViewScoped
public class PensionistaBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;

	@Inject
	private TipoDependencia tipoDependencia;
	@Inject
	private RemessaEventual remessaEventual;
	@Inject
	private FormatUtil formatUtil;

	@Inject
	private RemessaEventualRepositorio remessaEventualRepositorio;
	@Inject
	private TipoDependenciaRepositorio tipoDependenciaRepositorio;
	@Inject
	private PensionistaRepositorio pensionistaRepositorio;
	@Inject
	private ContraChequeRepositorio contraChequeRepositorio;

	@Inject
	private Beneficiario beneficiarioPensionista;
	@Inject
	private ContraCheque contraCheque;

	private Collection<RemessaEventual> listaRemessaEventual;
	private Collection<TipoDependencia> listaTipoDependencia;

	private Collection<Beneficiario> listaPensionista;
	private Collection<ContraCheque> listaContraCheque;

	private Integer tipoPensao;
	private Integer matriculaPensionista;
	private String nomePensionista;
	private String cpfPensionista;
	private String nomeServidor;
	private String cpfServidor;

	private int currentLevel;

	@PostConstruct
	public void init() {
		this.currentLevel = 1;
		if (this.loginBean
				.existeEntidadeSelecionada("/dados/entidade/pensionista/pensionistas.xhtml?faces-redirect=true")) {
			try {
				this.tipoPensao = 0;
				this.tipoDependencia.setId(0);
				this.matriculaPensionista = null;
				this.nomePensionista = "";
				this.cpfPensionista = "";
				this.nomeServidor = "";
				this.cpfServidor = "";

				this.listaRemessaEventual = this.remessaEventualRepositorio.listarPorEntidadeTipoSituacao(
						this.loginBean.getEntidade(), TypeArquivo.PENSIONISTA, SituacaoRemessa.PROCESSADA);
				this.listaTipoDependencia = this.tipoDependenciaRepositorio.listaTodos();
			} catch (RepositorioException e) {
				Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as remesas de servidor.");
			}
		}
	}

	public void pesquisar() {
		try {
			this.remessaEventual.setId(Long.valueOf(0));
			this.listaPensionista = this.pensionistaRepositorio.pesquisaPensionistas(this.loginBean.getEntidade(),
					this.remessaEventual, this.tipoPensao, this.tipoDependencia, this.matriculaPensionista,
					this.nomePensionista, this.cpfPensionista, this.nomeServidor, this.cpfServidor);

			this.beneficiarioPensionista = null;
			this.listaContraCheque = null;
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar nenhum servidor.");
		}
	}

	public void buscarDadosPensionista(Beneficiario pensionista) {
		try {
			this.beneficiarioPensionista = this.pensionistaRepositorio.pesquisaPensionista(this.loginBean.getEntidade(),
					pensionista.getMatricula());
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível detalhar os dados do pensionista.");
		}
	}

	public void buscarContraCheques(Beneficiario pensionista) {
		try {
			this.setListaContraCheque(
					this.contraChequeRepositorio.pesquisaPorBeneficiario(this.loginBean.getEntidade(), pensionista));
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível lista os contracheques.");
		}
	}

	public void detalharContraCheque(ContraCheque contraCheque) {
		try {
			this.setContraCheque(this.contraChequeRepositorio.pesquisaPorId(contraCheque));
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível detalhar o contracheque.");
		}
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public Collection<RemessaEventual> getListaRemessaEventual() {
		return listaRemessaEventual;
	}

	public Collection<TipoDependencia> getListaTipoDependencia() {
		return listaTipoDependencia;
	}

	public TipoDependencia getTipoDependencia() {
		return tipoDependencia;
	}

	public void setTipoDependencia(TipoDependencia tipoDependencia) {
		this.tipoDependencia = tipoDependencia;
	}

	public TipoPensao[] getListaTipoPensao() {
		return TipoPensao.values();
	}

	public Integer getTipoPensao() {
		return tipoPensao;
	}

	public void setTipoPensao(Integer tipoPensao) {
		this.tipoPensao = tipoPensao;
	}

	public Integer getMatriculaPensionista() {
		return matriculaPensionista;
	}

	public void setMatriculaPensionista(Integer matriculaPensionista) {
		this.matriculaPensionista = matriculaPensionista;
	}

	public String getNomePensionista() {
		return nomePensionista;
	}

	public void setNomePensionista(String nomePensionista) {
		this.nomePensionista = nomePensionista;
	}

	public String getCpfPensionista() {
		return cpfPensionista;
	}

	public void setCpfPensionista(String cpfPensionista) {
		this.cpfPensionista = cpfPensionista;
	}

	public String getNomeServidor() {
		return nomeServidor;
	}

	public void setNomeServidor(String nomeServidor) {
		this.nomeServidor = nomeServidor;
	}

	public String getCpfServidor() {
		return cpfServidor;
	}

	public void setCpfServidor(String cpfServidor) {
		this.cpfServidor = cpfServidor;
	}

	public Collection<Beneficiario> getListaPensionista() {
		return listaPensionista;
	}

	public void setListaPensionista(Collection<Beneficiario> listaPensionista) {
		this.listaPensionista = listaPensionista;
	}

	public Beneficiario getBeneficiarioPensionista() {
		return beneficiarioPensionista;
	}

	public void setBeneficiarioPensionista(Beneficiario beneficiarioPensionista) {
		this.beneficiarioPensionista = beneficiarioPensionista;
	}

	public Collection<ContraCheque> getListaContraCheque() {
		return listaContraCheque;
	}

	public void setListaContraCheque(Collection<ContraCheque> listaContraCheque) {
		this.listaContraCheque = listaContraCheque;
	}

	public ContraCheque getContraCheque() {
		return contraCheque;
	}

	public void setContraCheque(ContraCheque contraCheque) {
		this.contraCheque = contraCheque;
	}

}
