package br.gov.ac.tce.sicapweb.modelo.pensionista;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.servidor.VinculoFuncional;

@Entity
@NamedQueries({
		@NamedQuery(name = Pensao.buscarTodosPorEntidadeCJUR, query = "select p from Pensao p where p.entidade = :entidade"),
		@NamedQuery(name = Pensao.buscarTodosPorEntidadeCJURRegistroAtivo, query = "select p from Pensao p where p.entidade = :entidade and p.registroAtivo = true") })
public class Pensao implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarTodosPorEntidadeCJUR = "Pensao.buscarTodosPorEntidadeCJUR";
	public static final String buscarTodosPorEntidadeCJURRegistroAtivo = "Pensao.buscarTodosPorEntidadeCJURRegistroAtivo";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idBeneficiarioPensionista", nullable = false)
	private Beneficiario pensionista;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idBeneficiarioInstituidor", nullable = false)
	private Beneficiario servidor;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idVinculoFuncionalServidor", nullable = false)
	private VinculoFuncional vinculoFuncionalInstituidor;
	@Column(nullable = false)
	private LocalDate dataInicio;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoDependencia", nullable = false)
	private TipoDependencia tipoDependencia;
	@Column(nullable = false)
	private Integer tipoPensao;
	private LocalDate dataFim;
	@Column(nullable = false)
	private Boolean registroAtivo;
	private LocalDateTime dataInativacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessaEventual", nullable = false)
	private RemessaEventual remessaEventual;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Beneficiario getPensionista() {
		return pensionista;
	}

	public void setPensionista(Beneficiario pensionista) {
		this.pensionista = pensionista;
	}

	public Beneficiario getServidor() {
		return servidor;
	}

	public void setServidor(Beneficiario servidor) {
		this.servidor = servidor;
	}

	public LocalDate getDataInicio() {
		return dataInicio;
	}

	public void setDataInicio(LocalDate dataInicio) {
		this.dataInicio = dataInicio;
	}

	public TipoDependencia getTipoDependencia() {
		return tipoDependencia;
	}

	public void setTipoDependencia(TipoDependencia tipoDependencia) {
		this.tipoDependencia = tipoDependencia;
	}

	public TipoPensao getTipoPensao() {
		return TipoPensao.parse(this.tipoPensao);
	}

	public void setTipoPensao(TipoPensao tipoPensao) {
		this.tipoPensao = tipoPensao.getId();
	}

	public LocalDate getDataFim() {
		return dataFim;
	}

	public void setDataFim(LocalDate dataFim) {
		this.dataFim = dataFim;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public Boolean getRegistroAtivo() {
		return registroAtivo;
	}

	public void setRegistroAtivo(Boolean registroAtivo) {
		this.registroAtivo = registroAtivo;
	}

	public LocalDateTime getDataInativacao() {
		return dataInativacao;
	}

	public void setDataInativacao(LocalDateTime dataInativacao) {
		this.dataInativacao = dataInativacao;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public VinculoFuncional getVinculoFuncionalInstituidor() {
		return vinculoFuncionalInstituidor;
	}

	public void setVinculoFuncionalInstituidor(VinculoFuncional vinculoFuncionalInstituidor) {
		this.vinculoFuncionalInstituidor = vinculoFuncionalInstituidor;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dataFim == null) ? 0 : dataFim.hashCode());
		result = prime * result + ((dataInativacao == null) ? 0 : dataInativacao.hashCode());
		result = prime * result + ((dataInicio == null) ? 0 : dataInicio.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((pensionista == null) ? 0 : pensionista.hashCode());
		result = prime * result + ((registroAtivo == null) ? 0 : registroAtivo.hashCode());
		result = prime * result + ((remessaEventual == null) ? 0 : remessaEventual.hashCode());
		result = prime * result + ((servidor == null) ? 0 : servidor.hashCode());
		result = prime * result + ((tipoDependencia == null) ? 0 : tipoDependencia.hashCode());
		result = prime * result + ((tipoPensao == null) ? 0 : tipoPensao.hashCode());
		result = prime * result + ((vinculoFuncionalInstituidor == null) ? 0 : vinculoFuncionalInstituidor.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Pensao other = (Pensao) obj;
		if (dataFim == null) {
			if (other.dataFim != null)
				return false;
		} else if (!dataFim.equals(other.dataFim))
			return false;
		if (dataInativacao == null) {
			if (other.dataInativacao != null)
				return false;
		} else if (!dataInativacao.equals(other.dataInativacao))
			return false;
		if (dataInicio == null) {
			if (other.dataInicio != null)
				return false;
		} else if (!dataInicio.equals(other.dataInicio))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (pensionista == null) {
			if (other.pensionista != null)
				return false;
		} else if (!pensionista.equals(other.pensionista))
			return false;
		if (registroAtivo == null) {
			if (other.registroAtivo != null)
				return false;
		} else if (!registroAtivo.equals(other.registroAtivo))
			return false;
		if (remessaEventual == null) {
			if (other.remessaEventual != null)
				return false;
		} else if (!remessaEventual.equals(other.remessaEventual))
			return false;
		if (servidor == null) {
			if (other.servidor != null)
				return false;
		} else if (!servidor.equals(other.servidor))
			return false;
		if (tipoDependencia == null) {
			if (other.tipoDependencia != null)
				return false;
		} else if (!tipoDependencia.equals(other.tipoDependencia))
			return false;
		if (tipoPensao == null) {
			if (other.tipoPensao != null)
				return false;
		} else if (!tipoPensao.equals(other.tipoPensao))
			return false;
		if (vinculoFuncionalInstituidor == null) {
			if (other.vinculoFuncionalInstituidor != null)
				return false;
		} else if (!vinculoFuncionalInstituidor.equals(other.vinculoFuncionalInstituidor))
			return false;
		return true;
	}

}
