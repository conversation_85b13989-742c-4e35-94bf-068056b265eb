package br.gov.ac.tce.sicap.modelo.entidade.notificacao;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SqlResultSetMapping;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.Analise;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.SolicitacaoDocumento;
import br.gov.ac.tce.sicapanalise.auditoria.dto.NotificacaoDTO;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Entity
@SqlResultSetMapping(name = "NotificacaoDTOMapping", classes = {
		@ConstructorResult(targetClass = NotificacaoDTO.class, columns = {
				@ColumnResult(name = "id",type = Long.class),
				@ColumnResult(name = "idAnalise", type = Long.class),
				@ColumnResult(name = "idAcumulacao", type = Long.class),
				@ColumnResult(name = "idSolicitacaoDocumento", type = Long.class),
				@ColumnResult(name = "dataSolicitacao", type = LocalDateTime.class),
				@ColumnResult(name = "dataResposta", type = LocalDateTime.class),
				@ColumnResult(name = "dataPrazoResposta", type = LocalDateTime.class),
				@ColumnResult(name = "nomeEntidade", type = String.class),
				@ColumnResult(name = "mensagem", type = String.class),
				@ColumnResult(name = "assunto", type = String.class),
				@ColumnResult(name = "situacaoAtualBeneficiario", type = String.class),
		}) })
public class Notificacao implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(nullable = false, updatable = false)
	private LocalDateTime dataCriacao;
	@Column(nullable = false)
	private Boolean lida;
	@Column(columnDefinition = "text", nullable = false)
	private String mensagem;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(length = 2500, nullable = false)
	private String assunto;
	private Boolean exibir;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAnalise", nullable = false)
	private Analise analise;
	@Column(nullable = true, updatable = false)
	private LocalDateTime dataResposta;
	@Column(length = 2)
	private String situacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idSolicitacaoDocumento")
	private SolicitacaoDocumento solicitacaoDocumento;
	


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public LocalDateTime getDataCriacao() {
		return dataCriacao;
	}

	public void setDataCriacao(LocalDateTime dataCriacao) {
		this.dataCriacao = dataCriacao;
	}

	public LocalDateTime getDataResposta() {
		return dataResposta;
	}

	public void setDataResposta(LocalDateTime dataResposta) {
		this.dataResposta = dataResposta;
	}

	public Boolean getLida() {
		return lida;
	}

	public void setLida(Boolean lida) {
		this.lida = lida;
	}

	public String getMensagem() {
		return mensagem;
	}

	public void setMensagem(String mensagem) {
		this.mensagem = mensagem;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public String getAssunto() {
		return assunto;
	}

	public void setAssunto(String assunto) {
		this.assunto = assunto;
	}

	public Boolean getExibir() {
		return exibir;
	}

	public void setExibir(Boolean exibir) {
		this.exibir = exibir;
	}

	public Analise getAnalise() {
		return analise;
	}

	public void setAnalise(Analise analise) {
		this.analise = analise;
	}

	public String getSituacao() {
		return situacao;
	}

	public void setSituacao(String situacao) {
		this.situacao = situacao;
	}

	public SolicitacaoDocumento getSolicitacaoDocumento() {
		return solicitacaoDocumento;
	}

	public void setSolicitacaoDocumento(SolicitacaoDocumento solicitacaoDocumento) {
		this.solicitacaoDocumento = solicitacaoDocumento;
	}

	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((analise == null) ? 0 : analise.hashCode());
		result = prime * result + ((assunto == null) ? 0 : assunto.hashCode());
		result = prime * result + ((dataCriacao == null) ? 0 : dataCriacao.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((exibir == null) ? 0 : exibir.hashCode());
		result = prime * result + ((lida == null) ? 0 : lida.hashCode());
		result = prime * result + ((mensagem == null) ? 0 : mensagem.hashCode());
		result = prime * result + ((dataResposta == null) ? 0 : dataResposta.hashCode());
		result = prime * result + ((situacao == null) ? 0 : situacao.hashCode());
		result = prime * result + ((solicitacaoDocumento == null) ? 0 : solicitacaoDocumento.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Notificacao other = (Notificacao) obj;
		if (analise == null) {
			if (other.analise != null)
				return false;
		} else if (!analise.equals(other.analise))
			return false;
		if (assunto == null) {
			if (other.assunto != null)
				return false;
		} else if (!assunto.equals(other.assunto))
			return false;
		if (dataCriacao == null) {
			if (other.dataCriacao != null)
				return false;
		} else if (!dataCriacao.equals(other.dataCriacao))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (exibir == null) {
			if (other.exibir != null)
				return false;
		} else if (!exibir.equals(other.exibir))
			return false;
		if (lida == null) {
			if (other.lida != null)
				return false;
		} else if (!lida.equals(other.lida))
			return false;
		if (mensagem == null) {
			if (other.mensagem != null)
				return false;
		} else if (!mensagem.equals(other.mensagem))
			return false;
		if (dataResposta == null) {
			if (other.dataResposta != null)
				return false;
		} else if (!dataResposta.equals(other.dataResposta))
			return false;
		if (situacao == null) {
			if (other.situacao != null)
				return false;
		} else if (!situacao.equals(other.situacao))
			return false;
		if (solicitacaoDocumento == null) {
			if (other.solicitacaoDocumento != null)
				return false;
		} else if (!solicitacaoDocumento.equals(other.solicitacaoDocumento))
			return false;
		return true;
	}

}
