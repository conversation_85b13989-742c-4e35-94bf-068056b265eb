package br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;

@Entity
@Table(schema = "auditoria")
public class DistribuicaoAcumulacao implements Serializable {

	private static final long serialVersionUID = -6819213584795413743L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAcumulacao")
	@NotNull
	private Acumulacao acumulacao;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idUsuario")
	@NotNull
	private Usuario usuario;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idUsuarioAuditor")
	@NotNull
	private Usuario usuarioAuditor;
	
	@Column(nullable = false)
	private LocalDateTime dataEntrada;
	private LocalDateTime dataSaida;
	private Boolean ativo;
	
	@Column(length = 255)
	@NotNull
	private String despacho;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Acumulacao getAcumulacao() {
		return acumulacao;
	}
	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
	}
	
	public Boolean getAtivo() {
		return ativo;
	}
	public void setAtivo(Boolean ativo) {
		this.ativo = ativo;
	}
	public LocalDateTime getDataEntrada() {
		return dataEntrada;
	}
	public void setDataEntrada(LocalDateTime dataEntrada) {
		this.dataEntrada = dataEntrada;
	}
	public LocalDateTime getDataSaida() {
		return dataSaida;
	}
	public void setDataSaida(LocalDateTime dataSaida) {
		this.dataSaida = dataSaida;
	}
	public Usuario getUsuario() {
		return usuario;
	}
	public void setUsuario(Usuario usuario) {
		this.usuario = usuario;
	}
	public Usuario getUsuarioAuditor() {
		return usuarioAuditor;
	}
	public void setUsuarioAuditor(Usuario usuarioAuditor) {
		this.usuarioAuditor = usuarioAuditor;
	}
	public String getDespacho() {
		return despacho;
	}
	public void setDespacho(String despacho) {
		this.despacho = despacho;
	}
	
	
	
	
}
