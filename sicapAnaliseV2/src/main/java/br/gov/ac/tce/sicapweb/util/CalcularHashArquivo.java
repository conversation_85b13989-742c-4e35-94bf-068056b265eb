package br.gov.ac.tce.sicapweb.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class CalcularHashArquivo implements Serializable {

	private static final long serialVersionUID = 1L;

	private static final String ALGORITIMO = "SHA-1";

	public static String gerar(File arquivo) throws NoSuchAlgorithmException, FileNotFoundException {
		MessageDigest messageDigest = MessageDigest.getInstance(ALGORITIMO);
		InputStream inputStream = new FileInputStream(arquivo);
		byte[] buffer = new byte[8192];
		int read = 0;
		String output = null;
		try {
			while ((read = inputStream.read(buffer)) > 0) {
				messageDigest.update(buffer, 0, read);
			}
			byte[] sha1Sum = messageDigest.digest();
			BigInteger bigInteger = new BigInteger(1, sha1Sum);
			output = bigInteger.toString(16);
		} catch (IOException ioException) {
			throw new RuntimeException("Não Foi possivel processar o arquivo.", ioException);
		} finally {
			try {
				inputStream.close();
			} catch (IOException exception) {
				throw new RuntimeException("Não foi possivel fechar o arquivo.", exception);
			}
		}
		return output;
	}
}
