package br.gov.ac.tce.sicapanalise.auditoria.processo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ResponsavelSimplesPrestacaoContainer implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7514596827996902576L;
	
	private List<ResponsavelSimplesPrestacao> responsaveis = new ArrayList<>();

	public ResponsavelSimplesPrestacaoContainer() {}
	
	public ResponsavelSimplesPrestacaoContainer(List<ResponsavelSimplesPrestacao> responsaveis) {
		super();
		this.responsaveis = responsaveis;
	}

	public List<ResponsavelSimplesPrestacao> getResponsaveis() {
		return responsaveis;
	}

	public void setResponsaveis(List<ResponsavelSimplesPrestacao> responsaveis) {
		this.responsaveis = responsaveis;
	}
	
	

}
