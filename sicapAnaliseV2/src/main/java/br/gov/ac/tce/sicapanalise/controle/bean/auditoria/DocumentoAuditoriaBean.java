package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;

import org.primefaces.event.NodeSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import br.gov.ac.tce.sicapanalise.auditoria.business.AnaliseBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.AcumulacaoAnaliseDTO;
import br.gov.ac.tce.sicapanalise.auditoria.dto.DocumentoAuditoriaDTO;

@Named
@ViewScoped
public class DocumentoAuditoriaBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3451718676303697146L;

	@Inject
	private AnaliseBusiness analiseBusiness;

	private Collection<DocumentoAuditoriaDTO> listaDocumentosAnalise;

//	private AnaliseAcumulacao analiseAcumulacao;

	private DocumentoAuditoriaDTO documentoSelecionado;

	private TreeNode noSelecionado;

	private TreeNode root;
	
	@Inject
	private HttpServletRequest request;

	@PostConstruct
	public void inicializado() {
		Map<String, Object> sessionMap = FacesContext.getCurrentInstance().getExternalContext().getSessionMap();
		AcumulacaoAnaliseDTO acumulacaoAnalise = (AcumulacaoAnaliseDTO) sessionMap.get("acumulacaoAnalise");
		sessionMap.remove("analiseAcumulacao");
		
		if (acumulacaoAnalise != null && acumulacaoAnalise.getAnaliseId() != null) {
			listaDocumentosAnalise = analiseBusiness.retornaAnaliseDocumentos(acumulacaoAnalise.getAnaliseId());
			root = createDocuments();
		}
	}

	private TreeNode createDocuments() {
		Long idNotificacao = null;
		TreeNode no = null;
		TreeNode root = new DefaultTreeNode("Raiz", null);
		String headerTexto;
		DocumentoAuditoriaDTO noHeader = null;

		for (DocumentoAuditoriaDTO da : listaDocumentosAnalise) {
			if (!da.getIdNotificacao().equals(idNotificacao)) {
				idNotificacao = da.getIdNotificacao();

				if (!idNotificacao.equals(0L)) {
					headerTexto = "Notificação Nº ".concat(idNotificacao.toString());
				} else {
					headerTexto = "Relatórios de Auditoria";
				}
				noHeader = new DocumentoAuditoriaDTO();
				noHeader.setNomeTipoDocumento(headerTexto);

				no = new DefaultTreeNode(noHeader, root);
				no.setExpanded(true);
			}
			
			if(this.documentoSelecionado == null)
				this.documentoSelecionado = da;

			no.getChildren().add(new DefaultTreeNode("document",da, no));
		}
		
		return root;
	}

	public TreeNode getRoot() {
		return root;
	}

	public void setRoot(TreeNode root) {
		this.root = root;
	}


	public Collection<DocumentoAuditoriaDTO> getListaDocumentosAnalise() {
		return listaDocumentosAnalise;
	}

	public void setListaDocumentosAnalise(Collection<DocumentoAuditoriaDTO> listaDocumentosAnalise) {
		this.listaDocumentosAnalise = listaDocumentosAnalise;
	}

//	public AnaliseAcumulacao getAnaliseAcumulacao() {
//		return analiseAcumulacao;
//	}
//
//	public void setAnaliseAcumulacao(AnaliseAcumulacao analiseAcumulacao) {
//		this.documentoSelecionado = null;
//		this.analiseAcumulacao = analiseAcumulacao;
//
//	}

	public TreeNode getNoSelecionado() {
		return noSelecionado;
	}

	public void setNoSelecionado(TreeNode noSelecionado) {
		this.noSelecionado = noSelecionado;
	}

	public DocumentoAuditoriaDTO getDocumentoSelecionado() {
		return documentoSelecionado;
	}

	public void setDocumentoSelecionado(DocumentoAuditoriaDTO documentoSelecionado) {
		this.documentoSelecionado = documentoSelecionado;
	}


	
	public void onNodeSelect(NodeSelectEvent evento) {
		this.documentoSelecionado = (DocumentoAuditoriaDTO) evento.getTreeNode().getData();
	}

	public String enderecoDocumento() {
		if(documentoSelecionado != null && documentoSelecionado.getCaminhoDocumento() != null && !documentoSelecionado.getCaminhoDocumento().isEmpty()) {
			return request.getContextPath() +"/file/" + documentoSelecionado.getCaminhoDocumento() + "/" + documentoSelecionado.getNomeDocumento();
		}
			 
		return null;
	}
}
