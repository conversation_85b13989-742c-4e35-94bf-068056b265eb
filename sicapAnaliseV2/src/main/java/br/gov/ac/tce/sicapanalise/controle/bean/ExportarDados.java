package br.gov.ac.tce.sicapanalise.controle.bean;

import java.io.ByteArrayOutputStream;
import java.io.Serializable;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.http.HttpServletResponse;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.repositorio.EntidadeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import br.gov.ac.tce.sicapweb.repositorio.RemessaPeriodicaRepositorio;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.export.ooxml.JRXlsxExporter;

@Named
@ViewScoped
public class ExportarDados implements Serializable {

	private static final long serialVersionUID = 1L;

//	@Inject
//	private LoginBean loginBean;

//	@Inject
//	private Entidade entidade;
	@Inject
	private EntidadeRepositorio entidadeRepositorio;
	@Inject
	private RemessaPeriodicaRepositorio remessaPeriodicaRepositorio;
	@Inject
	private RelatorioConexao relatorioConexao;

	private Collection<Integer> listaExercicios;
	private Collection<Entidade> listaEntidade;
	private List<Entidade> entidadesSelecionadas;

	private String[] anosSelecionados;

	@PostConstruct
	public void init() {
		try {
			this.entidadesSelecionadas = new ArrayList<Entidade>();
			this.listaExercicios = this.remessaPeriodicaRepositorio.listaExercicio();
			this.listaEntidade = this.entidadeRepositorio.listaTodasRepositorioSicap();
		} catch (RepositorioException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public void teste() {
		List<String> anos = new LinkedList<>();
		List<String> idEntidades = new LinkedList<>();
		String parametroAno = null;
		String parametroEntidades = null;

		for (String a : this.anosSelecionados) {
			anos.add(a);
		}

		if (anos.size() != 0) {
			parametroAno = String.join(", ", anos);
		}

		for (Entidade e : this.entidadesSelecionadas) {
			idEntidades.add(e.getIdEntidadeCjur().toString());
		}

		if (idEntidades.size() != 0) {
			parametroEntidades = String.join(", ", idEntidades);
		}

		System.out.println("ano: " + parametroAno);
		System.out.println("idEntidade: " + parametroEntidades);
	}

	public void exportarDadosAnualEntidade() {

		List<String> anos = new LinkedList<>();
		List<String> idEntidades = new LinkedList<>();
		String parametroAno = null;
		String parametroEntidades = null;

		for (String a : this.anosSelecionados) {
			anos.add(a);
		}

		if (anos.size() != 0) {
			parametroAno = String.join(", ", anos);
		}

		for (Entidade e : this.entidadesSelecionadas) {
			idEntidades.add(e.getIdEntidadeCjur().toString());
		}

		if (idEntidades.size() != 0) {
			parametroEntidades = String.join(", ", idEntidades);
		}

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("idEntidades", parametroEntidades);
		parameters.put("ano", parametroAno);

		JasperPrint jasperPrint;
		try(Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext().getRealPath("relatorios/") + "/";
			String pathRelatorio = pathRelatorioDir + "exportarDados/" + "exportar_dados_anual_entidade.jasper";
			
			HttpServletResponse httpServletResponse = (HttpServletResponse) FacesContext.getCurrentInstance().getExternalContext().getResponse();
			httpServletResponse.setContentType("application/vnd.ms-excel");
			httpServletResponse.addHeader("Content-disposition", "attachment; filename=" + "dadosEntidade.xlsx");
			
			jasperPrint = JasperFillManager.fillReport(pathRelatorio, parameters, conexaoJasper);

			JRXlsxExporter xlsxExporter = new JRXlsxExporter();
			ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

			//xlsxExporter.setExporterInput(new SimpleExporterInput(jasperPrint));
			//xlsxExporter.setExporterOutput(new SimpleOutputStreamExporterOutput(byteArrayOutputStream));
			//xlsxExporter.setExporterOutput(new SimpleOutputStreamExporterOutput("dadosEntidade.xlsx"));
			
			xlsxExporter.setParameter(JRExporterParameter.JASPER_PRINT, jasperPrint);
			xlsxExporter.setParameter(JRExporterParameter.OUTPUT_FILE_NAME, "");
			xlsxExporter.setParameter(JRExporterParameter.OUTPUT_STREAM, byteArrayOutputStream);

			xlsxExporter.exportReport();			
			
			httpServletResponse.getOutputStream().write(byteArrayOutputStream.toByteArray());
			httpServletResponse.getOutputStream().flush();
			httpServletResponse.getOutputStream().close();
			httpServletResponse.flushBuffer();

			FacesContext.getCurrentInstance().responseComplete();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} 
	}

	public String[] getAnosSelecionados() {
		return anosSelecionados;
	}

	public void setAnosSelecionados(String[] anosSelecionados) {
		this.anosSelecionados = anosSelecionados;
	}

	public Collection<Integer> getListaExercicios() {
		return listaExercicios;
	}

	public void setListaExercicios(Collection<Integer> listaExercicios) {
		this.listaExercicios = listaExercicios;
	}

	public Collection<Entidade> getListaEntidade() {
		return listaEntidade;
	}

	public void setListaEntidade(Collection<Entidade> listaEntidade) {
		this.listaEntidade = listaEntidade;
	}

	public List<Entidade> getEntidadesSelecionadas() {
		return entidadesSelecionadas;
	}

	public void setEntidadesSelecionadas(List<Entidade> entidadesSelecionadas) {
		this.entidadesSelecionadas = entidadesSelecionadas;
	}

}
