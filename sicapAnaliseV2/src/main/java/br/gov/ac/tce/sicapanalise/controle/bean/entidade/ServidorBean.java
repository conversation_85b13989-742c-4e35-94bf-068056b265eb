package br.gov.ac.tce.sicapanalise.controle.bean.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.extensions.component.masterdetail.SelectLevelEvent;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.controle.conversor.FormatUtil;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.folha.ContraCheque;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.modelo.servidor.HistoricoFuncional;
import br.gov.ac.tce.sicapweb.modelo.servidor.RegimePrevidenciario;
import br.gov.ac.tce.sicapweb.modelo.servidor.TipoServidor;
import br.gov.ac.tce.sicapweb.modelo.servidor.TipoVinculo;
import br.gov.ac.tce.sicapweb.repositorio.RemessaEventualRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.CargoRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.ContraChequeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.HistoricoFuncionalRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.PensionistaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.ServidorRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.TipoVinculoRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.UnidadeLotacaoRepositorio;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Named
@ViewScoped
public class ServidorBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private Cargo cargo;
	@Inject
	private TipoVinculo tipoVinculo;
	@Inject
	private RemessaEventual remessaEventual;
	@Inject
	private FormatUtil formatUtil;

	@Inject
	private RemessaEventualRepositorio remessaEventualRepositorio;
	@Inject
	private TipoVinculoRepositorio tipoVinculoRepositorio;
	@Inject
	private CargoRepositorio cargoRepositorio;
	@Inject
	private ServidorRepositorio servidorRepositorio;
	@Inject
	private HistoricoFuncionalRepositorio historicoFuncionalRepositorio;
	@Inject
	private PensionistaRepositorio pensionistaRepositorio;
	@Inject
	private ContraChequeRepositorio contraChequeRepositorio;
	@Inject
	private UnidadeLotacaoRepositorio unidadeLotacaoRepositorio;

	@Inject
	private Beneficiario beneficiarioServidor;
	@Inject
	private ContraCheque contraCheque;

	private Collection<RemessaEventual> listaRemessaEventual;
	private Collection<TipoVinculo> listaTipoVinculo;
	private Collection<Cargo> listaCargo;
	private Collection<Beneficiario> listaServidor;
	private Collection<HistoricoFuncional> listaHistoricoFuncional;
	private Collection<Beneficiario> listaPensionista;
	private Collection<ContraCheque> listaContraCheque;
	private Collection<ContraCheque> listaLotacao;

	private Integer tipoServidor;
	private Integer regimePrevidenciario;
	private Integer matricula;
	private String nome;
	private String cpf;

	private int currentLevel;

	@PostConstruct
	public void init() {
		this.currentLevel = 1;
		if (this.loginBean.existeEntidadeSelecionada("/dados/entidade/servidor/servidores.xhtml?faces-redirect=true")) {
			try {
				this.tipoServidor = 0;
				this.tipoVinculo.setId(0);
				this.regimePrevidenciario = 0;
				this.cargo.setId(Long.valueOf(0));
				this.matricula = null;
				this.nome = "";
				this.cpf = "";

				this.listaRemessaEventual = this.remessaEventualRepositorio.listarPorEntidadeTipoSituacao(
						this.loginBean.getEntidade(), TypeArquivo.SERVIDOR, SituacaoRemessa.PROCESSADA);
				this.listaTipoVinculo = this.tipoVinculoRepositorio.listaTodos();
				this.listaCargo = this.cargoRepositorio.listaTodosPorEntidade(this.loginBean.getEntidade());
			} catch (RepositorioException e) {
				Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar as remesas de servidor.");
			}
		}
	}

	public void pesquisar() {
		try {
			this.remessaEventual.setId(Long.valueOf(0));
			this.listaServidor = this.servidorRepositorio.pesquisaServidores(this.loginBean.getEntidade(),
					this.remessaEventual, this.tipoServidor, this.tipoVinculo, this.regimePrevidenciario, this.cargo,
					this.matricula, this.nome, this.cpf);
			this.beneficiarioServidor = null;
			this.listaHistoricoFuncional = null;
			this.listaPensionista = null;
			this.listaContraCheque = null;
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar nenhum servidor.");
		}
	}

	public void buscarDadosServidor(Beneficiario servidor) {
		try {
			this.beneficiarioServidor = this.servidorRepositorio.pesquisaServidor(this.loginBean.getEntidade(),
					servidor.getMatricula());
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível detalhar os dados do servidor.");
		}
	}

	public void buscarHistoriocoFuncionalServidor(Beneficiario servidor) {
		try {
			this.listaHistoricoFuncional = this.historicoFuncionalRepositorio
					.pesquisaHistoricoPorBeneficiario(this.loginBean.getEntidade(), servidor);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível lista o histórico funcional.");
		}
	}

	public void buscarPensionistas(Beneficiario servidor) {
		try {
			this.listaPensionista = this.pensionistaRepositorio
					.pesquisarPensionistaPorServidor(this.loginBean.getEntidade(), servidor);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível lista o dependentes.");
		}
	}

	public void buscarLotacaoServidor(Beneficiario servidor) {
		try {
			this.listaLotacao = this.unidadeLotacaoRepositorio.listarLotacaoPorServidor(servidor);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível lista a lotação do servidor.");
		}
	}

	public void buscarContraCheques(Beneficiario servidor) {
		try {
			this.listaContraCheque = this.contraChequeRepositorio.pesquisaPorBeneficiario(this.loginBean.getEntidade(),
					servidor);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível lista os contracheques.");
		}
	}

	public void detalharContraCheque(ContraCheque contraCheque) {
		try {
			this.contraCheque = this.contraChequeRepositorio.pesquisaPorId(contraCheque);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível detalhar o contracheque.");
		}
	}

	public int handleNavigation(SelectLevelEvent selectLevelEvent) {
		return selectLevelEvent.getNewLevel();
	}

	public TipoServidor[] getListaTipoServidor() {
		return TipoServidor.values();
	}

	public RegimePrevidenciario[] getListaRegimePrevidenciario() {
		return RegimePrevidenciario.values();
	}

	public Collection<RemessaEventual> getListaRemessaEventual() {
		return listaRemessaEventual;
	}

	public FormatUtil getFormatUtil() {
		return formatUtil;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	public Integer getTipoServidor() {
		return tipoServidor;
	}

	public void setTipoServidor(Integer tipoServidor) {
		this.tipoServidor = tipoServidor;
	}

	public Integer getRegimePrevidenciario() {
		return regimePrevidenciario;
	}

	public void setRegimePrevidenciario(Integer regimePrevidenciario) {
		this.regimePrevidenciario = regimePrevidenciario;
	}

	public Integer getMatricula() {
		return matricula;
	}

	public void setMatricula(Integer matricula) {
		this.matricula = matricula;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public String getCpf() {
		return cpf;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public Collection<Cargo> getListaCargo() {
		return listaCargo;
	}

	public Collection<TipoVinculo> getListaTipoVinculo() {
		return listaTipoVinculo;
	}

	public Collection<Beneficiario> getListaServidor() {
		return listaServidor;
	}

	public Cargo getCargo() {
		return cargo;
	}

	public void setCargo(Cargo cargo) {
		this.cargo = cargo;
	}

	public TipoVinculo getTipoVinculo() {
		return tipoVinculo;
	}

	public void setTipoVinculo(TipoVinculo tipoVinculo) {
		this.tipoVinculo = tipoVinculo;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Beneficiario getBeneficiarioServidor() {
		return beneficiarioServidor;
	}

	public void setBeneficiarioServidor(Beneficiario beneficiarioServidor) {
		this.beneficiarioServidor = beneficiarioServidor;
	}

	public Collection<HistoricoFuncional> getListaHistoricoFuncional() {
		return listaHistoricoFuncional;
	}

	public void setListaHistoricoFuncional(Collection<HistoricoFuncional> listaHistoricoFuncional) {
		this.listaHistoricoFuncional = listaHistoricoFuncional;
	}

	public Collection<Beneficiario> getListaPensionista() {
		return listaPensionista;
	}

	public void setListaPensionista(Collection<Beneficiario> listaPensionista) {
		this.listaPensionista = listaPensionista;
	}

	public Collection<ContraCheque> getListaContraCheque() {
		return listaContraCheque;
	}

	public ContraCheque getContraCheque() {
		return contraCheque;
	}

	public void setContraCheque(ContraCheque contraCheque) {
		this.contraCheque = contraCheque;
	}

	public Collection<ContraCheque> getListaLotacao() {
		return listaLotacao;
	}

	public void setListaLotacao(Collection<ContraCheque> listaLotacao) {
		this.listaLotacao = listaLotacao;
	}

}
