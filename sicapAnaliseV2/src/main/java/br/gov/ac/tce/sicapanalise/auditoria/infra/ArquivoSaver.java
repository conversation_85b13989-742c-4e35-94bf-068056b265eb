package br.gov.ac.tce.sicapanalise.auditoria.infra;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.nio.channels.WritableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.apache.commons.lang3.StringUtils;
import org.primefaces.model.UploadedFile;

public class ArquivoSaver {

	public static final String SERVER_PATH = "/opt/repositorio/sicap/analise/acumulacao";

	public static final String PROCESSO_PATH = "/opt/repositorio/processo/sicap";
//	public static final String PROCESSO_PATH = "/repositorio/prestacao/documentos_pca/sicap";

	public String salvar(String arqDir, String arqNom, /*Part*/UploadedFile arquivo) {
		File file = null;
		FileOutputStream fos = null;

		// relatorios/idUsuario/arquivo
		String caminhoRelativo = SERVER_PATH + "/" + arqDir + "/" + arqNom;
		

		try {
			
			file = new File(caminhoRelativo);
			if (!file.exists()) {
				criaDiretorio(SERVER_PATH + "/" + arqDir); // cria o diretorio
				// opt/sicap/analise/acumulacao/#{caminho}/#{idUsuario}
				
				fos = new FileOutputStream(caminhoRelativo);
				fos.write(arquivo.getContents());
//				arquivo.write(caminhoRelativo);
			}

		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		return caminhoRelativo;
	}
	

	public String salvarDocumentosPCA(String dirOrigem, String arqOrigem) {
		File origem = null;
		FileOutputStream destino = null;
		String arqDestino = "";
		try {
			origem = new File(SERVER_PATH + "/" + dirOrigem + "/" + arqOrigem);
			arqDestino = arquivoNomeSemAcentoEspaco(arqOrigem);

			criaDiretorio(PROCESSO_PATH + "/" + dirOrigem);

			destino = new FileOutputStream(PROCESSO_PATH + "/" + dirOrigem + "/" + arqDestino);

			transferir(origem.toPath(), destino);

		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		return arqDestino;
	}

	public static void transferir(Path fonte, OutputStream outputStream) {
		try {
			FileInputStream input = new FileInputStream(fonte.toFile());
			try (ReadableByteChannel inputChannel = Channels.newChannel(input);
					WritableByteChannel outputChannel = Channels.newChannel(outputStream)) {

				ByteBuffer buffer = ByteBuffer.allocateDirect(1024 * 10);

				while (inputChannel.read(buffer) != -1) {
					buffer.flip();
					outputChannel.write(buffer);
					buffer.clear();

				}
			} catch (IOException e) {
				e.printStackTrace();
				throw new RuntimeException(e);
			}
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
	}

	private void criaDiretorio(String diretorio) throws IOException {
		Files.createDirectories(Paths.get(diretorio));
	}
	
	private String arquivoNomeSemAcentoEspaco(String arquivoNome) {
		return StringUtils.stripAccents(arquivoNome).replace(" ", "_");
	}
}
