package br.gov.ac.tce.sicapanalise.auditoria.business;

import java.time.LocalDateTime;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.inject.Inject;
import javax.validation.Valid;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DistribuicaoAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.PerfilGrupo;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.DistribuicaoRepositorio;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.GrupoRepositorio;

@Stateless
public class DistribuicaoBusiness {

	@Inject
	private DistribuicaoRepositorio distribuicaoRepositorio;

	@Inject
	private GrupoRepositorio grupoRepositorio;

	public Collection<DistribuicaoAcumulacao> listaDistribuicaoAcumulacoes(Usuario usuario) {
		return distribuicaoRepositorio.listaDistribuicaoAcumulacao(usuario);
	}

	public Collection<Usuario> listaUsuariosAuditoria() {
		return grupoRepositorio.listaUsuariosPorPerfil(PerfilGrupo.AUDITORIA);

	}

	public void iniciarDistribuicao(@Valid Collection<DistribuicaoAcumulacao> listaDistribuicaoAcumulacao) {


			listaDistribuicaoAcumulacao.forEach(da -> {
				boolean insere = false;

				// Verifica se há distribuição ativa para a Acumulação
				DistribuicaoAcumulacao distribuicaoAtiva = distribuicaoRepositorio
						.retornaDistribuicaoAcumulacaoAtiva(da.getAcumulacao());

				if (distribuicaoAtiva == null) {
					insere = true;
				} else if (!da.getUsuarioAuditor().equals(distribuicaoAtiva.getUsuarioAuditor())) {
					insere = true;

					// Desativa a antiga
					distribuicaoAtiva.setAtivo(false);
					distribuicaoAtiva.setDataSaida(LocalDateTime.now());
					distribuicaoRepositorio.salvar(distribuicaoAtiva);
				}

				if (insere) {
					da.setAtivo(true);
					da.setDataEntrada(LocalDateTime.now());
					distribuicaoRepositorio.salvar(da);

				}

			});

		
	}

}
