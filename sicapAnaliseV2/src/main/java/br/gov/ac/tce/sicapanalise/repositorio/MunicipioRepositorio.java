package br.gov.ac.tce.sicapanalise.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Municipio;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Uf;

@Stateless
public class MunicipioRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<Municipio> buscarPorUf(Uf uf) throws RepositorioException {
		Collection<Municipio> listaMunicipios = null;
		try {
			UaiCriteria<Municipio> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Municipio.class);
			uaiCriteria.andEquals("uf", uf);
			uaiCriteria.orderByAsc("nome");

			listaMunicipios = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro ao UfRepositoriobuscarTodos.", e.getCause());
		}
		return listaMunicipios;
	}
}
