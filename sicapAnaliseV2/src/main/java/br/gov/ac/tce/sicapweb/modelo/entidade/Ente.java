package br.gov.ac.tce.sicapweb.modelo.entidade;

public enum Ente {
	ACRELANDIA("ACRELÃNDIA", 1),
	ASSISBRASIL("ASSIS BRASIL", 2),
	BRASILEIA("BRASILÉIA", 3),
	BUJARI("BUJARI", 4),
	CAPIXABA("CAPIXABA", 5),
	CRUZEIRODOSUL("CRUZEIRO DO SUL", 6),
	EPITACIOLANDIA("EPITACIOLÃNDIA", 7),
	FEIJO("FEIJÓ", 8),
	JORDAO("JORDÃO", 9),
	MANCIOLIMA("MÃNCIO LIMA", 10),
	MANOELURBANO("MANOEL URBANO", 11),
	MARECHALTHAUMATURGO("MARECHAL THAUMATURGO", 12),
	PLACIDODECASTRO("P<PERSON><PERSON>CIDO DE CASTRO", 13),
	<PERSON>OR<PERSON><PERSON><PERSON>("PORTO ACRE", 14),
	POR<PERSON>WALTER("PORTO WALTER", 15),
	<PERSON><PERSON><PERSON><PERSON><PERSON>("R<PERSON> <PERSON>ANCO", 16),
	RODRIGUESALVES("RODRIGUES ALVES", 17),
	SANTAROSADOPURUS("SANTA ROSA DO PURUS", 18),
	SENAMADUREIRA("SENA MADUREIRA", 19),
	SENADORGUIOMARD("SENADOR GUIOMARD", 20),
	TARAUACA("TARAUACÁ", 21),
	XAPURI("XAPURI", 22),
	ESTADODOACRE("ESTADO DO ACRE", 23),
	TESTE("ENTE - AMBIENTE DE TESTES", 24);
	

	private String descricao;
	private Integer id;

	
	private Ente(String descricao, Integer id) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getDescricao() {
		return descricao;
	}
	
	public Integer getId() {
		return id;
	}
	
	public static Ente parse(Integer id) {
		Ente ente = null;

		for (Ente item : Ente.values()) {
			if (item.getId() == id) {
				ente = item;
				break;
			}
		}
		return ente;
	}
	
	public static Ente parse(String chaveEnum) {
		return Ente.valueOf(chaveEnum);
	}
}
