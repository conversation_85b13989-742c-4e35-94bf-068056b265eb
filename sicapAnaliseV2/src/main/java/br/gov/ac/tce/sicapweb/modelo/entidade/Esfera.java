package br.gov.ac.tce.sicapweb.modelo.entidade;

public enum Esfera {

	FEDERAL("FEDERAL", 1), ESTADUAL("ESTADUAL", 2), MUNICIPAL("MUNICIPAL", 3);

	private String nome;
	private Integer id;

	private Esfera(String nome, Integer id) {
		this.nome = nome;
		this.id = id;
	}

	public static Esfera parse(String chaveEnum) {
		return Esfera.valueOf(chaveEnum);
	}

	public static Esfera parse(Integer id) {
		Esfera esfera = null;

		for (Esfera item : Esfera.values()) {
			if (item.getId() == id) {
				esfera = item;
				break;
			}
		}
		return esfera;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}
}
