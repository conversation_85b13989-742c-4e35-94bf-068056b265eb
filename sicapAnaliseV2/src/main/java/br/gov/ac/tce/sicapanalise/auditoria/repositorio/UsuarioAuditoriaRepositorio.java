package br.gov.ac.tce.sicapanalise.auditoria.repositorio;

import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;

@Stateless
public class UsuarioAuditoriaRepositorio {

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager em;

	public Usuario retornaUsuarioPorId(Long id) {
		return em.find(Usuario.class, id);
	}

	public Usuario retornaServidorPorLogin(String login) {

		return em.createQuery(
				"SELECT DISTINCT u FROM Usuario u "
				+ "LEFT JOIN FETCH u.listaUsuarioGrupo ug "
				+ "LEFT JOIN FETCH ug.grupo g "
				+ "WHERE u.login = :pLogin "
				+ "AND u.ativo = true "	,
				Usuario.class).setParameter("pLogin", login).getSingleResult();

	}

	public Collection<Usuario> listaUsuarios() {
		Collection<Usuario> listaUsuarios = null;
		try {
			listaUsuarios = this.em
					.createQuery("SELECT u FROM Usuario u WHERE u.ativo = true ORDER BY u.nome", Usuario.class)
					.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println(e.getMessage());
		}
		return listaUsuarios;
	}

	public Collection<Usuario> listaUsuarioNoGrupo(Long grupoId) {
		Collection<Usuario> listaUsuarioNoGrupo = null;
		String jpql = "SELECT ug FROM UsuarioGrupo ug " + "JOIN FETCH ug.usuario u " + "JOIN FETCH ug.grupo g "
				+ "WHERE ug.grupo.id = :grupoId " + "AND ug.ativo =  true " + "AND u.ativo = true " + "ORDER BY u.nome";
		try {
			listaUsuarioNoGrupo = em.createQuery(jpql, Usuario.class).setParameter("grupoId", grupoId).getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaUsuarioNoGrupo;
	}

	public Collection<Usuario> listaUsuarioSemGrupo(Long grupoId) {
		Collection<Usuario> listaUsuarioSemGrupo = null;
		String sql = "SELECT s.* FROM auditoria.vw_Servidor s " + "LEFT OUTER JOIN auditoria.UsuarioGrupo ug "
				+ "ON ug.idUsuario = s.id " + "AND ug.idGrupo = :idGrupo " + "WHERE (ug.id IS NULL OR ug.ativo = 0) "
				+ "AND s.ativo = 1 " + "ORDER BY s.nome";
		try {
			listaUsuarioSemGrupo = em.createNativeQuery(sql, Usuario.class).setParameter("idGrupo", grupoId)
					.getResultList();
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		return listaUsuarioSemGrupo;
	}
}
