package br.gov.ac.tce.sicapanalise.controle.conversor;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;

public class CpfConverter implements Converter<Object> {

	@Override
	public Object getAsObject(FacesContext context, UIComponent component, String value) {
		/*
         * Ir� converter CPF formatado para um sem pontos e tra�o.
         * Ex.: 355.245.198-87 torna-se 35524519887.
         */
		String cpf = value;
        if (value!= null && !value.equals(""))
             cpf = value.replaceAll("\\.", "").replaceAll("\\-", "");

        return cpf;
	}

	@Override
	public String getAsString(FacesContext context, UIComponent component, Object value) {
		/*
         * Ir� converter CPF n�o formatado para um com pontos e tra�o.
         * Ex.: 35524519887 torna-se 355.245.198-87.
         */
         String cpf= (String)value;
         if (cpf != null && cpf.length() == 11)
              cpf = cpf.substring(0, 3) + "." + cpf.substring(3, 6) + "." + cpf.substring(6, 9) + "-" + cpf.substring(9, 11);

         return cpf;
	}

}
