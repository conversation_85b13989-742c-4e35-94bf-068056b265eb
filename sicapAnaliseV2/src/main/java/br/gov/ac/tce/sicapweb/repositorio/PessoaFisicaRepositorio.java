package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.PessoaFisica;

@Stateless
public class PessoaFisicaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<PessoaFisica> buscarTodosPorEntidade(Entidade entidade) throws RepositorioException {
		Collection<PessoaFisica> listaPessoaFisica = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<PessoaFisica> query = this.entityManager
						.createNamedQuery(PessoaFisica.buscarTodosPorEntidadeCJUR, PessoaFisica.class);
				query.setParameter("entidade", entidade);
				listaPessoaFisica = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscarTodosPorEntidade.", e.getCause());
		}
		return listaPessoaFisica;
	}

	public Collection<PessoaFisica> buscarTodosPorEntidadeRegistroAtivo(Entidade entidade) throws RepositorioException {
		Collection<PessoaFisica> listaPessoaFisica = null;
		try {
			if (entidade.getIdEntidadeCjur() != null) {
				TypedQuery<PessoaFisica> query = this.entityManager
						.createNamedQuery(PessoaFisica.buscarTodosPorEntidadeCJURRegistroAtivo, PessoaFisica.class);
				query.setParameter("entidade", entidade);
				listaPessoaFisica = query.getResultList();
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscarTodosPorEntidadeRegistroAtivo.", e.getCause());
		}
		return listaPessoaFisica;
	}

	public Object[] buscarPorCPF(String numeroCpf) {
		try {
			Query query = this.entityManager.createNativeQuery("select cadu.cpf, pf.nome  from "
					+ " dbo.CadastroUnico cadu, dbo.PessoaFisica pf, (select max(cadu.id) id, cadu.cpf from dbo.cadastrounico cadu group by cadu.cpf) ultimo "
					+ " where cadu.id = pf.idCadastroUnico" + "  and pf.registroAtivo = 1" + "  and cadu.cpf = ? "
					+ "  and cadu.id = ultimo.id ");

			query.setParameter(1, numeroCpf);

			return (Object[]) query.getSingleResult();
		} catch (Exception e) {
			return null;
		}
	}
}