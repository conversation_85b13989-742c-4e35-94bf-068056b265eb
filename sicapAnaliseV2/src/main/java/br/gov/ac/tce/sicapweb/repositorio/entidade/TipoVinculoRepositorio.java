package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.servidor.TipoVinculo;

@Stateless
public class TipoVinculoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<TipoVinculo> listaTodos() throws RepositorioException {
		Collection<TipoVinculo> listaTipoVinculo = null;
		try {
			UaiCriteria<TipoVinculo> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					TipoVinculo.class);
			listaTipoVinculo = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro tipoVinculoRepositorio.buscarTodos.", e.getCause());
		}
		return listaTipoVinculo;
	}
}
