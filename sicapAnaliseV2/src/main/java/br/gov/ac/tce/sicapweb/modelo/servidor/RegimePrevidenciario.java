package br.gov.ac.tce.sicapweb.modelo.servidor;

public enum RegimePrevidenciario {
	RGPS(Integer.valueOf(1), "Regime Geral de Previdência Social"), RPPS(Integer.valueOf(2),
			"Regime Próprio de Previdência Social");

	private Integer id;
	private String descricao;

	private RegimePrevidenciario(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static RegimePrevidenciario parse(Integer id) {
		RegimePrevidenciario regimePrevidenciario = null;
		for (RegimePrevidenciario item : RegimePrevidenciario.values()) {
			if (item.getId() == id) {
				regimePrevidenciario = item;
				break;
			}
		}
		return regimePrevidenciario;
	}

}
