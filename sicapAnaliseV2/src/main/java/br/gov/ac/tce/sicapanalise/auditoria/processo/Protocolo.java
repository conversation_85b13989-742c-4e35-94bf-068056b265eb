package br.gov.ac.tce.sicapanalise.auditoria.processo;

import java.io.Serializable;

import br.gov.ac.tce.sicapweb.modelo.entidade.Ente;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

public class Protocolo implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String cpfUsuarioSistema;
//	private ArquivoWSContainer arquivosPCA = new ArquivoWSContainer();
	private String nomeTipoProcesso;
	private String assunto;
	private String classe;
	private Integer exercicio;
	private String objeto;
	private ResponsavelSimplesPrestacaoContainer responsaveisContainer = new ResponsavelSimplesPrestacaoContainer();
	private ResponsavelSimplesPrestacao interessado = new ResponsavelSimplesPrestacao();
//	private String nomeEnte;
//	private Long idCjurEnte;
	private Ente ente;
//	private String nomeEntidade;
//	private Long idCjurEntidade;
	private Entidade entidade;
	private String tipoDocumento;
	private Integer numRemessa;
	public String getCpfUsuarioSistema() {
		return cpfUsuarioSistema;
	}
	public void setCpfUsuarioSistema(String cpfUsuarioSistema) {
		this.cpfUsuarioSistema = cpfUsuarioSistema;
	}
//	public ArquivoWSContainer getArquivosPCA() {
//		return arquivosPCA;
//	}
//	public void setArquivosPCA(ArquivoWSContainer arquivosPCA) {
//		this.arquivosPCA = arquivosPCA;
//	}
	public String getNomeTipoProcesso() {
		return nomeTipoProcesso;
	}
	public void setNomeTipoProcesso(String nomeTipoProcesso) {
		this.nomeTipoProcesso = nomeTipoProcesso;
	}
	public String getAssunto() {
		return assunto;
	}
	public void setAssunto(String assunto) {
		this.assunto = assunto;
	}
	public String getClasse() {
		return classe;
	}
	public void setClasse(String classe) {
		this.classe = classe;
	}
	public Integer getExercicio() {
		return exercicio;
	}
	public void setExercicio(Integer exercicio) {
		this.exercicio = exercicio;
	}
	public String getObjeto() {
		return objeto;
	}
	public void setObjeto(String objeto) {
		this.objeto = objeto;
	}
	public ResponsavelSimplesPrestacaoContainer getResponsaveisContainer() {
		return responsaveisContainer;
	}
	public void setResponsaveisContainer(ResponsavelSimplesPrestacaoContainer responsaveisContainer) {
		this.responsaveisContainer = responsaveisContainer;
	}
	public ResponsavelSimplesPrestacao getInteressado() {
		return interessado;
	}
	public void setInteressado(ResponsavelSimplesPrestacao interessado) {
		this.interessado = interessado;
	}
	
	public String getTipoDocumento() {
		return tipoDocumento;
	}
	public void setTipoDocumento(String tipoDocumento) {
		this.tipoDocumento = tipoDocumento;
	}
	public Integer getNumRemessa() {
		return numRemessa;
	}
	public void setNumRemessa(Integer numRemessa) {
		this.numRemessa = numRemessa;
	}
	public Ente getEnte() {
		return ente;
	}
	public void setEnte(Ente ente) {
		this.ente = ente;
	}
	public Entidade getEntidade() {
		return entidade;
	}
	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	
}
