package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;

import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;

@Named
@ViewScoped
public class DetalhamentoAcumulacaoBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1960071683805133860L;

	@Inject 
	private AcumulacaoBusiness acumulacaoBusiness;
	
	private Long idAcumulacao;
	
//	private Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao;
	
	private Acumulacao acumulacao;
	
	public Long getIdAcumulacao() {
		return idAcumulacao;
	}

	public void setIdAcumulacao(Long idAcumulacao) {
		this.idAcumulacao = idAcumulacao;
		this.acumulacao = acumulacaoBusiness.retornaAcumulacaoPorId(this.idAcumulacao);
		
	}

//	public Collection<DetalhamentoAcumulacao> getListaDetalhamentoAcumulacao() {
//		return listaDetalhamentoAcumulacao;
//	}
//
//	public void setListaDetalhamentoAcumulacao(Collection<DetalhamentoAcumulacao> listaDetalhamentoAcumulacao) {
//		this.listaDetalhamentoAcumulacao = listaDetalhamentoAcumulacao;
//	}

	public Acumulacao getAcumulacao() {
		return acumulacao;
	}

	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
//		retornaAcumulacao();
	}
	
//	private Acumulacao retornaAcumulacao() {
//		if (listaDetalhamentoAcumulacao == null || listaDetalhamentoAcumulacao.isEmpty()) 
//			return null;
//		return listaDetalhamentoAcumulacao.iterator().next().getAcumulacao();
//	}
	
}
