package br.gov.ac.tce.sicapanalise.auditoria.dto;

import java.io.Serializable;

public class CargoEntidadeDTO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Integer entidadeId;
	private String entidadeNome;
	private String cargoTipo;
	private String cargoReferencia;
	private String cargoNome;
	private Integer cargoQuantidadeServidores;
	private Integer entidadeQuantidadeServidores;
	
	public CargoEntidadeDTO() {}
	
	
	public CargoEntidadeDTO(Integer entidadeId, String entidadeNome, String cargoTipo, String cargoReferencia,
			String cargoNome, Integer cargoQuantidadeServidores, Integer entidadeQuantidadeServidores) {
		this.entidadeId = entidadeId;
		this.entidadeNome = entidadeNome;
		this.cargoTipo = cargoTipo;
		this.cargoReferencia = cargoReferencia;
		this.cargoNome = cargoNome;
		this.cargoQuantidadeServidores = cargoQuantidadeServidores;
		this.entidadeQuantidadeServidores = entidadeQuantidadeServidores;
	}


	public Integer getEntidadeId() {
		return entidadeId;
	}
	public void setEntidadeId(Integer entidadeId) {
		this.entidadeId = entidadeId;
	}
	public String getEntidadeNome() {
		return entidadeNome;
	}
	public void setEntidadeNome(String entidadeNome) {
		this.entidadeNome = entidadeNome;
	}
	public String getCargoTipo() {
		return cargoTipo;
	}
	public void setCargoTipo(String cargoTipo) {
		this.cargoTipo = cargoTipo;
	}
	public String getCargoReferencia() {
		return cargoReferencia;
	}
	public void setCargoReferencia(String cargoReferencia) {
		this.cargoReferencia = cargoReferencia;
	}
	public String getCargoNome() {
		return cargoNome;
	}
	public void setCargoNome(String cargoNome) {
		this.cargoNome = cargoNome;
	}
	public Integer getCargoQuantidadeServidores() {
		return cargoQuantidadeServidores;
	}
	public void setCargoQuantidadeServidores(Integer cargoQuantidadeServidores) {
		this.cargoQuantidadeServidores = cargoQuantidadeServidores;
	}
	public Integer getEntidadeQuantidadeServidores() {
		return entidadeQuantidadeServidores;
	}
	public void setEntidadeQuantidadeServidores(Integer entidadeQuantidadeServidores) {
		this.entidadeQuantidadeServidores = entidadeQuantidadeServidores;
	}
	
	

}
