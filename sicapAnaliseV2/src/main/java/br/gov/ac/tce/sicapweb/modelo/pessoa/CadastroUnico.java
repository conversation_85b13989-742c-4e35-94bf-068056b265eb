package br.gov.ac.tce.sicapweb.modelo.pessoa;

import java.io.Serializable;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQueries;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Entity
@Table(uniqueConstraints = {
		@UniqueConstraint(name = "cpfUnicoPorEntidade", columnNames = { "idEntidadeCjur", "cpf" }) })
@NamedQueries({
		@NamedQuery(name = CadastroUnico.buscarTodosPorEntidadeServidores, query = "select DISTINCT(c) from CadastroUnico c inner join c.listaPessoaFisica pf on pf.registroAtivo = true inner join c.listaBeneficiarios b inner join b.listaVinculosFuncionais v where c.entidade = :entidade and b.registroAtivo = true and b.tipoBeneficiario = 'S' and v.registroAtivo = true") })
@NamedNativeQueries({
		@NamedNativeQuery(name = CadastroUnico.buscarTodosPorEntidadePensionistas, query = "select c.* from CadastroUnico c "
				+ "inner join PessoaFisica pf on pf.idCadastroUnico = c.id and pf.idEntidadeCjur = :entidade and pf.registroAtivo = 1 "
				+ "inner join Beneficiario b on b.idCadastroUnico = c.id and b.tipoBeneficiario = 'P' and b.idEntidadeCjur = :entidade "
				+ "inner join Pensao p on p.idBeneficiarioPensionista = b.id and p.idEntidadeCjur = :entidade and p.registroAtivo = 1 "
				+ "where c.idEntidadeCjur = :entidade", resultClass = CadastroUnico.class) })
public class CadastroUnico implements Serializable {

	private static final long serialVersionUID = 1L;

	public static final String buscarTodosPorEntidadeServidores = "CadastroUnico.buscarTodosPorEntidadeServidores";
	public static final String buscarTodosPorEntidadePensionistas = "CadastroUnico.buscarTodosPorEntidadePensionistas";

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idEntidadeCjur", nullable = false)
	private Entidade entidade;
	@Column(length = 11, nullable = false)
	private String cpf;
	@OneToMany(mappedBy = "cadastroUnico", fetch = FetchType.LAZY)
	private Collection<PessoaFisica> listaPessoaFisica;
	@OneToMany(mappedBy = "cadastroUnico", fetch = FetchType.LAZY)
	private Collection<Beneficiario> listaBeneficiarios;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idRemessaEventual", nullable = false)
	private RemessaEventual remessaEventual;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Entidade getEntidade() {
		return entidade;
	}

	public void setEntidade(Entidade entidade) {
		this.entidade = entidade;
	}

	public String getCpf() {
		return cpf;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public Collection<PessoaFisica> getListaPessoaFisica() {
		return listaPessoaFisica;
	}

	public void setListaPessoaFisica(Collection<PessoaFisica> listaPessoaFisica) {
		this.listaPessoaFisica = listaPessoaFisica;
	}

	public Collection<Beneficiario> getListaBeneficiarios() {
		return listaBeneficiarios;
	}

	public void setListaBeneficiarios(Collection<Beneficiario> listaBeneficiarios) {
		this.listaBeneficiarios = listaBeneficiarios;
	}

	public RemessaEventual getRemessaEventual() {
		return remessaEventual;
	}

	public void setRemessaEventual(RemessaEventual remessaEventual) {
		this.remessaEventual = remessaEventual;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((cpf == null) ? 0 : cpf.hashCode());
		result = prime * result + ((entidade == null) ? 0 : entidade.hashCode());
		result = prime * result + ((remessaEventual == null) ? 0 : remessaEventual.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CadastroUnico other = (CadastroUnico) obj;
		if (cpf == null) {
			if (other.cpf != null)
				return false;
		} else if (!cpf.equals(other.cpf))
			return false;
		if (entidade == null) {
			if (other.entidade != null)
				return false;
		} else if (!entidade.equals(other.entidade))
			return false;
		if (remessaEventual == null) {
			if (other.remessaEventual != null)
				return false;
		} else if (!remessaEventual.equals(other.remessaEventual))
			return false;
		return true;
	}

}
