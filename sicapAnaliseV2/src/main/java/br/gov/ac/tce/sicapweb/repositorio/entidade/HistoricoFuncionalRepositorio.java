package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.servidor.HistoricoFuncional;

@Stateless
public class HistoricoFuncionalRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<HistoricoFuncional> pesquisaHistoricoPorBeneficiario(Entidade entidade, Beneficiario beneficiario)
			throws RepositorioException {
		Collection<HistoricoFuncional> listaHistoricoFuncional = null;
		try {
			UaiCriteria<HistoricoFuncional> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					HistoricoFuncional.class);

			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("beneficiario", beneficiario);
			uaiCriteria.orderByAsc("dataOcorrencia");

			listaHistoricoFuncional = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro historicoFuncionalRepositorio.pesquisaHistoricoPorBeneficiario",
					e.getCause());
		}

		return listaHistoricoFuncional;
	}
}
