package br.gov.ac.tce.sicap.modelo.entidade.auditoria;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;

import br.gov.ac.tce.sicap.util.UsuarioInterno;

@Entity
@Table(schema = "auditoria")
public class MatrizRisco implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(length = 255, nullable = false)
	private String nome;
	@Column(nullable = false)
	private LocalDateTime dataCadastro;
	@Column(nullable = false)
	private Long idUsuario;
	@OneToMany(mappedBy = "matrizRisco", fetch = FetchType.LAZY)
	private Collection<MetricasMatrizRisco> listaMetricas;

	@Transient
	private UsuarioInterno usuarioInterno;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public LocalDateTime getDataCadastro() {
		return dataCadastro;
	}

	public void setDataCadastro(LocalDateTime dataCadastro) {
		this.dataCadastro = dataCadastro;
	}

	public Long getIdUsuario() {
		return idUsuario;
	}

	public void setIdUsuario(Long idUsuario) {
		this.idUsuario = idUsuario;
	}

	public Collection<MetricasMatrizRisco> getListaMetricas() {
		return listaMetricas;
	}

	public void setListaMetricas(Collection<MetricasMatrizRisco> listaMetricas) {
		this.listaMetricas = listaMetricas;
	}

	public UsuarioInterno getUsuarioInterno() {
		return usuarioInterno;
	}

	public void setUsuarioInterno(UsuarioInterno usuarioInterno) {
		this.usuarioInterno = usuarioInterno;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((dataCadastro == null) ? 0 : dataCadastro.hashCode());
		result = prime * result + ((idUsuario == null) ? 0 : idUsuario.hashCode());
		result = prime * result + ((nome == null) ? 0 : nome.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		MatrizRisco other = (MatrizRisco) obj;
		if (dataCadastro == null) {
			if (other.dataCadastro != null)
				return false;
		} else if (!dataCadastro.equals(other.dataCadastro))
			return false;
		if (idUsuario == null) {
			if (other.idUsuario != null)
				return false;
		} else if (!idUsuario.equals(other.idUsuario))
			return false;
		if (nome == null) {
			if (other.nome != null)
				return false;
		} else if (!nome.equals(other.nome))
			return false;
		return true;
	}

}
