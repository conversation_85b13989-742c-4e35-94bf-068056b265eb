package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;

import javax.enterprise.inject.Model;
import javax.inject.Inject;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.HistoricoAnalise;
import br.gov.ac.tce.sicapanalise.auditoria.business.AcumulacaoBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.business.AnaliseBusiness;

@Model
public class AnaliseHistoricoBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4667860773055186677L;

	@Inject
	private AnaliseBusiness analiseBusiness;
	
	@Inject
	private AcumulacaoBusiness acumulacaoBusiness;
	
	private Long idAnalise;
	
	private Acumulacao acumulacao;
	
	private Collection<HistoricoAnalise> listaAnaliseHistorico;

	public Long getIdAnalise() {
		return idAnalise; 
	}

	public void setIdAnalise(Long idAnalise) {
		this.idAnalise = idAnalise;
		if (idAnalise != null && idAnalise > 0L) {
			this.listaAnaliseHistorico = analiseBusiness.retornaHistoricoAnalise(this.idAnalise);
			this.acumulacao = acumulacaoBusiness.retornaAcumulacaoPorAnalise(this.idAnalise);
		}
		
	}

	public Collection<HistoricoAnalise> getListaAnaliseHistorico() {
		return listaAnaliseHistorico;
	}

	public void setListaAnaliseHistorico(Collection<HistoricoAnalise> listaAnaliseHistorico) {
		this.listaAnaliseHistorico = listaAnaliseHistorico;
	}

	public Acumulacao getAcumulacao() {
		return acumulacao;
	}

	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
	}

	
	
	
	
}
