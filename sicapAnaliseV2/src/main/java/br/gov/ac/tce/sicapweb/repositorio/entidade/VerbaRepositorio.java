package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.folha.Verba;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Stateless
public class VerbaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<Verba> pesquisaVerbas(Entidade entidade, RemessaEventual remessaEventual, String basePrevidencia,
			String baseIRPF, String baseFGTS, String natureza, Integer tipoReferencia, String compoeVencimentoPadrao,
			Integer categoriaEconomica, Integer grupoNaturezaDespesa, Integer modalidadeAplicacao,
			String elementoDespesa, String nome) throws RepositorioException {
		Collection<Verba> listaVerba = null;

		try {
			UaiCriteria<Verba> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Verba.class);
			uaiCriteria.andEquals("entidade", entidade);

			if (remessaEventual.getId() != 0) {
				uaiCriteria.andEquals("remessaEventual", remessaEventual);
			}

			if (!basePrevidencia.equals("T")) {
				uaiCriteria.andEquals("basePrevidencia", basePrevidencia);
			}

			if (!baseIRPF.equals("T")) {
				uaiCriteria.andEquals("baseIRPF", baseIRPF);
			}

			if (!baseFGTS.equals("T")) {
				uaiCriteria.andEquals("baseFGTS", baseFGTS);
			}

			if (!natureza.equals("T")) {
				uaiCriteria.andEquals("natureza", natureza);
			}

			if (tipoReferencia != 0) {
				uaiCriteria.andEquals("tipoReferencia", tipoReferencia);
			}

			if (!compoeVencimentoPadrao.equals("T")) {
				uaiCriteria.andEquals("compoeVencimentoPadrao", compoeVencimentoPadrao);
			}

			if (categoriaEconomica != null) {
				uaiCriteria.andEquals("categoriaEconomica", categoriaEconomica);
			}

			if (grupoNaturezaDespesa != null) {
				uaiCriteria.andEquals("grupoNaturezaDespesa", grupoNaturezaDespesa);
			}

			if (modalidadeAplicacao != null) {
				uaiCriteria.andEquals("modalidadeAplicacao", modalidadeAplicacao);
			}

			if ((elementoDespesa != null) && (!elementoDespesa.isEmpty())) {
				uaiCriteria.andStringLike("elementoDespesa", "%" + elementoDespesa + "%");
			}

			if ((nome != null) && (!nome.isEmpty())) {
				uaiCriteria.andStringLike("descricao", "%" + nome + "%");
			}

			listaVerba = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro verbaRepositorio.pesquisaVerbas.", e.getCause());
		}
		return listaVerba;
	}

	public Verba pesquisaVerba(Entidade entidade, Long id) throws RepositorioException {
		Verba verba = null;

		try {
			UaiCriteria<Verba> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager, Verba.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("id", id);

			verba = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro verbaRepositorio.pesquisaVerba", e.getCause());
		}

		return verba;
	}

}
