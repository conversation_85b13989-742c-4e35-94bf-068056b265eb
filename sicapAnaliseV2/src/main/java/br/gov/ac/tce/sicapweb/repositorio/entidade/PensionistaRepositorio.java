package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.pensionista.TipoDependencia;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;

@Stateless
public class PensionistaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<Beneficiario> pesquisaPensionistas(Entidade entidade, RemessaEventual remessaEventual,
			Integer tipoPensao, TipoDependencia tipoDependencia, Integer matriculaPensionista, String nomePensionista,
			String cpfPensionista, String nomeServidor, String cpfServidor) throws RepositorioException {
		Collection<Beneficiario> listaBeneficiario = null;

		try {
			UaiCriteria<Beneficiario> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Beneficiario.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("tipoBeneficiario", "P");
			uaiCriteria.leftJoin("cadastroUnico");
			uaiCriteria.leftJoin("cadastroUnico.listaPessoaFisica");
			uaiCriteria.andEquals("cadastroUnico.listaPessoaFisica.registroAtivo", true);
			uaiCriteria.leftJoin("listaPensoes");
			uaiCriteria.andEquals("listaPensoes.registroAtivo", true);
			uaiCriteria.leftJoin("listaPensoes.tipoDependencia");
			uaiCriteria.leftJoin("listaPensoes.servidor");
			uaiCriteria.leftJoin("listaPensoes.servidor.cadastroUnico");
			uaiCriteria.leftJoin("listaPensoes.servidor.cadastroUnico.listaPessoaFisica");

			if (remessaEventual.getId() != 0) {
				uaiCriteria.andEquals("remessaEventual", remessaEventual);
			}

			if (tipoPensao != 0) {
				uaiCriteria.andEquals("listaPensoes.tipoPensao", tipoPensao);
			}

			if (tipoDependencia.getId() != 0) {
				uaiCriteria.andEquals("listaPensoes.tipoDependencia", tipoDependencia);
			}

			if (matriculaPensionista != null) {
				uaiCriteria.andEquals("matricula", matriculaPensionista);
			}

			if ((nomePensionista != null) && (!nomePensionista.isEmpty())) {
				uaiCriteria.andStringLike("cadastroUnico.listaPessoaFisica.nome", "%" + nomePensionista + "%");
			}

			if ((cpfPensionista != null) && (!cpfPensionista.isEmpty())) {
				uaiCriteria.andStringLike("cadastroUnico.cpf", "%" + cpfPensionista + "%");
			}

			if ((nomeServidor != null) && (!nomeServidor.isEmpty())) {
				uaiCriteria.andStringLike("listaPensoes.servidor.cadastroUnico.listaPessoaFisica.nome",
						"%" + nomeServidor + "%");
			}

			if ((cpfServidor != null) && (!cpfServidor.isEmpty())) {
				uaiCriteria.andStringLike("listaPensoes.servidor.cadastroUnico.cpf", "%" + cpfServidor + "%");
			}

			listaBeneficiario = uaiCriteria.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro pensionistaRepositorio.pesquisaPensionistas", e.getCause());
		}

		return listaBeneficiario;
	}

	public Collection<Beneficiario> pesquisarPensionistaPorServidor(Entidade entidade, Beneficiario servidor)
			throws RepositorioException {
		Collection<Beneficiario> listaBeneficiarioPensionista = null;

		try {
			UaiCriteria<Beneficiario> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Beneficiario.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("tipoBeneficiario", "P");
			uaiCriteria.innerJoin("listaPensoes");
			uaiCriteria.andEquals("listaPensoes.registroAtivo", true);
			uaiCriteria.innerJoin("cadastroUnico");
			uaiCriteria.innerJoin("cadastroUnico.listaPessoaFisica");
			uaiCriteria.andEquals("cadastroUnico.listaPessoaFisica.registroAtivo", true);
			uaiCriteria.andEquals("listaPensoes.servidor", servidor);

			listaBeneficiarioPensionista = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro pensionistaRepositorio.pesquisaPensionistaPorServidor", e.getCause());
		}

		return listaBeneficiarioPensionista;
	}

	public Beneficiario pesquisaPensionista(Entidade entidade, Integer matricula) throws RepositorioException {
		Beneficiario beneficiario = null;

		try {
			UaiCriteria<Beneficiario> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Beneficiario.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("tipoBeneficiario", "P");
			uaiCriteria.innerJoin("listaPensoes");
			uaiCriteria.andEquals("listaPensoes.registroAtivo", true);
			uaiCriteria.innerJoin("cadastroUnico");
			uaiCriteria.innerJoin("cadastroUnico.listaPessoaFisica");
			uaiCriteria.andEquals("matricula", matricula);

			beneficiario = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro pensionistaRepositorio.pesquisaPensionista", e.getCause());
		}

		return beneficiario;
	}
}
