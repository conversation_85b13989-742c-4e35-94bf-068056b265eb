package br.gov.ac.tce.sicapweb.modelo.folha;

public enum SituacaoBeneficiario {
	ATIVO(String.valueOf("A"), "Ativo"), INATIVO(String.valueOf("I"), "Inativo"), PENSIONISTA(String.valueOf("P"),
			"Pensionista");

	private String id;
	private String descricao;

	private SituacaoBeneficiario(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static SituacaoBeneficiario parse(String id) {
		SituacaoBeneficiario situacaoBeneficiario = null;
		for (SituacaoBeneficiario item : SituacaoBeneficiario.values()) {
			if (item.getId().equals(id)) {
				situacaoBeneficiario = item;
				break;
			}
		}
		return situacaoBeneficiario;
	}
}
