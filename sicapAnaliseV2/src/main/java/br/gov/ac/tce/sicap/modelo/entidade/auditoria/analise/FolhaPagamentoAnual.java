package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;

import org.hibernate.annotations.Immutable;

@Entity(name = "FolhaPagamentoAnual")
@Immutable
public class FolhaPagamentoAnual {

	@Id
	@Column(name = "id_folha_pagamento_anual")
	private Long id;

	@Column(name = "id_entidade")
	private Long idEntidade;

	private Long matricula;

	private String cpf;

	private String nome;

	@Column(length = 1)
	private String sexo;

	private String cargo;

	@Column(length = 10, name = "data_nascimento")
	private String dataNascimento;

	@Column(length = 10, name = "data_admissao_inicio_pensao")
	private String dataAdmissaoInicioPensao;

	@Column(length = 10, name = "data_ocorrencia")
	private String dataOcorrencia;

	@Column(name = "situacao_funcional")
	private String situacaoFuncional;

	@Column(name = "tipo_pensao")
	private String tipoPensao;

	private Integer ano;

	@Column(name = "valor_janeiro")
	private BigDecimal valorJaneiro;

	@Column(name = "valor_fevereiro")
	private BigDecimal valorFevereiro;

	@Column(name = "valor_marco")
	private BigDecimal valorMarco;

	@Column(name = "valor_abril")
	private BigDecimal valorAbril;

	@Column(name = "valor_maio")
	private BigDecimal valorMaio;

	@Column(name = "valor_junho")
	private BigDecimal valorJunho;

	@Column(name = "valor_julho")
	private BigDecimal valorJulho;

	@Column(name = "valor_agosto")
	private BigDecimal valorAgosto;

	@Column(name = "valor_setembro")
	private BigDecimal valorSetembro;

	@Column(name = "valor_outrubro")
	private BigDecimal valorOutubro;

	@Column(name = "valor_novembro")
	private BigDecimal valorNovembro;

	@Column(name = "valor_dezembro")
	private BigDecimal valorDezembro;

	@Column(name = "valor_decimo_terceiro")
	private BigDecimal valorDecimoTerceiro;

	public Long getId() {
		return id;
	}

	public Long getIdEntidade() {
		return idEntidade;
	}

	public Long getMatricula() {
		return matricula;
	}

	public String getCpf() {
		return cpf;
	}

	public String getNome() {
		return nome;
	}

	public String getSexo() {
		return sexo;
	}

	public String getCargo() {
		return cargo;
	}

	public String getDataNascimento() {
		return dataNascimento;
	}

	public String getDataAdmissaoInicioPensao() {
		return dataAdmissaoInicioPensao;
	}

	public String getDataOcorrencia() {
		return dataOcorrencia;
	}

	public String getSituacaoFuncional() {
		return situacaoFuncional;
	}

	public String getTipoPensao() {
		return tipoPensao;
	}

	public Integer getAno() {
		return ano;
	}

	public BigDecimal getValorJaneiro() {
		return valorJaneiro;
	}

	public BigDecimal getValorFevereiro() {
		return valorFevereiro;
	}

	public BigDecimal getValorMarco() {
		return valorMarco;
	}

	public BigDecimal getValorAbril() {
		return valorAbril;
	}

	public BigDecimal getValorMaio() {
		return valorMaio;
	}

	public BigDecimal getValorJunho() {
		return valorJunho;
	}

	public BigDecimal getValorJulho() {
		return valorJulho;
	}

	public BigDecimal getValorAgosto() {
		return valorAgosto;
	}

	public BigDecimal getValorSetembro() {
		return valorSetembro;
	}

	public BigDecimal getValorOutubro() {
		return valorOutubro;
	}

	public BigDecimal getValorNovembro() {
		return valorNovembro;
	}

	public BigDecimal getValorDezembro() {
		return valorDezembro;
	}

	public BigDecimal getValorDecimoTerceiro() {
		return valorDecimoTerceiro;
	}

	public BigDecimal getValorTotal() {
		return valorJaneiro.add(valorFevereiro).add(valorMarco).add(valorAbril).add(valorMarco).add(valorAbril)
				.add(valorMaio).add(valorJunho).add(valorJulho).add(valorAgosto).add(valorSetembro).add(valorOutubro)
				.add(valorNovembro).add(valorDezembro).add(valorDecimoTerceiro);
	}

}
