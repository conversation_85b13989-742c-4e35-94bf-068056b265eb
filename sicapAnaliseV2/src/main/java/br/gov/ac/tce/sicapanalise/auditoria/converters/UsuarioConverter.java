package br.gov.ac.tce.sicapanalise.auditoria.converters;

import java.io.Serializable;
import java.util.Map;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;

@FacesConverter("usuarioConverter")
public class UsuarioConverter implements Converter, Serializable {

	private static final long serialVersionUID = 1L;

	@Override
	public Object getAsObject(FacesContext context, UIComponent component, String id) {
		if (id == null || id.trim().isEmpty())
			return null;

		return this.getAttributesFrom(component).get(id);

	}

	@Override
	public String getAsString(FacesContext context, UIComponent component, Object usuarioObject) {
		if (usuarioObject == null)
			return null;

		Usuario usuario = (Usuario) usuarioObject;

		// adiciona item como atributo do componente
		this.addAttribute(component, usuario);

		return usuario.getId().toString();

	}

	protected void addAttribute(UIComponent component, Usuario usuario) {
		String key = usuario.getId().toString(); // codigo da entidade
		this.getAttributesFrom(component).put(key, usuario);
	}

	protected Map<String, Object> getAttributesFrom(UIComponent component) {
		return component.getAttributes();
	}
}
