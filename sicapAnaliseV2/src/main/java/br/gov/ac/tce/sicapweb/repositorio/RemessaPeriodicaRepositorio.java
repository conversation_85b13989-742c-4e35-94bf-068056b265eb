package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.ClassificacaoAdministrativa;
import br.gov.ac.tce.sicapweb.modelo.entidade.Ente;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.entidade.Esfera;
import br.gov.ac.tce.sicapweb.modelo.entidade.Poder;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.TempestividadeRemessa;
import br.gov.ac.tce.sicapweb.util.VerificaTempestividade;

@Stateless
public class RemessaPeriodicaRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	@SuppressWarnings("unchecked")
	public Collection<Integer> listaExercicio() throws RepositorioException {
		Collection<Integer> listaExercicio = null;

		try {
			Query query = this.entityManager.createNativeQuery("SELECT e.ano FROM auditoria.vw_lista_exercicio e");

			listaExercicio = query.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro remessaPeriodicaRepositorio.listaExercicio.", e.getCause());
		}
		return listaExercicio;
	}

	@SuppressWarnings("unchecked")
	public Collection<TempestividadeRemessa> consultaSituacaoRemessa(Integer exercicio, Integer ente, Integer poder,
			Integer idEntidadeCjur, Integer classificacaoAdministrativa, Integer esfera) throws RepositorioException {
		Collection<Object[]> listaSituacaoRemessa = null;
		Collection<TempestividadeRemessa> listaTempestividadeRemessa = new ArrayList<TempestividadeRemessa>();
		
		LocalDate hoje = LocalDate.now();
		Integer anoAtual = hoje.getYear();
		Integer mesAtual = hoje.getMonthValue();
		
		VerificaTempestividade verificaTempestividade = new VerificaTempestividade(getPrazoEnvioRemessaAtual(anoAtual, mesAtual));
		verificaTempestividade.definirRemessaAtual();
		
		
		String sql;
		List<String> condicoes = new LinkedList<>();
		String where = "";

		try {
			sql = "SELECT * FROM auditoria.vw_tempestividadeRemessa tr ";

			if (exercicio != 0) {
				condicoes.add("tr.ano = " + exercicio);
			}

			if (ente != 0) {
				condicoes.add("tr.ente ='" + Ente.parse(ente) + "'");
			}

			if (poder != 0) {
				condicoes.add("tr.poder = '" + Poder.parse(poder) + "'");
			}

			if (idEntidadeCjur != 0) {
				condicoes.add("tr.idEntidadeCjur = " + idEntidadeCjur);
			}

			if (classificacaoAdministrativa != 0) {
				condicoes.add(
						"tr.classificacao = '" + ClassificacaoAdministrativa.parse(classificacaoAdministrativa) + "'");
			}

			if (esfera != 0) {
				condicoes.add("tr.esfera = '" + Esfera.parse(esfera) + "'");
			}

			if (condicoes.size() != 0) {
				where = "where " + String.join(" AND ", condicoes);
			}

			sql += where;

			Query query = this.entityManager.createNativeQuery(sql);

			listaSituacaoRemessa = (Collection<Object[]>) query.getResultList();

			for (Object[] object : listaSituacaoRemessa) {
				listaTempestividadeRemessa.add(verificaTempestividade.verificarRemessa(object));
			}

		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("remessaPeriodicaRepositorio.consultaSituacaoRemessa.", e.getCause());
		}
		return listaTempestividadeRemessa;
	}

	public Remessa pesquisarRemessa(Integer idEntidadeCjur, Integer ano, Integer mes) throws RepositorioException {
		Remessa remessa = null;
		Entidade entidade = new Entidade();

		try {
			if ((idEntidadeCjur != null) && (ano != null) && (mes != null)) {

				entidade.setIdEntidadeCjur(idEntidadeCjur);

				UaiCriteria<Remessa> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
						Remessa.class);
				uaiCriteria.innerJoin("entidade");
				uaiCriteria.andEquals("entidade", entidade);
				uaiCriteria.andEquals("competencia.ano", ano);
				uaiCriteria.andEquals("competencia.mes", mes);

				remessa = uaiCriteria.getSingleResult();
			}
		} catch (NoResultException nre) {
			remessa = null;
		} catch (Exception e) {
			throw new RepositorioException("Erro remessaPeriodicaRepositorio.pesquisarRemessa.", e.getCause());
		}
		return remessa;
	}

	public Collection<Remessa> listaRemessasPorSituacaoEntidade(SituacaoRemessa situacaoRemessa, Entidade entidade)
			throws RepositorioException {
		Collection<Remessa> listaRemessas = null;

		try {
			UaiCriteria<Remessa> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					Remessa.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("situacao", situacaoRemessa.getId());
			uaiCriteria.orderByDesc("competencia.ano");
			uaiCriteria.orderByDesc("competencia.mes");

			listaRemessas = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro remessaPeriodicaRepositorio.listaRemessasPorSituacaoEntidade",
					e.getCause());

		}
		return listaRemessas;
	}

//	@SuppressWarnings("unchecked")
	public LocalDateTime getPrazoEnvioRemessaAtual(Integer ano, Integer mes) throws RepositorioException {
		LocalDateTime prazoEnvioRemessaAtual = null;

		Timestamp data = null;
//		String data = null;
		
		try {
			Query query = this.entityManager.createNativeQuery("select p.prazoEnvio from auditoria.PrazoEnvioRemessa p where p.ano = ? and p.mes = ?");

			query.setParameter(1, ano);
			query.setParameter(2, mes);
			
//			data = (String) query.getSingleResult();
//
//			data = data.replace(" ", "T");
//			
//			prazoEnvioRemessaAtual = LocalDateTime.parse(data);
			
			data = (Timestamp) query.getSingleResult();
			
			prazoEnvioRemessaAtual = data.toLocalDateTime();

			 
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro remessaPeriodicaRepositorio.getPrazoEnvioRemessaAtual", e.getCause());
		}
		return prazoEnvioRemessaAtual;
	}

	// @SuppressWarnings("unchecked")
	// public Collection<Remessa>
	// listaRemessasPorSituacaoEntidade(SituacaoRemessa situacaoRemessa,
	// Entidade entidade)
	// throws RepositorioException {
	// Collection<Remessa> listaRemessas = null;
	//
	// try {
	// Query query = this.entityManager.createNativeQuery(
	// "Select * from remessa.remessa r where r.idEntidadeCjur = ? and
	// r.situacao = ?", Remessa.class);
	//
	// query.setParameter(1, entidade.getIdEntidadeCjur());
	// query.setParameter(2, situacaoRemessa.getId());
	//
	// listaRemessas = query.getResultList();
	//
	// for (Remessa remessa : listaRemessas) {
	// System.out.println("remessa.ano: " + remessa.getCompetencia().getAno());
	// System.out.println("remessa.mes: " + remessa.getCompetencia().getMes());
	// }
	//
	// } catch (Exception e) {
	// throw new RepositorioException("Erro
	// remessaPeriodicaRepositorio.listaRemessasPorSituacaoEntidade",
	// e.getCause());
	//
	// }
	// return listaRemessas;
	// }

}
