package br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashSet;
import java.util.stream.Collectors;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(schema = "auditoria")
@NamedQueries({
		@NamedQuery(name = "grupo.listaUsuarios", query = "SELECT ug FROM UsuarioGrupo ug "
																  + "JOIN FETCH ug.usuario "
																  + "JOIN FETCH ug.grupo "
																  + "where ug.grupo = :grupo and ug.ativo = true") })
public class Grupo implements Serializable {

	private static final long serialVersionUID = -8522607324376289080L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	private String nome;
	private Boolean ativo;
	
	@Enumerated(EnumType.STRING)
	private PerfilGrupo perfil;

	@OneToMany(fetch = FetchType.LAZY,mappedBy = "grupo" )
	private Collection<UsuarioGrupo> listaUsuarioGrupo = new HashSet<>();

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public Collection<UsuarioGrupo> getListaUsuarioGrupo() {
		return listaUsuarioGrupo;
	}

	public void setListaUsuarioGrupo(Collection<UsuarioGrupo> listaUsuarioGrupo) {
		this.listaUsuarioGrupo = listaUsuarioGrupo;
	}

	public Boolean getAtivo() {
		return ativo;
	}

	public void setAtivo(Boolean ativo) {
		this.ativo = ativo;
	}
	
	public PerfilGrupo getPerfil() {
		return perfil;
	}

	public void setPerfil(PerfilGrupo perfil) {
		this.perfil = perfil;
	}

	public Collection<UsuarioGrupo> getListaUsuarioGrupoAtivo() {
		return listaUsuarioGrupo.stream().filter(u -> u.getAtivo()).collect(Collectors.toList());
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Grupo other = (Grupo) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

	
	
}
