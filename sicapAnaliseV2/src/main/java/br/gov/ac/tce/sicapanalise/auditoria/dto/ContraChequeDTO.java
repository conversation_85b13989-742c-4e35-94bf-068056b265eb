package br.gov.ac.tce.sicapanalise.auditoria.dto;

import java.math.BigDecimal;

public class ContraChequeDTO {
	private String cpf;
	private String nome;
	private Integer mes;
	private Integer ano;
	private String entidadeNome;
	private Integer matricula;
	private String tipoFolha;
	private BigDecimal totalVencimentos;
	private BigDecimal totalDescontos;
	private String verbaCodigo;
	private String verbaDescricao;
	private String verbaNatureza;
	private String verbaReferencia;
	private BigDecimal verbaValor;
	
	
	
	public ContraChequeDTO(String cpf, String nome, Integer mes, Integer ano, String entidadeNome, Integer matricula,
			String tipoFolha, BigDecimal totalVencimentos, BigDecimal totalDescontos, String verbaCodigo,
			String verbaDescricao, String verbaNatureza, String verbaReferencia, BigDecimal verbaValor) {
		super();
		this.cpf = cpf;
		this.nome = nome;
		this.mes = mes;
		this.ano = ano;
		this.entidadeNome = entidadeNome;
		this.matricula = matricula;
		this.tipoFolha = tipoFolha;
		this.totalVencimentos = totalVencimentos;
		this.totalDescontos = totalDescontos;
		this.verbaCodigo = verbaCodigo;
		this.verbaDescricao = verbaDescricao;
		this.verbaNatureza = verbaNatureza;
		this.verbaReferencia = verbaReferencia;
		this.verbaValor = verbaValor;
	}
	public String getCpf() {
		return cpf;
	}
	public void setCpf(String cpf) {
		this.cpf = cpf;
	}
	public String getNome() {
		return nome;
	}
	public void setNome(String nome) {
		this.nome = nome;
	}
	public Integer getMes() {
		return mes;
	}
	public void setMes(Integer mes) {
		this.mes = mes;
	}
	public Integer getAno() {
		return ano;
	}
	public void setAno(Integer ano) {
		this.ano = ano;
	}
	public String getEntidadeNome() {
		return entidadeNome;
	}
	public void setEntidadeNome(String entidadeNome) {
		this.entidadeNome = entidadeNome;
	}
	public Integer getMatricula() {
		return matricula;
	}
	public void setMatricula(Integer matricula) {
		this.matricula = matricula;
	}
	public String getTipoFolha() {
		return tipoFolha;
	}
	public void setTipoFolha(String tipoFolha) {
		this.tipoFolha = tipoFolha;
	}
	public BigDecimal getTotalVencimentos() {
		return totalVencimentos;
	}
	public void setTotalVencimentos(BigDecimal totalVencimentos) {
		this.totalVencimentos = totalVencimentos;
	}
	public BigDecimal getTotalDescontos() {
		return totalDescontos;
	}
	public void setTotalDescontos(BigDecimal totalDescontos) {
		this.totalDescontos = totalDescontos;
	}
	public String getVerbaCodigo() {
		return verbaCodigo;
	}
	public void setVerbaCodigo(String verbaCodigo) {
		this.verbaCodigo = verbaCodigo;
	}
	public String getVerbaDescricao() {
		return verbaDescricao;
	}
	public void setVerbaDescricao(String verbaDescricao) {
		this.verbaDescricao = verbaDescricao;
	}
	public String getVerbaNatureza() {
		return verbaNatureza;
	}
	public void setVerbaNatureza(String verbaNatureza) {
		this.verbaNatureza = verbaNatureza;
	}
	public String getVerbaReferencia() {
		return verbaReferencia;
	}
	public void setVerbaReferencia(String verbaReferencia) {
		this.verbaReferencia = verbaReferencia;
	}
	public BigDecimal getVerbaValor() {
		return verbaValor;
	}
	public void setVerbaValor(BigDecimal verbaValor) {
		this.verbaValor = verbaValor;
	}
	
	
	
}
