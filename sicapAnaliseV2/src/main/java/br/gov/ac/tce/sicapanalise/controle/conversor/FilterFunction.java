package br.gov.ac.tce.sicapanalise.controle.conversor;

import java.text.Normalizer;
import java.util.Locale;

public class FilterFunction {

	public boolean filterByName(Object value, Object filter, Locale locale) {

		String filterText = (filter == null) ? null : filter.toString().trim();
		if (filterText == null || filterText.equals("")) {
			return true;
		}

		if (value == null) {
			return false;
		}

		String nome = Normalizer.normalize(value.toString(), Normalizer.Form.NFD);
		nome = nome.replaceAll("\\p{InCombiningDiacriticalMarks}+", "");

		filterText = Normalizer.normalize(filterText, Normalizer.Form.NFD);
		filterText = filterText.replaceAll("\\p{InCombiningDiacriticalMarks}+", "");

		if (nome.contains(filterText)) {
			return true;
		} else {
			return false;
		}
	}

}
