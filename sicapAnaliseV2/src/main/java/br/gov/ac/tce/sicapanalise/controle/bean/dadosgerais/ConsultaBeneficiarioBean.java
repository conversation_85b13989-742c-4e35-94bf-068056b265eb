package br.gov.ac.tce.sicapanalise.controle.bean.dadosgerais;

import java.io.ByteArrayInputStream;
import java.io.Serializable;
import java.sql.Connection;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.model.DefaultStreamedContent;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;
import br.gov.ac.tce.sicapweb.modelo.folha.ContraCheque;
import br.gov.ac.tce.sicapweb.modelo.folha.TipoBeneficiario;
import br.gov.ac.tce.sicapweb.modelo.folha.VerbasContraCheque;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.pessoa.CadastroUnico;
import br.gov.ac.tce.sicapweb.modelo.servidor.HistoricoFuncional;
import br.gov.ac.tce.sicapweb.repositorio.BeneficiarioRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.RelatorioConexao;
import br.gov.ac.tce.sicapweb.repositorio.RemessaPeriodicaRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.ContraChequeRepositorio;
import br.gov.ac.tce.sicapweb.repositorio.entidade.HistoricoFuncionalRepositorio;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;

@Named
@ViewScoped
public class ConsultaBeneficiarioBean implements Serializable {

	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;
	@Inject
	private CadastroUnico cadastroUnico;
	@Inject
	private Beneficiario beneficiario;
	@Inject
	private ContraCheque contraCheque;

	@Inject
	private BeneficiarioRepositorio beneficiarioRepositorio;
	@Inject
	private HistoricoFuncionalRepositorio historicoFuncionalRepositorio;
	@Inject
	private ContraChequeRepositorio contraChequeRepositorio;
	@Inject
	private RemessaPeriodicaRepositorio remessaPeriodicaRepositorio;
	@Inject
	private RelatorioConexao relatorioConexao;

	private Collection<Object> listaBeneficiario;
	private Collection<Beneficiario> listaVinculos;
	private Collection<HistoricoFuncional> listaHistoricoFuncional;
	private Collection<ContraCheque> listaContraCheque;
	private Collection<Integer> listaExercicio;
	private Collection<VerbasContraCheque> listaVerbasContraCheque;

	private String nome;
	private String cpf;

	private String nomeSelecionado;
	private String cpfSelecionado;
	private Integer exercicio;
	private Integer exercicioRelatorioVerbas;

	private int currentLevel;

	@PostConstruct
	public void init() {
		try {
			this.currentLevel = 1;
			this.nome = "";
			this.cpf = "";
			this.nomeSelecionado = "";
			this.cpfSelecionado = "";
			this.exercicio = 2016;
			this.listaExercicio = this.remessaPeriodicaRepositorio.listaExercicio();
		} catch (RepositorioException e) {
			Mensagem.setMensagem(MensagemType.ERRO, "Ocorreu um erro ao selecionar o filtro para geração de relatório.",
					"");
		}
	}

	public void pesquisar() {
		try {
			this.listaBeneficiario = this.beneficiarioRepositorio.pesquisaTodos(somenteNumeros(this.cpf), this.nome);
			this.nomeSelecionado = "";
			this.cpfSelecionado = "";
			this.currentLevel = 1;
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível listar nenhuma verba.");
		}
	}

	public void pesquisarVinculos(String cpf, String nome) {
		try {
			this.cpfSelecionado = cpf;
			this.nomeSelecionado = nome;

			this.listaVinculos = this.beneficiarioRepositorio.pesquisaVinculos(somenteNumeros(cpf));
//			this.listaVinculos = this.beneficiarioRepositorio.pesquisaVinculosPorCpf(somenteNumeros(cpf));
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Nenhum vínculo encontrado.");
		}
	}

	public void detalharVinculo(Beneficiario beneficiario) {
		try {
			this.listaHistoricoFuncional = null;
			this.listaContraCheque = null;
			this.beneficiario = this.beneficiarioRepositorio.pesquisaVinculoPorId(beneficiario.getId());
			if (beneficiario.getTipoBeneficiario() == TipoBeneficiario.SERVIDOR) {
				this.listaHistoricoFuncional = this.historicoFuncionalRepositorio
						.pesquisaHistoricoPorBeneficiario(beneficiario.getEntidade(), beneficiario);
			}
			this.listaContraCheque = this.contraChequeRepositorio.pesquisaPorBeneficiario(beneficiario.getEntidade(),
					beneficiario);
		} catch (RepositorioException e) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível detalhar os dados do vínculo.");
		}
	}

	public void detalharContraCheque(ContraCheque contraCheque) {
		try {
			this.contraCheque = this.contraChequeRepositorio.pesquisaPorId(contraCheque);
			this.listaVerbasContraCheque = this.contraChequeRepositorio.pesquisaPorVerbasContraCheque(contraCheque);
		} catch (RepositorioException e) {
			e.printStackTrace();
			Messenger.mostrarMensagem(MessageType.ERRO, "Não foi possível detalhar o contracheque.");
		}
	}

	public DefaultStreamedContent gerarFichaAnualRendimentos() throws Exception {
		String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext().getRealPath("relatorios/")
				+ "/";
		String pathRelatorio = pathRelatorioDir + "fichaAnualVinculosRendimentoTotalBruto.jasper";

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("cpf", this.cpfSelecionado);
		parameters.put("usuario", this.loginBean.getUsuario().getNome());
		parameters.put("exercicio", this.exercicio);
		parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
		parameters.put("SUBREPORT_DIR", pathRelatorioDir);

		String nomeArquivo = this.cpfSelecionado + "-fichaAnualVinculosRendimentos.pdf";
		
		return gerarPdf(nomeArquivo,pathRelatorio, parameters );
		
	}

	public DefaultStreamedContent gerarRelatorioFinanceiroVerbas() throws Exception {
		String pathRelatorioDir = FacesContext.getCurrentInstance().getExternalContext()
				.getRealPath("relatorios/fichaFinanceira") + "/";
		String pathRelatorio = pathRelatorioDir + "relatorioFinanceiro.jasper";

		HashMap<String, Object> parameters = new LinkedHashMap<String, Object>();
		parameters.put("cpf", this.cpfSelecionado);
		parameters.put("usuario", this.loginBean.getUsuario().getNome());
		parameters.put("ano", this.exercicioRelatorioVerbas);
		parameters.put("REPORT_IMAGE_DIR", pathRelatorioDir);
		parameters.put("SUBREPORT_DIR", pathRelatorioDir);
		
		String nomeArquivo = this.cpfSelecionado + "-relatorioFinanceiro-" + this.exercicioRelatorioVerbas + ".pdf";
		return gerarPdf(nomeArquivo,pathRelatorio, parameters );
	}
	
	private DefaultStreamedContent gerarPdf(String nomeArquivo, String caminhoRelatorio , Map<String, Object> parametros) throws Exception {
		try (Connection conexaoJasper = this.relatorioConexao.getConnection()) {
			
			JasperPrint jasperPrint = JasperFillManager.fillReport(caminhoRelatorio, parametros,
					conexaoJasper);
			byte[] relatarioBytes = JasperExportManager.exportReportToPdf(jasperPrint);
			return new DefaultStreamedContent(new ByteArrayInputStream(relatarioBytes),"application/pdf",nomeArquivo);

		} 
	}

	public CadastroUnico getCadastroUnico() {
		return cadastroUnico;
	}

	public void setCadastroUnico(CadastroUnico cadastroUnico) {
		this.cadastroUnico = cadastroUnico;
	}

	public Collection<Object> getListaBeneficiario() {
		return listaBeneficiario;
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public String getCpf() {
		return cpf;
	}

	public void setCpf(String cpf) {
		this.cpf = cpf;
	}

	public int getCurrentLevel() {
		return currentLevel;
	}

	public void setCurrentLevel(int currentLevel) {
		this.currentLevel = currentLevel;
	}

	public Collection<Beneficiario> getListaVinculos() {
		return listaVinculos;
	}

	public void setListaVinculos(Collection<Beneficiario> listaVinculos) {
		this.listaVinculos = listaVinculos;
	}

	public String getNomeSelecionado() {
		return nomeSelecionado;
	}

	public void setNomeSelecionado(String nomeSelecionado) {
		this.nomeSelecionado = nomeSelecionado;
	}

	public String getCpfSelecionado() {
		return cpfSelecionado;
	}

	public void setCpfSelecionado(String cpfSelecionado) {
		this.cpfSelecionado = cpfSelecionado;
	}

	public Beneficiario getBeneficiario() {
		return beneficiario;
	}

	public void setBeneficiario(Beneficiario beneficiario) {
		this.beneficiario = beneficiario;
	}

	public Collection<HistoricoFuncional> getListaHistoricoFuncional() {
		return listaHistoricoFuncional;
	}

	public void setListaHistoricoFuncional(Collection<HistoricoFuncional> listaHistoricoFuncional) {
		this.listaHistoricoFuncional = listaHistoricoFuncional;
	}

	public Collection<ContraCheque> getListaContraCheque() {
		return listaContraCheque;
	}

	public void setListaContraCheque(Collection<ContraCheque> listaContraCheque) {
		this.listaContraCheque = listaContraCheque;
	}

	public ContraCheque getContraCheque() {
		return contraCheque;
	}

	public void setContraCheque(ContraCheque contraCheque) {
		this.contraCheque = contraCheque;
	}

	public Integer getExercicio() {
		return exercicio;
	}

	public void setExercicio(Integer exercicio) {
		this.exercicio = exercicio;
	}

	public Collection<Integer> getListaExercicio() {
		return listaExercicio;
	}

	public void setListaExercicio(Collection<Integer> listaExercicio) {
		this.listaExercicio = listaExercicio;
	}

	public Collection<VerbasContraCheque> getListaVerbasContraCheque() {
		return listaVerbasContraCheque;
	}

	public void setListaVerbasContraCheque(Collection<VerbasContraCheque> listaVerbasContraCheque) {
		this.listaVerbasContraCheque = listaVerbasContraCheque;
	}

	public Integer getExercicioRelatorioVerbas() {
		return exercicioRelatorioVerbas;
	}

	public void setExercicioRelatorioVerbas(Integer exercicioRelatorioVerbas) {
		this.exercicioRelatorioVerbas = exercicioRelatorioVerbas;
	}

	private String somenteNumeros(String valor) {
		if (valor != null) {
			return valor.replaceAll("[^0-9]", "");

		}
		return "";
	}

}
