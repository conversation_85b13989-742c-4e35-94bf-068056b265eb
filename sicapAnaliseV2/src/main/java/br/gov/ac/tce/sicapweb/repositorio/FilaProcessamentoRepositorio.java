package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.TypedQuery;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.remessa.FilaProcessamento;
import br.gov.ac.tce.sicapweb.modelo.remessa.Remessa;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.remessa.SituacaoRemessa;
import br.gov.ac.tce.sicapweb.xml.validador.TypeArquivo;

@Stateless
public class FilaProcessamentoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public void adicionar(FilaProcessamento filaProcessamento) throws RepositorioException {
		try {
			this.entityManager.persist(filaProcessamento);
		} catch (Exception e) {
			throw new RepositorioException("Erro ao inserir registro na fila de processamento.", e.getCause());
		}
	}

	public void atualizar(FilaProcessamento filaProcessamento) throws RepositorioException {
		try {
			this.entityManager.merge(filaProcessamento);
		} catch (Exception e) {
			throw new RepositorioException("Erro ao atualizar registro na fila de processamento.", e.getCause());
		}
	}

	public void removerFila(RemessaEventual remessaEventual) throws RepositorioException {
		try {
			Query query = this.entityManager.createNativeQuery(
					"DELETE FROM remessa.FilaProcessamento where idEntidadeCjur = :entidade AND idRemessa = :remessa AND tipoArquivo = :tipoArquivo AND situacaoProcessamento = 'EI'");
			query.setParameter("entidade", remessaEventual.getEntidade().getIdEntidadeCjur());
			query.setParameter("remessa", remessaEventual.getId());
			query.setParameter("tipoArquivo", remessaEventual.getTipoRemessa().getId());
			query.executeUpdate();
		} catch (Exception e) {
			System.out.println("removerFila");
			e.printStackTrace();
			throw new RepositorioException("filaProcessamentoRepositorio -> removerFila.", e.getCause());
		}
	}

	public void removerRemessaDescartada(Remessa remessa) throws RepositorioException {
		Collection<FilaProcessamento> listafilaProcessamento = null;
		try {
			TypedQuery<FilaProcessamento> query = this.entityManager
					.createNamedQuery(FilaProcessamento.buscarPorTipoIdRemessaEntidade, FilaProcessamento.class);
			query.setParameter("idRemessa", remessa.getId());
			query.setParameter("tipoArquivo", TypeArquivo.CONTRACHEQUE.getId());
			query.setParameter("entidade", remessa.getEntidade());
			listafilaProcessamento = query.getResultList();
			for (FilaProcessamento filaProcessamento : listafilaProcessamento) {
				filaProcessamento.setSituacaoProcessamento(SituacaoRemessa.DESCARTADA);
				filaProcessamento.setConteudoXML(null);
				atualizar(filaProcessamento);
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Descartar remessa da fila.", e.getCause());
		}
	}

	public Collection<FilaProcessamento> listarTodasPendentes() throws RepositorioException {
		Collection<FilaProcessamento> listaFilaProcessamento = null;
		try {
			TypedQuery<FilaProcessamento> query = this.entityManager
					.createNamedQuery(FilaProcessamento.listarTodasPendentes, FilaProcessamento.class);
			listaFilaProcessamento = query.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro FilaProcessamento.listarTodasPendentes.", e.getCause());
		}
		return listaFilaProcessamento;
	}

	public FilaProcessamento buscarPorRemessaEventual(RemessaEventual remessaEventual) throws RepositorioException {
		FilaProcessamento filaProcessamento = null;
		try {
			TypedQuery<FilaProcessamento> query = this.entityManager
					.createNamedQuery(FilaProcessamento.buscarPorTipoIdRemessaEntidade, FilaProcessamento.class);
			query.setParameter("idRemessa", remessaEventual.getId());
			query.setParameter("tipoArquivo", remessaEventual.getTipoRemessa().getId());
			query.setParameter("entidade", remessaEventual.getEntidade());
			filaProcessamento = query.getSingleResult();

		} catch (NoResultException noResultException) {
			noResultException.printStackTrace();
			throw new RepositorioException("Remessa n�o encontrada na fila de processamento.",
					noResultException.getCause());
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Descartar remessa da fila.", e.getCause());
		}
		return filaProcessamento;
	}
}
