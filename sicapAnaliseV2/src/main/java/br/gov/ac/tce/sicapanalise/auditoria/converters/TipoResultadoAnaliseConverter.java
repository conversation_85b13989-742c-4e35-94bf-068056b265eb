package br.gov.ac.tce.sicapanalise.auditoria.converters;

import java.util.Map;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.TipoResultadoAnalise;

@FacesConverter("tipoResultadoAnaliseConverter")
public class TipoResultadoAnaliseConverter implements Converter {

	@Override
	public Object getAsObject(FacesContext contexto, UIComponent component, String valor) {
		if (valor == null || valor.trim().isEmpty())
			return null;
//		System.out.println("Convertendo para Objeto: " + valor);
		
		return this.getAttributesFrom(component).get(valor);

//		try {
//			TipoResultadoAnalise tra = new TipoResultadoAnalise();
//			tra.setId(Long.valueOf(valor));
//			return tra;
//		} catch (Exception e) {
//			e.printStackTrace();
////			System.out.println("Erro TipoResultadoAnaliseConverter: " + e.getMessage());
//			return null;
//		}
		
	}

	@Override
	public String getAsString(FacesContext contexto, UIComponent component, Object objeto) {
		if (objeto == null)
			return null;
//		System.out.println("Convertendo para String: " + objeto);

		TipoResultadoAnalise tra = (TipoResultadoAnalise) objeto;
		
		// adiciona objeto como atributo do componente
		this.addAttribute(component, tra);
		
		return tra.getId().toString();
	}
	
	protected void addAttribute(UIComponent component, TipoResultadoAnalise tipoResultadoAnalise) {
		String key = tipoResultadoAnalise.getId().toString(); // codigo da entidade
		this.getAttributesFrom(component).put(key, tipoResultadoAnalise);
	}

	protected Map<String, Object> getAttributesFrom(UIComponent component) {
		return component.getAttributes();
	}

}
