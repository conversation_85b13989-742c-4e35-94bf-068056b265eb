package br.gov.ac.tce.sicapanalise.controle.conversor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class FormatUtil implements Serializable {

	private static final long serialVersionUID = 1L;

	public String formatDateTime(LocalDateTime localDateTime) {
		String dataTime = null;
		if (localDateTime == null) {
			return null;
		} else {
			try {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
				dataTime = localDateTime.format(formatter);
				return dataTime;
			} catch (Exception e) {
			}
		}
		return dataTime;
	}

	public String formatStringParaDateTime(String data) {
		String dataTime = null;
		LocalDateTime dateTime;

		if ((!data.isEmpty()) && (data != null)) {
			data = data.replace(" ", "T");
			dateTime = LocalDateTime.parse(data);
		} else {
			dateTime = null;
		}

		if (dateTime == null) {
			return "Sem envio";
		} else {
			try {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
				dataTime = dateTime.format(formatter);
				return dataTime;
			} catch (Exception e) {
			}
		}
		return dataTime;
	}

	public String formatBoolean(Boolean valor) {
		if (valor == null) {
			return "";
		} else if (valor == true) {
			return "Sim";
		} else {
			return "Não";
		}
	}
}
