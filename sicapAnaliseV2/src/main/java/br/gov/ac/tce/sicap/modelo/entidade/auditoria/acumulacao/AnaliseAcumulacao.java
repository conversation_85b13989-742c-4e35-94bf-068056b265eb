package br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao;

import java.io.Serializable;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.Analise;
import br.gov.ac.tce.sicapanalise.auditoria.dto.AcumulacaoAnaliseDTO;

@Entity
@Table(schema = "auditoria", uniqueConstraints = {
		@UniqueConstraint(name = "unicaAnaliseAcumulacao", columnNames = { "idAnalise", "idAcumulacao" }) })
@SqlResultSetMapping(name = "acumulacaoAnaliseMapping", classes = {
		@ConstructorResult(targetClass = AcumulacaoAnaliseDTO.class, columns = {
				@ColumnResult(name = "acumulacaoId", type = Long.class),
				@ColumnResult(name = "acumulacaoAno", type = Integer.class),
				@ColumnResult(name = "acumulacaoMes", type = Integer.class),
				@ColumnResult(name = "acumulacaoCpf", type = String.class),
				@ColumnResult(name = "acumulacaoNome", type = String.class),
				@ColumnResult(name = "acumulacaoPontuacao", type = Double.class),
				@ColumnResult(name = "acumulacaoSituacaoCodigo", type = String.class),
				@ColumnResult(name = "acumulacaoSituacaoDescricao", type = String.class),
				@ColumnResult(name = "acumulacaoQuantidadeVinculos", type = Integer.class),
				@ColumnResult(name = "analiseId", type = Long.class),
				@ColumnResult(name = "analiseDataCriacao", type = LocalDateTime.class),
				@ColumnResult(name = "analiseNumeroProcesso", type = String.class),
				@ColumnResult(name = "analiseDocumentosSolicitados", type = Integer.class),
				@ColumnResult(name = "analiseDocumentosEntregues", type = Integer.class),
				@ColumnResult(name = "analiseDocumentosNaoEntregues", type = Integer.class),
				@ColumnResult(name = "analiseRelatorios", type = Integer.class),
				@ColumnResult(name = "acumulacaoAnaliseId", type = Long.class)
				}) 
		})
public class AnaliseAcumulacao implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAnalise", nullable = false)
	private Analise analise;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAcumulacao", nullable = false)
	private Acumulacao acumulacao;
	@Column(nullable = false, updatable = false)
	private LocalDateTime dataCriacao;


	public AnaliseAcumulacao() {}
	
	public AnaliseAcumulacao(Analise analise, Acumulacao acumulacao) {
		this.analise = analise;
		this.acumulacao = acumulacao;
		this.dataCriacao = LocalDateTime.now();
	}
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Analise getAnalise() {
		return analise;
	}

	public void setAnalise(Analise analise) {
		this.analise = analise;
	}

	public Acumulacao getAcumulacao() {
		return acumulacao;
	}

	public void setAcumulacao(Acumulacao acumulacao) {
		this.acumulacao = acumulacao;
	}

	public LocalDateTime getDataCriacao() {
		return dataCriacao;
	}

	public void setDataCriacao(LocalDateTime dataCriacao) {
		this.dataCriacao = dataCriacao;
	}
	
	

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((acumulacao == null) ? 0 : acumulacao.hashCode());
		result = prime * result + ((analise == null) ? 0 : analise.hashCode());
		result = prime * result + ((dataCriacao == null) ? 0 : dataCriacao.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AnaliseAcumulacao other = (AnaliseAcumulacao) obj;
		if (acumulacao == null) {
			if (other.acumulacao != null)
				return false;
		} else if (!acumulacao.equals(other.acumulacao))
			return false;
		if (analise == null) {
			if (other.analise != null)
				return false;
		} else if (!analise.equals(other.analise))
			return false;
		if (dataCriacao == null) {
			if (other.dataCriacao != null)
				return false;
		} else if (!dataCriacao.equals(other.dataCriacao))
			return false;
		return true;
	}

}
