package br.gov.ac.tce.sicapweb.modelo.servidor;

public enum TipoServidor {
	CIVIL(Integer.valueOf(1), "Civil"), MILITAR(Integer.valueOf(2), "Militar");

	private Integer id;
	private String descricao;

	private TipoServidor(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoServidor parse(Integer id) {
		TipoServidor tipoServidor = null;
		for (TipoServidor item : TipoServidor.values()) {
			if (item.getId() == id) {
				tipoServidor = item;
				break;
			}
		}
		return tipoServidor;
	}
}
