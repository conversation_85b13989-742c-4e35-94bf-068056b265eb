package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.time.Year;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.PrimeFaces;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.AnaliseAcumulacao;
import br.gov.ac.tce.sicapanalise.auditoria.business.AnaliseBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.business.EntidadeBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.business.ProtocoloBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.DocumentoAuditoriaDTO;
import br.gov.ac.tce.sicapanalise.auditoria.processo.Protocolo;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ResponsavelSimplesPrestacao;
import br.gov.ac.tce.sicapanalise.auditoria.processo.ResponsavelSimplesPrestacaoContainer;
import br.gov.ac.tce.sicapanalise.auditoria.processo.RespostaProcessoWS;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapweb.modelo.entidade.Ente;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Named
@ViewScoped
public class AcumulacaoProtocoloBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Inject
	private LoginBean loginBean;

	@Inject
	private AnaliseBusiness analiseBusiness;

	@Inject
	private ProtocoloBusiness protocoloBusiness;
	
	@Inject
	private EntidadeBusiness entidadeBusiness;

	@Inject
	private FacesContext facesContext;
	

	private Protocolo protocolo;

	private ResponsavelSimplesPrestacao responsavel;
	
	private AnaliseAcumulacao analiseAcumulacao;
	
	private List<DocumentoAuditoriaDTO> documentos;

	private int tabIndex = 0;

	@PostConstruct
	void inicializado() {
//		System.out.println("Usuario: " + loginBean.getUsuario().getCpf());
		resetaResponsavel();
		inicializaProtocolo();

	}

	private void inicializaProtocolo() {
		protocolo = new Protocolo();
		protocolo.getInteressado().setCpfCnpj("04.035.135/0001-43");
		protocolo.getInteressado().setNome("TRIBUNAL DE CONTAS DO ESTADO DO ACRE");
//		protocolo.getInteressado().setEmail("???");
		protocolo.setAssunto("INSPEÇÃO");
		protocolo.setClasse("ACUMULAÇÃO DE CARGOS");
		protocolo.setNomeTipoProcesso("CONTROLE EXTERNO");
		protocolo.setExercicio(Year.now().getValue());
		protocolo.setCpfUsuarioSistema(formataCpfCnpj(loginBean.getUsuario().getCpf()));
	}

	public Protocolo getProtocolo() {
		return protocolo;
	}

	public void setProtocolo(Protocolo protocolo) {
		this.protocolo = protocolo;
	}

	public List<String> listaTipoProcesso() {
		return protocoloBusiness.listarTipoProcesso();
	}

	public List<String> listaTipoDocumento() {
		return protocoloBusiness.listarTipoDocumento();
	}

	public ResponsavelSimplesPrestacao getResponsavel() {
		return responsavel;
	}

	public void setResponsavel(ResponsavelSimplesPrestacao responsavel) {
		this.responsavel = responsavel;
	}
	


	public AnaliseAcumulacao getAnaliseAcumulacao() {
		return analiseAcumulacao;
	}

	public void setAnaliseAcumulacao(AnaliseAcumulacao analiseAcumulacao) {
		this.analiseAcumulacao = analiseAcumulacao;
		carregaDadosAnaliseAcumulacao(analiseAcumulacao);
	}

	private void carregaDadosAnaliseAcumulacao(AnaliseAcumulacao analiseAcumulacao) {
		
		int numeroRemessa = analiseAcumulacao.getAcumulacao().getId().intValue();
		this.protocolo.setNumRemessa(numeroRemessa);
		
		Long idAcumulacao = analiseAcumulacao.getAcumulacao().getId();
		Entidade ultimaEntidadeAdmissao = entidadeBusiness.retornaUltimaEntidadeAdmissao(idAcumulacao);
		
		if(ultimaEntidadeAdmissao != null) {
			this.protocolo.setEntidade(ultimaEntidadeAdmissao);
			this.protocolo.setEnte(ultimaEntidadeAdmissao.getEnteEnum());
		}
		
		
		Long idAnalise = analiseAcumulacao.getAnalise().getId();
		this.documentos = new ArrayList<>(analiseBusiness.retornaAnaliseDocumentos(idAnalise));
		
		PrimeFaces.current().ajax().update("formProtocolo");
	}

	public List<DocumentoAuditoriaDTO> getDocumentos() {
		return documentos;
	}

	public void setDocumentos(List<DocumentoAuditoriaDTO> documentos) {
		this.documentos = documentos;
	}

	
	
	public int getTabIndex() {
		return tabIndex;
	}

	public void setTabIndex(int tabIndex) {
		this.tabIndex = tabIndex;
	}

	public void addResponsavel() {
		ResponsavelSimplesPrestacaoContainer responsaveisContainer = protocolo.getResponsaveisContainer();
		if (responsaveisContainer != null && responsaveisContainer.getResponsaveis() != null
				&& !responsaveisContainer.getResponsaveis().contains(responsavel)) {

			String cpfCnpjResp = responsavel.getCpfCnpj();
			String nomResp = responsavel.getNome();
			cpfCnpjResp = formataCpfCnpj(cpfCnpjResp);

			if (cpfCnpjResp != null && !nomResp.equals(null) && !nomResp.isEmpty()) {
				responsavel.setCpfCnpj(cpfCnpjResp);
				responsaveisContainer.getResponsaveis().add(responsavel);
				resetaResponsavel();
			} else {
				facesContext.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR,
						"Informe os dados corretos para o responsável", ""));
			}

		}
	}

	public void resetaResponsavel() {
		responsavel = new ResponsavelSimplesPrestacao();
	}

	public void mascaraCpfCnpj() {
		String cpfCnpjInteressado = protocolo.getInteressado().getCpfCnpj();
		String cpfCnpjResponsavel = responsavel.getCpfCnpj();

		if (cpfCnpjInteressado != null) {
			cpfCnpjInteressado = formataCpfCnpj(cpfCnpjInteressado);
			protocolo.getInteressado().setCpfCnpj(cpfCnpjInteressado);
		}

		if (cpfCnpjResponsavel != null) {
			cpfCnpjResponsavel = formataCpfCnpj(cpfCnpjResponsavel);
			responsavel.setCpfCnpj(cpfCnpjResponsavel);
		}

	}

	public String gerar() {
		boolean valido = true;

		ResponsavelSimplesPrestacao interessado = protocolo.getInteressado();
		ResponsavelSimplesPrestacaoContainer responsaveisContainer = protocolo.getResponsaveisContainer();
		if (interessado.getCpfCnpj().isEmpty() && interessado.getNome().isEmpty()
				&& responsaveisContainer.getResponsaveis().isEmpty()) {
			valido = false;
			facesContext.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR,
					"Informe os dados do interessado ou responsável(eis)", ""));
		}
		if (valido) {
			RespostaProcessoWS retorno = protocoloBusiness.gerarProtocolo(protocolo, analiseAcumulacao.getAnalise().getId(), documentos);

			if (retorno.getStatus().equals("RESPONSE")) {
				facesContext.getExternalContext().getFlash().setKeepMessages(true);
				facesContext.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, "Processo Criado", "A Análise da Acumulação nº " + analiseAcumulacao.getAcumulacao().getId() + " foi protocolada com sucesso." ));
				return "index?faces-redirect=true";
			} else {
				facesContext.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, retorno.getObject(), ""));
			}
		}
		return null;

	}

	private String formataCpfCnpj(String valor) {
		valor = valor.replaceAll("[^\\d]", "");
		switch (valor.length()) {
		case 11:
			return valor.substring(0, 3) + "." + valor.substring(3, 6) + "." + valor.substring(6, 9) + "-"
					+ valor.substring(9, 11);

		case 14:
			return valor.substring(0, 2) + "." + valor.substring(2, 5) + "." + valor.substring(5, 8) + "/"
					+ valor.substring(8, 12) + "-" + valor.substring(12, 14);

		default:
			return null;

		}

	}

	public Ente[] getEntes() {
		return Ente.values();
	}

	public List<Entidade> getEntidades() {
		return protocoloBusiness.retornaEntidades();
	}
}
