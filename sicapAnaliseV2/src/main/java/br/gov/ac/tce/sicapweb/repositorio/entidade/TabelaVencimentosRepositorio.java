package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.cargo.Cargo;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.remessa.RemessaEventual;
import br.gov.ac.tce.sicapweb.modelo.tabelavencimento.TabelaVencimentos;

@Stateless
public class TabelaVencimentosRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<TabelaVencimentos> pesquisaTabelasVencimentos(Entidade entidade, RemessaEventual remessaEventual,
			String nome, Cargo cargo) throws RepositorioException {
		Collection<TabelaVencimentos> listaTabelaVencimentos = null;

		try {
			UaiCriteria<TabelaVencimentos> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					TabelaVencimentos.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.innerJoin("listaCargos");

			if (remessaEventual.getId() != 0) {
				uaiCriteria.andEquals("remessaEventual", remessaEventual);
			}

			if (cargo.getId() != 0) {
				uaiCriteria.andEquals("listaCargos.cargo", cargo);
			}

			if ((nome != null) && (!nome.isEmpty())) {
				uaiCriteria.andStringLike("nome", "%" + nome + "%");
			}

			listaTabelaVencimentos = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro tabelaVencimentosRepositorio.pesquisaTabelasVencimentos.",
					e.getCause());
		}
		return listaTabelaVencimentos;
	}

	public TabelaVencimentos pesquisaTabelaVencimentosPorId(Entidade entidade, Long id) throws RepositorioException {
		TabelaVencimentos tabelaVencimentos = null;

		try {
			UaiCriteria<TabelaVencimentos> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					TabelaVencimentos.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("id", id);

			tabelaVencimentos = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro tabelaVencimentosRepositorio.pesquisaTabelaVencimentosPorId",
					e.getCause());
		}

		return tabelaVencimentos;
	}

}
