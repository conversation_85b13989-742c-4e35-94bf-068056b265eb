package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.transaction.Transactional;

import org.primefaces.PrimeFaces;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.Acumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao.DistribuicaoAcumulacao;
import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicapanalise.auditoria.business.DistribuicaoBusiness;
import br.gov.ac.tce.sicapanalise.controle.bean.LoginBean;
import br.gov.ac.tce.sicapanalise.util.Mensagem;
import br.gov.ac.tce.sicapanalise.util.MensagemType;

@Named
@ViewScoped
public class DistribuicaoAcumulacaoBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -478305503135991580L;

	@Inject
	private LoginBean loginBean;

	@Inject
	private DistribuicaoBusiness distribuicaoBusiness;

	@Inject
	private FacesContext contexto;

	private Collection<Acumulacao> listaAcumulacao;

	private Collection<DistribuicaoAcumulacao> listaDistribuicaoAcumulacao;

	private List<DistribuicaoAcumulacao> listaDistribuicaoAcumulacaoSelecionada;

	private Collection<Usuario> listaAuditor;

	private Usuario auditorGeral;

	private String despachoGeral;

	@PostConstruct
	void carregaListaDistribuicaoAcumulacao() {
		listaDistribuicaoAcumulacao = distribuicaoBusiness.listaDistribuicaoAcumulacoes(loginBean.getUsuario());
		listaAuditor = distribuicaoBusiness.listaUsuariosAuditoria();
	}

	public Collection<Acumulacao> getListaAcumulacao() {
		return listaAcumulacao;
	}

	public void setListaAcumulacao(Collection<Acumulacao> listaAcumulacao) {
		this.listaAcumulacao = listaAcumulacao;

		// Quando este atributo for setado, carregar a listaDistribuicaoAcumulacao de
		// acordo com a listaAcumulacao
		if (listaDistribuicaoAcumulacao == null) {
			listaDistribuicaoAcumulacao = new ArrayList<>();
		}
		listaDistribuicaoAcumulacao.clear();

		listaAcumulacao.forEach(a -> {
			DistribuicaoAcumulacao da = new DistribuicaoAcumulacao();
			da.setAcumulacao(a);
			listaDistribuicaoAcumulacao.add(da);
		});

	}

	public Collection<DistribuicaoAcumulacao> getListaDistribuicaoAcumulacao() {
		if (listaDistribuicaoAcumulacao == null) {
			listaDistribuicaoAcumulacao = distribuicaoBusiness.listaDistribuicaoAcumulacoes(loginBean.getUsuario());
		}
		return listaDistribuicaoAcumulacao;
	}

	public void setListaDistribuicaoAcumulacao(Collection<DistribuicaoAcumulacao> listaDistribuicaoAcumulacao) {
		this.listaDistribuicaoAcumulacao = listaDistribuicaoAcumulacao;
	}

	public Collection<Usuario> getListaAuditor() {
		return listaAuditor;
	}

	public void setListaAuditor(Collection<Usuario> listaAuditor) {
		this.listaAuditor = listaAuditor;
	}

	public Usuario getAuditorGeral() {
		return auditorGeral;
	}

	public void setAuditorGeral(Usuario auditorGeral) {
		this.auditorGeral = auditorGeral;
	}

	public String getDespachoGeral() {
		return despachoGeral;
	}

	public void setDespachoGeral(String despachoGeral) {
		this.despachoGeral = despachoGeral;
	}

	public List<DistribuicaoAcumulacao> getListaDistribuicaoAcumulacaoSelecionada() {
		return listaDistribuicaoAcumulacaoSelecionada;
	}

	public void setListaDistribuicaoAcumulacaoSelecionada(
			List<DistribuicaoAcumulacao> listaDistribuicaoAcumulacaoSelecionada) {
		this.listaDistribuicaoAcumulacaoSelecionada = listaDistribuicaoAcumulacaoSelecionada;
	}

	
	public long qtdDistribuicaoAcumulacaoAtivas() {
		long quantidade = 0;
		if (this.listaDistribuicaoAcumulacao != null) {
			quantidade = this.listaDistribuicaoAcumulacao.stream().filter(da -> da.getDataSaida() == null).count();
		}
		return quantidade;
	}

	public void temAcumulacaoSelecionada() {
		if (this.listaDistribuicaoAcumulacaoSelecionada == null
				|| this.listaDistribuicaoAcumulacaoSelecionada.isEmpty()) {
			Mensagem.setMensagem(MensagemType.ERRO, "Nenhuma Acumulação foi selecionada.", "");
		}
	}

	@Transactional
	public void iniciarDistribuicao() {

		defineAuditorParaAcumulacao();
		defineDespachoParaDistribuicao();
		defineUsuarioDaDistribuicao();

		Optional<DistribuicaoAcumulacao> erroDistribuicaoAcumulacao = this.listaDistribuicaoAcumulacao.stream()
				.filter(da -> da.getUsuarioAuditor() == null || da.getDespacho() == null || da.getDespacho().isEmpty())
				.findAny();

		if (erroDistribuicaoAcumulacao.isPresent()) {
			contexto.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Distribuição não Realizada",
					"Existe(m) acumulação(ões) sem despacho informado ou auditor responsável não definido."));

		} else {

			distribuicaoBusiness.iniciarDistribuicao(listaDistribuicaoAcumulacao);

			contexto.addMessage(null, new FacesMessage(FacesMessage.SEVERITY_INFO, "Distribuição Concluída",
					"As acumulações foram distribuídas para seus respectivos responsáveis com sucesso."));

			PrimeFaces.current().executeScript("PF('tblAcmVar').unselectAllRows()");
			// Fecha o modal(auditoria/distribuicao/distribuir.xhtml)
			PrimeFaces.current().executeScript("PF('dlgIniciarDistribuicaoVar').hide()");
			// Atualiza a tela(auditoria/acumulacao/index.xhtml)
			PrimeFaces.current().ajax().update(":masterDetail");

		}

	}

	public List<Acumulacao> retornaAcumulacoesSelecionadas() {
		return this.listaDistribuicaoAcumulacaoSelecionada.stream().map(da -> da.getAcumulacao())
				.collect(Collectors.toList());
	}
	
	
	public void defineAuditorParaAcumulacao() {
		if (this.listaDistribuicaoAcumulacao != null) {
			this.listaDistribuicaoAcumulacao.forEach(da -> {
				da.setUsuarioAuditor(auditorGeral);
			});
		}
	}

	public void defineDespachoParaDistribuicao() {
		if (this.listaDistribuicaoAcumulacao != null) {
			this.listaDistribuicaoAcumulacao.forEach(da -> {
				da.setDespacho(despachoGeral);
			});
		}
	}
	
	public void defineUsuarioDaDistribuicao() {
		if (this.listaDistribuicaoAcumulacao != null) {
			this.listaDistribuicaoAcumulacao.forEach(da -> {
				da.setUsuario(loginBean.getUsuario());
			});
		}
	}

}
