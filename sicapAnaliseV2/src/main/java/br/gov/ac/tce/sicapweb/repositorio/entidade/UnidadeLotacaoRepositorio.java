package br.gov.ac.tce.sicapweb.repositorio.entidade;

import java.io.Serializable;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import com.uaihebert.uaicriteria.UaiCriteria;
import com.uaihebert.uaicriteria.UaiCriteriaFactory;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.folha.ContraCheque;
import br.gov.ac.tce.sicapweb.modelo.pessoa.Beneficiario;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Municipio;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.Uf;
import br.gov.ac.tce.sicapweb.modelo.unidadelotacao.UnidadeLotacao;

@Stateless
public class UnidadeLotacaoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Collection<UnidadeLotacao> pesquisaTodos(Entidade entidade) throws RepositorioException {
		Collection<UnidadeLotacao> listaUnidadesLotacao = null;

		try {
			UaiCriteria<UnidadeLotacao> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					UnidadeLotacao.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.innerJoin("municipio");
			uaiCriteria.innerJoin("municipio.uf");

			uaiCriteria.orderByAsc("nome");

			listaUnidadesLotacao = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro unidadeLotacaoRepositorio.pesquisaUnidadeLotacao.", e.getCause());
		}
		return listaUnidadesLotacao;
	}

	public Collection<UnidadeLotacao> buscarPorFiltros(Entidade entidade, UnidadeLotacao unidadeLotacao, Uf uf,
			Municipio municipio, String nome) throws RepositorioException {
		Collection<UnidadeLotacao> listaUnidadesLotacao = null;

		try {
			UaiCriteria<UnidadeLotacao> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					UnidadeLotacao.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.innerJoin("municipio");
			uaiCriteria.innerJoin("municipio.uf");

			if (unidadeLotacao.getId() != 0) {
				uaiCriteria.andEquals("id", unidadeLotacao.getId());
			}

			if (municipio.getId() != 0) {
				uaiCriteria.andEquals("municipio", municipio);
			}

			if (uf.getId() != 0) {
				uaiCriteria.andEquals("municipio.uf", uf);
			}

			if ((nome != null) && (!nome.isEmpty())) {
				uaiCriteria.andStringLike("nome", "%" + nome + "%");
			}

			uaiCriteria.orderByAsc("nome");

			listaUnidadesLotacao = uaiCriteria.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro unidadeLotacaoRepositorio.buscarPorFiltros", e.getCause());
		}
		return listaUnidadesLotacao;
	}

	public UnidadeLotacao pesquisaPorId(Entidade entidade, Long id) throws RepositorioException {
		UnidadeLotacao unidadeLotacao = null;

		try {
			UaiCriteria<UnidadeLotacao> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					UnidadeLotacao.class);
			uaiCriteria.andEquals("entidade", entidade);
			uaiCriteria.andEquals("id", id);

			unidadeLotacao = uaiCriteria.getSingleResult();
		} catch (Exception e) {
			throw new RepositorioException("Erro unidadeLotacaoRepositorio.pesquisaPorId", e.getCause());
		}

		return unidadeLotacao;
	}

	public Collection<ContraCheque> listarLotacaoPorServidor(Beneficiario servidor) throws RepositorioException {
		Collection<ContraCheque> listaLotacao;

		try {
			UaiCriteria<ContraCheque> uaiCriteria = UaiCriteriaFactory.createQueryCriteria(this.entityManager,
					ContraCheque.class);
			uaiCriteria.andEquals("beneficiario", servidor);
			uaiCriteria.orderByAsc("ano");
			uaiCriteria.orderByAsc("mes");

			listaLotacao = uaiCriteria.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro unidadeLotacaoRepositorio.pesquisaPorId", e.getCause());
		}

		return listaLotacao;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> listaUnidadesPorCompetencia(Entidade entidade, Municipio municipio, Uf uf, String nome)
			throws RepositorioException {
		Collection<Object[]> listaUnidades;

		String sql;
		List<String> condicoes = new LinkedList<>();
		String where = "";

		try {
			sql = "SELECT * FROM auditoria.vw_lotacao_por_competencia lc";

			condicoes.add("lc.idEntidadeCjur = " + entidade.getIdEntidadeCjur());

			if (municipio.getId() != 0) {
				condicoes.add("lc.idMunicipio = " + municipio.getId());
			}

			if (uf.getId() != 0) {
				condicoes.add("lc.idUf = " + uf.getId());
			}

			if ((nome != null) && (!nome.isEmpty())) {
				condicoes.add("lc.nomeUnidade like '%" + nome + "%'");
			}

			if (condicoes.size() != 0) {
				where = "where " + String.join(" AND ", condicoes);
			}

			sql += where;

			Query query = this.entityManager.createNativeQuery(sql);

			listaUnidades = (Collection<Object[]>) query.getResultList();
		} catch (Exception e) {
			throw new RepositorioException("Erro unidadeLotacaoRepositorio.listaUnidadesPorCompetencia", e.getCause());
		}
		return listaUnidades;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> buscarPorIdUnidadesPorCompetencia(UnidadeLotacao unidadeLotacao)
			throws RepositorioException {
		Collection<Object[]> listaUnidades;

		try {

			Query query = this.entityManager
					.createNativeQuery("SELECT resultado.idRemessa, resultado.idUnidadeLotacao, "
							+ "resultado.ano, resultado.mes, resultado.nomeMes, " + "SUM(resultado.qtd) as quantidade "
							+ "FROM (SELECT DISTINCT lc.idBeneficiario, lc.idRemessa, lc.idUnidadeLotacao, "
							+ "lc.ano, lc.nomeMes, lc.mes, COUNT(1) as qtd "
							+ "FROM auditoria.vw_lotacao_por_competencia lc "
							+ "GROUP BY lc.idBeneficiario, lc.idRemessa, lc.idUnidadeLotacao, lc.ano, lc.nomeMes, "
							+ "lc.mes) resultado " + "WHERE resultado.idUnidadeLotacao = :id "
							+ "GROUP BY resultado.idRemessa, resultado.idUnidadeLotacao, resultado.nomeMes, resultado.ano, resultado.mes "
							+ "ORDER BY resultado.ano, resultado.mes");
			query.setParameter("id", unidadeLotacao.getId());

			listaUnidades = (Collection<Object[]>) query.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro unidadeLotacaoRepositorio.listaUnidadesPorCompetencia", e.getCause());
		}
		return listaUnidades;
	}

	@SuppressWarnings("unchecked")
	public Collection<Object[]> buscarServidoresPorRemessa(Long idRemessa, Long idUnidadeLotacao)
			throws RepositorioException {
		Collection<Object[]> listaServidores = null;

		try {
			Query query = this.entityManager
					.createNativeQuery("SELECT cadu.cpf, pf.nome, b.matricula, c.nome as nomeCargo "
							+ "FROM ContraCheque cc " + "INNER JOIN Beneficiario b " + "ON b.id = cc.idBeneficiario "
							+ "INNER JOIN CadastroUnico cadu " + "ON cadu.id = b.idCadastroUnico "
							+ "INNER JOIN PessoaFisica pf "
							+ "ON pf.idCadastroUnico = cadu.id AND pf.registroAtivo = 1 " + "LEFT JOIN Cargo c "
							+ "ON c.id = cc.idCargo " + "WHERE cc.idRemessa = ? AND cc.idUnidadeLotacao = ?"
							+ "GROUP BY cadu.cpf, pf.nome, b.matricula, c.nome " + "ORDER BY pf.nome");
			query.setParameter(1, idRemessa);
			query.setParameter(2, idUnidadeLotacao);

			listaServidores = ((Collection<Object[]>) query.getResultList());
		} catch (Exception e) {
			e.printStackTrace();
			throw new RepositorioException("Erro ContraChequeRepositorio.pesquisaPorServidores", e.getCause());
		}

		return listaServidores;
	}
}
