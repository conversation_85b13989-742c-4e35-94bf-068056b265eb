package br.gov.ac.tce.sicap.modelo.entidade.auditoria.acumulacao;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise.SolicitacaoDocumento;

@Entity
@Table(schema = "auditoria")
public class SolicitacaoDocumentoAcumulacao implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idAnaliseAcumulacao", nullable = false)
	private AnaliseAcumulacao analiseAcumulacao;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idSolicitacaoDocumento", nullable = false)
	private SolicitacaoDocumento solicitacaoDocumento;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idDetalhamentoAcumulacao", nullable = false)
	private DetalhamentoAcumulacao detalhamentoAcumulacao;
	@Column(nullable = false)
	private Long idUsuarioCjur;
	@Column(nullable = false)
	private Boolean envioFinalizado;
	@Enumerated(EnumType.STRING)
	private SituacaoBeneficiario situacaoAtualBeneficiario;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public AnaliseAcumulacao getAnaliseAcumulacao() {
		return analiseAcumulacao;
	}

	public void setAnaliseAcumulacao(AnaliseAcumulacao analiseAcumulacao) {
		this.analiseAcumulacao = analiseAcumulacao;
	}

	public SolicitacaoDocumento getSolicitacaoDocumento() {
		return solicitacaoDocumento;
	}

	public void setSolicitacaoDocumento(SolicitacaoDocumento solicitacaoDocumento) {
		this.solicitacaoDocumento = solicitacaoDocumento;
	}

	public DetalhamentoAcumulacao getDetalhamentoAcumulacao() {
		return detalhamentoAcumulacao;
	}

	public void setDetalhamentoAcumulacao(DetalhamentoAcumulacao detalhamentoAcumulacao) {
		this.detalhamentoAcumulacao = detalhamentoAcumulacao;
	}

	public Long getIdUsuarioCjur() {
		return idUsuarioCjur;
	}

	public void setIdUsuarioCjur(Long idUsuarioCjur) {
		this.idUsuarioCjur = idUsuarioCjur;
	}

	public Boolean getEnvioFinalizado() {
		return envioFinalizado;
	}

	public void setEnvioFinalizado(Boolean envioFinalizado) {
		this.envioFinalizado = envioFinalizado;
	}

	public SituacaoBeneficiario getSituacaoAtualBeneficiario() {
		return situacaoAtualBeneficiario;
	}

	public void setSituacaoAtualBeneficiario(SituacaoBeneficiario situacaoAtualBeneficiario) {
		this.situacaoAtualBeneficiario = situacaoAtualBeneficiario;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((analiseAcumulacao == null) ? 0 : analiseAcumulacao.hashCode());
		result = prime * result + ((detalhamentoAcumulacao == null) ? 0 : detalhamentoAcumulacao.hashCode());
		result = prime * result + ((envioFinalizado == null) ? 0 : envioFinalizado.hashCode());
		result = prime * result + ((idUsuarioCjur == null) ? 0 : idUsuarioCjur.hashCode());
		result = prime * result + ((situacaoAtualBeneficiario == null) ? 0 : situacaoAtualBeneficiario.hashCode());
		result = prime * result + ((solicitacaoDocumento == null) ? 0 : solicitacaoDocumento.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		SolicitacaoDocumentoAcumulacao other = (SolicitacaoDocumentoAcumulacao) obj;
		if (analiseAcumulacao == null) {
			if (other.analiseAcumulacao != null)
				return false;
		} else if (!analiseAcumulacao.equals(other.analiseAcumulacao))
			return false;
		if (detalhamentoAcumulacao == null) {
			if (other.detalhamentoAcumulacao != null)
				return false;
		} else if (!detalhamentoAcumulacao.equals(other.detalhamentoAcumulacao))
			return false;
		if (envioFinalizado == null) {
			if (other.envioFinalizado != null)
				return false;
		} else if (!envioFinalizado.equals(other.envioFinalizado))
			return false;
		if (idUsuarioCjur == null) {
			if (other.idUsuarioCjur != null)
				return false;
		} else if (!idUsuarioCjur.equals(other.idUsuarioCjur))
			return false;
		if (situacaoAtualBeneficiario != other.situacaoAtualBeneficiario)
			return false;
		if (solicitacaoDocumento == null) {
			if (other.solicitacaoDocumento != null)
				return false;
		} else if (!solicitacaoDocumento.equals(other.solicitacaoDocumento))
			return false;
		return true;
	}

}
