package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

public enum SituacaoSolicitacaoDocumento {
	AG("Aguardando"), PR("Parcialmente respondida"), RE("Respondida");

	private final String descricao;

	private SituacaoSolicitacaoDocumento(String descricao) {
		this.descricao = descricao;
	}

	public String getDescricao() {
		return descricao;
	}

//	public static SituacaoSolicitacaoDocumento parse(String id) {
//		SituacaoSolicitacaoDocumento situacaoSolicitacaoDocumento = null;
//		for (SituacaoSolicitacaoDocumento item : SituacaoSolicitacaoDocumento.values()) {
//			if (item.getId() == id) {
//				tipoAuditoria = item;
//				break;
//			}
//		}
//		return situacaoSolicitacaoDocumento;
//	}
}
