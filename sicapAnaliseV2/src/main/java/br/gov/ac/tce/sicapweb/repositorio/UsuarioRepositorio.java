package br.gov.ac.tce.sicapweb.repositorio;

import java.io.Serializable;
import java.sql.Connection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;
import br.gov.ac.tce.sicapweb.modelo.usuario.Usuario;

@Stateless
public class UsuarioRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	public Usuario buscaUsuarioPorLogin(String login) throws RepositorioException {
		Usuario usuario = null;
		// System.out.println("login: " + login);
		try {
			Query query = entityManager.createNativeQuery(
					"select p.id, p.nome, pf.numeroCpf, p.email from comum.Pessoa p inner join comum.PessoaFisica pf on pf.id = p.id where pf.numeroCpf = :login");
			query.setParameter("login", login);

			Object[] resultado = (Object[]) query.getSingleResult();
			usuario = new Usuario();
			usuario.setId((Integer) resultado[0]);
			usuario.setNome((String) resultado[1]);
			usuario.setLogin((String) resultado[2]);
			usuario.setEmail((String) resultado[3]);

		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscarUsuarioPorLogin.", e.getCause());
		}
		return usuario;
	}

	public Boolean validarAssinaturaRemessa(Integer id, String senha) throws RepositorioException {
		try {
			System.out.println("metodo errado.");
			Query query = entityManager
					.createNativeQuery("SELECT u.* FROM comum.usuario u WHERE u.id = :id AND u.senha = :senha");

			query.setParameter("id", id);
			query.setParameter("senha", senha);

			if (query.getSingleResult() != null) {
				return true;
			} else {
				return false;
			}

		} catch (Exception e) {
			throw new RepositorioException("Erro ao validarAssinaturaRemessa.", e.getCause());
		}
	}

	public Usuario buscarPorId(Integer idUsuario, Entidade entidade) throws RepositorioException {
		Usuario usuario = null;
		try {
			if ((idUsuario != null) && (entidade != null)) {
				Query query = entityManager.createNativeQuery(
						"SELECT p.id, p.nome, pf.numeroCpf, p.email, tr.descricao FROM PESSOAL.comum.Pessoa p INNER JOIN PESSOAL.comum.PessoaFisica pf ON pf.id = p.id INNER JOIN PESSOAL.cjur.Responsavel r ON r.usuario_id = p.id AND r.unidadeGestora_id = :idEntidade AND r.tipoResponsavel_id = 6 INNER JOIN PESSOAL.cjur.TipoResponsavel tr ON tr.id = r.tipoResponsavel_id WHERE pf.id = :idUsuario GROUP BY p.id, p.nome, pf.numeroCpf, p.email, tr.descricao");
				query.setParameter("idEntidade", entidade.getIdEntidadeCjur());
				query.setParameter("idUsuario", idUsuario);

				Object[] resultado = (Object[]) query.getSingleResult();
				usuario = new Usuario();
				usuario.setId((Integer) resultado[0]);
				usuario.setNome((String) resultado[1]);
				usuario.setLogin((String) resultado[2]);
				usuario.setEmail((String) resultado[3]);
				usuario.setTipoResponsavel((String) resultado[4]);
			}
		} catch (Exception e) {
			throw new RepositorioException("Erro ao buscar usuario por id.", e.getCause());
		}
		return usuario;
	}

}
