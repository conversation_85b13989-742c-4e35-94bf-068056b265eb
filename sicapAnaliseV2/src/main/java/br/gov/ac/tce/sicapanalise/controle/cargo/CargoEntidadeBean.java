package br.gov.ac.tce.sicapanalise.controle.cargo;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.message.MessageType;
import br.gov.ac.tce.message.Messenger;
import br.gov.ac.tce.sicapanalise.auditoria.business.EntidadeBusiness;
import br.gov.ac.tce.sicapanalise.auditoria.dto.CargoEntidadeDTO;
import br.gov.ac.tce.sicapanalise.repositorio.RepositorioException;
import br.gov.ac.tce.sicapweb.modelo.entidade.Entidade;

@Named
@ViewScoped
public class CargoEntidadeBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	@Inject
	private EntidadeBusiness entidadeBusiness;
	
	
	private List<Entidade> listaEntidades;
	private Collection<Object[]> listaCompetencias;
	
	private Collection<CargoEntidadeDTO> listaCargosEntidades;
	
	private String competencia;
	private Integer entidade = 0;
	
	
	@PostConstruct
	public void init(){
		try {
			listaEntidades = entidadeBusiness.listaEntidades();
			listaCompetencias = entidadeBusiness.listaCompetenciasContraCheque(entidade);
		} catch (Exception e) {
			System.out.println(e.getMessage());
		}
		
	}
	
	public void listarCompetencia() throws RepositorioException {
		if (entidade == null ) {
			entidade = 0;
		}
		listaCompetencias = entidadeBusiness.listaCompetenciasContraCheque(entidade);
	}
	
	public void pesquisar() {
		if (competencia == null || competencia.equals("0")) {
			Messenger.mostrarMensagem(MessageType.ERRO, "Selecione uma competência.");
		} else {
			Integer mes = Integer.valueOf(competencia.split("/")[0]);
			Integer ano = Integer.valueOf(competencia.split("/")[1]);
			listaCargosEntidades = entidadeBusiness.listaCargosPorEntidade(entidade, ano, mes);
		}
		
	}
	

	public List<Entidade> getListaEntidades() {
		return listaEntidades;
	}

	public void setListaEntidades(List<Entidade> listaEntidades) {
		this.listaEntidades = listaEntidades;
	}

	public Collection<Object[]> getListaCompetencias() {
		return listaCompetencias;
	}

	public void setListaCompetencias(Collection<Object[]> listaCompetencias) {
		this.listaCompetencias = listaCompetencias;
	}

	
	public Collection<CargoEntidadeDTO> getListaCargosEntidades() {
		return listaCargosEntidades;
	}

	public void setListaCargosEntidades(Collection<CargoEntidadeDTO> listaCargosEntidades) {
		this.listaCargosEntidades = listaCargosEntidades;
	}

	public String getCompetencia() {
		return competencia;
	}

	public void setCompetencia(String competencia) {
		this.competencia = competencia;
	}

	public Integer getEntidade() {
		return entidade;
	}

	public void setEntidade(Integer entidade) {
		this.entidade = entidade;
	}
	
	
}
