package br.gov.ac.tce.sicapanalise.controle.bean.auditoria;

import java.io.Serializable;
import java.util.Collection;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import br.gov.ac.tce.sicapanalise.auditoria.dto.NotificacaoDTO;
import br.gov.ac.tce.sicapanalise.auditoria.repositorio.NotificacaoRepositorio;

@Named
@ViewScoped
public class NotificacaoEmitidaBean  implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	@Inject
	private NotificacaoRepositorio notifRepo;
	private Collection<NotificacaoDTO> notificacoes;
	
	@PostConstruct
	void inicializado() {
		this.notificacoes = notifRepo.listaNotificacoes();
	}

	public Collection<NotificacaoDTO> getNotificacoes() {
		return notificacoes;
	}

	public void setNotificacoes(Collection<NotificacaoDTO> notificacoes) {
		this.notificacoes = notificacoes;
	}
	
	
	
}
