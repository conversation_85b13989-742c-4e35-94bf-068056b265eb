package br.gov.ac.tce.sicapanalise.repositorio;

import java.io.Serializable;
import java.util.Collection;

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

@Stateless
public class UsuarioAutorizacaoRepositorio implements Serializable {

	private static final long serialVersionUID = 1L;

	@PersistenceContext(unitName = "sicapAnalise")
	private EntityManager entityManager;

	@SuppressWarnings("unchecked")
	public Collection<Object[]> listaUsuariosAutorizados() throws RepositorioException {
		Collection<Object[]> listaUsuariosAutorizados = null;
		try {
			Query query = this.entityManager
					.createNativeQuery("select login from auditoria.usuarioAutorizacao ORDER BY login");

			listaUsuariosAutorizados = (Collection<Object[]>) query.getResultList();

		} catch (Exception e) {
			throw new RepositorioException("Erro ao consultaUsuarios.", e.getCause());
		}
		return listaUsuariosAutorizados;
	}
}
