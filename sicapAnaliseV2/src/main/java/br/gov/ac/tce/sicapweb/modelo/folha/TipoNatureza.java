package br.gov.ac.tce.sicapweb.modelo.folha;

public enum TipoNatureza {

	CREDITO(String.valueOf("C"), "Crédito"), DEBITO(String.valueOf("D"), "Débito");

	private String id;
	private String descricao;

	private TipoNatureza(String id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public String getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoNatureza parse(String id) {
		TipoNatureza tipoNatureza = null;
		for (TipoNatureza item : TipoNatureza.values()) {
			if (item.getId().equals(id)) {
				tipoNatureza = item;
				break;
			}
		}
		return tipoNatureza;
	}

}
