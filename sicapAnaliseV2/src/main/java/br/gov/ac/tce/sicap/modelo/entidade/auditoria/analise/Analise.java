package br.gov.ac.tce.sicap.modelo.entidade.auditoria.analise;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import br.gov.ac.tce.sicap.modelo.entidade.auditoria.responsavel.Usuario;
import br.gov.ac.tce.sicap.modelo.entidade.notificacao.Notificacao;

@Entity
@Table(schema = "auditoria")
public class Analise implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;
	@Column(length = 25)
	private String numeroProcessoEletronico;
	@Column(length = 35, nullable = false)
	private String usuarioResponsavel;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoAnalise", nullable = false)
	private TipoAnalise tipoAnalise;
	@Column(length = 2, nullable = false)
	private String situacao;
	@Column(nullable = false)
	private LocalDateTime dataCriacao;
//	@Column(nullable = false)
//	private Long idUsuario;
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idUsuario", nullable = false)
	private Usuario usuario;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "idTipoSituacaoAnalise", nullable = true)
	private TipoSituacaoAnalise situacaoAnalise;

	@OneToMany(mappedBy = "analise", fetch = FetchType.LAZY)
	private Set<HistoricoAnalise> listaHistoricoAnalise;
	@OneToMany(mappedBy = "analise", fetch = FetchType.LAZY)
	private Collection<Notificacao> listaNotificacao = new HashSet<>();


	@OneToMany(mappedBy = "analise", fetch = FetchType.LAZY)
	private Set<SolicitacaoDocumento> listaSolicitacaoDocumento = new HashSet<>();
	@OneToMany(mappedBy = "analise", fetch = FetchType.LAZY)
	private Set<RelatorioAnalise> listaRelatorioAnalise = new HashSet<>();

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getNumeroProcessoEletronico() {
		return numeroProcessoEletronico;
	}

	public void setNumeroProcessoEletronico(String numeroProcessoEletronico) {
		this.numeroProcessoEletronico = numeroProcessoEletronico;
	}

	public String getUsuarioResponsavel() {
		return usuarioResponsavel;
	}

	public void setUsuarioResponsavel(String usuarioResponsavel) {
		this.usuarioResponsavel = usuarioResponsavel;
	}

	public TipoAnalise getTipoAnalise() {
		return tipoAnalise;
	}

	public void setTipoAnalise(TipoAnalise tipoAnalise) {
		this.tipoAnalise = tipoAnalise;
	}

	public String getSituacao() {
		return this.situacao;
	}

	public void setSituacao(String situacao) {
		this.situacao = situacao;
	}

	public LocalDateTime getDataCriacao() {
		return dataCriacao;
	}

	public void setDataCriacao(LocalDateTime dataCriacao) {
		this.dataCriacao = dataCriacao;
	}

	public Usuario getIdUsuario() {
		return usuario;
	}

	public void setUsuario(Usuario usario) {
		this.usuario = usario;
	}

	public Collection<HistoricoAnalise> getListaHistoricoAnalise() {
		return listaHistoricoAnalise;
	}

	public void setListaHistoricoAnalise(Set<HistoricoAnalise> listaHistoricoAnalise) {
		this.listaHistoricoAnalise = listaHistoricoAnalise;
	}

//	public Collection<AnaliseAcumulacao> getListaAnaliseAcumulacao() {
//		return listaAnaliseAcumulacao;
//	}
//
//	public void setListaAnaliseAcumulacao(HashSet<AnaliseAcumulacao> listaAnaliseAcumulacao) {
//		this.listaAnaliseAcumulacao = listaAnaliseAcumulacao;
//	}

	public Collection<SolicitacaoDocumento> getListaSolicitacaoDocumento() {
		return listaSolicitacaoDocumento;
	}

	public void setListaSolicitacaoDocumento(HashSet<SolicitacaoDocumento> listaSolicitacaoDocumento) {
		this.listaSolicitacaoDocumento = listaSolicitacaoDocumento;
	}

	public Collection<Notificacao> getListaNotificacao() {
		return listaNotificacao;
	}

	public void setListaNotificacao(Collection<Notificacao> listaNotificacao) {
		this.listaNotificacao = listaNotificacao;
	}

	public TipoSituacaoAnalise getSituacaoAnalise() {
		return situacaoAnalise;
	}

	public void setSituacaoAnalise(TipoSituacaoAnalise situacaoAnalise) {
		this.situacaoAnalise = situacaoAnalise;
	}

	public Set<RelatorioAnalise> getListaRelatorioAnalise() {
		return listaRelatorioAnalise;
	}

	public void setListaRelatorioAnalise(Set<RelatorioAnalise> listaRelatorioAnalise) {
		this.listaRelatorioAnalise = listaRelatorioAnalise;
	}


	public Collection<SolicitacaoDocumento> getListaSolicitacaoDocumentoEntregue() {
		Collection<SolicitacaoDocumento> listaSolicitacaoDocumentoEntregue = null;

		listaSolicitacaoDocumentoEntregue = listaSolicitacaoDocumento.stream().filter(
				sd -> sd.getSituacao() == SituacaoSolicitacaoDocumento.RE )
				.collect(Collectors.toList());

		return listaSolicitacaoDocumentoEntregue;
	}
	

	public String getCorTexto() {

		for (SolicitacaoDocumento solicitacaoDocumento : listaSolicitacaoDocumento) {
			if (solicitacaoDocumento.getSituacao() != SituacaoSolicitacaoDocumento.RE) {

				if (solicitacaoDocumento.getDataPrazoResposta().isBefore(LocalDateTime.now()))
					return "corTextoVermelho";

				if (solicitacaoDocumento.getDataPrazoResposta().isAfter(LocalDateTime.now()))
					return "corTextoLaranja";
			}
		}

		if (!listaSolicitacaoDocumento.isEmpty()) {
			return "corTextoVerde";
		}

		return null;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Analise other = (Analise) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		return true;
	}

}
