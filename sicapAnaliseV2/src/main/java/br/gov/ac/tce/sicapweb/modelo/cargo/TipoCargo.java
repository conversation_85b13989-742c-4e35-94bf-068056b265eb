package br.gov.ac.tce.sicapweb.modelo.cargo;

public enum TipoCargo {
	EFETIVO(Integer.valueOf(1), "Efetivo"), COMISSIONADO(Integer.valueOf(2),
			"Comissionado"), TEMPORARIO(Integer.valueOf(3), "Temporário");

	private Integer id;
	private String descricao;

	private TipoCargo(Integer id, String descricao) {
		this.id = id;
		this.descricao = descricao;
	}

	public Integer getId() {
		return id;
	}

	public String getDescricao() {
		return descricao;
	}

	public static TipoCargo parse(Integer id) {
		TipoCargo tipoCargo = null;
		for (TipoCargo item : TipoCargo.values()) {
			if (item.getId() == id) {
				tipoCargo = item;
				break;
			}
		}
		return tipoCargo;
	}
}
