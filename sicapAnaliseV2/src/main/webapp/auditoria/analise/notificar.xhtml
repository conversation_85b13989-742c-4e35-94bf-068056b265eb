<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">


	<!-- 	<p:panel header="Informações da Notificação" id="notifInfo"> -->

	<h:form>

		<!-- 			<p:panel header="Informações da Notificação"> -->
		<p:fieldset legend="Parâmetros para notificação">
			<div class="ui-g">
				<div class="ui-g-12 ui-lg-6">
					<h:panelGroup>
						<h:outputText for="prazoDias" value="Prazo(Dias):"
							styleClass="FontBold" />
						<p:inputNumber id="prazoDias"
							validatorMessage="O prazo deve ser informado." decimalPlaces="0"
							value="#{notificarAnaliseBean.notificacao.prazoDias}">
							<f:validateLongRange minimum="1" />
							<p:ajax event="blur" update="dataPrazoResposta" />
						</p:inputNumber>
						<p:message for="prazoDias" display="text" />
					</h:panelGroup>
				</div>
				<div class="ui-g-12 ui-lg-6">
					<h:panelGroup>
						<h:outputText value="Data Fim do Prazo:" styleClass="FontBold" />
						<p:inputText id="dataPrazoResposta"
							value="#{notificarAnaliseBean.notificacao.dataPrazoResposta.toLocalDate()}"
							readonly="true">
							<f:converter converterId="converter.DateConverter" />
						</p:inputText>
					</h:panelGroup>
				</div>
			</div>
			<div class="ui-g">
				<div class="ui-g-12">
					<h:panelGroup>
						<h:outputText for="despachoNotificacao" value="Despacho:"
							styleClass="FontBold" />
						<p:inputTextarea id="despachoNotificacao" rows="2"
							validatorMessage="O despacho deve conter no mínimo 30 caracteres."
							value="#{notificarAnaliseBean.notificacao.despacho}">
							<f:validateLength minimum="30" />
						</p:inputTextarea>
						<p:message for="despachoNotificacao" display="text" />
					</h:panelGroup>
				</div>
			</div>

			<div class="ui-g">
				<div class="ui-g-12">
					<p:commandButton icon="fa fa-fw fa-check white" update="@form"
						style="width: auto;" value="Confirmar" process="@form"
						action="#{notificarAnaliseBean.notificarAnalise()}">
						<f:setPropertyActionListener value="#{loginBean.usuario}"
							target="#{notificarAnaliseBean.notificacao.usuario}" />

					</p:commandButton>
					<p:commandButton type="button" styleClass="RedButton"
						style="width: auto;" onclick="PF('dlgNotifAnlVar').hide()"
						icon="fa fa-fw fa-close white" value="Cancelar">

					</p:commandButton>
				</div>
			</div>
		</p:fieldset>
	</h:form>
	<div class="EmptyBox10" />
	<h:form>
		<p:fieldset legend="Acumulações a notificar">
		<p:dataTable var="acumulacao" id="tblAcmNotif"
			value="#{notificarAnaliseBean.acumulacoes}" rows="5"
			paginatorAlwaysVisible="false" paginator="true"
			tableStyle="table-layout: auto" reflow="true"
			emptyMessage="Nenhuma acumulação foi selecionada"
			paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
			rowsPerPageTemplate="5,10,15"
			currentPageReportTemplate="página {currentPage} de {totalPages}"
			paginatorPosition="bottom">

			<p:column headerText="Cód. Acumulação" width="5%"
				styleClass="TexAlCenter">
				<h:outputText value="#{acumulacao.acumulacaoId}" />
			</p:column>
			<p:column headerText="CPF" styleClass="TexAlCenter" width="15%">
				<h:outputText value="#{acumulacao.acumulacaoCpf}">
					<f:converter converterId="converter.CpfConverter" />
				</h:outputText>
			</p:column>
			<p:column headerText="Nome">
				<h:outputText value="#{acumulacao.acumulacaoNome}" />
			</p:column>
			<p:column headerText="Qtd. Vinculos" styleClass="TexAlCenter">
				<h:outputText value="#{acumulacao.acumulacaoQuantidadeVinculos}" />
			</p:column>
		</p:dataTable>
		</p:fieldset>
		<p:ajaxStatus onstart="PF('statusAjaxDialog').show();"
						onsuccess="PF('statusAjaxDialog').hide();" />
	</h:form>
	
</ui:composition>