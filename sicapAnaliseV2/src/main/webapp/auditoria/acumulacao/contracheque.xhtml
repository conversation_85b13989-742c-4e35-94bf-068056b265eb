<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">


	<h:form id="frmContraCheque">
		<p:panel>
			<div class="ui-g">
				<div class="ui-g-12">
					<h:outputText value="CPF: " styleClass="FontBold" />
					<h:outputText
						value="#{auditoriaContraChequeBean.contraCheque[0].cpf}">
						<f:converter converterId="converter.CpfConverter" />
					</h:outputText>
					<h:outputText
						value=" - #{auditoriaContraChequeBean.contraCheque[0].nome}" />
				</div>
			</div>
			<div class="ui-g">
				<div class="ui-g-12">
					<h:outputText value="Competência: " styleClass="FontBold" />
					<h:outputText
						value="#{auditoriaContraChequeBean.contraCheque[0].mes}/#{auditoriaContraChequeBean.contraCheque[0].ano}" />
				</div>
			</div>
			<div class="ui-g">
				<div class="ui-g-12 ui-lg-6">
					<h:outputText
						value="#{auditoriaContraChequeBean.contraCheque[0].entidadeNome.toUpperCase()}" />
				</div>
				<div class="ui-g-12 ui-lg-6 TexAlRight">
					<h:outputText value="Matrícula: " styleClass="FontBold" />
					<h:outputText
						value="#{auditoriaContraChequeBean.contraCheque[0].matricula}" />
				</div>
			</div>
			<p:separator />
			<div class="ui-g">
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<h:panelGroup>
						<h:outputText value="Folha" styleClass="FontBold" />
						<p:inputText readonly="true"
							value="#{auditoriaContraChequeBean.contraCheque[0].tipoFolha}" />
					</h:panelGroup>
				</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<h:panelGroup>
						<h:outputText value="Total Vencimentos" styleClass="FontBold" />
						<p:inputText readonly="true"
							value="#{auditoriaContraChequeBean.contraCheque[0].totalVencimentos}"
							style="float: right;">
							<f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
						</p:inputText>
					</h:panelGroup>
				</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<h:panelGroup>
						<h:outputText value="Total Descontos" styleClass="FontBold" />
						<p:inputText readonly="true"
							value="#{auditoriaContraChequeBean.contraCheque[0].totalDescontos}">
							<f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
						</p:inputText>
					</h:panelGroup>
				</div>
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<h:panelGroup>
						<h:outputText value="Total Líquido" styleClass="FontBold" />
						<p:inputText readonly="true"
							value="#{auditoriaContraChequeBean.valorLiquido}">
							<f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
						</p:inputText>
					</h:panelGroup>
				</div>
			</div>
			<div class="ui-g">
				<div class="ui-g-12">
					<p:dataTable tableStyle="table-layout: auto" reflow="true"
						value="#{auditoriaContraChequeBean.contraCheque}" var="vcc">
						<f:facet name="header">
							<h:outputText value="Verbas" />
						</f:facet>
						
						<p:column headerText="Código">
							<h:outputText value="#{vcc.verbaCodigo}" style="float:right" />
						</p:column>
						<p:column headerText="Nome">
							<h:outputText value="#{vcc.verbaDescricao}" />
						</p:column>
						<p:column headerText="Natureza">
							<h:outputText
								value="#{vcc.verbaNatureza == 'C' ? 'Crédito' : 'Débito'}" />
						</p:column>
						<p:column headerText="Referência">
							<h:outputText value="#{vcc.verbaReferencia}" style="float:right" />
						</p:column>
						<p:column headerText="Valor R$">
							<h:outputText value="#{vcc.verbaValor}" style="float:right">
								<f:convertNumber pattern="R$ ###,##0.00" locale="pt_br" />
							</h:outputText>
						</p:column>
					</p:dataTable>
				</div>
			</div>
		</p:panel>
	</h:form>
</ui:composition>
