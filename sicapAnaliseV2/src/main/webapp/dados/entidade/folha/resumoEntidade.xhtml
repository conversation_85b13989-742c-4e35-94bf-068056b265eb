
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
		<h:form prependId="false">
			<p:messages id="msg" />

			<p:fieldset
				legend="Dados para consulta por resumo de folha e entidade">

				<div class="ui-g">

					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Competência: " styleClass="FontBold" />
							<p:selectOneMenu value="#{entidadeBean.ano}">
								<f:selectItem itemLabel="Selecione um Competência"
									itemValue="#{null}" noSelectionOption="true" />
								<f:selectItems value="#{entidadeBean.listaCompetenciaAno()}"
									var="competencia" itemLabel="#{competencia}"
									itemValue="#{competencia}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>

				<div class="ui-g">
					<div class="ui-g-12">

						<p:dataTable id="tblEntidade" var="entidade"
							rowKey="#{entidade.idEntidadeCjur}"
							tableStyle="table-layout: auto;" reflow="true"
							selection="#{entidadeBean.listaEntidadeSelecionadas}"
							value="#{entidadeBean.listaEntidade}"
							emptyMessage="Nenhuma entidade encontrada." rows="10"
							paginatorAlwaysVisible="false" paginator="true"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column selectionMode="multiple"
								style="width:30px;text-align:center;">

							</p:column>

							<p:column headerText="Nome"
								filterBy="#{entidade.nome}#{utilBean.removeAcentos(entidade.nome)}"
								filterMatchMode="contains" filterStyle="width: 50% !important;">
								<h:outputText value="#{entidade.nome}" />
							</p:column>

							<p:column headerText="Ente"
								filterBy="#{entidade.ente}#{utilBean.removeAcentos(entidade.ente)}"
								filterMatchMode="contains" filterStyle="width: 100% !important;">
								<h:outputText value="#{entidade.ente}" />
							</p:column>

							<p:column headerText="Poder">
								<h:outputText value="#{entidade.poder}" />
							</p:column>

							<p:column headerText="Classificacao Administrativa">
								<h:outputText value="#{entidade.classificacaoAdministrativa}" />
							</p:column>
						</p:dataTable>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width: auto;"
							action="#{entidadeBean.pesquisar()}"
							icon="fa fa-fw fa-search white"
							update="msg resumoEntidadeId:tblResumoAnalitico resumoEntidadeId:tblResumoSintetico" />
					</div>
				</div>

			</p:fieldset>
		</h:form>

		<div class="EmptyBox10"></div>

		<h:form prependId="false">
			<div class="OvAuto">
				<p:fieldset legend="Resultados da consulta">

					<p:tabView effect="drop" id="resumoEntidadeId"
						widgetVar="resumoEntidade">


						<p:tab title="Analítico">
							<div class="EmptyBox5"></div>
							<p:dataTable id="tblResumoAnalitico" var="resumo"
								value="#{entidadeBean.listaResumoPorEntidade}"
								sortBy="#{resumo.entidadeNome}" expandableRowGroups="true"
								emptyMessage="Nenhum registro encontrado." paginator="true"
								rows="20"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<f:facet name="header">
									<div align="left">
										<p:commandButton style="width: auto;" id="toggler" 
											type="button" value="Colunas" icon="fa fa-fw fa-list white" />
										<p:columnToggler datasource="tblResumoAnalitico"
											trigger="toggler" />
									</div>
								</f:facet>

								<f:facet name="{Exporters}">
									<h:commandLink style="float:left; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls" target="tblResumoAnalitico"
											fileName="resumoFolhaAnualAnalitico" />
									</h:commandLink>
								</f:facet>

								<p:headerRow>
									<p:column colspan="31" styleClass="FontBold" sortable="true">
										<h:outputText value="#{resumo.entidadeNome}" />
									</p:column>
								</p:headerRow>

								<p:column headerText="Matrícula" styleClass="TexAlCenter"
								sortBy="#{resumo.beneficiarioMatricula}"
									filterBy="#{resumo.beneficiarioMatricula}"
									filterFunction="#{entidadeBean.filtroSemAcento}" width="100">
									<h:outputText value="#{resumo.beneficiarioMatricula}" />
								</p:column>

								<p:column headerText="CPF" styleClass="TexAlCenter" width="100"
									sortBy="#{resumo.beneficiarioCpf}"
									filterStyle="width: 100% !important;"
									filterBy="#{resumo.beneficiarioCpf}"
									filterFunction="#{entidadeBean.filtroSemAcento}">
									<h:outputText value="#{resumo.beneficiarioCpf}">
										<f:converter converterId="converter.CpfConverter" />
									</h:outputText>
								</p:column>

								<p:column headerText="Nome" width="250"
									sortBy="#{resumo.beneficiarioNome}"
									filterBy="#{resumo.beneficiarioNome}"
									filterStyle="width: 100% !important;"
									filterFunction="#{entidadeBean.filtroSemAcento}">
									<h:outputText value="#{resumo.beneficiarioNome}" />
								</p:column>

								<p:column headerText="Sexo" styleClass="TexAlCenter" width="30">
									<h:outputText value="#{resumo.beneficiarioSexo}" />
								</p:column>

								<p:column headerText="Data de Nascimento"
									styleClass="TexAlCenter" width="80">
									<h:outputText value="#{resumo.beneficiarioDataNascimento}">
										<f:converter converterId="converter.DateConverter" />
									</h:outputText>
								</p:column>

								<p:column headerText="Cargo" width="200"
									sortBy="#{resumo.beneficiarioCargo}"
									filterBy="#{resumo.beneficiarioCargo}"
									filterStyle="width: 100% !important;"
									filterFunction="#{entidadeBean.filtroSemAcento}">
									<h:outputText value="#{resumo.beneficiarioCargo}" />
								</p:column>

								<p:column headerText="Tipo Cargo" width="100">
									<h:outputText value="#{resumo.beneficiarioCargoTipo}" />
								</p:column>

								<p:column headerText="Tipo Vínculo" width="200">
									<h:outputText value="#{resumo.vinculoTipo}" />
								</p:column>

								<p:column headerText="Entidade" width="200"
									filterBy="#{resumo.entidadeNome}"
									filterStyle="width: 100% !important;"
									filterFunction="#{entidadeBean.filtroSemAcento}">
									<h:outputText value="#{resumo.entidadeNome}" />
								</p:column>

								<p:column headerText="Regime Previdenciário" width="80"
									styleClass="TexAlCenter">
									<h:outputText value="#{resumo.regimePrevidenciario}" />
								</p:column>

								<p:column headerText="Lotação" width="200"
								filterBy="#{resumo.unidadeLotacao}"
								filterStyle="width: 100% !important;"
									filterFunction="#{entidadeBean.filtroSemAcento}">
									<h:outputText value="#{resumo.unidadeLotacao}" />
								</p:column>
								<p:column headerText="Município" width="100"
								filterBy="#{resumo.municipioLotacao}"
								filterStyle="width: 100% !important;"
									filterFunction="#{entidadeBean.filtroSemAcento}">
									<h:outputText value="#{resumo.municipioLotacao}" />
								</p:column>

								<p:column headerText="Data Admissão/Inicio Pensão"
									styleClass="TexAlCenter" width="95">
									<h:outputText value="#{resumo.dataAdmissaoInicioPensao}">
										<f:converter converterId="converter.DateConverter" />
									</h:outputText>
								</p:column>

								<p:column headerText="Situacão Funcional" width="100">
									<h:outputText value="#{resumo.situacaoFuncional}" />
								</p:column>

								<p:column headerText="Data da Última Situacão Funcional"
									width="80">
									<h:outputText value="#{resumo.dataSituacaoFuncional}">
										<f:converter converterId="converter.DateConverter" />
									</h:outputText>
								</p:column>

								<p:column headerText="Tipo de Pensão" width="100">
									<h:outputText value="#{resumo.pensaoTipo}" />
								</p:column>

								<p:column headerText="Ano" width="40">
									<h:outputText value="#{resumo.ano}" />
								</p:column>

								<p:column headerText="Janeiro" width="100">
									<h:outputText value="#{resumo.valorJaneiro}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Fevereiro" width="100">
									<h:outputText value="#{resumo.valorFevereiro}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Março" width="100">
									<h:outputText value="#{resumo.valorMarco}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Abril" width="100">
									<h:outputText value="#{resumo.valorAbril}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Maio" width="100">
									<h:outputText value="#{resumo.valorMaio}" style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Junho" width="100">
									<h:outputText value="#{resumo.valorJunho}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Julho" width="100">
									<h:outputText value="#{resumo.valorJulho}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Agosto" width="100">
									<h:outputText value="#{resumo.valorAgosto}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Setembro" width="100">
									<h:outputText value="#{resumo.valorSetembro}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Outubro" width="100">
									<h:outputText value="#{resumo.valorOutubro}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Novembro" width="100">
									<h:outputText value="#{resumo.valorNovembro}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Dezembro" width="100">
									<h:outputText value="#{resumo.valorDezembro}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="13º" width="100">
									<h:outputText value="#{resumo.valorDecimo}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

								<p:column headerText="Total" width="100">
									<h:outputText value="#{resumo.valorTotal}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>

							</p:dataTable>
						</p:tab>
						<p:tab title="Sintético">
							<p:dataTable id="tblResumoSintetico" var="entidade"
								value="#{entidadeBean.listaResumoSintetico}"
								emptyMessage="Nenhum registro encontrado." paginator="true"
								rows="20"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} {Exporters}"
								rowsPerPageTemplate="20,30,40,50,100,500"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorPosition="bottom">

								<f:facet name="{Exporters}">
									<h:commandLink style="float:left; padding:12px 6px"
										title="Exportar para o Excel">
										<p:graphicImage value="/resources/imagens/logoExcel.png"
											width="24" />
										<p:dataExporter type="xls" target="tblResumoSintetico"
											fileName="resumoFolhaAnualSintetico" />
									</h:commandLink>
								</f:facet>

								<p:column headerText="Entidade" styleClass="TexAlCenter"
									width="250">
									<h:outputText value="#{entidade}" />
								</p:column>

								<p:column headerText="Janeiro" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,1)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Fevereiro" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,2)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Março" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,3)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Abril" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,4)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Maio" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,5)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Junho" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,6)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Julho" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,7)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Agosto" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,8)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Setembro" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,9)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Outubro" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,10)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Novembro" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,11)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Dezembro" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,12)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="13º" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,13)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>
								<p:column headerText="Total" width="130">
									<h:outputText
										value="#{entidadeBean.getValorResumoSintetico(entidade,14)}"
										style="float: right;">
										<f:convertNumber type="currency" currencySymbol="R$" />
									</h:outputText>
								</p:column>


							</p:dataTable>
						</p:tab>
					</p:tabView>
				</p:fieldset>
			</div>
		</h:form>
	</ui:define>
</ui:composition>
