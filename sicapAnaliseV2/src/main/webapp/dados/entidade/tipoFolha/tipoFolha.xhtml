<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
<!-- 		<p:fieldset legend="Lista de Tipos de Folha"> -->
			<h:form prependId="false">
				<p:fieldset legend="Dados para a consulta por tipos de folha">
				
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Nome: " styleClass="FontBold" />
							<p:inputText value="#{tipoFolhaBean.nome}" />
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" icon="fa fa-fw fa-search white" style="width: auto;"
							actionListener="#{tipoFolhaBean.pesquisar()}"
							update="tabelaTipoFolha" />
					</div>
				</div>


				</p:fieldset>


			<div class="EmptyBox10"></div>

			<p:fieldset legend="Resultados da consulta">
						<p:dataTable id="tabelaTipoFolha" var="tipoFolha"
							widgetVar="tabelaTipoFolha"
							value="#{tipoFolhaBean.listaTipoFolha}"
							emptyMessage="Nenhum tipo de folha encontrado." rows="10"
							sortBy="#{tipoFolha.descricao}"
							paginator="true"
							paginatorAlwaysVisible="false"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50,100,300,500"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column headerText="Tipo de Folha" width="15%">
								<h:outputText value="#{tipoFolha.descricao}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de tipos de folha: #{tipoFolhaBean.listaTipoFolha.size()}" />
							</f:facet>

						</p:dataTable>
					</p:fieldset>

			</h:form>

	</ui:define>
</ui:composition>