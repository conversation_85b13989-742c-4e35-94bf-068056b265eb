PrimeFaces.locales['pt'] = {
                closeText: '<PERSON><PERSON><PERSON>',
                prevText: 'Anterior',
                nextText: '<PERSON>róxi<PERSON>',
                currentText: '<PERSON>ç<PERSON>',
                monthNames: ['Janeiro','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','Set<PERSON><PERSON>','Out<PERSON><PERSON>','Novembro','<PERSON>zemb<PERSON>'],
                monthNamesShort: ['<PERSON>','Fev','<PERSON>','Abr','<PERSON>','Jun', 'Jul','A<PERSON>','Set','Out','Nov','Dez'],
                dayNames: ['<PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>ua<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
                dayNamesShort: ['Dom','Seg','Ter','Qua','Qui','Sex','Sáb'],
                dayNamesMin: ['D','S','T','Q','Q','S','S'],
                weekHeader: 'Se<PERSON>',
                firstDay: 1,
                isRTL: false,
                showMonthAfterYear: false,
                yearSuffix: '',
                timeOnlyTitle: '<PERSON><PERSON> Ho<PERSON>',
                timeText: 'Tempo',
                hourText: 'Hora',
                minuteText: 'Minuto',
                secondText: 'Segundo',
                currentText: 'Data Atual',
                ampm: false,
                month: 'Mês',
                week: 'Semana',
                day: 'Dia',
                allDayText : 'Todo Dia'
};
