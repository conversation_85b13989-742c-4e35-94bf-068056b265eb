<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="titillium_webbold_italic" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="333" d="M29 0l71 313h279l-72 -313h-278zM158 528l194 906h293l-229 -906h-258z" />
<glyph unicode="&#x22;" horiz-adv-x="626" d="M223 895l119 514h256l-139 -514h-236zM567 895l119 514h256l-139 -514h-236z" />
<glyph unicode="#" d="M104 338l58 229h196l54 217h-197l57 230h197l84 338h238l-84 -338h213l84 338h237l-84 -338h197l-58 -230h-196l-53 -217h196l-57 -229h-197l-84 -338h-237l84 338h-213l-84 -338h-238l84 338h-197zM596 567h213l53 217h-213z" />
<glyph unicode="$" d="M180 57l45 199q106 -20 217 -33l168 375q-131 61 -185 135t-54 201q0 193 125 318.5t333 125.5q45 0 127 -8l95 209h176l-107 -240l127 -36l-51 -195q-80 12 -170 22l-160 -352q143 -66 191.5 -135.5t48.5 -198.5q0 -225 -139.5 -347t-372.5 -122q-33 0 -86 5l-100 -224 h-175l113 250zM612 215q106 4 160.5 49.5t54.5 147.5q0 41 -14 64.5t-59 49.5zM649 967q0 -43 14.5 -66.5t59.5 -52.5l133 297h-18q-88 0 -138.5 -49t-50.5 -129z" />
<glyph unicode="%" d="M221 12l869 1463l161 -62l-868 -1462zM293 1016q0 53 16 123q74 297 320 297q188 0 188 -176q0 -53 -16 -121q-74 -299 -320 -299q-188 0 -188 176zM494 1039.5q0 -35.5 27.5 -35.5t44 30.5t33.5 99t17 103.5t-27.5 35t-43 -29t-33.5 -98.5t-18 -105zM662 176 q0 53 16 123q73 297 319 297q188 0 189 -176q0 -53 -17 -121q-74 -299 -319 -299q-188 0 -188 176zM860 198.5q0 -34.5 27.5 -34.5t45 30.5t35 99t17.5 103.5t-27.5 35t-44 -29.5t-35 -99.5t-18.5 -104.5z" />
<glyph unicode="&#x26;" horiz-adv-x="1312" d="M129 340q0 170 99.5 285.5t301.5 197.5q-61 106 -61 219q0 190 113 282.5t299 92.5q360 0 360 -309q0 -145 -74.5 -229t-285.5 -168l155 -209q25 37 58.5 117.5t42.5 130.5h252q-23 -92 -89.5 -224.5t-123.5 -199.5l163 -191l-190 -164l-148 189q-190 -184 -438 -184.5 t-341 97t-93 267.5zM414 367q-1 -158 194 -158q66 0 142.5 36t119.5 79l-241 342q-215 -100 -215 -299zM743 1012q0 -90 41 -154q113 59 148 104.5t35 120.5q0 105 -90 105q-133 0 -134 -176z" />
<glyph unicode="'" horiz-adv-x="282" d="M223 895l119 514h256l-139 -514h-236z" />
<glyph unicode="(" horiz-adv-x="479" d="M104 270q0 182 50.5 379t119.5 346q152 322 263 471l51 66h256q-59 -88 -146.5 -255t-146.5 -310.5t-108.5 -338t-49.5 -339.5q0 -274 25 -473l8 -68h-262q-59 199 -60 522z" />
<glyph unicode=")" horiz-adv-x="481" d="M-66 -242q59 88 146.5 255t147 310.5t108.5 338t49 339.5q0 274 -25 474l-8 67h262q59 -199 60 -522q0 -182 -50.5 -379t-119.5 -346q-152 -322 -262 -471l-52 -66h-256z" />
<glyph unicode="*" horiz-adv-x="866" d="M332 938l215 160l-209 149l94 129l207 -149l80 248l153 -52l-77 -249h252v-152h-252l77 -246l-149 -45l-84 248l-211 -162z" />
<glyph unicode="+" d="M219 391v246h346v344h246v-344h350v-246h-350v-348h-246v348h-346z" />
<glyph unicode="," horiz-adv-x="358" d="M-109 -266l240 579h301l-313 -579h-228z" />
<glyph unicode="-" horiz-adv-x="546" d="M109 420l61 246h518l-61 -246h-518z" />
<glyph unicode="." horiz-adv-x="311" d="M16 0l72 313h279l-72 -313h-279z" />
<glyph unicode="/" horiz-adv-x="958" d="M76 41l882 1429l259 -90l-883 -1429z" />
<glyph unicode="0" d="M195 409.5q0 434.5 177 700.5t449 266q442 0 442.5 -434t-177 -700.5t-449.5 -266.5q-442 0 -442 434.5zM483 461q0 -135 34 -189.5t128 -54.5t173 114.5t118 268.5t39 289t-34 190.5t-129 55.5t-174 -115t-117 -269.5t-38 -289.5z" />
<glyph unicode="1" d="M397 1057l523 295h243l-311 -1352h-281l236 1020l-334 -184z" />
<glyph unicode="2" d="M129 0l55 229l408 355q196 170 274 257t78 159.5t-42 103.5t-132 31q-135 0 -299 -25l-51 -6l37 205q197 68 388 67.5t289.5 -76t98.5 -241.5t-83 -280.5t-265 -254.5l-373 -282h547l-60 -242h-870z" />
<glyph unicode="3" d="M121 49l59 193q233 -25 360.5 -25t205 60.5t77.5 166.5q0 59 -37.5 85t-138.5 28l-258 4l51 209l265 12q80 4 157.5 72t77.5 154q0 127 -184 127q-156 0 -297 -21l-53 -6l28 184q182 84 390 84t306.5 -69.5t98.5 -216t-54.5 -228.5t-191.5 -151q74 -43 101.5 -95.5 t27.5 -158.5q0 -215 -159.5 -348.5t-422.5 -133.5q-90 0 -192 18.5t-160 37.5z" />
<glyph unicode="4" d="M143 229l47 205l566 918h307l-571 -889h276l84 364h278l-83 -364h124l-55 -234h-123l-55 -239h-279l56 239h-572z" />
<glyph unicode="5" d="M127 63l63 213q238 -59 373 -59t215 74t80 164t-37 124.5t-102 34.5q-117 0 -203 -32l-31 -11l-204 47l227 736h805l-56 -242h-581l-121 -319q146 63 236 63q356 0 356 -328q0 -242 -153.5 -397.5t-385.5 -155.5q-106 0 -227 21.5t-188 44.5z" />
<glyph unicode="6" d="M203 457q0 156 52 315.5t145.5 295.5t242 222t324.5 86q135 0 290 -33l54 -12l-88 -227q-160 31 -322 31q-236 0 -354 -336q164 72 276 71q371 0 371 -356q0 -244 -144.5 -391.5t-410.5 -147.5q-205 0 -320.5 120t-115.5 362zM492 428q0 -74 39.5 -142.5t125.5 -68.5 q121 0 184.5 68.5t63.5 201.5q0 142 -166 142q-115 0 -200 -37l-29 -12q-18 -78 -18 -152z" />
<glyph unicode="7" d="M240 37l710 989l17 84h-564l56 242h844l-82 -355l-725 -1034z" />
<glyph unicode="8" d="M133 315q0 203 266 381q-43 43 -63.5 84t-21.5 58.5t-1 60.5q0 193 161 336t429 143q193 0 298.5 -78.5t105.5 -193.5q0 -152 -58.5 -241t-177.5 -169q106 -80 107 -252q0 -207 -183.5 -340t-425 -133t-339 86t-97.5 258zM438 359.5q0 -74.5 40 -110.5t143.5 -36 t181.5 67.5t78 151.5q0 51 -18.5 80t-69.5 51h-181q-74 -27 -124 -78t-50 -125.5zM602 932q0 -49 13.5 -75t54.5 -60h180q61 35 107.5 92t46.5 113.5t-40 95.5t-129.5 39t-161 -64.5t-71.5 -140.5z" />
<glyph unicode="9" d="M162 20l86 244q119 -31 266.5 -31t253.5 85t141 225l-53 -13q-182 -41 -250 -41q-176 0 -263 86.5t-87 276.5q0 184 141 344q70 80 190 130t269 50q195 0 289 -136t94 -380q0 -414 -225.5 -649.5t-548.5 -235.5q-135 0 -260 33zM530 891q0 -94 37 -123t107 -29 q131 0 250 23l43 8q12 68 12 123q0 227 -184 227q-106 0 -185.5 -67.5t-79.5 -161.5z" />
<glyph unicode=":" horiz-adv-x="323" d="M20 0l72 313h279l-72 -313h-279zM164 606l72 314h278l-72 -314h-278z" />
<glyph unicode=";" horiz-adv-x="358" d="M-111 -266l240 579h301l-313 -579h-228zM195 606l71 314h279l-72 -314h-278z" />
<glyph unicode="&#x3c;" d="M233 401v226l832 374v-278l-535 -203l535 -221v-279z" />
<glyph unicode="=" d="M238 178v248h909v-248h-909zM238 604v248h909v-248h-909z" />
<glyph unicode="&#x3e;" d="M317 20v279l535 221l-535 203v278l832 -374v-226z" />
<glyph unicode="?" horiz-adv-x="882" d="M246 0l78 344h278l-78 -344h-278zM336 1147l29 215q184 55 327 55q377 0 377 -319q0 -139 -65.5 -228.5t-243.5 -212.5q-111 -76 -127 -149l-19 -82h-258q0 100 17.5 192.5t191.5 217.5q121 84 167 132t46 106.5t-35.5 77.5t-107.5 19q-135 0 -258 -18z" />
<glyph unicode="@" horiz-adv-x="1964" d="M158 269.5q0 144.5 28 285.5q119 498 389 716t725 218q209 0 365 -47q289 -84 379 -320q45 -113 45 -239.5t-41 -278.5q-88 -338 -205 -481t-301 -143t-244 90q-68 -33 -184.5 -64t-209.5 -31t-159.5 49.5t-91.5 125.5q-33 98 -32.5 188t18.5 158q111 553 516 553 q98 0 500 -68q-135 -590 -146 -674q-8 -68 82 -67q59 0 106.5 87t88.5 251q51 199 51 276.5t-20.5 153t-81.5 131.5q-137 121 -478.5 121t-538 -180.5t-278.5 -524.5q-27 -115 -26.5 -204t15.5 -167t60.5 -147.5t114.5 -122.5q145 -109 414 -109l280 19l-53 -227 q-199 -20 -291 -21q-190 0 -341.5 54.5t-245 147.5t-150.5 218q-59 129 -59 273.5zM897 373q0 -162 86 -164h6q117 0 244 78q8 82 106 499q-131 29 -186 29q-166 0 -238 -319q-18 -76 -18 -123z" />
<glyph unicode="A" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43z" />
<glyph unicode="B" horiz-adv-x="1173" d="M133 0l322 1393h483q365 0 365 -301q0 -133 -57.5 -219.5t-172.5 -155.5q80 -47 106.5 -102.5t26.5 -139.5q0 -205 -142 -340t-407 -135h-524zM471 242h242q92 0 147 63.5t55 163.5q0 115 -133 115h-231zM604 819h211q84 0 141.5 60.5t57.5 154.5q0 117 -121 117h-211z " />
<glyph unicode="C" horiz-adv-x="1011" d="M203 379q0 299 100 551q55 139 135 246.5t204 174t278 66.5q150 0 301 -45l49 -16l-66 -209q-122 24 -282 24h-13q-127 0 -224 -112.5t-144 -286.5t-47 -373q0 -178 165 -178q158 0 285 21l47 6l-22 -219q-190 -54 -346 -54q-420 0 -420 404z" />
<glyph unicode="D" horiz-adv-x="1239" d="M133 0l322 1393h438q459 0 459 -424q0 -299 -111 -541q-125 -270 -342 -371q-121 -57 -266 -57h-500zM473 246h184q104 0 186.5 75.5t127.5 191.5t67.5 233.5t22.5 219t-56.5 141.5t-168.5 40h-156z" />
<glyph unicode="E" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819z" />
<glyph unicode="F" horiz-adv-x="976" d="M133 0l322 1393h802l-57 -246h-520l-96 -420h430l-60 -246h-428l-110 -481h-283z" />
<glyph unicode="G" horiz-adv-x="1163" d="M209 414q0 332 115 577q125 270 356 373q123 53 248 53t231.5 -20.5t163.5 -40.5l55 -21l-65 -205q-203 41 -383 41q-111 0 -198 -72.5t-134 -189.5q-98 -236 -98 -497q0 -190 205 -191l129 10l71 310h-131l55 233h412l-174 -745q-139 -54 -397 -54q-461 0 -461 439z" />
<glyph unicode="H" horiz-adv-x="1269" d="M133 0l322 1393h282l-129 -557h439l129 557h282l-321 -1393h-283l135 590h-438l-135 -590h-283z" />
<glyph unicode="I" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283z" />
<glyph unicode="J" horiz-adv-x="579" d="M12 -141l47 245q86 4 117 31t57 141l259 1117h282l-258 -1117q-47 -207 -110 -307q-33 -51 -89 -71q-104 -39 -305 -39z" />
<glyph unicode="K" horiz-adv-x="1105" d="M133 0l322 1393h282l-139 -602l131 16l361 586h309l-445 -699l129 -694h-297l-90 559l-155 -14l-125 -545h-283z" />
<glyph unicode="L" horiz-adv-x="864" d="M133 0l322 1393h282l-264 -1147h440l-55 -246h-725z" />
<glyph unicode="M" horiz-adv-x="1615" d="M133 0l322 1393h438l2 -1014l463 1014h446l-321 -1393h-283l266 1147h-12l-498 -1086h-266l-2 1086h-10l-262 -1147h-283z" />
<glyph unicode="N" horiz-adv-x="1333" d="M133 0l322 1393h448l74 -1092h24l248 1092h273l-322 -1393h-426l-90 1112h-25l-260 -1112h-266z" />
<glyph unicode="O" horiz-adv-x="1259" d="M289 907q82 238 239.5 374t389.5 136q452 0 452 -424v-2q0 -280 -84 -523t-244.5 -368t-382.5 -125q-452 0 -452 424q0 270 82 508zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61 q-102 -100 -161.5 -304t-59.5 -346.5z" />
<glyph unicode="P" horiz-adv-x="1120" d="M133 0l322 1393h401q227 0 328.5 -93.5t101.5 -300.5q0 -293 -160.5 -445.5t-443.5 -152.5h-174l-92 -401h-283zM565 647h154q129 0 202.5 91t73.5 257q0 78 -36.5 115t-122.5 37h-156z" />
<glyph unicode="Q" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q452 0 452 -424q0 -283 -84 -525.5t-252 -373.5q-45 -33 -69 -45l98 -276l-268 -88l-88 295q-42 -5 -62 -5q-209 0 -323.5 105.5t-114.5 320.5zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5 t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304t-59.5 -346.5z" />
<glyph unicode="R" horiz-adv-x="1144" d="M133 0l322 1393h401q221 0 325.5 -93.5t104.5 -294t-88 -335.5t-223 -189l112 -481h-282l-92 442h-195l-102 -442h-283zM573 684h183q110 0 174.5 88t64.5 194.5t-34.5 145.5t-120.5 39h-158z" />
<glyph unicode="S" horiz-adv-x="1013" d="M102 59l48 203q231 -41 363 -41t193.5 46t61.5 157q0 59 -36 91t-175 95.5t-198.5 139t-59.5 211.5q0 199 129 327.5t344 128.5q98 0 205.5 -19.5t165.5 -37.5l57 -21l-51 -200q-226 37 -369 37q-90 0 -142 -50.5t-52 -120t32.5 -101.5t180 -97.5t202 -138t54.5 -211.5 q0 -231 -142.5 -356.5t-384.5 -125.5q-96 0 -202.5 20.5t-165.5 41.5z" />
<glyph unicode="T" horiz-adv-x="933" d="M283 1147l57 246h897l-57 -246h-310l-264 -1147h-282l264 1147h-305z" />
<glyph unicode="U" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316z" />
<glyph unicode="V" horiz-adv-x="1136" d="M319 0l29 1393h283l-49 -1147h67l500 1147h283l-631 -1393h-482z" />
<glyph unicode="W" horiz-adv-x="1771" d="M293 0l63 1393h283l-86 -1133h29l462 1112h312l-68 -1112h29l459 1133h282l-596 -1393h-409l67 1012l-405 -1012h-422z" />
<glyph unicode="X" horiz-adv-x="1064" d="M2 0l524 702l-184 691h299l100 -490l336 490h297l-512 -693l193 -700h-297l-113 498l-346 -498h-297z" />
<glyph unicode="Y" horiz-adv-x="1032" d="M324 1393h286l99 -543l346 543h299l-574 -803l-139 -590h-262l139 602z" />
<glyph unicode="Z" horiz-adv-x="1003" d="M74 0l63 281l719 817l10 49h-528l57 246h856l-65 -285l-723 -813l-12 -49h536l-57 -246h-856z" />
<glyph unicode="[" horiz-adv-x="503" d="M-31 -250l414 1780h469l-57 -234h-181l-305 -1312h180l-57 -234h-463z" />
<glyph unicode="\" horiz-adv-x="991" d="M381 1438h287l270 -1438h-285z" />
<glyph unicode="]" horiz-adv-x="501" d="M-53 -250l57 234h180l305 1312h-180l58 234h462l-413 -1780h-469z" />
<glyph unicode="^" d="M113 641l397 711h233l398 -711h-287l-223 444l-232 -444h-286z" />
<glyph unicode="_" horiz-adv-x="978" d="M-66 -420l58 256h917l-57 -256h-918z" />
<glyph unicode="`" horiz-adv-x="1060" d="M500 1356l131 225l452 -184l-114 -203z" />
<glyph unicode="a" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387z" />
<glyph unicode="b" horiz-adv-x="1079" d="M119 45l319 1389h275l-113 -490q47 41 118 73t126 32q166 0 236.5 -95.5t70.5 -238.5q0 -322 -143.5 -530t-389.5 -208q-98 0 -223 16.5t-200 35.5zM436 231q90 -20 167 -20t130 51t82 129q55 158 55 291t-108 133q-41 0 -94.5 -25.5t-85.5 -52.5l-35 -24z" />
<glyph unicode="c" horiz-adv-x="837" d="M168 332q0 313 138 515t396 202q123 0 261 -50l45 -14l-70 -201q-158 27 -248 27q-117 0 -180.5 -128t-63.5 -299t156 -171l219 16l-22 -202q-149 -51 -299 -51q-1 0 -2 -1q-148 1 -239 95t-91 262z" />
<glyph unicode="d" horiz-adv-x="1081" d="M164 338q0 174 56 331.5t177 267.5t283 110q70 0 205 -33l41 -10l100 430h274l-331 -1434h-267l19 106q-41 -47 -118.5 -89t-143.5 -42q-145 0 -220 94.5t-75 268.5zM442 340q0 -131 105 -131q33 0 91 37t99 73l41 37l99 428q-119 29 -194 29t-131 -81t-83 -187.5 t-27 -204.5z" />
<glyph unicode="e" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-191 -57 -377 -57q-1 0 -2 -1q-185 0 -269 84q-85 84 -85 246zM477 602h84q141 0 197.5 31 t56.5 104q0 88 -102 88q-182 0 -236 -223z" />
<glyph unicode="f" horiz-adv-x="671" d="M53 -432l287 1218h-94l57 238h88l10 41q49 215 128 304t245 89q80 0 168 -37l27 -10l-49 -201q-72 14 -126 14q-1 1 -2 1q-53 0 -74 -40q-22 -40 -42 -122l-10 -39h215l-58 -233h-209l-184 -791l-129 -432h-248z" />
<glyph unicode="g" horiz-adv-x="991" d="M18 -190q0 96 69 158.5t181 117.5q-41 41 -41 95.5t31 103.5t61 77l31 29q-139 82 -139 256t116.5 288t307.5 114q88 0 209 -29l39 -8l299 12l-52 -221h-118q8 -45 8 -105q0 -158 -122 -267t-306 -109l-88 10q-31 -41 -31 -70t39 -41t154 -28q290 -43 290 -258 q0 -183 -132 -294.5t-361 -111.5q-444 0 -445 281zM268 -133q0 -43 45 -76t144 -33q241 0 241 143q0 28 -19.5 44t-44 22t-130 23.5t-119.5 21.5q-41 -22 -79 -62t-38 -83zM463 659q0 -114 143 -114q72 0 117 48t45 118q0 114 -141 114h-2q-72 0 -117 -47t-45 -119z" />
<glyph unicode="h" horiz-adv-x="1050" d="M113 0l329 1434h275l-115 -506q45 45 121 83t135 38q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-54 27.5q-70 0 -174 -92l-35 -31l-158 -688h-274z" />
<glyph unicode="i" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM371 1130l65 281h277l-64 -281h-278z" />
<glyph unicode="j" horiz-adv-x="497" d="M-143 -252q158 82 195.5 120t60.5 132l235 1024h275l-236 -1024q-18 -82 -29.5 -123t-36 -89t-47 -71.5t-73.5 -56.5q-70 -49 -258 -131zM367 1112l65 281h277l-64 -281h-278z" />
<glyph unicode="k" horiz-adv-x="956" d="M113 0l329 1434h275l-185 -797l70 14l260 371h301l-356 -492l135 -530h-280l-99 424l-82 -12l-94 -412h-274z" />
<glyph unicode="l" horiz-adv-x="499" d="M113 0l329 1434h275l-330 -1434h-274z" />
<glyph unicode="m" horiz-adv-x="1589" d="M113 0l235 1024h262l-10 -98q78 78 182 110q42 12 102 12q1 0 2 1q60 0 112 -37q54 -37 67 -74l14 -35q18 16 49 41t116 65t155 40q246 0 246 -289q0 -92 -39 -258l-115 -502h-274l114 481q37 154 37 211.5t-16.5 88t-54 30.5t-88 -29.5t-83.5 -60.5l-32 -29 q-14 -123 -31 -190l-115 -502h-274l110 481q37 154 37 213.5t-17.5 88t-55.5 28.5q-68 0 -170 -92l-33 -31l-158 -688h-274z" />
<glyph unicode="n" horiz-adv-x="1050" d="M113 0l235 1024h262l-12 -100q47 47 124 86t136 39q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-56 27.5q-72 0 -174 -92l-33 -31l-158 -688h-274z" />
<glyph unicode="o" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 149 -129 149h-2q-111 0 -178.5 -140.5t-67.5 -314.5z" />
<glyph unicode="p" horiz-adv-x="1079" d="M14 -430l334 1454h268l-20 -106q51 51 126 91t134 40q150 0 223.5 -95.5t73.5 -264.5t-56.5 -329.5t-177 -271.5t-282.5 -111q-92 0 -207 31l-41 12l-104 -450h-271zM440 240q119 -29 194 -29t131 81t82.5 187.5t26.5 204.5q0 131 -104 131q-33 0 -91 -37t-99 -73 l-41 -37z" />
<glyph unicode="q" horiz-adv-x="1077" d="M164 338q0 174 58.5 335t179 267.5t278.5 106.5q143 0 424 -52l90 -16l-324 -1409h-274l125 539q-12 -14 -35.5 -37t-92 -60t-134.5 -37q-143 0 -219 94.5t-76 268.5zM442 340q0 -131 105 -131q33 0 91 37t99 73l41 37l99 428q-121 29 -199 29q-100 0 -168 -152.5 t-68 -320.5z" />
<glyph unicode="r" horiz-adv-x="684" d="M113 0l235 1024h273l-37 -147q61 55 157.5 109t163.5 63l-80 -287q-98 -31 -239 -94l-49 -23l-150 -645h-274z" />
<glyph unicode="s" horiz-adv-x="884" d="M94 45l51 201q197 -37 340.5 -37t143.5 100q0 27 -29 42.5t-98 35.5q-145 41 -215 96.5t-70 157.5q0 190 125 298t301 108q96 0 188.5 -15.5t135.5 -32.5l45 -16l-49 -203q-212 33 -345 33q-61 0 -93.5 -28.5t-32.5 -66.5t28.5 -56.5t148 -55.5t180 -85t60.5 -156 q0 -193 -126 -291.5t-334 -98.5q-88 0 -177.5 17.5t-132.5 36.5z" />
<glyph unicode="t" horiz-adv-x="673" d="M203 190q0 89 30 216l91 385h-111l61 233h105l59 262h275l-60 -262h215l-57 -233h-213l-94 -410q-17 -70 -17 -115q0 -57 60 -56l139 9l-22 -196q-108 -47 -224 -47q-1 0 -2 -1q-114 1 -174 63t-61 152z" />
<glyph unicode="u" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289z" />
<glyph unicode="v" horiz-adv-x="948" d="M250 0l8 1024h268l-28 -797h37l350 797h276l-481 -1024h-430z" />
<glyph unicode="w" horiz-adv-x="1560" d="M217 0l59 1024h277l-66 -791h48l325 771h311l-41 -771h48l305 791h276l-418 -1024h-456l39 637l-258 -637h-449z" />
<glyph unicode="x" horiz-adv-x="880" d="M12 0l402 512l-160 512h278l80 -313l213 313h279l-404 -532l164 -492h-278l-80 301l-209 -301h-285z" />
<glyph unicode="y" horiz-adv-x="950" d="M209 -430l211 430h-170l8 1024h268l-28 -797h30l357 797h276l-680 -1454h-272z" />
<glyph unicode="z" horiz-adv-x="847" d="M70 0l51 229l514 562h-385l59 233h707l-58 -227l-516 -564h394l-60 -233h-706z" />
<glyph unicode="{" horiz-adv-x="589" d="M129 563l49 195q125 33 165 68.5t58 109.5l60 299q51 172 159.5 241.5t317.5 77.5l-45 -235q-84 -8 -125 -45t-57 -103l-49 -258q-29 -117 -83.5 -176t-160.5 -96q125 -70 125 -176q0 -55 -46 -211t-46 -193q0 -98 118 -98l-63 -235q-150 0 -238 67.5t-88 188.5 q0 43 43 203.5t43 201.5q0 102 -137 174z" />
<glyph unicode="|" horiz-adv-x="686" d="M322 -430v1864h274v-1864h-274z" />
<glyph unicode="}" horiz-adv-x="587" d="M-55 -279l45 236q84 8 125 45t57 102l49 258q29 117 83.5 176.5t160.5 96.5q-125 70 -125 176q0 55 46 211t46 192q0 98 -119 99l64 235q150 0 237.5 -67.5t87.5 -188.5q0 -43 -43 -203.5t-43 -201.5q0 -102 138 -174l-49 -195q-125 -33 -165 -68.5t-59 -109.5l-59 -299 q-51 -172 -159.5 -241.5t-317.5 -78.5z" />
<glyph unicode="~" d="M246 578q129 106 252 106q57 0 212.5 -45t196.5 -45q76 0 180 57l35 21l17 -219q-39 -39 -107.5 -72t-126 -33t-219.5 45t-201 45t-95 -20.5t-91 -40.5l-33 -19z" />
<glyph unicode="&#xa1;" horiz-adv-x="329" d="M-76 -410l230 906h258l-195 -906h-293zM190 711l72 313h279l-72 -313h-279z" />
<glyph unicode="&#xa2;" d="M299 407.5q0 38.5 18 104.5q53 211 172 323.5t287 122.5l47 209h221l-53 -215l144 -20l-70 -230q-145 6 -242.5 6.5t-156 -43.5t-86.5 -153q-10 -45 -10.5 -85t20.5 -67q37 -47 168 -47l221 9l-43 -230q-76 -12 -154 -18l-51 -226h-217l47 211q-90 6 -151.5 49.5t-84 107 t-24.5 108.5t-2 83.5z" />
<glyph unicode="&#xa3;" d="M154 0l53 233h194l101 441h-164l55 233h162l18 74q57 225 146.5 317.5t265.5 92.5q84 0 244 -39l49 -13l-64 -206q-129 16 -205.5 16t-106.5 -45t-56 -156l-10 -41h321l-55 -233h-320l-102 -441h188l140 37l-7 -235l-151 -35h-696z" />
<glyph unicode="&#xa5;" d="M211 256l57 242h297l10 45l-6 22h-284l53 242h143l-221 545h305l201 -473l364 473h365l-438 -545h145l-57 -242h-271l-18 -22l-12 -45h286l-55 -242h-287l-59 -256h-280l61 256h-299z" />
<glyph unicode="&#xa8;" horiz-adv-x="1052" d="M440 1231l64 276h227l-65 -276h-226zM838 1231l63 276h227l-65 -276h-225z" />
<glyph unicode="&#xa9;" horiz-adv-x="1312" d="M297 892q0 249 159.5 413.5t408.5 164.5t404.5 -168.5t155.5 -416.5t-155.5 -413t-403.5 -165t-408.5 168t-160.5 417zM424 888.5q0 -190.5 125 -324.5t312.5 -134t312 133t124.5 323.5t-125.5 326t-311 135.5t-311.5 -134.5t-126 -325zM616 889.5q0 191.5 62.5 263.5 t208.5 72q80 0 145 -25l19 -6l-15 -176q-72 12 -125 12t-68.5 -28.5t-15.5 -102.5q0 -154 72 -154l137 11l15 -168q-57 -37 -185.5 -37t-189 73.5t-60.5 265z" />
<glyph unicode="&#xab;" horiz-adv-x="987" d="M109 434l47 125l512 334l-76 -268l-232 -133l164 -158l-67 -246zM621 434l47 125l512 334l-76 -268l-232 -133l164 -158l-67 -246z" />
<glyph unicode="&#xad;" horiz-adv-x="546" d="M109 420l61 246h518l-61 -246h-518z" />
<glyph unicode="&#xae;" horiz-adv-x="1312" d="M297 891q0 250 159.5 414.5t407.5 164.5t404.5 -168.5t156.5 -416.5t-154.5 -413t-403.5 -165t-409.5 167t-160.5 417zM424 888.5q0 -188.5 127 -323.5t312.5 -135t310 134t124.5 323.5t-125.5 325t-311 135.5t-311.5 -135.5t-126 -324zM614 565v645h279q106 0 169.5 -52 t63.5 -147.5t-16 -139.5t-61 -80l84 -226h-205l-58 191h-59v-191h-197zM809 901h57q70 0 70 81t-72 81h-55v-162z" />
<glyph unicode="&#xb4;" horiz-adv-x="1144" d="M500 1399l540 186l21 -246l-541 -159z" />
<glyph unicode="&#xb8;" horiz-adv-x="1619" d="M598 -446l41 165q61 -2 94 -2q74 0 86 50q6 25 -9 32.5t-54 7.5h-45l45 203h106l-22 -78h12q190 0 190 -125q0 -27 -8 -61q-51 -215 -264 -215q-82 0 -147 16z" />
<glyph unicode="&#xbb;" horiz-adv-x="970" d="M39 88l76 268l231 133l-164 158l68 246l348 -346l-47 -125zM530 88l76 268l232 133l-164 158l67 246l349 -346l-48 -125z" />
<glyph unicode="&#xbf;" horiz-adv-x="884" d="M53 -74q0 139 65.5 228.5t243.5 212.5q111 76 127 149l19 82h258q0 -100 -17.5 -192.5t-191.5 -217.5q-121 -84 -167 -132t-46 -106.5t36 -77.5t107 -19q135 0 258 18l41 6l-28 -215q-184 -55 -328 -55q-377 0 -377 319zM520 680l78 344h279l-78 -344h-279z" />
<glyph unicode="&#xc0;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM649 1702l135 244l455 -187l-125 -215z" />
<glyph unicode="&#xc1;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM705 1784l540 186l21 -246l-541 -157z" />
<glyph unicode="&#xc2;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM559 1595l361 312h163l197 -312h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#xc3;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM575 1794q61 53 128 92t123.5 39t164 -43t127.5 -43q43 0 142 57l32 21l4 -193q-49 -49 -118.5 -89t-119.5 -40t-158.5 44t-137.5 44q-47 0 -148 -61l-30 -18z" />
<glyph unicode="&#xc4;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM631 1614l63 276h228l-66 -276h-225zM1038 1614l64 276h227l-67 -276h-224z" />
<glyph unicode="&#xc5;" horiz-adv-x="1128" d="M27 0l614 1364q-2 16 -2 42t8 62q29 119 117 179.5t208 60.5t179 -59q43 -41 43 -109q0 -96 -55 -174l-41 -1366h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM821 1449q0 -13 18.5 -28.5t59.5 -15.5q92 0 107 63q2 8 2 23.5t-22.5 28t-55.5 12.5 q-91 -1 -107 -64q-2 -6 -2 -19z" />
<glyph unicode="&#xc6;" horiz-adv-x="1544" d="M29 0l630 1413h1145l-61 -268l-565 4l-78 -326h450l-59 -256h-453l-71 -301h565l-59 -266h-832l57 244h-292l-109 -244h-268zM524 506h238l149 641h-106z" />
<glyph unicode="&#xc7;" horiz-adv-x="1011" d="M203 379q0 299 100 551q55 139 135 246.5t204 174t278 66.5q150 0 301 -45l49 -16l-66 -209q-127 25 -295 24q-127 0 -224 -112.5t-144 -286.5t-47 -373q0 -178 165 -178q158 0 285 21l47 6l-22 -219q-190 -53 -346 -54h-21l-12 -43h12q190 0 191 -125q0 -27 -9 -61 q-51 -215 -264 -215q-82 0 -147 16l-25 7l41 165q61 -2 94 -2q74 0 86 50q6 25 -9 32.5t-54 7.5h-45l39 179q-297 55 -297 393z" />
<glyph unicode="&#xc8;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM553 1702l135 244l455 -187l-125 -215z" />
<glyph unicode="&#xc9;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM694 1784l541 186l20 -246l-540 -157z" />
<glyph unicode="&#xca;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM532 1602l361 311h164l196 -311h-249l-54 112l-127 -112h-291z" />
<glyph unicode="&#xcb;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM588 1614l63 276h228l-66 -276h-225zM995 1614l64 276h227l-67 -276h-224z" />
<glyph unicode="&#xcc;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM295 1702l135 244l455 -187l-125 -215z" />
<glyph unicode="&#xcd;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM449 1784l540 186l21 -246l-541 -157z" />
<glyph unicode="&#xce;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM270 1602l361 311h164l196 -311h-250l-53 112l-127 -112h-291z" />
<glyph unicode="&#xcf;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM324 1638l63 277h227l-65 -277h-225zM731 1638l64 277h227l-68 -277h-223z" />
<glyph unicode="&#xd1;" horiz-adv-x="1333" d="M133 0l322 1393h448l74 -1092h24l248 1092h273l-322 -1393h-426l-90 1112h-25l-260 -1112h-266zM666 1792q61 53 127.5 92t123 39t164 -43t127.5 -43q43 0 142 59l32 19l4 -193q-49 -49 -118.5 -89t-119.5 -40t-158.5 44t-137.5 44q-55 0 -147 -59l-31 -20z" />
<glyph unicode="&#xd2;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM676 1702l135 244l455 -187l-125 -215z" />
<glyph unicode="&#xd3;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM774 1784l541 186l20 -246l-540 -157z" />
<glyph unicode="&#xd4;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM633 1595l360 312h164l197 -312h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#xd5;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM672 1792q61 53 127.5 92t123 39t164 -43t125.5 -43q45 0 144 59l33 19l4 -193q-49 -49 -119 -89t-120 -40t-158.5 44t-137.5 44q-55 0 -145 -59l-33 -20z" />
<glyph unicode="&#xd6;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM657 1599l64 277h248l-66 -277h-246zM1075 1599l64 277h247l-65 -277h-246z" />
<glyph unicode="&#xd8;" horiz-adv-x="1259" d="M109 -166l186 275q-88 106 -88 292q0 268 82 506t239.5 374t389.5 136q141 0 235 -41l186 273l152 -64l-209 -307q88 -109 88 -287q0 -281 -84 -524q-170 -492 -641 -492q-123 0 -223 39l-178 -262zM498 410l501 741q-41 20 -130 20t-150 -61q-102 -100 -161.5 -304 t-59.5 -376v-20zM578 242q41 -20 130 -20.5t158 69.5q96 96 154.5 290.5t58.5 381.5v16z" />
<glyph unicode="&#xd9;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM662 1702l135 244l454 -187l-125 -215z" />
<glyph unicode="&#xda;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM719 1784l541 186l20 -246l-541 -157z" />
<glyph unicode="&#xdb;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM604 1595l361 312h163l197 -312h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#xdc;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM625 1599l63 277h248l-66 -277h-245zM1042 1599l64 277h248l-66 -277h-246z" />
<glyph unicode="&#xdd;" horiz-adv-x="1032" d="M324 1393h286l99 -543l346 543h299l-574 -803l-139 -590h-262l139 602zM686 1784l541 186l20 -246l-540 -157z" />
<glyph unicode="&#xdf;" horiz-adv-x="1155" d="M111 0l251 1065q54 223 171.5 308t325.5 85t311.5 -76.5t103.5 -220.5q0 -106 -41 -176t-147.5 -112t-144.5 -67.5t-38 -60t24.5 -57t99.5 -72t101 -71.5q66 -55 66 -176q0 -182 -112.5 -288t-309.5 -106q-133 0 -280 43l-50 15l60 200q143 -25 234 -24.5t135 36.5t44 79 t-20.5 63.5t-109.5 73.5t-124.5 102t-35.5 147.5t53 166t157.5 116.5t132 73t27.5 67q0 92 -155 92q-98 0 -151.5 -46t-78.5 -151l-241 -1028h-258z" />
<glyph unicode="&#xe0;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM590 1356l131 225l453 -184l-115 -203z" />
<glyph unicode="&#xe1;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM668 1399l540 186l21 -246l-541 -159z" />
<glyph unicode="&#xe2;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM434 1178l344 358h205l176 -358h-270l-49 131l-119 -131h-287zM442 348 q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387z" />
<glyph unicode="&#xe3;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM563 1405q16 14 41 36.5t88.5 59.5t116.5 37t145.5 -44t112.5 -44q39 0 141 61l33 19l8 -191l-41 -36q-24 -21 -87.5 -58t-113 -37t-142.5 45t-120 45q-49 0 -143 -61l-33 -20z" />
<glyph unicode="&#xe4;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM496 1231l63 276h248l-66 -276h-245zM913 1231l64 276h248l-66 -276h-246z" />
<glyph unicode="&#xe5;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM668 1264q0 145 106 237q70 61 167 61.5t134 -63.5q23 -37 23 -80q0 -154 -105 -239q-72 -59 -168 -59.5t-133 63.5q-25 39 -24 80zM821 1324q0 -11 11.5 -26.5t37.5 -15.5q55 0 74 59q2 8 2 19.5t-12 25t-35 13.5 q-58 -1 -74 -56q-4 -8 -4 -19z" />
<glyph unicode="&#xe6;" horiz-adv-x="1462" d="M106.5 210q-0.5 60 10.5 103q59 258 313 305q115 20 188.5 20.5t96.5 0.5q14 27 14 59q0 92 -65 93q-127 0 -312 -21l-61 -8l43 239q260 45 416.5 45.5t218.5 -83.5q115 84 268.5 84t232.5 -72q86 -80 86 -230q0 -82 -26 -178l-47 -174l-551 4q-6 -28 -6 -55 q0 -119 141 -119q98 0 297 15l55 4l-47 -209q-186 -57 -389 -57.5t-278 100.5q-53 -29 -173 -65t-188 -36q-129 0 -194 105q-43 70 -43.5 130zM377 293q0 -82 63 -82q96 0 207 27q-2 20 -2 65t23 131q-51 0 -162 -8q-45 -4 -80 -33q-49 -37 -49 -100zM979 606l307 -4 q14 51 14 82t-4 49q-16 78 -109 78t-137 -52t-71 -153z" />
<glyph unicode="&#xe7;" horiz-adv-x="839" d="M170 332q0 313 138 515t397 202q123 0 262 -50l43 -14l-70 -201q-158 27 -248 27q-117 0 -180 -128t-63 -299t155 -171l219 16l-22 -202q-129 -45 -264 -52l-13 -43h13q190 0 190 -125q0 -27 -8 -61q-51 -215 -264 -215q-82 0 -148 16l-24 7l41 165q61 -2 94 -2 q74 0 86 50q6 25 -9.5 32.5t-54.5 7.5h-45l39 175q-123 20 -193.5 111t-70.5 239z" />
<glyph unicode="&#xe8;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM403 1356l132 225l452 -184l-115 -203zM477 602h84 q141 0 197.5 31t56.5 104q0 88 -102 88q-182 0 -236 -223z" />
<glyph unicode="&#xe9;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM477 602h84q141 0 197.5 31t56.5 104q0 88 -102 88 q-182 0 -236 -223zM557 1399l541 186l20 -246l-540 -159z" />
<glyph unicode="&#xea;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM379 1178l344 358h205l176 -358h-270l-50 131 l-118 -131h-287zM477 602h84q141 0 197.5 31t56.5 104q0 88 -102 88q-182 0 -236 -223z" />
<glyph unicode="&#xeb;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM432 1231l64 276h247l-65 -276h-246zM477 602h84 q141 0 197.5 31t56.5 104q0 88 -102 88q-182 0 -236 -223zM850 1231l63 276h248l-65 -276h-246z" />
<glyph unicode="&#xec;" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM145 1356l131 225l453 -184l-115 -203z" />
<glyph unicode="&#xed;" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM352 1399l541 186l20 -246l-540 -159z" />
<glyph unicode="&#xee;" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM141 1178l344 358h205l176 -358h-270l-49 131l-119 -131h-287z" />
<glyph unicode="&#xef;" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM182 1231l64 276h248l-66 -276h-246zM600 1231l64 276h247l-65 -276h-246z" />
<glyph unicode="&#xf1;" horiz-adv-x="1050" d="M113 0l235 1024h262l-12 -100l35 30q35 33 100.5 64t124.5 31q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-74.5 27.5t-188.5 -123l-158 -688h-274zM506 1405q16 14 40.5 36.5t88 59.5t117 37t145.5 -44t113 -44q39 0 141 61l33 19 l8 -191l-41 -36q-25 -21 -88.5 -58t-112.5 -37t-142 45t-120 45q-49 0 -143 -61l-33 -20z" />
<glyph unicode="&#xf2;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149q-111 0 -178.5 -140.5t-67.5 -314.5zM465 1356l131 225l453 -184 l-115 -203z" />
<glyph unicode="&#xf3;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149q-111 0 -178.5 -140.5t-67.5 -314.5zM559 1399l541 186l20 -246l-540 -159 z" />
<glyph unicode="&#xf4;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM410 1178l344 358h204l177 -358h-271l-49 131l-119 -131h-286zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149 q-111 0 -178.5 -140.5t-67.5 -314.5z" />
<glyph unicode="&#xf5;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149q-111 0 -178.5 -140.5t-67.5 -314.5zM455 1405q16 14 40.5 36.5t88 59.5 t117 37t145.5 -44t112 -44q39 0 142 61l33 19l8 -191l-41 -36q-25 -21 -88.5 -58t-112.5 -37t-142 45t-118 45q-51 0 -147 -61l-31 -20z" />
<glyph unicode="&#xf6;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149q-111 0 -178.5 -140.5t-67.5 -314.5zM467 1231l63 276h228l-66 -276h-225z M864 1231l64 276h227l-65 -276h-226z" />
<glyph unicode="&#xf8;" horiz-adv-x="1015" d="M115 -156l149 234q-98 94 -98 273t59.5 333t181 260.5t277.5 106.5q98 0 174 -25l131 215l146 -63l-146 -228q98 -94 98 -273t-55 -330.5t-177 -259t-286 -107.5q-96 0 -176 22l-135 -221zM438 346l127 199l162 266q-18 6 -43 6q-111 0 -178.5 -140t-67.5 -315v-16z M526 217q25 -4 43 -4q111 0 178.5 140.5t67.5 314.5v10l-127 -197z" />
<glyph unicode="&#xf9;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM473 1356l131 225l453 -184l-115 -203z" />
<glyph unicode="&#xfa;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM567 1399l541 186l20 -246l-540 -159z" />
<glyph unicode="&#xfb;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM403 1178l345 358h204l176 -358h-270l-49 131l-119 -131h-287z" />
<glyph unicode="&#xfc;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM442 1231l64 276h248l-66 -276h-246zM860 1231l64 276h247l-65 -276h-246z " />
<glyph unicode="&#xfd;" horiz-adv-x="950" d="M209 -430l211 430h-170l8 1024h268l-28 -797h30l357 797h276l-680 -1454h-272zM539 1399l540 186l21 -246l-541 -159z" />
<glyph unicode="&#xff;" horiz-adv-x="950" d="M209 -430l211 430h-170l8 1024h268l-28 -797h30l357 797h276l-680 -1454h-272zM406 1231l63 276h248l-66 -276h-245zM823 1231l64 276h248l-66 -276h-246z" />
<glyph unicode="&#x100;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM590 1651l51 223h670l-51 -223h-670z" />
<glyph unicode="&#x101;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM567 1284l51 223h633l-53 -223h-631z" />
<glyph unicode="&#x102;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1393h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM657 1843q0 27 5 55h249q-2 -10 -2 -18q0 -61 76 -61t107 79h249q-49 -145 -141 -219.5t-253 -74.5t-225.5 68.5t-64.5 170.5z" />
<glyph unicode="&#x103;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM582 1454q0 35 6 74h248v-15q0 -76 91 -75.5t124 90.5h247q-106 -319 -446 -320q-270 0 -270 246z" />
<glyph unicode="&#x104;" horiz-adv-x="1128" d="M27 0l626 1393h488l-43 -1383v-10h-17q-45 -33 -90 -85t-45 -82t27 -30l76 7l-27 -226q-117 -23 -199 -22.5t-127 40.5t-45 117q0 131 185 281h-21l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43z" />
<glyph unicode="&#x105;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-20 -2q-45 -29 -94.5 -84t-49.5 -86t27 -31l76 7l-27 -226q-117 -23 -199 -22.5t-127 40.5t-45 117q0 131 187 283q0 2 11 68.5t15 85.5q-84 -113 -196 -160 q-47 -20 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387z" />
<glyph unicode="&#x106;" horiz-adv-x="1011" d="M203 379q0 299 100 551q55 139 135 246.5t204 174t278 66.5q150 0 301 -45l49 -16l-66 -209q-127 25 -295 24q-127 0 -224 -112.5t-144 -286.5t-47 -373q0 -178 165 -178q158 0 285 21l47 6l-22 -219q-190 -54 -346 -54q-420 0 -420 404zM766 1784l541 186l20 -246 l-541 -157z" />
<glyph unicode="&#x107;" horiz-adv-x="837" d="M168 332q0 313 138 515t396 202q123 0 261 -50l45 -14l-70 -201q-158 27 -248 27q-117 0 -180.5 -128t-63.5 -299t156 -171l219 16l-22 -202q-150 -51 -299.5 -51.5t-240.5 94t-91 262.5zM528 1399l541 186l21 -246l-541 -159z" />
<glyph unicode="&#x108;" horiz-adv-x="1011" d="M203 379q0 299 100 551q55 139 135 246.5t204 174t278 66.5q150 0 301 -45l49 -16l-66 -209q-127 25 -295 24q-127 0 -224 -112.5t-144 -286.5t-47 -373q0 -178 165 -178q158 0 285 21l47 6l-22 -219q-190 -54 -346 -54q-420 0 -420 404zM598 1595l360 312h164l197 -312 h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#x109;" horiz-adv-x="837" d="M168 332q0 313 138 515t396 202q123 0 261 -50l45 -14l-70 -201q-158 27 -248 27q-117 0 -180.5 -128t-63.5 -299t156 -171l219 16l-22 -202q-150 -51 -299.5 -51.5t-240.5 94t-91 262.5zM397 1178l344 358h164l176 -358h-250l-49 131l-118 -131h-267z" />
<glyph unicode="&#x10a;" horiz-adv-x="1011" d="M203 379q0 299 100 551q55 139 135 246.5t204 174t278 66.5q150 0 301 -45l49 -16l-66 -209q-127 25 -295 24q-127 0 -224 -112.5t-144 -286.5t-47 -373q0 -178 165 -178q158 0 285 21l47 6l-22 -219q-190 -54 -346 -54q-420 0 -420 404zM829 1579l66 283h242l-70 -283 h-238z" />
<glyph unicode="&#x10b;" horiz-adv-x="837" d="M168 332q0 313 138 515t396 202q123 0 261 -50l45 -14l-70 -201q-158 27 -248 27q-117 0 -180.5 -128t-63.5 -299t156 -171l219 16l-22 -202q-150 -51 -299.5 -51.5t-240.5 94t-91 262.5zM614 1192l66 281h238l-66 -281h-238z" />
<glyph unicode="&#x10c;" horiz-adv-x="1011" d="M203 379q0 299 100 551q55 139 135 246.5t204 174t278 66.5q150 0 301 -45l49 -16l-66 -209q-127 25 -295 24q-127 0 -224 -112.5t-144 -286.5t-47 -373q0 -178 165 -178q158 0 285 21l47 6l-22 -219q-190 -54 -346 -54q-420 0 -420 404zM682 1898h270l60 -116l108 116 h269l-328 -309h-189z" />
<glyph unicode="&#x10d;" horiz-adv-x="837" d="M168 332q0 313 138 515t396 202q123 0 261 -50l45 -14l-70 -201q-158 27 -248 27q-117 0 -180.5 -128t-63.5 -299t156 -171l219 16l-22 -202q-150 -51 -299.5 -51.5t-240.5 94t-91 262.5zM502 1536h252l59 -135l109 135h264l-342 -358h-174z" />
<glyph unicode="&#x10e;" horiz-adv-x="1239" d="M133 0l322 1393h438q459 0 459 -424q0 -299 -111 -541q-57 -125 -139 -220t-203 -151.5t-266 -56.5h-500zM473 246h184q104 0 186.5 75.5t127.5 191.5t67.5 233.5t22.5 219t-56.5 141.5t-168.5 40h-156zM618 1898h271l59 -116l109 116h268l-328 -309h-188z" />
<glyph unicode="&#x10f;" horiz-adv-x="1304" d="M164 338q0 174 56 331.5t177 267.5t283 110q70 0 205 -33l41 -10l100 430h274l-331 -1434h-267l19 106q-41 -47 -118.5 -89t-143.5 -42q-145 0 -220 94.5t-75 268.5zM442 340q0 -131 105 -131q33 0 91 37t99 73l41 37l99 428q-119 29 -194 29t-131 -81t-83 -187.5 t-27 -204.5zM1272 879l125 514h250l-123 -514h-252z" />
<glyph unicode="&#x110;" horiz-adv-x="1255" d="M150 0l129 563h-111l59 279h117l127 551h438q459 0 459 -424q0 -299 -111 -541q-125 -270 -342 -371q-121 -57 -266 -57h-499zM489 246h185q104 0 186 75.5t127 191.5t67.5 233.5t22.5 219t-56 141.5t-169 40h-156l-69 -305h221l-62 -279h-225z" />
<glyph unicode="&#x111;" horiz-adv-x="1081" d="M164 338q0 174 56 331.5t177 267.5t283 110q70 0 205 -33l41 -10l26 116h-245l55 244h248l16 70h274l-16 -70h111l-58 -244h-110l-258 -1120h-267l19 106q-41 -47 -118.5 -89t-143.5 -42q-145 0 -220 94.5t-75 268.5zM442 340q0 -131 105 -131q33 0 91 37t99 73l41 37 l99 428q-119 29 -194 29t-131 -81t-83 -187.5t-27 -204.5z" />
<glyph unicode="&#x112;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM567 1651l51 223h670l-51 -223h-670z" />
<glyph unicode="&#x113;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM446 1284l52 223h632l-53 -223h-631zM477 602h84 q141 0 197.5 31t56.5 104q0 88 -102 88q-182 0 -236 -223z" />
<glyph unicode="&#x114;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM604 1843q0 27 4 55h250q-2 -10 -2 -18q0 -61 76 -61t106 79h250q-49 -145 -141 -219.5t-253 -74.5t-225.5 68.5t-64.5 170.5z" />
<glyph unicode="&#x115;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM477 602h84q141 0 197.5 31t56.5 104q0 88 -102 88 q-182 0 -236 -223zM494 1454q0 35 6 74h248v-15q0 -76 91 -75.5t124 90.5h247q-106 -319 -446 -320q-270 0 -270 246z" />
<glyph unicode="&#x116;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM754 1579l65 283h242l-70 -283h-237z" />
<glyph unicode="&#x117;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM477 602h84q141 0 197.5 31t56.5 104q0 88 -102 88 q-182 0 -236 -223zM625 1192l65 281h238l-66 -281h-237z" />
<glyph unicode="&#x118;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-24q-45 -33 -90 -85t-45 -82t26 -30l76 7l-27 -226q-117 -23 -198.5 -22.5t-126.5 40.5t-45 117q0 131 184 281h-549z" />
<glyph unicode="&#x119;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-18 -6 -41 -13q-111 -92 -110 -147q0 -29 26 -29l76 6l-27 -225q-117 -23 -198.5 -22.5 t-126.5 40.5t-45 126t114 208q-18 -2 -55 -2q-176 0 -261 84t-85 246zM477 602h84q141 0 197.5 31t56.5 104q0 88 -102 88q-182 0 -236 -223z" />
<glyph unicode="&#x11a;" horiz-adv-x="1017" d="M133 0l320 1393h817l-58 -246h-532l-76 -328h422l-57 -246h-420l-76 -327h535l-56 -246h-819zM602 1935h270l60 -116l108 116h269l-328 -309h-188z" />
<glyph unicode="&#x11b;" horiz-adv-x="974" d="M166 305q0 162 32.5 289t97 229.5t176.5 163t255 60.5q358 0 358 -306q0 -195 -131 -268.5t-436 -73.5h-72q-6 -25 -6 -77t42 -82.5t106 -30.5q141 0 289 20l53 7l-31 -203q-190 -57 -376.5 -57.5t-271.5 83.5t-85 246zM477 602h84q141 0 197.5 31t56.5 104q0 88 -102 88 q-182 0 -236 -223zM494 1536h251l60 -135l108 135h265l-342 -358h-174z" />
<glyph unicode="&#x11c;" horiz-adv-x="1163" d="M209 414q0 332 115 577q125 270 356 373q123 53 248 53t231.5 -20.5t163.5 -40.5l55 -21l-65 -205q-203 41 -383 41q-111 0 -198 -72.5t-134 -189.5q-98 -236 -98 -497q0 -190 205 -191l129 10l71 310h-131l55 233h412l-174 -745q-139 -54 -397 -54q-461 0 -461 439z M614 1546l344 359h205l176 -359h-270l-49 131l-119 -131h-287z" />
<glyph unicode="&#x11d;" horiz-adv-x="991" d="M18 -190q0 96 69 158.5t181 117.5q-41 41 -41 95.5t31 103.5t61 77l31 29q-139 82 -139 256t116.5 288t307.5 114q88 0 209 -29l39 -8l299 12l-52 -221h-118q8 -45 8 -105q0 -158 -122 -267t-306 -109l-88 10q-31 -41 -31 -70t39 -41t154 -28q291 -43 290 -259 q0 -182 -132 -293.5t-361 -111.5q-444 0 -445 281zM268 -133q0 -43 45 -76t144 -33q242 0 241 144q0 27 -19.5 43t-44 22t-130 23.5t-119.5 21.5q-41 -22 -79 -62t-38 -83zM371 1178l344 358h164l176 -358h-250l-49 131l-119 -131h-266zM463 659q0 -114 143 -114 q72 0 117 48t45 118q0 115 -143 114q-72 0 -117 -47t-45 -119z" />
<glyph unicode="&#x11e;" horiz-adv-x="1163" d="M209 414q0 332 115 577q125 270 356 373q123 53 248 53t231.5 -20.5t163.5 -40.5l55 -21l-65 -205q-203 41 -383 41q-111 0 -198 -72.5t-134 -189.5q-98 -236 -98 -497q0 -190 205 -191l129 10l71 310h-131l55 233h412l-174 -745q-139 -54 -397 -54q-461 0 -461 439z M688 1827q0 31 4 69h269v-26q6 -63 94 -63.5t121 89.5h268q-51 -156 -169 -248t-277.5 -92t-231.5 70q-78 74 -78 201z" />
<glyph unicode="&#x11f;" horiz-adv-x="991" d="M18 -190q0 96 69 158.5t181 117.5q-41 41 -41 95.5t31 103.5t61 77l31 29q-139 82 -139 256t116.5 288t307.5 114q88 0 209 -29l39 -8l299 12l-52 -221h-118q8 -45 8 -105q0 -158 -122 -267t-306 -109l-88 10q-31 -41 -31 -70t39 -41t154 -28q291 -43 290 -259 q0 -182 -132 -293.5t-361 -111.5q-444 0 -445 281zM268 -133q0 -43 45 -76t144 -33q242 0 241 144q0 27 -19.5 43t-44 22t-130 23.5t-119.5 21.5q-41 -22 -79 -62t-38 -83zM451 1454q0 35 6 74h248v-15q0 -76 91 -75.5t124 90.5h247q-106 -319 -446 -320q-270 0 -270 246z M463 659q0 -114 143 -114q72 0 117 48t45 118q0 115 -143 114q-72 0 -117 -47t-45 -119z" />
<glyph unicode="&#x120;" horiz-adv-x="1163" d="M209 414q0 332 115 577q125 270 356 373q123 53 248 53t231.5 -20.5t163.5 -40.5l55 -21l-65 -205q-203 41 -383 41q-111 0 -198 -72.5t-134 -189.5q-98 -236 -98 -497q0 -190 205 -191l129 10l71 310h-131l55 233h412l-174 -745q-139 -54 -397 -54q-461 0 -461 439z M858 1499l66 281h258l-66 -281h-258z" />
<glyph unicode="&#x121;" horiz-adv-x="991" d="M18 -190q0 96 69 158.5t181 117.5q-41 41 -41 95.5t31 103.5t61 77l31 29q-139 82 -139 256t116.5 288t307.5 114q88 0 209 -29l39 -8l299 12l-52 -221h-118q8 -45 8 -105q0 -158 -122 -267t-306 -109l-88 10q-31 -41 -31 -70t39 -41t154 -28q291 -43 290 -259 q0 -182 -132 -293.5t-361 -111.5q-444 0 -445 281zM268 -133q0 -43 45 -76t144 -33q242 0 241 144q0 27 -19.5 43t-44 22t-130 23.5t-119.5 21.5q-41 -22 -79 -62t-38 -83zM463 659q0 -114 143 -114q72 0 117 48t45 118q0 115 -143 114q-72 0 -117 -47t-45 -119zM602 1192 l66 281h237l-65 -281h-238z" />
<glyph unicode="&#x122;" horiz-adv-x="1163" d="M209 414q0 332 115 577q125 270 356 373q123 53 248 53t231.5 -20.5t163.5 -40.5l55 -21l-65 -205q-203 41 -383 41q-111 0 -198 -72.5t-134 -189.5q-98 -236 -98 -497q0 -190 205 -191l129 10l71 310h-131l55 233h412l-174 -745q-139 -54 -397 -54q-461 0 -461 439z M279 -633l198 486h252l-219 -486h-231z" />
<glyph unicode="&#x123;" horiz-adv-x="991" d="M18 -190q0 96 69 158.5t181 117.5q-41 41 -41 95.5t31 103.5t61 77l31 29q-139 82 -139 256t116.5 288t307.5 114q88 0 209 -29l39 -8l299 12l-52 -221h-118q8 -45 8 -105q0 -158 -122 -267t-306 -109l-88 10q-31 -41 -31 -70t39 -41t154 -28q291 -43 290 -259 q0 -182 -132 -293.5t-361 -111.5q-444 0 -445 281zM268 -133q0 -43 45 -76t144 -33q242 0 241 144q0 27 -19.5 43t-44 22t-130 23.5t-119.5 21.5q-41 -22 -79 -62t-38 -83zM463 659q0 -114 143 -114q72 0 117 48t45 118q0 115 -143 114q-72 0 -117 -47t-45 -119zM586 1184 l219 483h233l-200 -483h-252z" />
<glyph unicode="&#x124;" horiz-adv-x="1269" d="M133 0l322 1393h282l-129 -557h439l129 557h282l-321 -1393h-283l135 590h-438l-135 -590h-283zM631 1595l360 312h164l197 -312h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#x125;" horiz-adv-x="1050" d="M113 0l329 1434h275l-115 -506q45 45 121 83t135 38q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-54 27.5q-70 0 -174 -92l-35 -31l-158 -688h-274zM559 1534l361 311h163l197 -311h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#x126;" horiz-adv-x="1286" d="M141 0l228 985h-133v6l57 238h131l39 164h270l-39 -164h463l39 164h270l-39 -164h132l-62 -244h-125l-227 -985h-271l132 569h-463l-131 -569h-271zM604 834h463l35 151h-463z" />
<glyph unicode="&#x127;" horiz-adv-x="1050" d="M113 0l249 1085h-106l55 238h105l26 111h275l-25 -111h252l-57 -238h-250l-35 -157q45 45 121 83t135 38q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-54 27.5q-70 0 -174 -92l-35 -31l-158 -688h-274z" />
<glyph unicode="&#x128;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM313 1774q61 53 128 92t123.5 39t164 -43t127.5 -43q43 0 141 57l33 20l4 -192q-49 -49 -118.5 -89t-120 -40t-159 44t-136.5 44q-47 0 -148 -61l-30 -19z" />
<glyph unicode="&#x129;" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM190 1405q16 14 41 36.5t88.5 59.5t116.5 37t145.5 -44t112.5 -44q39 0 142 61l32 19l9 -191l-41 -36q-25 -21 -88.5 -58t-112.5 -37t-142.5 45t-119.5 45q-49 0 -144 -61l-32 -20z" />
<glyph unicode="&#x12a;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM307 1673l51 223h670l-51 -223h-670z" />
<glyph unicode="&#x12b;" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM207 1284l51 223h633l-53 -223h-631z" />
<glyph unicode="&#x12c;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM369 1843q0 27 4 55h250q-2 -10 -2 -18q0 -61 75.5 -61t106.5 79h250q-49 -145 -141.5 -219.5t-253 -74.5t-225 68.5t-64.5 170.5z" />
<glyph unicode="&#x12d;" horiz-adv-x="497" d="M113 0l235 1024h275l-236 -1024h-274zM223 1454q0 35 6 74h248v-15q0 -76 91 -75.5t124 90.5h248q-106 -319 -446 -320q-270 0 -271 246z" />
<glyph unicode="&#x12e;" horiz-adv-x="548" d="M-39 -281q0 131 184 281h-12l322 1393h282l-321 -1393q-54 -23 -107 -78t-53 -87t27 -32l75 7l-26 -226q-117 -23 -199 -22.5t-127 40.5t-45 117z" />
<glyph unicode="&#x12f;" horiz-adv-x="497" d="M-76 -283.5q0 74.5 57.5 152.5t131.5 135l235 1020h275l-236 -1024q-57 -29 -112.5 -81t-55.5 -84t27 -32l76 7l-27 -226q-117 -23 -199 -22.5t-127 40.5t-45 114.5zM371 1130l65 281h277l-64 -281h-278z" />
<glyph unicode="&#x130;" horiz-adv-x="548" d="M133 0l322 1393h282l-321 -1393h-283zM514 1579l66 283h241l-69 -283h-238z" />
<glyph unicode="&#x131;" horiz-adv-x="499" d="M113 0l235 1024h275l-236 -1024h-274z" />
<glyph unicode="&#x134;" horiz-adv-x="579" d="M12 -141l47 245q86 4 117 31t57 141l259 1117h282l-258 -1117q-47 -207 -110 -307q-33 -51 -89 -71q-104 -39 -305 -39zM295 1595l360 312h164l197 -312h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#x135;" horiz-adv-x="497" d="M-143 -252q158 82 195.5 120t60.5 132l235 1024h275l-236 -1024q-18 -82 -29.5 -123t-36 -89t-47 -71.5t-73.5 -56.5q-70 -49 -258 -131zM164 1178l344 358h164l176 -358h-250l-49 131l-119 -131h-266z" />
<glyph unicode="&#x136;" horiz-adv-x="1105" d="M133 0l322 1393h282l-139 -602l131 16l361 586h309l-445 -699l129 -694h-297l-90 559l-155 -14l-125 -545h-283zM242 -633l198 486h252l-219 -486h-231z" />
<glyph unicode="&#x137;" horiz-adv-x="956" d="M113 0l329 1434h275l-185 -797l70 14l260 371h301l-356 -492l135 -530h-280l-99 424l-82 -12l-94 -412h-274zM152 -633l198 486h252l-219 -486h-231z" />
<glyph unicode="&#x139;" horiz-adv-x="864" d="M133 0l322 1393h282l-264 -1147h440l-55 -246h-725zM594 1784l541 186l20 -246l-541 -157z" />
<glyph unicode="&#x13a;" horiz-adv-x="499" d="M113 0l329 1434h275l-330 -1434h-274zM461 1784l540 186l21 -246l-541 -157z" />
<glyph unicode="&#x13b;" horiz-adv-x="864" d="M133 0l322 1393h282l-264 -1147h440l-55 -246h-725zM139 -633l199 486h252l-219 -486h-232z" />
<glyph unicode="&#x13c;" horiz-adv-x="499" d="M-104 -633l198 486h252l-219 -486h-231zM113 0l329 1434h275l-330 -1434h-274z" />
<glyph unicode="&#x13d;" horiz-adv-x="950" d="M133 0l322 1393h282l-264 -1147h440l-55 -246h-725zM834 879l124 514h250l-123 -514h-251z" />
<glyph unicode="&#x13e;" horiz-adv-x="722" d="M113 0l329 1434h275l-330 -1434h-274zM690 879l125 514h250l-123 -514h-252z" />
<glyph unicode="&#x141;" horiz-adv-x="866" d="M94 582l207 127l158 684h282l-110 -480l196 123l84 -188l-346 -215l-88 -387h441l-56 -246h-725l99 426l-58 -35z" />
<glyph unicode="&#x142;" horiz-adv-x="598" d="M100 594l226 141l161 699h275l-115 -500l82 51l84 -188l-231 -146l-150 -651h-274l102 451l-76 -48z" />
<glyph unicode="&#x143;" horiz-adv-x="1333" d="M133 0l322 1393h448l74 -1092h24l248 1092h273l-322 -1393h-426l-90 1112h-25l-260 -1112h-266zM776 1784l541 186l20 -246l-540 -157z" />
<glyph unicode="&#x144;" horiz-adv-x="1050" d="M113 0l235 1024h262l-12 -100l35 30q35 33 100.5 64t124.5 31q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-74.5 27.5t-188.5 -123l-158 -688h-274zM569 1399l541 186l20 -246l-540 -159z" />
<glyph unicode="&#x145;" horiz-adv-x="1333" d="M133 0l322 1393h448l74 -1092h24l248 1092h273l-322 -1393h-426l-90 1112h-25l-260 -1112h-266zM375 -633l198 486h252l-219 -486h-231z" />
<glyph unicode="&#x146;" horiz-adv-x="1050" d="M113 0l235 1024h262l-12 -100q47 47 124 86t136 39q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-56 27.5q-72 0 -174 -92l-33 -31l-158 -688h-274zM197 -633l198 486h252l-219 -486h-231z" />
<glyph unicode="&#x147;" horiz-adv-x="1333" d="M133 0l322 1393h448l74 -1092h24l248 1092h273l-322 -1393h-426l-90 1112h-25l-260 -1112h-266zM707 1896h270l59 -116l109 116h268l-328 -309h-188z" />
<glyph unicode="&#x148;" horiz-adv-x="1050" d="M113 0l235 1024h262l-12 -100q47 47 124 86t136 39q250 0 250 -289q0 -74 -43 -258l-115 -502h-274l110 481q37 154 37 214.5t-15 88t-56 27.5q-72 0 -174 -92l-33 -31l-158 -688h-274zM537 1536h251l60 -135l108 135h265l-342 -358h-174z" />
<glyph unicode="&#x14a;" horiz-adv-x="1333" d="M133 0l322 1393h448l74 -1092h22l250 1092h273l-68 -289h2l-258 -1116q-35 -150 -62.5 -224.5t-85 -123t-130 -59.5t-213.5 -11l47 246q86 4 116.5 30.5t57.5 141.5l2 12h-156l-90 1112h-25l-260 -1112h-266z" />
<glyph unicode="&#x14b;" horiz-adv-x="1028" d="M106 0l242 1022h260l-24 -96q158 121 258 121q244 0 243 -267q0 -102 -32 -245l-125 -521q-49 -207 -142.5 -312t-322.5 -206l-66 234q156 78 201 124t70 150l127 531q20 84 20 145q0 111 -88 111q-57 0 -162 -68l-35 -23l-163 -700h-261z" />
<glyph unicode="&#x14c;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM649 1651l51 223h670l-51 -223h-670z" />
<glyph unicode="&#x14d;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149q-111 0 -178.5 -140.5t-67.5 -314.5zM459 1284l51 223h633l-53 -223h-631z " />
<glyph unicode="&#x14e;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM698 1843q0 27 4 55h250q-2 -10 -2 -18q0 -61 76 -61t107 79h249q-49 -145 -141 -219.5t-253 -74.5t-225.5 68.5t-64.5 170.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149q-111 0 -178.5 -140.5t-67.5 -314.5zM453 1454q0 35 6 74h248v-15 q0 -76 91 -75.5t124 90.5h247q-106 -319 -446 -320q-270 0 -270 246z" />
<glyph unicode="&#x150;" horiz-adv-x="1259" d="M207 401q0 268 82 506t239.5 374t389.5 136q453 0 452 -426q0 -280 -84 -523t-244.5 -368t-382.5 -125q-453 0 -452 426zM498 459.5q0 -142.5 44 -190.5t149.5 -48t174.5 70q96 96 154.5 290.5t58.5 381.5q0 113 -43 160.5t-149.5 47.5t-167.5 -61q-102 -100 -161.5 -304 t-59.5 -346.5zM653 1597l230 408l223 -92l-248 -395zM1073 1597l227 406l224 -92l-248 -395z" />
<glyph unicode="&#x151;" horiz-adv-x="1015" d="M164 362q0 164 59.5 318t181 260.5t277.5 106.5q403 0 403 -388q0 -166 -55 -317.5t-177 -259t-286 -107.5q-403 0 -403 387zM397 1286l318 387l182 -135l-336 -385zM436 358q0 -149 131 -149q111 0 178.5 140t67.5 315q0 150 -131 149q-111 0 -178.5 -140.5 t-67.5 -314.5zM793 1286l319 389l182 -137l-336 -385z" />
<glyph unicode="&#x152;" horiz-adv-x="1701" d="M193 362q0 147 47 345q90 379 257 555t418 176q86 0 222 -25h827l-61 -268h-566l-80 -322h451l-59 -256l-449 4l-72 -303h564l-62 -268h-821q-152 -25 -238 -25q-379 0 -378 387zM475 437.5q0 -99.5 46.5 -145.5t150.5 -46q55 0 190 12l215 897q-104 12 -190 12 q-139 0 -225.5 -103t-145.5 -357q-41 -170 -41 -269.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1554" d="M156 337q0 73 27.5 183.5t65.5 197.5t101 167q131 162 391 162q190 0 279 -111q129 111 293 111q311 0 338 -248q4 -33 4 -88.5t-25 -128.5l-59 -189l-549 4q-8 -32 -8 -59q0 -115 149 -115q96 0 297 15l58 4l-48 -209q-186 -57 -385.5 -57.5t-275.5 100.5 q-135 -100 -320 -101q-289 0 -325 240q-8 49 -8 122zM420 352q0 -133 125 -133q131 0 192 146q76 174 76 309q0 66 -29.5 97.5t-99.5 31.5q-98 0 -152.5 -76t-83 -194t-28.5 -181zM1069 606l311 -4q16 66 16.5 92.5t-1.5 38.5q-12 63 -108.5 63.5t-145.5 -47t-72 -143.5z " />
<glyph unicode="&#x154;" horiz-adv-x="1144" d="M133 0l322 1393h401q221 0 325.5 -93.5t104.5 -294t-88 -335.5t-223 -189l112 -481h-282l-92 442h-195l-102 -442h-283zM573 684h183q111 0 175 88t64 194.5t-34.5 145.5t-120.5 39h-158zM657 1784l541 186l21 -246l-541 -157z" />
<glyph unicode="&#x155;" horiz-adv-x="684" d="M113 0l235 1024h273l-37 -147q61 55 157.5 109t163.5 63l-80 -287q-98 -31 -239 -94l-49 -23l-150 -645h-274zM362 1399l541 186l21 -246l-541 -159z" />
<glyph unicode="&#x156;" horiz-adv-x="1144" d="M133 0l322 1393h401q221 0 325.5 -93.5t104.5 -294t-88 -335.5t-223 -189l112 -481h-282l-92 442h-195l-102 -442h-283zM270 -633l199 486h252l-219 -486h-232zM573 684h183q110 0 174.5 88t64.5 194.5t-34.5 145.5t-120.5 39h-158z" />
<glyph unicode="&#x157;" horiz-adv-x="684" d="M-90 -633l199 486h251l-219 -486h-231zM113 0l235 1024h273l-37 -147q61 55 157.5 109t163.5 63l-80 -287q-98 -31 -239 -94l-49 -23l-150 -645h-274z" />
<glyph unicode="&#x158;" horiz-adv-x="1144" d="M133 0l322 1393h401q221 0 325.5 -93.5t104.5 -294t-88 -335.5t-223 -189l112 -481h-282l-92 442h-195l-102 -442h-283zM573 684h183q111 0 175 88t64 194.5t-34.5 145.5t-120.5 39h-158zM578 1898h270l59 -116l109 116h268l-328 -309h-188z" />
<glyph unicode="&#x159;" horiz-adv-x="684" d="M113 0l235 1024h273l-37 -147q61 55 157.5 109t163.5 63l-80 -287q-98 -31 -239 -94l-49 -23l-150 -645h-274zM346 1536h252l59 -135l109 135h264l-342 -358h-174z" />
<glyph unicode="&#x15a;" horiz-adv-x="1013" d="M102 59l48 203q231 -41 363 -41t193.5 46t61.5 157q0 59 -36 91t-175 95.5t-198.5 139t-59.5 211.5q0 199 129 327.5t344 128.5q98 0 205.5 -19.5t165.5 -37.5l57 -21l-51 -200q-226 37 -369 37q-90 0 -142 -50.5t-52 -120t32.5 -101.5t180 -97.5t202 -138t54.5 -211.5 q0 -231 -142.5 -356.5t-384.5 -125.5q-96 0 -202.5 20.5t-165.5 41.5zM674 1784l540 186l21 -246l-541 -157z" />
<glyph unicode="&#x15b;" horiz-adv-x="884" d="M94 45l51 201q197 -37 340.5 -37t143.5 100q0 27 -29 42.5t-98 35.5q-145 41 -215 96.5t-70 157.5q0 190 125 298t301 108q96 0 188.5 -15.5t135.5 -32.5l45 -16l-49 -203q-212 33 -345 33q-61 0 -93.5 -28.5t-32.5 -66.5t28.5 -56.5t148 -55.5t180 -85t60.5 -156 q0 -193 -126 -291.5t-334 -98.5q-88 0 -177.5 17.5t-132.5 36.5zM504 1399l540 186l21 -246l-541 -159z" />
<glyph unicode="&#x15c;" horiz-adv-x="1013" d="M102 59l48 203q231 -41 363 -41t193.5 46t61.5 157q0 59 -36 91t-175 95.5t-198.5 139t-59.5 211.5q0 199 129 327.5t344 128.5q98 0 205.5 -19.5t165.5 -37.5l57 -21l-51 -200q-226 37 -369 37q-90 0 -142 -50.5t-52 -120t32.5 -101.5t180 -97.5t202 -138t54.5 -211.5 q0 -231 -142.5 -356.5t-384.5 -125.5q-96 0 -202.5 20.5t-165.5 41.5zM500 1595l360 312h164l197 -312h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#x15d;" horiz-adv-x="884" d="M94 45l51 201q197 -37 340.5 -37t143.5 100q0 27 -29 42.5t-98 35.5q-145 41 -215 96.5t-70 157.5q0 190 125 298t301 108q96 0 188.5 -15.5t135.5 -32.5l45 -16l-49 -203q-212 33 -345 33q-61 0 -93.5 -28.5t-32.5 -66.5t28.5 -56.5t148 -55.5t180 -85t60.5 -156 q0 -193 -126 -291.5t-334 -98.5q-88 0 -177.5 17.5t-132.5 36.5zM365 1178l344 358h163l177 -358h-250l-49 131l-119 -131h-266z" />
<glyph unicode="&#x15e;" horiz-adv-x="1013" d="M102 59l48 203q231 -41 363 -41t193.5 46t61.5 157q0 59 -36 91t-175 95.5t-198.5 139t-59.5 211.5q0 199 129 327.5t344 128.5q98 0 205.5 -19.5t165.5 -37.5l57 -21l-51 -200q-225 37 -369 37q-90 0 -142 -50.5t-52 -120t32.5 -101.5t180 -97.5t202 -138t54.5 -211.5 q0 -227 -137.5 -352t-372.5 -130l-13 -43h13q190 0 190 -125q0 -27 -8 -61q-51 -215 -264 -215q-82 0 -148 16l-24 7l41 165q61 -2 94 -2q74 0 86 50q6 25 -9.5 32.5t-53.5 7.5h-46l37 173q-168 16 -293 63z" />
<glyph unicode="&#x15f;" horiz-adv-x="888" d="M96 45l51 201q197 -37 340.5 -37t143.5 100q0 27 -29 42.5t-98 35.5q-145 41 -215 96.5t-70 157.5q0 190 125 298t301 108q96 0 188.5 -15.5t137.5 -32.5l43 -16l-49 -203q-211 33 -344 33q-61 0 -94 -28.5t-33 -66.5t28.5 -56.5t148.5 -55.5t180 -85t60 -156 q0 -188 -121.5 -286.5t-324.5 -103.5l-12 -43h12q190 0 190 -125q0 -27 -8 -61q-51 -215 -264 -215q-82 0 -147 16l-25 7l41 165q61 -2 94 -2q74 0 86 50q6 25 -9 32.5t-54 7.5h-45l36 173q-137 16 -233 53z" />
<glyph unicode="&#x160;" horiz-adv-x="1013" d="M102 59l48 203q231 -41 363 -41t193.5 46t61.5 157q0 59 -36 91t-175 95.5t-198.5 139t-59.5 211.5q0 199 129 327.5t344 128.5q98 0 205.5 -19.5t165.5 -37.5l57 -21l-51 -200q-226 37 -369 37q-90 0 -142 -50.5t-52 -120t32.5 -101.5t180 -97.5t202 -138t54.5 -211.5 q0 -231 -142.5 -356.5t-384.5 -125.5q-96 0 -202.5 20.5t-165.5 41.5zM598 1898h270l60 -116l108 116h269l-328 -309h-189z" />
<glyph unicode="&#x161;" horiz-adv-x="884" d="M94 45l51 201q197 -37 340.5 -37t143.5 100q0 27 -29 42.5t-98 35.5q-145 41 -215 96.5t-70 157.5q0 190 125 298t301 108q96 0 188.5 -15.5t135.5 -32.5l45 -16l-49 -203q-212 33 -345 33q-61 0 -93.5 -28.5t-32.5 -66.5t28.5 -56.5t148 -55.5t180 -85t60.5 -156 q0 -193 -126 -291.5t-334 -98.5q-88 0 -177.5 17.5t-132.5 36.5zM451 1536h251l60 -135l108 135h265l-342 -358h-175z" />
<glyph unicode="&#x162;" horiz-adv-x="933" d="M262 -446l41 165q61 -2 94 -2q74 0 86 50q6 25 -9 32.5t-54 7.5h-45l43 193h-94l264 1147h-305l57 246h897l-57 -246h-310l-264 -1147h-84l-18 -68h12q190 0 191 -125q0 -27 -9 -61q-51 -215 -264 -215q-82 0 -147 16z" />
<glyph unicode="&#x163;" horiz-adv-x="673" d="M186 -446l41 165q61 -2 95 -2q74 0 86 50q6 25 -9.5 32.5t-54.5 7.5h-45l41 185q-68 23 -102.5 77t-34.5 132t30 205l91 385h-111l61 233h105l59 262h275l-60 -262h215l-57 -233h-213l-94 -410q-16 -70 -17 -115q0 -57 60 -57l139 10l-22 -196q-104 -45 -224 -48l-12 -43 h12q190 0 191 -125q0 -27 -8 -61q-51 -215 -265 -215q-82 0 -147 16z" />
<glyph unicode="&#x164;" horiz-adv-x="933" d="M283 1147l57 246h897l-57 -246h-310l-264 -1147h-282l264 1147h-305zM512 1898h270l60 -116l108 116h269l-328 -309h-189z" />
<glyph unicode="&#x165;" horiz-adv-x="950" d="M203 189.5q0 89.5 30 216.5l91 385h-111l61 233h105l59 262h275l-60 -262h215l-57 -233h-213l-94 -410q-17 -70 -17 -115q0 -57 60 -57l139 10l-22 -196q-109 -47 -224.5 -47.5t-176 62t-60.5 152zM918 879l124 514h250l-123 -514h-251z" />
<glyph unicode="&#x166;" horiz-adv-x="944" d="M199 508l55 244h248l92 395h-305l57 246h897l-57 -246h-309l-93 -395h263l-60 -244h-258l-117 -508h-282l116 508h-247z" />
<glyph unicode="&#x167;" horiz-adv-x="679" d="M135 410l51 237h107l35 144h-111l62 233h104l59 262h275l-60 -262h215l-57 -233h-213l-33 -144h187l-51 -237h-191l-6 -29q-16 -70 -16 -115q0 -57 59 -57l139 10l-22 -196q-109 -47 -224.5 -47.5t-176 62t-60.5 152t31 216.5v4h-103z" />
<glyph unicode="&#x168;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM614 1794q61 53 128 92t123.5 39t164 -43t125.5 -43q45 0 143 57l33 21l4 -193 q-49 -49 -118.5 -89t-119.5 -40t-159 44t-137 44q-47 0 -146 -61l-32 -18z" />
<glyph unicode="&#x169;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM467 1405q16 14 40.5 36.5t88 59.5t117 37t145.5 -44t113 -44q39 0 141 61 l33 19l8 -191l-41 -36q-25 -21 -88 -58t-112.5 -37t-142.5 45t-118 45q-51 0 -147 -61l-31 -20z" />
<glyph unicode="&#x16a;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM625 1651l51 223h670l-52 -223h-669z" />
<glyph unicode="&#x16b;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM471 1284l51 223h633l-53 -223h-631z" />
<glyph unicode="&#x16c;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM657 1843q0 27 5 55h249q-2 -10 -2 -18q0 -61 76 -61t107 79h249q-49 -145 -141 -219.5 t-253 -74.5t-225.5 68.5t-64.5 170.5z" />
<glyph unicode="&#x16d;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM487 1454q0 35 7 74h247v-15q0 -76 91.5 -75.5t123.5 90.5h248 q-106 -319 -446 -320q-270 0 -271 246z" />
<glyph unicode="&#x16e;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM721 1622q0 180 127 266q88 61 208 61.5t181 -59.5q41 -41 41 -108q0 -33 -10 -72 q-29 -117 -117 -178.5t-209 -61.5t-180 62q-41 41 -41 90zM907 1710q-6 -29 14.5 -46t61.5 -17q92 0 107 63q8 35 -17.5 49.5t-58.5 14.5q-90 0 -107 -64z" />
<glyph unicode="&#x16f;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM602 1223q1 145 107 237q70 61 167 61.5t134 -63.5q23 -37 22 -80 q0 -154 -104 -239q-72 -59 -168 -59.5t-133 63.5q-25 39 -25 80zM760 1303q-8 -23 5 -42.5t40 -19.5q55 0 74 59q8 25 -7.5 41.5t-37.5 16.5q-57 0 -74 -55z" />
<glyph unicode="&#x170;" horiz-adv-x="1218" d="M211 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t41.5 -87t147 -24.5t159 56.5t84.5 195.5l213 920h282l-213 -920q-59 -262 -193 -380t-356 -118q-446 0 -446 316zM606 1597l230 408l223 -92l-248 -395zM1026 1597l227 406l224 -92l-248 -395z" />
<glyph unicode="&#x171;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024h-262l12 100q-47 -47 -124 -86t-136 -39q-250 0 -250 289zM446 1286l318 387l182 -135l-336 -385zM842 1286l319 389l182 -137l-335 -385 z" />
<glyph unicode="&#x172;" horiz-adv-x="1224" d="M215 291q0 106 27 227l202 875h283l-213 -920q-18 -78 -18 -140.5t42 -87t147.5 -24.5t158.5 56.5t84 195.5l213 920h282l-213 -920q-51 -223 -155.5 -341t-282.5 -146q-43 -35 -80 -81t-37 -74t27 -28l76 7l-27 -226q-117 -23 -198.5 -22.5t-127 40.5t-45.5 117 q0 123 164 265q-309 43 -309 307z" />
<glyph unicode="&#x173;" horiz-adv-x="1050" d="M178 264q0 80 43 258l115 502h274l-110 -481q-37 -154 -37 -213.5t17.5 -88t56.5 -28.5q70 0 172 92l32 31l158 688h275l-236 -1024q-47 -25 -100.5 -79t-53.5 -86t27 -32l76 7l-27 -226q-117 -23 -198.5 -22.5t-127 40.5t-45.5 114.5t57.5 151.5t129.5 134l12 98 q-47 -47 -124 -86t-136 -39q-250 0 -250 289z" />
<glyph unicode="&#x174;" horiz-adv-x="1771" d="M293 0l63 1393h283l-86 -1133h29l462 1112h312l-68 -1112h29l459 1133h282l-596 -1393h-409l67 1012l-405 -1012h-422zM877 1595l360 312h164l196 -312h-249l-54 113l-127 -113h-290z" />
<glyph unicode="&#x175;" horiz-adv-x="1560" d="M217 0l59 1024h277l-66 -791h48l325 771h311l-41 -771h48l305 791h276l-418 -1024h-456l39 637l-258 -637h-449zM694 1178l344 358h164l176 -358h-250l-49 131l-118 -131h-267z" />
<glyph unicode="&#x176;" horiz-adv-x="1032" d="M324 1393h286l99 -543l346 543h299l-574 -803l-139 -590h-262l139 602zM514 1595l360 312h164l197 -312h-250l-53 113l-127 -113h-291z" />
<glyph unicode="&#x177;" horiz-adv-x="950" d="M209 -430l211 430h-170l8 1024h268l-28 -797h30l357 797h276l-680 -1454h-272zM377 1178l344 358h164l176 -358h-250l-49 131l-119 -131h-266z" />
<glyph unicode="&#x178;" horiz-adv-x="1032" d="M324 1393h286l99 -543l346 543h299l-574 -803l-139 -590h-262l139 602zM559 1614l64 276h227l-66 -276h-225zM967 1614l63 276h227l-67 -276h-223z" />
<glyph unicode="&#x179;" horiz-adv-x="1003" d="M74 0l63 281l719 817l10 49h-528l57 246h856l-65 -285l-723 -813l-12 -49h536l-57 -246h-856zM627 1784l540 186l21 -246l-541 -157z" />
<glyph unicode="&#x17a;" horiz-adv-x="847" d="M70 0l51 229l514 562h-385l59 233h707l-58 -227l-516 -564h394l-60 -233h-706zM467 1399l541 186l20 -246l-541 -159z" />
<glyph unicode="&#x17b;" horiz-adv-x="1003" d="M74 0l63 281l719 817l10 49h-528l57 246h856l-65 -285l-723 -813l-12 -49h536l-57 -246h-856zM752 1579l65 283h242l-70 -283h-237z" />
<glyph unicode="&#x17c;" horiz-adv-x="847" d="M70 0l51 229l514 562h-385l59 233h707l-58 -227l-516 -564h394l-60 -233h-706zM565 1192l66 281h237l-65 -281h-238z" />
<glyph unicode="&#x17d;" horiz-adv-x="1003" d="M74 0l63 281l719 817l10 49h-528l57 246h856l-65 -285l-723 -813l-12 -49h536l-57 -246h-856zM584 1898h270l59 -116l109 116h268l-327 -309h-189z" />
<glyph unicode="&#x17e;" horiz-adv-x="847" d="M70 0l51 229l514 562h-385l59 233h707l-58 -227l-516 -564h394l-60 -233h-706zM430 1536h252l59 -135l109 135h264l-342 -358h-174z" />
<glyph unicode="&#x192;" d="M35 -471l49 217q102 -6 158 -6q90 0 120 131l213 897h-180l58 254h182l12 43q63 221 143 303t220.5 82t265.5 -27l-55 -229q-121 4 -183 4q-106 0 -131 -149l-4 -27h273l-60 -254h-272l-215 -897q-51 -213 -142.5 -291t-261.5 -78q-49 0 -157 21z" />
<glyph unicode="&#x1fa;" horiz-adv-x="1128" d="M27 0l614 1364q-2 16 -2 42t8 62q29 119 117 179.5t208 60.5t179 -59q43 -41 43 -109q0 -96 -55 -174l-41 -1366h-283l8 242h-405l-109 -242h-282zM524 487h310l28 660h-43zM707 1972l540 187l21 -246l-541 -160zM821 1449q0 -13 18.5 -28.5t59.5 -15.5q92 0 107 63 q2 8 2 23.5t-22.5 28t-55.5 12.5q-91 -1 -107 -64q-2 -6 -2 -19z" />
<glyph unicode="&#x1fb;" horiz-adv-x="1081" d="M164 348q0 236 119 453q59 109 163.5 178.5t237.5 69.5q141 0 418 -50l94 -18l-154 -662l-55 -313l-272 -10q18 125 26 160q-84 -113 -196 -160q-47 -21 -92 -21q-150 0 -219.5 101.5t-69.5 271.5zM442 348q0 -72 22.5 -105.5t71 -33.5t109 45t100.5 90l41 47l95 395 q-136 29 -189 29q-78 0 -137 -80q-113 -152 -113 -387zM655 1223q1 145 107 237q70 61 167 61.5t134 -63.5q23 -37 22 -80q0 -154 -104 -239q-72 -59 -168 -59.5t-133 63.5q-25 39 -25 80zM725 1792l541 186l22 -266l-540 -160zM809 1283.5q0 -11.5 11 -27t38 -15.5 q55 0 74 59q2 8 2 19.5t-12.5 25t-34.5 13.5q-58 0 -74 -55q-4 -8 -4 -19.5z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1544" d="M29 0l630 1413h1145l-61 -268l-565 4l-78 -326h450l-59 -256h-453l-71 -301h565l-59 -266h-832l57 244h-292l-109 -244h-268zM524 506h238l149 641h-106zM991 1784l541 186l20 -246l-540 -157z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1462" d="M106.5 210q-0.5 60 10.5 103q59 258 313 305q115 20 188.5 20.5t96.5 0.5q14 27 14 59q0 92 -65 93q-127 0 -312 -21l-61 -8l43 239q260 45 416.5 45.5t218.5 -83.5q115 84 268.5 84t232.5 -72q86 -80 86 -230q0 -82 -26 -178l-47 -174l-551 4q-6 -28 -6 -55 q0 -119 141 -119q98 0 297 15l55 4l-47 -209q-186 -57 -389 -57.5t-278 100.5q-53 -29 -173 -65t-188 -36q-129 0 -194 105q-43 70 -43.5 130zM377 293q0 -82 63 -82q96 0 207 27q-2 20 -2 65t23 131q-51 0 -162 -8q-45 -4 -80 -33q-49 -37 -49 -100zM772 1399l541 186 l20 -246l-540 -159zM979 606l307 -4q14 51 14 82t-4 49q-16 78 -109 78t-137 -52t-71 -153z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1263" d="M111 -166l186 275q-88 106 -88 292q0 268 82 506t239.5 374t389.5 136q141 0 235 -41l186 273l152 -64l-209 -307q88 -109 88 -287q0 -281 -84 -524q-170 -492 -641 -492q-123 0 -223 39l-178 -262zM500 410l501 741q-41 20 -130 20t-150 -61q-102 -100 -161.5 -304 t-59.5 -376v-20zM580 242q41 -20 130 -20.5t158 69.5q96 96 154.5 290.5t58.5 381.5v16zM772 1784l541 186l22 -266l-540 -158z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1015" d="M115 -156l147 230q-98 94 -98 273t59.5 333t181 260.5t277.5 106.5q94 0 174 -23l133 217l146 -63l-148 -230q98 -98 98 -276t-55 -330t-177 -259.5t-286 -107.5q-100 0 -176 25l-133 -219zM436 344l129 201l160 262q-20 6 -43 6q-111 0 -178.5 -140.5t-67.5 -314.5v-14z M524 213q25 -4 43 -4q111 0 178.5 140t67.5 315v12l-125 -195zM549 1399l541 186l20 -246l-541 -159z" />
<glyph unicode="&#x218;" horiz-adv-x="1013" d="M102 59l48 203q231 -41 363 -41t193.5 46t61.5 157q0 59 -36 91t-175 95.5t-198.5 139t-59.5 211.5q0 199 129 327.5t344 128.5q98 0 205.5 -19.5t165.5 -37.5l57 -21l-51 -200q-226 37 -369 37q-90 0 -142 -50.5t-52 -120t32.5 -101.5t180 -97.5t202 -138t54.5 -211.5 q0 -231 -142.5 -356.5t-384.5 -125.5q-96 0 -202.5 20.5t-165.5 41.5zM170 -633l199 486h252l-220 -486h-231z" />
<glyph unicode="&#x219;" horiz-adv-x="884" d="M94 45l51 201q197 -37 340.5 -37t143.5 100q0 27 -29 42.5t-98 35.5q-145 41 -215 96.5t-70 157.5q0 190 125 298t301 108q96 0 188.5 -15.5t135.5 -32.5l45 -16l-49 -203q-212 33 -345 33q-61 0 -93.5 -28.5t-32.5 -66.5t28.5 -56.5t148 -55.5t180 -85t60.5 -156 q0 -193 -126 -291.5t-334 -98.5q-88 0 -177.5 17.5t-132.5 36.5zM119 -633l198 486h252l-219 -486h-231z" />
<glyph unicode="&#x21a;" horiz-adv-x="933" d="M119 -633l198 486h252l-219 -486h-231zM283 1147l57 246h897l-57 -246h-310l-264 -1147h-282l264 1147h-305z" />
<glyph unicode="&#x21b;" horiz-adv-x="673" d="M31 -633l198 486h252l-219 -486h-231zM203 189.5q0 89.5 30 216.5l91 385h-111l61 233h105l59 262h275l-60 -262h215l-57 -233h-213l-94 -410q-17 -70 -17 -115q0 -57 60 -57l139 10l-22 -196q-109 -47 -224.5 -47.5t-176 62t-60.5 152z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1619" d="M723 1178l344 358h164l176 -358h-250l-49 131l-119 -131h-266z" />
<glyph unicode="&#x2da;" horiz-adv-x="1619" d="M608 1223q1 145 105 237q72 61 169 61.5t134 -63.5q23 -37 22 -80q0 -154 -104 -239q-72 -59 -168 -59.5t-135 63.5q-23 39 -23 80zM762 1283.5q0 -11.5 11 -27t38 -15.5q55 0 74 59q2 8 2 19.5t-12.5 25t-34.5 13.5q-58 0 -74 -55q-4 -8 -4 -19.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="913" d="M403 1405q16 14 41 36.5t88.5 59.5t116.5 37t145.5 -44t112.5 -44q39 0 142 61l32 19l9 -191l-41 -36q-25 -21 -88.5 -58t-112.5 -37t-142.5 45t-119.5 45q-49 0 -144 -61l-32 -20z" />
<glyph unicode="&#x2000;" horiz-adv-x="1079" />
<glyph unicode="&#x2001;" horiz-adv-x="2159" />
<glyph unicode="&#x2002;" horiz-adv-x="1079" />
<glyph unicode="&#x2003;" horiz-adv-x="2159" />
<glyph unicode="&#x2004;" horiz-adv-x="719" />
<glyph unicode="&#x2005;" horiz-adv-x="539" />
<glyph unicode="&#x2006;" horiz-adv-x="359" />
<glyph unicode="&#x2007;" horiz-adv-x="359" />
<glyph unicode="&#x2008;" horiz-adv-x="269" />
<glyph unicode="&#x2009;" horiz-adv-x="431" />
<glyph unicode="&#x200a;" horiz-adv-x="119" />
<glyph unicode="&#x2010;" horiz-adv-x="546" d="M109 420l61 246h518l-61 -246h-518z" />
<glyph unicode="&#x2011;" horiz-adv-x="546" d="M109 420l61 246h518l-61 -246h-518z" />
<glyph unicode="&#x2012;" horiz-adv-x="546" d="M109 420l61 246h518l-61 -246h-518z" />
<glyph unicode="&#x2013;" horiz-adv-x="1077" d="M154 428v234h1024v-234h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2101" d="M154 428v234h2048v-234h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="356" d="M197 868l280 523h205l-215 -523h-270z" />
<glyph unicode="&#x2019;" horiz-adv-x="356" d="M197 870l215 523h270l-281 -523h-204z" />
<glyph unicode="&#x201a;" horiz-adv-x="307" d="M-135 -338l215 522h270l-280 -522h-205z" />
<glyph unicode="&#x201c;" horiz-adv-x="743" d="M193 870l280 523h205l-215 -523h-270zM582 870l280 523h205l-215 -523h-270z" />
<glyph unicode="&#x201d;" horiz-adv-x="745" d="M197 870l215 523h270l-281 -523h-204zM586 870l215 523h270l-280 -523h-205z" />
<glyph unicode="&#x201e;" horiz-adv-x="710" d="M-186 -348l280 522h205l-215 -522h-270zM264 -348l281 522h205l-215 -522h-271z" />
<glyph unicode="&#x2022;" horiz-adv-x="937" d="M328 205v573h512v-573h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1257" d="M16 0l72 313h279l-72 -313h-279zM487 0l72 313h279l-72 -313h-279zM963 0l71 313h279l-72 -313h-278z" />
<glyph unicode="&#x202f;" horiz-adv-x="431" />
<glyph unicode="&#x2039;" horiz-adv-x="475" d="M109 434l47 125l512 334l-76 -268l-232 -133l164 -158l-67 -246z" />
<glyph unicode="&#x203a;" horiz-adv-x="479" d="M39 88l76 268l231 133l-164 158l68 246l348 -346l-47 -125z" />
<glyph unicode="&#x205f;" horiz-adv-x="539" />
<glyph unicode="&#x20ac;" d="M186 375l50 215h83q16 94 29 141h-82l51 215h101q190 430 583 430q154 0 293 -45l47 -14l-63 -203q-123 23 -287 23t-276 -191h422l-50 -215h-456q-20 -74 -29 -141h455l-49 -215h-420q4 -160 162 -160q137 0 274 18l47 7l-22 -211q-184 -54 -336 -54q-408 0 -408 394v6 h-119z" />
<glyph unicode="&#x2122;" horiz-adv-x="1411" d="M438 1157v162h410v-162h-84v-477h-180v477h-146zM899 678v641h211l84 -324l92 324h209v-641h-168v356l-70 -313h-112l-78 313v-356h-168z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1024" d="M0 0v1024h1024v-1024h-1024z" />
<hkern u1="&#x22;" u2="&#x135;" k="-109" />
<hkern u1="&#x22;" u2="&#x12d;" k="-111" />
<hkern u1="&#x22;" u2="&#x12b;" k="-100" />
<hkern u1="&#x22;" u2="&#x129;" k="-143" />
<hkern u1="&#x22;" u2="&#xef;" k="-113" />
<hkern u1="&#x22;" u2="&#xee;" k="-131" />
<hkern u1="&#x22;" u2="&#xec;" k="-174" />
<hkern u1="&#x22;" u2="&#x40;" k="23" />
<hkern u1="&#x22;" u2="&#x2f;" k="125" />
<hkern u1="&#x22;" u2="&#x26;" k="68" />
<hkern u1="&#x26;" u2="&#x201d;" k="84" />
<hkern u1="&#x26;" u2="&#x2019;" k="84" />
<hkern u1="&#x26;" u2="&#x21a;" k="55" />
<hkern u1="&#x26;" u2="&#x1fc;" k="-14" />
<hkern u1="&#x26;" u2="&#x1fa;" k="-14" />
<hkern u1="&#x26;" u2="&#x178;" k="94" />
<hkern u1="&#x26;" u2="&#x176;" k="94" />
<hkern u1="&#x26;" u2="&#x174;" k="35" />
<hkern u1="&#x26;" u2="&#x166;" k="55" />
<hkern u1="&#x26;" u2="&#x164;" k="55" />
<hkern u1="&#x26;" u2="&#x134;" k="-27" />
<hkern u1="&#x26;" u2="&#x104;" k="-14" />
<hkern u1="&#x26;" u2="&#x102;" k="-14" />
<hkern u1="&#x26;" u2="&#x100;" k="-14" />
<hkern u1="&#x26;" u2="&#xdd;" k="94" />
<hkern u1="&#x26;" u2="&#xc6;" k="-14" />
<hkern u1="&#x26;" u2="&#xc5;" k="-14" />
<hkern u1="&#x26;" u2="&#xc4;" k="-14" />
<hkern u1="&#x26;" u2="&#xc3;" k="-14" />
<hkern u1="&#x26;" u2="&#xc2;" k="-14" />
<hkern u1="&#x26;" u2="&#xc1;" k="-14" />
<hkern u1="&#x26;" u2="&#xc0;" k="-14" />
<hkern u1="&#x26;" u2="Y" k="94" />
<hkern u1="&#x26;" u2="X" k="-12" />
<hkern u1="&#x26;" u2="W" k="35" />
<hkern u1="&#x26;" u2="V" k="45" />
<hkern u1="&#x26;" u2="T" k="55" />
<hkern u1="&#x26;" u2="J" k="-27" />
<hkern u1="&#x26;" u2="A" k="-14" />
<hkern u1="&#x26;" u2="&#x27;" k="90" />
<hkern u1="&#x26;" u2="&#x22;" k="90" />
<hkern u1="&#x27;" u2="&#x135;" k="-109" />
<hkern u1="&#x27;" u2="&#x12d;" k="-111" />
<hkern u1="&#x27;" u2="&#x12b;" k="-100" />
<hkern u1="&#x27;" u2="&#x129;" k="-143" />
<hkern u1="&#x27;" u2="&#xef;" k="-113" />
<hkern u1="&#x27;" u2="&#xee;" k="-131" />
<hkern u1="&#x27;" u2="&#xec;" k="-174" />
<hkern u1="&#x27;" u2="&#x40;" k="23" />
<hkern u1="&#x27;" u2="&#x2f;" k="125" />
<hkern u1="&#x27;" u2="&#x26;" k="68" />
<hkern u1="&#x28;" u2="&#x21b;" k="49" />
<hkern u1="&#x28;" u2="&#x219;" k="51" />
<hkern u1="&#x28;" u2="&#x218;" k="43" />
<hkern u1="&#x28;" u2="&#x1ff;" k="63" />
<hkern u1="&#x28;" u2="&#x1fe;" k="61" />
<hkern u1="&#x28;" u2="&#x1fd;" k="63" />
<hkern u1="&#x28;" u2="&#x1fc;" k="39" />
<hkern u1="&#x28;" u2="&#x1fb;" k="63" />
<hkern u1="&#x28;" u2="&#x1fa;" k="39" />
<hkern u1="&#x28;" u2="&#x17e;" k="41" />
<hkern u1="&#x28;" u2="&#x17d;" k="23" />
<hkern u1="&#x28;" u2="&#x17c;" k="41" />
<hkern u1="&#x28;" u2="&#x17b;" k="23" />
<hkern u1="&#x28;" u2="&#x17a;" k="41" />
<hkern u1="&#x28;" u2="&#x179;" k="23" />
<hkern u1="&#x28;" u2="&#x177;" k="47" />
<hkern u1="&#x28;" u2="&#x175;" k="53" />
<hkern u1="&#x28;" u2="&#x173;" k="59" />
<hkern u1="&#x28;" u2="&#x172;" k="37" />
<hkern u1="&#x28;" u2="&#x171;" k="59" />
<hkern u1="&#x28;" u2="&#x170;" k="37" />
<hkern u1="&#x28;" u2="&#x16f;" k="59" />
<hkern u1="&#x28;" u2="&#x16e;" k="37" />
<hkern u1="&#x28;" u2="&#x16d;" k="59" />
<hkern u1="&#x28;" u2="&#x16c;" k="37" />
<hkern u1="&#x28;" u2="&#x16b;" k="59" />
<hkern u1="&#x28;" u2="&#x16a;" k="37" />
<hkern u1="&#x28;" u2="&#x169;" k="59" />
<hkern u1="&#x28;" u2="&#x168;" k="37" />
<hkern u1="&#x28;" u2="&#x167;" k="49" />
<hkern u1="&#x28;" u2="&#x165;" k="49" />
<hkern u1="&#x28;" u2="&#x161;" k="51" />
<hkern u1="&#x28;" u2="&#x160;" k="43" />
<hkern u1="&#x28;" u2="&#x15f;" k="51" />
<hkern u1="&#x28;" u2="&#x15e;" k="43" />
<hkern u1="&#x28;" u2="&#x15d;" k="51" />
<hkern u1="&#x28;" u2="&#x15c;" k="43" />
<hkern u1="&#x28;" u2="&#x15b;" k="51" />
<hkern u1="&#x28;" u2="&#x15a;" k="43" />
<hkern u1="&#x28;" u2="&#x159;" k="-37" />
<hkern u1="&#x28;" u2="&#x158;" k="39" />
<hkern u1="&#x28;" u2="&#x157;" k="49" />
<hkern u1="&#x28;" u2="&#x156;" k="39" />
<hkern u1="&#x28;" u2="&#x155;" k="49" />
<hkern u1="&#x28;" u2="&#x154;" k="39" />
<hkern u1="&#x28;" u2="&#x153;" k="63" />
<hkern u1="&#x28;" u2="&#x152;" k="61" />
<hkern u1="&#x28;" u2="&#x151;" k="63" />
<hkern u1="&#x28;" u2="&#x150;" k="61" />
<hkern u1="&#x28;" u2="&#x14f;" k="63" />
<hkern u1="&#x28;" u2="&#x14e;" k="61" />
<hkern u1="&#x28;" u2="&#x14d;" k="63" />
<hkern u1="&#x28;" u2="&#x14c;" k="61" />
<hkern u1="&#x28;" u2="&#x14b;" k="49" />
<hkern u1="&#x28;" u2="&#x14a;" k="39" />
<hkern u1="&#x28;" u2="&#x148;" k="49" />
<hkern u1="&#x28;" u2="&#x147;" k="39" />
<hkern u1="&#x28;" u2="&#x146;" k="49" />
<hkern u1="&#x28;" u2="&#x145;" k="39" />
<hkern u1="&#x28;" u2="&#x144;" k="49" />
<hkern u1="&#x28;" u2="&#x143;" k="39" />
<hkern u1="&#x28;" u2="&#x142;" k="27" />
<hkern u1="&#x28;" u2="&#x141;" k="39" />
<hkern u1="&#x28;" u2="&#x13e;" k="27" />
<hkern u1="&#x28;" u2="&#x13d;" k="39" />
<hkern u1="&#x28;" u2="&#x13c;" k="27" />
<hkern u1="&#x28;" u2="&#x13b;" k="39" />
<hkern u1="&#x28;" u2="&#x13a;" k="27" />
<hkern u1="&#x28;" u2="&#x139;" k="39" />
<hkern u1="&#x28;" u2="&#x137;" k="27" />
<hkern u1="&#x28;" u2="&#x136;" k="39" />
<hkern u1="&#x28;" u2="&#x135;" k="29" />
<hkern u1="&#x28;" u2="&#x131;" k="29" />
<hkern u1="&#x28;" u2="&#x130;" k="39" />
<hkern u1="&#x28;" u2="&#x12f;" k="29" />
<hkern u1="&#x28;" u2="&#x12e;" k="-6" />
<hkern u1="&#x28;" u2="&#x12d;" k="29" />
<hkern u1="&#x28;" u2="&#x12c;" k="39" />
<hkern u1="&#x28;" u2="&#x12b;" k="29" />
<hkern u1="&#x28;" u2="&#x12a;" k="39" />
<hkern u1="&#x28;" u2="&#x129;" k="29" />
<hkern u1="&#x28;" u2="&#x128;" k="39" />
<hkern u1="&#x28;" u2="&#x127;" k="27" />
<hkern u1="&#x28;" u2="&#x126;" k="39" />
<hkern u1="&#x28;" u2="&#x125;" k="27" />
<hkern u1="&#x28;" u2="&#x124;" k="39" />
<hkern u1="&#x28;" u2="&#x122;" k="61" />
<hkern u1="&#x28;" u2="&#x120;" k="61" />
<hkern u1="&#x28;" u2="&#x11e;" k="61" />
<hkern u1="&#x28;" u2="&#x11c;" k="61" />
<hkern u1="&#x28;" u2="&#x11b;" k="63" />
<hkern u1="&#x28;" u2="&#x11a;" k="39" />
<hkern u1="&#x28;" u2="&#x119;" k="63" />
<hkern u1="&#x28;" u2="&#x118;" k="39" />
<hkern u1="&#x28;" u2="&#x117;" k="63" />
<hkern u1="&#x28;" u2="&#x116;" k="39" />
<hkern u1="&#x28;" u2="&#x115;" k="63" />
<hkern u1="&#x28;" u2="&#x114;" k="39" />
<hkern u1="&#x28;" u2="&#x113;" k="63" />
<hkern u1="&#x28;" u2="&#x112;" k="39" />
<hkern u1="&#x28;" u2="&#x111;" k="63" />
<hkern u1="&#x28;" u2="&#x110;" k="39" />
<hkern u1="&#x28;" u2="&#x10f;" k="63" />
<hkern u1="&#x28;" u2="&#x10e;" k="39" />
<hkern u1="&#x28;" u2="&#x10d;" k="63" />
<hkern u1="&#x28;" u2="&#x10c;" k="61" />
<hkern u1="&#x28;" u2="&#x10b;" k="63" />
<hkern u1="&#x28;" u2="&#x10a;" k="61" />
<hkern u1="&#x28;" u2="&#x109;" k="63" />
<hkern u1="&#x28;" u2="&#x108;" k="61" />
<hkern u1="&#x28;" u2="&#x107;" k="63" />
<hkern u1="&#x28;" u2="&#x106;" k="61" />
<hkern u1="&#x28;" u2="&#x105;" k="63" />
<hkern u1="&#x28;" u2="&#x104;" k="39" />
<hkern u1="&#x28;" u2="&#x103;" k="63" />
<hkern u1="&#x28;" u2="&#x102;" k="39" />
<hkern u1="&#x28;" u2="&#x101;" k="63" />
<hkern u1="&#x28;" u2="&#x100;" k="39" />
<hkern u1="&#x28;" u2="&#xff;" k="47" />
<hkern u1="&#x28;" u2="&#xfd;" k="47" />
<hkern u1="&#x28;" u2="&#xfc;" k="59" />
<hkern u1="&#x28;" u2="&#xfb;" k="59" />
<hkern u1="&#x28;" u2="&#xfa;" k="59" />
<hkern u1="&#x28;" u2="&#xf9;" k="59" />
<hkern u1="&#x28;" u2="&#xf8;" k="63" />
<hkern u1="&#x28;" u2="&#xf6;" k="63" />
<hkern u1="&#x28;" u2="&#xf5;" k="63" />
<hkern u1="&#x28;" u2="&#xf4;" k="63" />
<hkern u1="&#x28;" u2="&#xf3;" k="63" />
<hkern u1="&#x28;" u2="&#xf2;" k="63" />
<hkern u1="&#x28;" u2="&#xf1;" k="49" />
<hkern u1="&#x28;" u2="&#xef;" k="29" />
<hkern u1="&#x28;" u2="&#xee;" k="29" />
<hkern u1="&#x28;" u2="&#xed;" k="29" />
<hkern u1="&#x28;" u2="&#xec;" k="29" />
<hkern u1="&#x28;" u2="&#xeb;" k="63" />
<hkern u1="&#x28;" u2="&#xea;" k="63" />
<hkern u1="&#x28;" u2="&#xe9;" k="63" />
<hkern u1="&#x28;" u2="&#xe8;" k="63" />
<hkern u1="&#x28;" u2="&#xe7;" k="63" />
<hkern u1="&#x28;" u2="&#xe6;" k="63" />
<hkern u1="&#x28;" u2="&#xe5;" k="63" />
<hkern u1="&#x28;" u2="&#xe4;" k="63" />
<hkern u1="&#x28;" u2="&#xe3;" k="63" />
<hkern u1="&#x28;" u2="&#xe2;" k="63" />
<hkern u1="&#x28;" u2="&#xe1;" k="63" />
<hkern u1="&#x28;" u2="&#xe0;" k="63" />
<hkern u1="&#x28;" u2="&#xdf;" k="27" />
<hkern u1="&#x28;" u2="&#xdc;" k="37" />
<hkern u1="&#x28;" u2="&#xdb;" k="37" />
<hkern u1="&#x28;" u2="&#xda;" k="37" />
<hkern u1="&#x28;" u2="&#xd9;" k="37" />
<hkern u1="&#x28;" u2="&#xd8;" k="61" />
<hkern u1="&#x28;" u2="&#xd6;" k="61" />
<hkern u1="&#x28;" u2="&#xd5;" k="61" />
<hkern u1="&#x28;" u2="&#xd4;" k="61" />
<hkern u1="&#x28;" u2="&#xd3;" k="61" />
<hkern u1="&#x28;" u2="&#xd2;" k="61" />
<hkern u1="&#x28;" u2="&#xd1;" k="39" />
<hkern u1="&#x28;" u2="&#xcf;" k="39" />
<hkern u1="&#x28;" u2="&#xce;" k="39" />
<hkern u1="&#x28;" u2="&#xcd;" k="39" />
<hkern u1="&#x28;" u2="&#xcc;" k="39" />
<hkern u1="&#x28;" u2="&#xcb;" k="39" />
<hkern u1="&#x28;" u2="&#xca;" k="39" />
<hkern u1="&#x28;" u2="&#xc9;" k="39" />
<hkern u1="&#x28;" u2="&#xc8;" k="39" />
<hkern u1="&#x28;" u2="&#xc7;" k="61" />
<hkern u1="&#x28;" u2="&#xc6;" k="39" />
<hkern u1="&#x28;" u2="&#xc5;" k="39" />
<hkern u1="&#x28;" u2="&#xc4;" k="39" />
<hkern u1="&#x28;" u2="&#xc3;" k="39" />
<hkern u1="&#x28;" u2="&#xc2;" k="39" />
<hkern u1="&#x28;" u2="&#xc1;" k="39" />
<hkern u1="&#x28;" u2="&#xc0;" k="39" />
<hkern u1="&#x28;" u2="&#x7b;" k="51" />
<hkern u1="&#x28;" u2="z" k="41" />
<hkern u1="&#x28;" u2="y" k="47" />
<hkern u1="&#x28;" u2="w" k="53" />
<hkern u1="&#x28;" u2="v" k="47" />
<hkern u1="&#x28;" u2="u" k="59" />
<hkern u1="&#x28;" u2="t" k="49" />
<hkern u1="&#x28;" u2="s" k="51" />
<hkern u1="&#x28;" u2="r" k="49" />
<hkern u1="&#x28;" u2="q" k="63" />
<hkern u1="&#x28;" u2="p" k="49" />
<hkern u1="&#x28;" u2="o" k="63" />
<hkern u1="&#x28;" u2="n" k="49" />
<hkern u1="&#x28;" u2="m" k="49" />
<hkern u1="&#x28;" u2="l" k="27" />
<hkern u1="&#x28;" u2="k" k="27" />
<hkern u1="&#x28;" u2="j" k="29" />
<hkern u1="&#x28;" u2="i" k="29" />
<hkern u1="&#x28;" u2="h" k="27" />
<hkern u1="&#x28;" u2="f" k="39" />
<hkern u1="&#x28;" u2="e" k="63" />
<hkern u1="&#x28;" u2="d" k="63" />
<hkern u1="&#x28;" u2="c" k="63" />
<hkern u1="&#x28;" u2="b" k="25" />
<hkern u1="&#x28;" u2="a" k="63" />
<hkern u1="&#x28;" u2="Z" k="23" />
<hkern u1="&#x28;" u2="U" k="37" />
<hkern u1="&#x28;" u2="S" k="43" />
<hkern u1="&#x28;" u2="R" k="39" />
<hkern u1="&#x28;" u2="Q" k="61" />
<hkern u1="&#x28;" u2="P" k="39" />
<hkern u1="&#x28;" u2="O" k="61" />
<hkern u1="&#x28;" u2="N" k="39" />
<hkern u1="&#x28;" u2="M" k="39" />
<hkern u1="&#x28;" u2="L" k="39" />
<hkern u1="&#x28;" u2="K" k="39" />
<hkern u1="&#x28;" u2="I" k="39" />
<hkern u1="&#x28;" u2="H" k="39" />
<hkern u1="&#x28;" u2="G" k="61" />
<hkern u1="&#x28;" u2="F" k="39" />
<hkern u1="&#x28;" u2="E" k="39" />
<hkern u1="&#x28;" u2="D" k="39" />
<hkern u1="&#x28;" u2="C" k="61" />
<hkern u1="&#x28;" u2="B" k="39" />
<hkern u1="&#x28;" u2="A" k="39" />
<hkern u1="&#x28;" u2="&#x28;" k="47" />
<hkern u1="&#x29;" u2="&#x7d;" k="41" />
<hkern u1="&#x29;" u2="]" k="41" />
<hkern u1="&#x29;" u2="&#x29;" k="49" />
<hkern u1="&#x2a;" u2="&#x21a;" k="-12" />
<hkern u1="&#x2a;" u2="&#x1fc;" k="59" />
<hkern u1="&#x2a;" u2="&#x1fa;" k="59" />
<hkern u1="&#x2a;" u2="&#x177;" k="-23" />
<hkern u1="&#x2a;" u2="&#x166;" k="-12" />
<hkern u1="&#x2a;" u2="&#x164;" k="-12" />
<hkern u1="&#x2a;" u2="&#x135;" k="-106" />
<hkern u1="&#x2a;" u2="&#x134;" k="16" />
<hkern u1="&#x2a;" u2="&#x129;" k="-59" />
<hkern u1="&#x2a;" u2="&#x127;" k="-25" />
<hkern u1="&#x2a;" u2="&#x126;" k="-49" />
<hkern u1="&#x2a;" u2="&#x104;" k="59" />
<hkern u1="&#x2a;" u2="&#x102;" k="59" />
<hkern u1="&#x2a;" u2="&#x100;" k="59" />
<hkern u1="&#x2a;" u2="&#xff;" k="-23" />
<hkern u1="&#x2a;" u2="&#xfd;" k="-23" />
<hkern u1="&#x2a;" u2="&#xef;" k="-31" />
<hkern u1="&#x2a;" u2="&#xee;" k="-129" />
<hkern u1="&#x2a;" u2="&#xc6;" k="59" />
<hkern u1="&#x2a;" u2="&#xc5;" k="59" />
<hkern u1="&#x2a;" u2="&#xc4;" k="59" />
<hkern u1="&#x2a;" u2="&#xc3;" k="59" />
<hkern u1="&#x2a;" u2="&#xc2;" k="59" />
<hkern u1="&#x2a;" u2="&#xc1;" k="59" />
<hkern u1="&#x2a;" u2="&#xc0;" k="59" />
<hkern u1="&#x2a;" u2="y" k="-23" />
<hkern u1="&#x2a;" u2="x" k="-27" />
<hkern u1="&#x2a;" u2="v" k="-20" />
<hkern u1="&#x2a;" u2="T" k="-12" />
<hkern u1="&#x2a;" u2="J" k="16" />
<hkern u1="&#x2a;" u2="A" k="59" />
<hkern u1="&#x2c;" u2="v" k="61" />
<hkern u1="&#x2c;" u2="f" k="33" />
<hkern u1="&#x2c;" u2="V" k="88" />
<hkern u1="&#x2d;" u2="&#x166;" k="72" />
<hkern u1="&#x2d;" u2="&#x142;" k="-39" />
<hkern u1="&#x2d;" u2="&#x141;" k="-47" />
<hkern u1="&#x2d;" u2="x" k="63" />
<hkern u1="&#x2d;" u2="v" k="29" />
<hkern u1="&#x2d;" u2="f" k="35" />
<hkern u1="&#x2d;" u2="X" k="51" />
<hkern u1="&#x2d;" u2="V" k="66" />
<hkern u1="&#x2e;" u2="v" k="61" />
<hkern u1="&#x2e;" u2="f" k="33" />
<hkern u1="&#x2e;" u2="V" k="88" />
<hkern u1="&#x2f;" u2="&#x219;" k="29" />
<hkern u1="&#x2f;" u2="&#x1ff;" k="41" />
<hkern u1="&#x2f;" u2="&#x1fe;" k="20" />
<hkern u1="&#x2f;" u2="&#x1fd;" k="41" />
<hkern u1="&#x2f;" u2="&#x1fc;" k="76" />
<hkern u1="&#x2f;" u2="&#x1fb;" k="41" />
<hkern u1="&#x2f;" u2="&#x1fa;" k="76" />
<hkern u1="&#x2f;" u2="&#x17e;" k="23" />
<hkern u1="&#x2f;" u2="&#x17c;" k="23" />
<hkern u1="&#x2f;" u2="&#x17a;" k="23" />
<hkern u1="&#x2f;" u2="&#x173;" k="29" />
<hkern u1="&#x2f;" u2="&#x171;" k="29" />
<hkern u1="&#x2f;" u2="&#x16f;" k="29" />
<hkern u1="&#x2f;" u2="&#x16d;" k="29" />
<hkern u1="&#x2f;" u2="&#x16b;" k="29" />
<hkern u1="&#x2f;" u2="&#x169;" k="29" />
<hkern u1="&#x2f;" u2="&#x161;" k="29" />
<hkern u1="&#x2f;" u2="&#x15f;" k="29" />
<hkern u1="&#x2f;" u2="&#x15d;" k="29" />
<hkern u1="&#x2f;" u2="&#x15b;" k="29" />
<hkern u1="&#x2f;" u2="&#x159;" k="33" />
<hkern u1="&#x2f;" u2="&#x157;" k="33" />
<hkern u1="&#x2f;" u2="&#x155;" k="33" />
<hkern u1="&#x2f;" u2="&#x153;" k="41" />
<hkern u1="&#x2f;" u2="&#x152;" k="20" />
<hkern u1="&#x2f;" u2="&#x151;" k="41" />
<hkern u1="&#x2f;" u2="&#x150;" k="20" />
<hkern u1="&#x2f;" u2="&#x14f;" k="41" />
<hkern u1="&#x2f;" u2="&#x14e;" k="20" />
<hkern u1="&#x2f;" u2="&#x14d;" k="41" />
<hkern u1="&#x2f;" u2="&#x14c;" k="20" />
<hkern u1="&#x2f;" u2="&#x14b;" k="33" />
<hkern u1="&#x2f;" u2="&#x148;" k="33" />
<hkern u1="&#x2f;" u2="&#x146;" k="33" />
<hkern u1="&#x2f;" u2="&#x144;" k="33" />
<hkern u1="&#x2f;" u2="&#x135;" k="-18" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-76" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-80" />
<hkern u1="&#x2f;" u2="&#x129;" k="-117" />
<hkern u1="&#x2f;" u2="&#x123;" k="27" />
<hkern u1="&#x2f;" u2="&#x122;" k="20" />
<hkern u1="&#x2f;" u2="&#x121;" k="27" />
<hkern u1="&#x2f;" u2="&#x120;" k="20" />
<hkern u1="&#x2f;" u2="&#x11f;" k="27" />
<hkern u1="&#x2f;" u2="&#x11e;" k="20" />
<hkern u1="&#x2f;" u2="&#x11d;" k="27" />
<hkern u1="&#x2f;" u2="&#x11c;" k="20" />
<hkern u1="&#x2f;" u2="&#x11b;" k="41" />
<hkern u1="&#x2f;" u2="&#x119;" k="41" />
<hkern u1="&#x2f;" u2="&#x117;" k="41" />
<hkern u1="&#x2f;" u2="&#x115;" k="41" />
<hkern u1="&#x2f;" u2="&#x113;" k="41" />
<hkern u1="&#x2f;" u2="&#x111;" k="41" />
<hkern u1="&#x2f;" u2="&#x10f;" k="41" />
<hkern u1="&#x2f;" u2="&#x10d;" k="41" />
<hkern u1="&#x2f;" u2="&#x10c;" k="20" />
<hkern u1="&#x2f;" u2="&#x10b;" k="41" />
<hkern u1="&#x2f;" u2="&#x10a;" k="20" />
<hkern u1="&#x2f;" u2="&#x109;" k="41" />
<hkern u1="&#x2f;" u2="&#x108;" k="20" />
<hkern u1="&#x2f;" u2="&#x107;" k="41" />
<hkern u1="&#x2f;" u2="&#x106;" k="20" />
<hkern u1="&#x2f;" u2="&#x105;" k="41" />
<hkern u1="&#x2f;" u2="&#x104;" k="76" />
<hkern u1="&#x2f;" u2="&#x103;" k="41" />
<hkern u1="&#x2f;" u2="&#x102;" k="76" />
<hkern u1="&#x2f;" u2="&#x101;" k="41" />
<hkern u1="&#x2f;" u2="&#x100;" k="76" />
<hkern u1="&#x2f;" u2="&#xfc;" k="29" />
<hkern u1="&#x2f;" u2="&#xfb;" k="29" />
<hkern u1="&#x2f;" u2="&#xfa;" k="29" />
<hkern u1="&#x2f;" u2="&#xf9;" k="29" />
<hkern u1="&#x2f;" u2="&#xf8;" k="41" />
<hkern u1="&#x2f;" u2="&#xf6;" k="41" />
<hkern u1="&#x2f;" u2="&#xf5;" k="41" />
<hkern u1="&#x2f;" u2="&#xf4;" k="41" />
<hkern u1="&#x2f;" u2="&#xf3;" k="41" />
<hkern u1="&#x2f;" u2="&#xf2;" k="41" />
<hkern u1="&#x2f;" u2="&#xf1;" k="33" />
<hkern u1="&#x2f;" u2="&#xef;" k="-92" />
<hkern u1="&#x2f;" u2="&#xee;" k="-41" />
<hkern u1="&#x2f;" u2="&#xec;" k="-150" />
<hkern u1="&#x2f;" u2="&#xeb;" k="41" />
<hkern u1="&#x2f;" u2="&#xea;" k="41" />
<hkern u1="&#x2f;" u2="&#xe9;" k="41" />
<hkern u1="&#x2f;" u2="&#xe8;" k="41" />
<hkern u1="&#x2f;" u2="&#xe7;" k="41" />
<hkern u1="&#x2f;" u2="&#xe6;" k="41" />
<hkern u1="&#x2f;" u2="&#xe5;" k="41" />
<hkern u1="&#x2f;" u2="&#xe4;" k="41" />
<hkern u1="&#x2f;" u2="&#xe3;" k="41" />
<hkern u1="&#x2f;" u2="&#xe2;" k="41" />
<hkern u1="&#x2f;" u2="&#xe1;" k="41" />
<hkern u1="&#x2f;" u2="&#xe0;" k="41" />
<hkern u1="&#x2f;" u2="&#xd8;" k="20" />
<hkern u1="&#x2f;" u2="&#xd6;" k="20" />
<hkern u1="&#x2f;" u2="&#xd5;" k="20" />
<hkern u1="&#x2f;" u2="&#xd4;" k="20" />
<hkern u1="&#x2f;" u2="&#xd3;" k="20" />
<hkern u1="&#x2f;" u2="&#xd2;" k="20" />
<hkern u1="&#x2f;" u2="&#xc7;" k="20" />
<hkern u1="&#x2f;" u2="&#xc6;" k="76" />
<hkern u1="&#x2f;" u2="&#xc5;" k="76" />
<hkern u1="&#x2f;" u2="&#xc4;" k="76" />
<hkern u1="&#x2f;" u2="&#xc3;" k="76" />
<hkern u1="&#x2f;" u2="&#xc2;" k="76" />
<hkern u1="&#x2f;" u2="&#xc1;" k="76" />
<hkern u1="&#x2f;" u2="&#xc0;" k="76" />
<hkern u1="&#x2f;" u2="z" k="23" />
<hkern u1="&#x2f;" u2="u" k="29" />
<hkern u1="&#x2f;" u2="s" k="29" />
<hkern u1="&#x2f;" u2="r" k="33" />
<hkern u1="&#x2f;" u2="q" k="41" />
<hkern u1="&#x2f;" u2="p" k="33" />
<hkern u1="&#x2f;" u2="o" k="41" />
<hkern u1="&#x2f;" u2="n" k="33" />
<hkern u1="&#x2f;" u2="m" k="33" />
<hkern u1="&#x2f;" u2="g" k="27" />
<hkern u1="&#x2f;" u2="e" k="41" />
<hkern u1="&#x2f;" u2="d" k="41" />
<hkern u1="&#x2f;" u2="c" k="41" />
<hkern u1="&#x2f;" u2="a" k="41" />
<hkern u1="&#x2f;" u2="Q" k="20" />
<hkern u1="&#x2f;" u2="O" k="20" />
<hkern u1="&#x2f;" u2="G" k="20" />
<hkern u1="&#x2f;" u2="C" k="20" />
<hkern u1="&#x2f;" u2="A" k="76" />
<hkern u1="&#x2f;" u2="&#x2f;" k="532" />
<hkern u1="&#x3a;" u2="&#x142;" k="-25" />
<hkern u1="&#x3a;" u2="&#x141;" k="-20" />
<hkern u1="&#x3a;" u2="V" k="45" />
<hkern u1="&#x3b;" u2="&#x142;" k="-20" />
<hkern u1="&#x3b;" u2="&#x141;" k="-16" />
<hkern u1="&#x3b;" u2="V" k="45" />
<hkern u1="&#x40;" u2="&#x21a;" k="20" />
<hkern u1="&#x40;" u2="&#x178;" k="63" />
<hkern u1="&#x40;" u2="&#x176;" k="63" />
<hkern u1="&#x40;" u2="&#x166;" k="20" />
<hkern u1="&#x40;" u2="&#x164;" k="20" />
<hkern u1="&#x40;" u2="&#xdd;" k="63" />
<hkern u1="&#x40;" u2="Y" k="63" />
<hkern u1="&#x40;" u2="V" k="25" />
<hkern u1="&#x40;" u2="T" k="20" />
<hkern u1="A" u2="&#x2122;" k="68" />
<hkern u1="A" u2="&#xae;" k="57" />
<hkern u1="A" u2="&#x7d;" k="27" />
<hkern u1="A" u2="&#x7c;" k="94" />
<hkern u1="A" u2="v" k="31" />
<hkern u1="A" u2="f" k="14" />
<hkern u1="A" u2="\" k="84" />
<hkern u1="A" u2="V" k="61" />
<hkern u1="A" u2="&#x3f;" k="35" />
<hkern u1="A" u2="&#x2a;" k="61" />
<hkern u1="A" u2="&#x29;" k="33" />
<hkern u1="B" u2="&#x201d;" k="20" />
<hkern u1="B" u2="&#x201c;" k="23" />
<hkern u1="B" u2="&#x2019;" k="20" />
<hkern u1="B" u2="&#x2018;" k="23" />
<hkern u1="B" u2="&#x21a;" k="14" />
<hkern u1="B" u2="&#x1fc;" k="23" />
<hkern u1="B" u2="&#x1fa;" k="23" />
<hkern u1="B" u2="&#x178;" k="61" />
<hkern u1="B" u2="&#x176;" k="61" />
<hkern u1="B" u2="&#x174;" k="20" />
<hkern u1="B" u2="&#x166;" k="14" />
<hkern u1="B" u2="&#x164;" k="14" />
<hkern u1="B" u2="&#x123;" k="10" />
<hkern u1="B" u2="&#x121;" k="10" />
<hkern u1="B" u2="&#x11f;" k="10" />
<hkern u1="B" u2="&#x11d;" k="10" />
<hkern u1="B" u2="&#x104;" k="23" />
<hkern u1="B" u2="&#x102;" k="23" />
<hkern u1="B" u2="&#x100;" k="23" />
<hkern u1="B" u2="&#xee;" k="-20" />
<hkern u1="B" u2="&#xdd;" k="61" />
<hkern u1="B" u2="&#xc6;" k="23" />
<hkern u1="B" u2="&#xc5;" k="23" />
<hkern u1="B" u2="&#xc4;" k="23" />
<hkern u1="B" u2="&#xc3;" k="23" />
<hkern u1="B" u2="&#xc2;" k="23" />
<hkern u1="B" u2="&#xc1;" k="23" />
<hkern u1="B" u2="&#xc0;" k="23" />
<hkern u1="B" u2="&#x7d;" k="57" />
<hkern u1="B" u2="&#x7c;" k="55" />
<hkern u1="B" u2="g" k="10" />
<hkern u1="B" u2="]" k="70" />
<hkern u1="B" u2="\" k="41" />
<hkern u1="B" u2="Y" k="61" />
<hkern u1="B" u2="X" k="14" />
<hkern u1="B" u2="W" k="20" />
<hkern u1="B" u2="V" k="29" />
<hkern u1="B" u2="T" k="14" />
<hkern u1="B" u2="A" k="23" />
<hkern u1="B" u2="&#x2f;" k="31" />
<hkern u1="B" u2="&#x29;" k="59" />
<hkern u1="B" u2="&#x27;" k="23" />
<hkern u1="B" u2="&#x22;" k="23" />
<hkern u1="C" u2="&#x135;" k="-82" />
<hkern u1="C" u2="&#x12d;" k="-63" />
<hkern u1="C" u2="&#x12b;" k="-80" />
<hkern u1="C" u2="&#x129;" k="-109" />
<hkern u1="C" u2="&#xef;" k="-90" />
<hkern u1="C" u2="&#xee;" k="-104" />
<hkern u1="C" u2="&#xec;" k="-111" />
<hkern u1="C" u2="&#x2a;" k="-33" />
<hkern u1="C" u2="&#x29;" k="25" />
<hkern u1="D" u2="&#x2122;" k="31" />
<hkern u1="D" u2="&#x7d;" k="61" />
<hkern u1="D" u2="&#x7c;" k="57" />
<hkern u1="D" u2="x" k="10" />
<hkern u1="D" u2="]" k="70" />
<hkern u1="D" u2="\" k="49" />
<hkern u1="D" u2="X" k="37" />
<hkern u1="D" u2="V" k="29" />
<hkern u1="D" u2="&#x2f;" k="35" />
<hkern u1="D" u2="&#x29;" k="66" />
<hkern u1="E" u2="&#x135;" k="-82" />
<hkern u1="E" u2="&#x12d;" k="-70" />
<hkern u1="E" u2="&#x12b;" k="-63" />
<hkern u1="E" u2="&#x129;" k="-106" />
<hkern u1="E" u2="&#xef;" k="-78" />
<hkern u1="E" u2="&#xee;" k="-104" />
<hkern u1="E" u2="&#xec;" k="-141" />
<hkern u1="E" u2="v" k="14" />
<hkern u1="E" u2="&#x29;" k="20" />
<hkern u1="F" u2="&#x203a;" k="37" />
<hkern u1="F" u2="&#x2039;" k="35" />
<hkern u1="F" u2="&#x2026;" k="84" />
<hkern u1="F" u2="&#x201e;" k="84" />
<hkern u1="F" u2="&#x201c;" k="20" />
<hkern u1="F" u2="&#x201a;" k="84" />
<hkern u1="F" u2="&#x2018;" k="20" />
<hkern u1="F" u2="&#x2014;" k="29" />
<hkern u1="F" u2="&#x2013;" k="29" />
<hkern u1="F" u2="&#x219;" k="16" />
<hkern u1="F" u2="&#x1ff;" k="18" />
<hkern u1="F" u2="&#x1fd;" k="18" />
<hkern u1="F" u2="&#x1fc;" k="41" />
<hkern u1="F" u2="&#x1fb;" k="18" />
<hkern u1="F" u2="&#x1fa;" k="41" />
<hkern u1="F" u2="&#x17e;" k="12" />
<hkern u1="F" u2="&#x17c;" k="12" />
<hkern u1="F" u2="&#x17a;" k="12" />
<hkern u1="F" u2="&#x173;" k="16" />
<hkern u1="F" u2="&#x171;" k="16" />
<hkern u1="F" u2="&#x16f;" k="16" />
<hkern u1="F" u2="&#x16d;" k="16" />
<hkern u1="F" u2="&#x16b;" k="16" />
<hkern u1="F" u2="&#x169;" k="16" />
<hkern u1="F" u2="&#x161;" k="16" />
<hkern u1="F" u2="&#x15f;" k="16" />
<hkern u1="F" u2="&#x15d;" k="16" />
<hkern u1="F" u2="&#x15b;" k="16" />
<hkern u1="F" u2="&#x159;" k="23" />
<hkern u1="F" u2="&#x157;" k="23" />
<hkern u1="F" u2="&#x155;" k="23" />
<hkern u1="F" u2="&#x153;" k="18" />
<hkern u1="F" u2="&#x151;" k="18" />
<hkern u1="F" u2="&#x14f;" k="18" />
<hkern u1="F" u2="&#x14d;" k="18" />
<hkern u1="F" u2="&#x14b;" k="23" />
<hkern u1="F" u2="&#x148;" k="23" />
<hkern u1="F" u2="&#x146;" k="23" />
<hkern u1="F" u2="&#x144;" k="23" />
<hkern u1="F" u2="&#x135;" k="-111" />
<hkern u1="F" u2="&#x134;" k="10" />
<hkern u1="F" u2="&#x131;" k="23" />
<hkern u1="F" u2="&#x12d;" k="-100" />
<hkern u1="F" u2="&#x12b;" k="-94" />
<hkern u1="F" u2="&#x129;" k="-135" />
<hkern u1="F" u2="&#x123;" k="14" />
<hkern u1="F" u2="&#x121;" k="14" />
<hkern u1="F" u2="&#x11f;" k="14" />
<hkern u1="F" u2="&#x11d;" k="14" />
<hkern u1="F" u2="&#x11b;" k="18" />
<hkern u1="F" u2="&#x119;" k="18" />
<hkern u1="F" u2="&#x117;" k="18" />
<hkern u1="F" u2="&#x115;" k="18" />
<hkern u1="F" u2="&#x113;" k="18" />
<hkern u1="F" u2="&#x111;" k="18" />
<hkern u1="F" u2="&#x10f;" k="18" />
<hkern u1="F" u2="&#x10d;" k="18" />
<hkern u1="F" u2="&#x10b;" k="18" />
<hkern u1="F" u2="&#x109;" k="18" />
<hkern u1="F" u2="&#x107;" k="18" />
<hkern u1="F" u2="&#x105;" k="18" />
<hkern u1="F" u2="&#x104;" k="41" />
<hkern u1="F" u2="&#x103;" k="18" />
<hkern u1="F" u2="&#x102;" k="41" />
<hkern u1="F" u2="&#x101;" k="18" />
<hkern u1="F" u2="&#x100;" k="41" />
<hkern u1="F" u2="&#xfc;" k="16" />
<hkern u1="F" u2="&#xfb;" k="16" />
<hkern u1="F" u2="&#xfa;" k="16" />
<hkern u1="F" u2="&#xf9;" k="16" />
<hkern u1="F" u2="&#xf8;" k="18" />
<hkern u1="F" u2="&#xf6;" k="18" />
<hkern u1="F" u2="&#xf5;" k="18" />
<hkern u1="F" u2="&#xf4;" k="18" />
<hkern u1="F" u2="&#xf3;" k="18" />
<hkern u1="F" u2="&#xf2;" k="18" />
<hkern u1="F" u2="&#xf1;" k="23" />
<hkern u1="F" u2="&#xef;" k="-109" />
<hkern u1="F" u2="&#xee;" k="-135" />
<hkern u1="F" u2="&#xec;" k="-170" />
<hkern u1="F" u2="&#xeb;" k="18" />
<hkern u1="F" u2="&#xea;" k="18" />
<hkern u1="F" u2="&#xe9;" k="18" />
<hkern u1="F" u2="&#xe8;" k="18" />
<hkern u1="F" u2="&#xe7;" k="18" />
<hkern u1="F" u2="&#xe6;" k="18" />
<hkern u1="F" u2="&#xe5;" k="18" />
<hkern u1="F" u2="&#xe4;" k="18" />
<hkern u1="F" u2="&#xe3;" k="18" />
<hkern u1="F" u2="&#xe2;" k="18" />
<hkern u1="F" u2="&#xe1;" k="18" />
<hkern u1="F" u2="&#xe0;" k="18" />
<hkern u1="F" u2="&#xc6;" k="41" />
<hkern u1="F" u2="&#xc5;" k="41" />
<hkern u1="F" u2="&#xc4;" k="41" />
<hkern u1="F" u2="&#xc3;" k="41" />
<hkern u1="F" u2="&#xc2;" k="41" />
<hkern u1="F" u2="&#xc1;" k="41" />
<hkern u1="F" u2="&#xc0;" k="41" />
<hkern u1="F" u2="&#xbb;" k="37" />
<hkern u1="F" u2="&#xab;" k="35" />
<hkern u1="F" u2="z" k="12" />
<hkern u1="F" u2="u" k="16" />
<hkern u1="F" u2="s" k="16" />
<hkern u1="F" u2="r" k="23" />
<hkern u1="F" u2="q" k="18" />
<hkern u1="F" u2="p" k="23" />
<hkern u1="F" u2="o" k="18" />
<hkern u1="F" u2="n" k="23" />
<hkern u1="F" u2="m" k="23" />
<hkern u1="F" u2="g" k="14" />
<hkern u1="F" u2="e" k="18" />
<hkern u1="F" u2="d" k="18" />
<hkern u1="F" u2="c" k="18" />
<hkern u1="F" u2="a" k="18" />
<hkern u1="F" u2="J" k="10" />
<hkern u1="F" u2="A" k="41" />
<hkern u1="F" u2="&#x3b;" k="31" />
<hkern u1="F" u2="&#x3a;" k="31" />
<hkern u1="F" u2="&#x2f;" k="63" />
<hkern u1="F" u2="&#x2e;" k="84" />
<hkern u1="F" u2="&#x2d;" k="29" />
<hkern u1="F" u2="&#x2c;" k="84" />
<hkern u1="F" u2="&#x2a;" k="-23" />
<hkern u1="G" u2="&#x135;" k="-45" />
<hkern u1="G" u2="&#x12d;" k="-29" />
<hkern u1="G" u2="&#x12b;" k="-41" />
<hkern u1="G" u2="&#x129;" k="-68" />
<hkern u1="G" u2="&#xef;" k="-53" />
<hkern u1="G" u2="&#xee;" k="-68" />
<hkern u1="G" u2="&#xec;" k="-68" />
<hkern u1="G" u2="&#x7d;" k="25" />
<hkern u1="G" u2="&#x7c;" k="25" />
<hkern u1="G" u2="f" k="10" />
<hkern u1="G" u2="]" k="23" />
<hkern u1="G" u2="\" k="20" />
<hkern u1="G" u2="V" k="23" />
<hkern u1="G" u2="&#x29;" k="43" />
<hkern u1="H" u2="&#x135;" k="-18" />
<hkern u1="H" u2="&#x129;" k="-45" />
<hkern u1="H" u2="&#xef;" k="-16" />
<hkern u1="H" u2="&#xee;" k="-41" />
<hkern u1="H" u2="&#xec;" k="-78" />
<hkern u1="H" u2="&#x7d;" k="27" />
<hkern u1="H" u2="&#x7c;" k="25" />
<hkern u1="H" u2="f" k="12" />
<hkern u1="H" u2="]" k="25" />
<hkern u1="H" u2="&#x29;" k="41" />
<hkern u1="I" u2="&#x135;" k="-18" />
<hkern u1="I" u2="&#x129;" k="-45" />
<hkern u1="I" u2="&#xef;" k="-16" />
<hkern u1="I" u2="&#xee;" k="-41" />
<hkern u1="I" u2="&#xec;" k="-78" />
<hkern u1="I" u2="&#x7d;" k="27" />
<hkern u1="I" u2="&#x7c;" k="25" />
<hkern u1="I" u2="f" k="12" />
<hkern u1="I" u2="]" k="25" />
<hkern u1="I" u2="&#x29;" k="41" />
<hkern u1="J" u2="&#x135;" k="-27" />
<hkern u1="J" u2="&#x12d;" k="-12" />
<hkern u1="J" u2="&#x129;" k="-51" />
<hkern u1="J" u2="&#xef;" k="-23" />
<hkern u1="J" u2="&#xee;" k="-49" />
<hkern u1="J" u2="&#xec;" k="-84" />
<hkern u1="J" u2="&#x7d;" k="23" />
<hkern u1="J" u2="&#x7c;" k="23" />
<hkern u1="J" u2="f" k="10" />
<hkern u1="J" u2="]" k="23" />
<hkern u1="J" u2="&#x29;" k="39" />
<hkern u1="K" u2="&#x135;" k="-49" />
<hkern u1="K" u2="&#x12d;" k="-111" />
<hkern u1="K" u2="&#x12b;" k="-109" />
<hkern u1="K" u2="&#x129;" k="-147" />
<hkern u1="K" u2="&#xef;" k="-119" />
<hkern u1="K" u2="&#xee;" k="-68" />
<hkern u1="K" u2="&#xec;" k="-156" />
<hkern u1="K" u2="v" k="25" />
<hkern u1="K" u2="f" k="14" />
<hkern u1="K" u2="&#x2a;" k="-18" />
<hkern u1="L" u2="&#x2122;" k="129" />
<hkern u1="L" u2="&#x201e;" k="-14" />
<hkern u1="L" u2="&#xae;" k="131" />
<hkern u1="L" u2="&#x7c;" k="82" />
<hkern u1="L" u2="v" k="70" />
<hkern u1="L" u2="f" k="12" />
<hkern u1="L" u2="\" k="131" />
<hkern u1="L" u2="V" k="115" />
<hkern u1="L" u2="&#x3f;" k="20" />
<hkern u1="L" u2="&#x3b;" k="-14" />
<hkern u1="L" u2="&#x2c;" k="-12" />
<hkern u1="L" u2="&#x2a;" k="129" />
<hkern u1="M" u2="&#x135;" k="-18" />
<hkern u1="M" u2="&#x129;" k="-45" />
<hkern u1="M" u2="&#xef;" k="-16" />
<hkern u1="M" u2="&#xee;" k="-41" />
<hkern u1="M" u2="&#xec;" k="-78" />
<hkern u1="M" u2="&#x7d;" k="27" />
<hkern u1="M" u2="&#x7c;" k="25" />
<hkern u1="M" u2="f" k="12" />
<hkern u1="M" u2="]" k="25" />
<hkern u1="M" u2="&#x29;" k="41" />
<hkern u1="N" u2="&#x135;" k="-18" />
<hkern u1="N" u2="&#x129;" k="-45" />
<hkern u1="N" u2="&#xef;" k="-16" />
<hkern u1="N" u2="&#xee;" k="-41" />
<hkern u1="N" u2="&#xec;" k="-78" />
<hkern u1="N" u2="&#x7d;" k="27" />
<hkern u1="N" u2="&#x7c;" k="25" />
<hkern u1="N" u2="f" k="12" />
<hkern u1="N" u2="]" k="25" />
<hkern u1="N" u2="&#x29;" k="41" />
<hkern u1="O" u2="&#x2122;" k="29" />
<hkern u1="O" u2="&#x7d;" k="61" />
<hkern u1="O" u2="&#x7c;" k="57" />
<hkern u1="O" u2="x" k="10" />
<hkern u1="O" u2="]" k="70" />
<hkern u1="O" u2="\" k="49" />
<hkern u1="O" u2="X" k="31" />
<hkern u1="O" u2="V" k="29" />
<hkern u1="O" u2="&#x2f;" k="33" />
<hkern u1="O" u2="&#x29;" k="66" />
<hkern u1="P" u2="&#x2039;" k="35" />
<hkern u1="P" u2="&#x2026;" k="102" />
<hkern u1="P" u2="&#x201e;" k="102" />
<hkern u1="P" u2="&#x201a;" k="102" />
<hkern u1="P" u2="&#x1fc;" k="43" />
<hkern u1="P" u2="&#x1fa;" k="43" />
<hkern u1="P" u2="&#x17d;" k="14" />
<hkern u1="P" u2="&#x17b;" k="14" />
<hkern u1="P" u2="&#x179;" k="14" />
<hkern u1="P" u2="&#x178;" k="55" />
<hkern u1="P" u2="&#x176;" k="55" />
<hkern u1="P" u2="&#x174;" k="23" />
<hkern u1="P" u2="&#x135;" k="-18" />
<hkern u1="P" u2="&#x134;" k="10" />
<hkern u1="P" u2="&#x104;" k="43" />
<hkern u1="P" u2="&#x102;" k="43" />
<hkern u1="P" u2="&#x100;" k="43" />
<hkern u1="P" u2="&#xee;" k="-41" />
<hkern u1="P" u2="&#xdd;" k="55" />
<hkern u1="P" u2="&#xc6;" k="43" />
<hkern u1="P" u2="&#xc5;" k="43" />
<hkern u1="P" u2="&#xc4;" k="43" />
<hkern u1="P" u2="&#xc3;" k="43" />
<hkern u1="P" u2="&#xc2;" k="43" />
<hkern u1="P" u2="&#xc1;" k="43" />
<hkern u1="P" u2="&#xc0;" k="43" />
<hkern u1="P" u2="&#xab;" k="35" />
<hkern u1="P" u2="&#x7d;" k="59" />
<hkern u1="P" u2="&#x7c;" k="43" />
<hkern u1="P" u2="]" k="70" />
<hkern u1="P" u2="\" k="35" />
<hkern u1="P" u2="Z" k="14" />
<hkern u1="P" u2="Y" k="55" />
<hkern u1="P" u2="X" k="45" />
<hkern u1="P" u2="W" k="23" />
<hkern u1="P" u2="V" k="29" />
<hkern u1="P" u2="J" k="10" />
<hkern u1="P" u2="A" k="43" />
<hkern u1="P" u2="&#x2f;" k="63" />
<hkern u1="P" u2="&#x2e;" k="102" />
<hkern u1="P" u2="&#x2c;" k="102" />
<hkern u1="P" u2="&#x29;" k="63" />
<hkern u1="Q" u2="&#x2122;" k="29" />
<hkern u1="Q" u2="&#x7d;" k="61" />
<hkern u1="Q" u2="&#x7c;" k="57" />
<hkern u1="Q" u2="x" k="10" />
<hkern u1="Q" u2="]" k="70" />
<hkern u1="Q" u2="\" k="49" />
<hkern u1="Q" u2="X" k="31" />
<hkern u1="Q" u2="V" k="29" />
<hkern u1="Q" u2="&#x2f;" k="33" />
<hkern u1="Q" u2="&#x29;" k="66" />
<hkern u1="R" u2="&#xee;" k="-16" />
<hkern u1="R" u2="&#x7d;" k="31" />
<hkern u1="R" u2="&#x7c;" k="51" />
<hkern u1="R" u2="\" k="39" />
<hkern u1="R" u2="V" k="27" />
<hkern u1="R" u2="&#x2f;" k="27" />
<hkern u1="R" u2="&#x29;" k="35" />
<hkern u1="S" u2="&#x135;" k="-25" />
<hkern u1="S" u2="&#x129;" k="-37" />
<hkern u1="S" u2="&#xef;" k="-23" />
<hkern u1="S" u2="&#xee;" k="-47" />
<hkern u1="S" u2="&#xec;" k="-57" />
<hkern u1="S" u2="&#x7d;" k="35" />
<hkern u1="S" u2="&#x7c;" k="37" />
<hkern u1="S" u2="v" k="10" />
<hkern u1="S" u2="f" k="12" />
<hkern u1="S" u2="]" k="35" />
<hkern u1="S" u2="\" k="25" />
<hkern u1="S" u2="V" k="25" />
<hkern u1="S" u2="&#x29;" k="47" />
<hkern u1="T" u2="&#x15d;" k="68" />
<hkern u1="T" u2="&#x159;" k="66" />
<hkern u1="T" u2="&#x155;" k="14" />
<hkern u1="T" u2="&#x151;" k="74" />
<hkern u1="T" u2="&#x135;" k="-135" />
<hkern u1="T" u2="&#x131;" k="94" />
<hkern u1="T" u2="&#x12d;" k="-121" />
<hkern u1="T" u2="&#x12b;" k="-117" />
<hkern u1="T" u2="&#x129;" k="-158" />
<hkern u1="T" u2="&#x127;" k="-23" />
<hkern u1="T" u2="&#x11f;" k="104" />
<hkern u1="T" u2="&#x11d;" k="72" />
<hkern u1="T" u2="&#x109;" k="100" />
<hkern u1="T" u2="&#xf5;" k="113" />
<hkern u1="T" u2="&#xef;" k="-131" />
<hkern u1="T" u2="&#xee;" k="-158" />
<hkern u1="T" u2="&#xec;" k="-195" />
<hkern u1="T" u2="&#xea;" k="80" />
<hkern u1="T" u2="&#xe8;" k="63" />
<hkern u1="T" u2="x" k="74" />
<hkern u1="T" u2="v" k="80" />
<hkern u1="T" u2="f" k="18" />
<hkern u1="T" u2="&#x2f;" k="86" />
<hkern u1="T" u2="&#x2a;" k="-43" />
<hkern u1="T" u2="&#x26;" k="25" />
<hkern u1="U" u2="&#x135;" k="-31" />
<hkern u1="U" u2="&#x12d;" k="-18" />
<hkern u1="U" u2="&#x12b;" k="-14" />
<hkern u1="U" u2="&#x129;" k="-55" />
<hkern u1="U" u2="&#xef;" k="-27" />
<hkern u1="U" u2="&#xee;" k="-53" />
<hkern u1="U" u2="&#xec;" k="-90" />
<hkern u1="U" u2="&#x7d;" k="25" />
<hkern u1="U" u2="&#x7c;" k="20" />
<hkern u1="U" u2="f" k="10" />
<hkern u1="U" u2="]" k="23" />
<hkern u1="U" u2="&#x2f;" k="31" />
<hkern u1="U" u2="&#x29;" k="41" />
<hkern u1="V" u2="&#x203a;" k="57" />
<hkern u1="V" u2="&#x2039;" k="80" />
<hkern u1="V" u2="&#x2026;" k="94" />
<hkern u1="V" u2="&#x201e;" k="94" />
<hkern u1="V" u2="&#x201c;" k="37" />
<hkern u1="V" u2="&#x201a;" k="94" />
<hkern u1="V" u2="&#x2018;" k="37" />
<hkern u1="V" u2="&#x2014;" k="72" />
<hkern u1="V" u2="&#x2013;" k="72" />
<hkern u1="V" u2="&#x219;" k="39" />
<hkern u1="V" u2="&#x218;" k="18" />
<hkern u1="V" u2="&#x1ff;" k="53" />
<hkern u1="V" u2="&#x1fe;" k="31" />
<hkern u1="V" u2="&#x1fd;" k="53" />
<hkern u1="V" u2="&#x1fc;" k="59" />
<hkern u1="V" u2="&#x1fb;" k="53" />
<hkern u1="V" u2="&#x1fa;" k="59" />
<hkern u1="V" u2="&#x17e;" k="27" />
<hkern u1="V" u2="&#x17c;" k="27" />
<hkern u1="V" u2="&#x17a;" k="27" />
<hkern u1="V" u2="&#x177;" k="10" />
<hkern u1="V" u2="&#x175;" k="18" />
<hkern u1="V" u2="&#x173;" k="29" />
<hkern u1="V" u2="&#x171;" k="29" />
<hkern u1="V" u2="&#x16f;" k="29" />
<hkern u1="V" u2="&#x16d;" k="29" />
<hkern u1="V" u2="&#x16b;" k="29" />
<hkern u1="V" u2="&#x169;" k="29" />
<hkern u1="V" u2="&#x161;" k="39" />
<hkern u1="V" u2="&#x160;" k="18" />
<hkern u1="V" u2="&#x15f;" k="39" />
<hkern u1="V" u2="&#x15e;" k="18" />
<hkern u1="V" u2="&#x15d;" k="39" />
<hkern u1="V" u2="&#x15c;" k="18" />
<hkern u1="V" u2="&#x15b;" k="39" />
<hkern u1="V" u2="&#x15a;" k="18" />
<hkern u1="V" u2="&#x159;" k="20" />
<hkern u1="V" u2="&#x157;" k="35" />
<hkern u1="V" u2="&#x155;" k="20" />
<hkern u1="V" u2="&#x153;" k="53" />
<hkern u1="V" u2="&#x152;" k="31" />
<hkern u1="V" u2="&#x151;" k="53" />
<hkern u1="V" u2="&#x150;" k="31" />
<hkern u1="V" u2="&#x14f;" k="53" />
<hkern u1="V" u2="&#x14e;" k="31" />
<hkern u1="V" u2="&#x14d;" k="53" />
<hkern u1="V" u2="&#x14c;" k="31" />
<hkern u1="V" u2="&#x14b;" k="35" />
<hkern u1="V" u2="&#x148;" k="35" />
<hkern u1="V" u2="&#x146;" k="35" />
<hkern u1="V" u2="&#x144;" k="35" />
<hkern u1="V" u2="&#x135;" k="-80" />
<hkern u1="V" u2="&#x134;" k="16" />
<hkern u1="V" u2="&#x131;" k="35" />
<hkern u1="V" u2="&#x12d;" k="-113" />
<hkern u1="V" u2="&#x12b;" k="-106" />
<hkern u1="V" u2="&#x129;" k="-147" />
<hkern u1="V" u2="&#x127;" k="-12" />
<hkern u1="V" u2="&#x123;" k="35" />
<hkern u1="V" u2="&#x122;" k="31" />
<hkern u1="V" u2="&#x121;" k="35" />
<hkern u1="V" u2="&#x120;" k="31" />
<hkern u1="V" u2="&#x11f;" k="35" />
<hkern u1="V" u2="&#x11e;" k="31" />
<hkern u1="V" u2="&#x11d;" k="35" />
<hkern u1="V" u2="&#x11c;" k="31" />
<hkern u1="V" u2="&#x11b;" k="53" />
<hkern u1="V" u2="&#x119;" k="53" />
<hkern u1="V" u2="&#x117;" k="53" />
<hkern u1="V" u2="&#x115;" k="53" />
<hkern u1="V" u2="&#x113;" k="53" />
<hkern u1="V" u2="&#x111;" k="53" />
<hkern u1="V" u2="&#x10f;" k="53" />
<hkern u1="V" u2="&#x10d;" k="53" />
<hkern u1="V" u2="&#x10c;" k="31" />
<hkern u1="V" u2="&#x10b;" k="53" />
<hkern u1="V" u2="&#x10a;" k="31" />
<hkern u1="V" u2="&#x109;" k="53" />
<hkern u1="V" u2="&#x108;" k="31" />
<hkern u1="V" u2="&#x107;" k="53" />
<hkern u1="V" u2="&#x106;" k="31" />
<hkern u1="V" u2="&#x105;" k="53" />
<hkern u1="V" u2="&#x104;" k="59" />
<hkern u1="V" u2="&#x103;" k="53" />
<hkern u1="V" u2="&#x102;" k="59" />
<hkern u1="V" u2="&#x101;" k="53" />
<hkern u1="V" u2="&#x100;" k="59" />
<hkern u1="V" u2="&#xff;" k="10" />
<hkern u1="V" u2="&#xfd;" k="10" />
<hkern u1="V" u2="&#xfc;" k="29" />
<hkern u1="V" u2="&#xfb;" k="29" />
<hkern u1="V" u2="&#xfa;" k="29" />
<hkern u1="V" u2="&#xf9;" k="29" />
<hkern u1="V" u2="&#xf8;" k="53" />
<hkern u1="V" u2="&#xf6;" k="53" />
<hkern u1="V" u2="&#xf5;" k="53" />
<hkern u1="V" u2="&#xf4;" k="53" />
<hkern u1="V" u2="&#xf3;" k="53" />
<hkern u1="V" u2="&#xf2;" k="53" />
<hkern u1="V" u2="&#xf1;" k="35" />
<hkern u1="V" u2="&#xef;" k="-121" />
<hkern u1="V" u2="&#xee;" k="-98" />
<hkern u1="V" u2="&#xec;" k="-162" />
<hkern u1="V" u2="&#xeb;" k="53" />
<hkern u1="V" u2="&#xea;" k="53" />
<hkern u1="V" u2="&#xe9;" k="53" />
<hkern u1="V" u2="&#xe8;" k="53" />
<hkern u1="V" u2="&#xe7;" k="53" />
<hkern u1="V" u2="&#xe6;" k="53" />
<hkern u1="V" u2="&#xe5;" k="53" />
<hkern u1="V" u2="&#xe4;" k="53" />
<hkern u1="V" u2="&#xe3;" k="53" />
<hkern u1="V" u2="&#xe2;" k="53" />
<hkern u1="V" u2="&#xe1;" k="53" />
<hkern u1="V" u2="&#xe0;" k="53" />
<hkern u1="V" u2="&#xd8;" k="31" />
<hkern u1="V" u2="&#xd6;" k="31" />
<hkern u1="V" u2="&#xd5;" k="31" />
<hkern u1="V" u2="&#xd4;" k="31" />
<hkern u1="V" u2="&#xd3;" k="31" />
<hkern u1="V" u2="&#xd2;" k="31" />
<hkern u1="V" u2="&#xc7;" k="31" />
<hkern u1="V" u2="&#xc6;" k="59" />
<hkern u1="V" u2="&#xc5;" k="59" />
<hkern u1="V" u2="&#xc4;" k="59" />
<hkern u1="V" u2="&#xc3;" k="59" />
<hkern u1="V" u2="&#xc2;" k="59" />
<hkern u1="V" u2="&#xc1;" k="59" />
<hkern u1="V" u2="&#xc0;" k="59" />
<hkern u1="V" u2="&#xbb;" k="57" />
<hkern u1="V" u2="&#xae;" k="20" />
<hkern u1="V" u2="&#xab;" k="80" />
<hkern u1="V" u2="z" k="27" />
<hkern u1="V" u2="y" k="10" />
<hkern u1="V" u2="x" k="12" />
<hkern u1="V" u2="w" k="18" />
<hkern u1="V" u2="v" k="10" />
<hkern u1="V" u2="u" k="29" />
<hkern u1="V" u2="s" k="39" />
<hkern u1="V" u2="r" k="35" />
<hkern u1="V" u2="q" k="53" />
<hkern u1="V" u2="p" k="35" />
<hkern u1="V" u2="o" k="53" />
<hkern u1="V" u2="n" k="35" />
<hkern u1="V" u2="m" k="35" />
<hkern u1="V" u2="g" k="35" />
<hkern u1="V" u2="e" k="53" />
<hkern u1="V" u2="d" k="53" />
<hkern u1="V" u2="c" k="53" />
<hkern u1="V" u2="a" k="53" />
<hkern u1="V" u2="S" k="18" />
<hkern u1="V" u2="Q" k="31" />
<hkern u1="V" u2="O" k="31" />
<hkern u1="V" u2="J" k="16" />
<hkern u1="V" u2="G" k="31" />
<hkern u1="V" u2="C" k="31" />
<hkern u1="V" u2="A" k="59" />
<hkern u1="V" u2="&#x40;" k="35" />
<hkern u1="V" u2="&#x3b;" k="51" />
<hkern u1="V" u2="&#x3a;" k="51" />
<hkern u1="V" u2="&#x2f;" k="84" />
<hkern u1="V" u2="&#x2e;" k="94" />
<hkern u1="V" u2="&#x2d;" k="72" />
<hkern u1="V" u2="&#x2c;" k="94" />
<hkern u1="V" u2="&#x2a;" k="-39" />
<hkern u1="V" u2="&#x26;" k="41" />
<hkern u1="W" u2="&#x159;" k="18" />
<hkern u1="W" u2="&#x155;" k="18" />
<hkern u1="W" u2="&#x135;" k="-76" />
<hkern u1="W" u2="&#x131;" k="31" />
<hkern u1="W" u2="&#x12d;" k="-104" />
<hkern u1="W" u2="&#x12b;" k="-100" />
<hkern u1="W" u2="&#x129;" k="-141" />
<hkern u1="W" u2="&#xef;" k="-115" />
<hkern u1="W" u2="&#xee;" k="-96" />
<hkern u1="W" u2="&#xec;" k="-156" />
<hkern u1="W" u2="x" k="10" />
<hkern u1="W" u2="v" k="10" />
<hkern u1="W" u2="&#x40;" k="29" />
<hkern u1="W" u2="&#x2f;" k="74" />
<hkern u1="W" u2="&#x2a;" k="-37" />
<hkern u1="W" u2="&#x29;" k="25" />
<hkern u1="W" u2="&#x26;" k="35" />
<hkern u1="X" u2="&#x2039;" k="63" />
<hkern u1="X" u2="&#x201c;" k="37" />
<hkern u1="X" u2="&#x2018;" k="37" />
<hkern u1="X" u2="&#x2014;" k="66" />
<hkern u1="X" u2="&#x2013;" k="66" />
<hkern u1="X" u2="&#x21b;" k="12" />
<hkern u1="X" u2="&#x1ff;" k="55" />
<hkern u1="X" u2="&#x1fe;" k="33" />
<hkern u1="X" u2="&#x1fd;" k="51" />
<hkern u1="X" u2="&#x1fb;" k="51" />
<hkern u1="X" u2="&#x177;" k="25" />
<hkern u1="X" u2="&#x175;" k="31" />
<hkern u1="X" u2="&#x173;" k="39" />
<hkern u1="X" u2="&#x171;" k="39" />
<hkern u1="X" u2="&#x16f;" k="39" />
<hkern u1="X" u2="&#x16d;" k="39" />
<hkern u1="X" u2="&#x16b;" k="39" />
<hkern u1="X" u2="&#x169;" k="39" />
<hkern u1="X" u2="&#x167;" k="12" />
<hkern u1="X" u2="&#x165;" k="12" />
<hkern u1="X" u2="&#x153;" k="55" />
<hkern u1="X" u2="&#x152;" k="33" />
<hkern u1="X" u2="&#x151;" k="55" />
<hkern u1="X" u2="&#x150;" k="33" />
<hkern u1="X" u2="&#x14f;" k="55" />
<hkern u1="X" u2="&#x14e;" k="33" />
<hkern u1="X" u2="&#x14d;" k="55" />
<hkern u1="X" u2="&#x14c;" k="33" />
<hkern u1="X" u2="&#x135;" k="-49" />
<hkern u1="X" u2="&#x12d;" k="-127" />
<hkern u1="X" u2="&#x12b;" k="-123" />
<hkern u1="X" u2="&#x129;" k="-164" />
<hkern u1="X" u2="&#x127;" k="-16" />
<hkern u1="X" u2="&#x123;" k="41" />
<hkern u1="X" u2="&#x122;" k="33" />
<hkern u1="X" u2="&#x121;" k="41" />
<hkern u1="X" u2="&#x120;" k="33" />
<hkern u1="X" u2="&#x11f;" k="41" />
<hkern u1="X" u2="&#x11e;" k="33" />
<hkern u1="X" u2="&#x11d;" k="41" />
<hkern u1="X" u2="&#x11c;" k="33" />
<hkern u1="X" u2="&#x11b;" k="55" />
<hkern u1="X" u2="&#x119;" k="55" />
<hkern u1="X" u2="&#x117;" k="55" />
<hkern u1="X" u2="&#x115;" k="55" />
<hkern u1="X" u2="&#x113;" k="55" />
<hkern u1="X" u2="&#x111;" k="51" />
<hkern u1="X" u2="&#x10f;" k="51" />
<hkern u1="X" u2="&#x10d;" k="55" />
<hkern u1="X" u2="&#x10c;" k="33" />
<hkern u1="X" u2="&#x10b;" k="55" />
<hkern u1="X" u2="&#x10a;" k="33" />
<hkern u1="X" u2="&#x109;" k="55" />
<hkern u1="X" u2="&#x108;" k="33" />
<hkern u1="X" u2="&#x107;" k="55" />
<hkern u1="X" u2="&#x106;" k="33" />
<hkern u1="X" u2="&#x105;" k="51" />
<hkern u1="X" u2="&#x103;" k="51" />
<hkern u1="X" u2="&#x101;" k="51" />
<hkern u1="X" u2="&#xff;" k="25" />
<hkern u1="X" u2="&#xfd;" k="25" />
<hkern u1="X" u2="&#xfc;" k="39" />
<hkern u1="X" u2="&#xfb;" k="39" />
<hkern u1="X" u2="&#xfa;" k="39" />
<hkern u1="X" u2="&#xf9;" k="39" />
<hkern u1="X" u2="&#xf8;" k="55" />
<hkern u1="X" u2="&#xf6;" k="55" />
<hkern u1="X" u2="&#xf5;" k="55" />
<hkern u1="X" u2="&#xf4;" k="55" />
<hkern u1="X" u2="&#xf3;" k="55" />
<hkern u1="X" u2="&#xf2;" k="55" />
<hkern u1="X" u2="&#xef;" k="-135" />
<hkern u1="X" u2="&#xee;" k="-66" />
<hkern u1="X" u2="&#xec;" k="-170" />
<hkern u1="X" u2="&#xeb;" k="55" />
<hkern u1="X" u2="&#xea;" k="55" />
<hkern u1="X" u2="&#xe9;" k="55" />
<hkern u1="X" u2="&#xe8;" k="55" />
<hkern u1="X" u2="&#xe7;" k="55" />
<hkern u1="X" u2="&#xe6;" k="51" />
<hkern u1="X" u2="&#xe5;" k="51" />
<hkern u1="X" u2="&#xe4;" k="51" />
<hkern u1="X" u2="&#xe3;" k="51" />
<hkern u1="X" u2="&#xe2;" k="51" />
<hkern u1="X" u2="&#xe1;" k="51" />
<hkern u1="X" u2="&#xe0;" k="51" />
<hkern u1="X" u2="&#xd8;" k="33" />
<hkern u1="X" u2="&#xd6;" k="33" />
<hkern u1="X" u2="&#xd5;" k="33" />
<hkern u1="X" u2="&#xd4;" k="33" />
<hkern u1="X" u2="&#xd3;" k="33" />
<hkern u1="X" u2="&#xd2;" k="33" />
<hkern u1="X" u2="&#xc7;" k="33" />
<hkern u1="X" u2="&#xab;" k="63" />
<hkern u1="X" u2="&#x7c;" k="-10" />
<hkern u1="X" u2="y" k="25" />
<hkern u1="X" u2="w" k="31" />
<hkern u1="X" u2="v" k="25" />
<hkern u1="X" u2="u" k="39" />
<hkern u1="X" u2="t" k="12" />
<hkern u1="X" u2="q" k="51" />
<hkern u1="X" u2="o" k="55" />
<hkern u1="X" u2="g" k="41" />
<hkern u1="X" u2="f" k="10" />
<hkern u1="X" u2="e" k="55" />
<hkern u1="X" u2="d" k="51" />
<hkern u1="X" u2="c" k="55" />
<hkern u1="X" u2="a" k="51" />
<hkern u1="X" u2="Q" k="33" />
<hkern u1="X" u2="O" k="33" />
<hkern u1="X" u2="G" k="33" />
<hkern u1="X" u2="C" k="33" />
<hkern u1="X" u2="&#x3f;" k="-10" />
<hkern u1="X" u2="&#x2d;" k="66" />
<hkern u1="X" u2="&#x2a;" k="-31" />
<hkern u1="Y" u2="&#x17a;" k="80" />
<hkern u1="Y" u2="&#x177;" k="41" />
<hkern u1="Y" u2="&#x171;" k="88" />
<hkern u1="Y" u2="&#x16b;" k="88" />
<hkern u1="Y" u2="&#x169;" k="80" />
<hkern u1="Y" u2="&#x159;" k="47" />
<hkern u1="Y" u2="&#x155;" k="-6" />
<hkern u1="Y" u2="&#x151;" k="82" />
<hkern u1="Y" u2="&#x14f;" k="88" />
<hkern u1="Y" u2="&#x14d;" k="117" />
<hkern u1="Y" u2="&#x135;" k="-59" />
<hkern u1="Y" u2="&#x131;" k="115" />
<hkern u1="Y" u2="&#x12d;" k="-141" />
<hkern u1="Y" u2="&#x12b;" k="-135" />
<hkern u1="Y" u2="&#x129;" k="-178" />
<hkern u1="Y" u2="&#x127;" k="-18" />
<hkern u1="Y" u2="&#x11f;" k="88" />
<hkern u1="Y" u2="&#x113;" k="102" />
<hkern u1="Y" u2="&#xff;" k="37" />
<hkern u1="Y" u2="&#xfc;" k="82" />
<hkern u1="Y" u2="&#xf5;" k="88" />
<hkern u1="Y" u2="&#xf2;" k="111" />
<hkern u1="Y" u2="&#xf1;" k="115" />
<hkern u1="Y" u2="&#xef;" k="-150" />
<hkern u1="Y" u2="&#xee;" k="-78" />
<hkern u1="Y" u2="&#xed;" k="-14" />
<hkern u1="Y" u2="&#xec;" k="-182" />
<hkern u1="Y" u2="&#xeb;" k="100" />
<hkern u1="Y" u2="&#xea;" k="121" />
<hkern u1="Y" u2="&#xe8;" k="51" />
<hkern u1="Y" u2="&#xae;" k="35" />
<hkern u1="Y" u2="&#x7d;" k="-12" />
<hkern u1="Y" u2="&#x7c;" k="-25" />
<hkern u1="Y" u2="x" k="66" />
<hkern u1="Y" u2="v" k="63" />
<hkern u1="Y" u2="f" k="33" />
<hkern u1="Y" u2="]" k="-12" />
<hkern u1="Y" u2="&#x40;" k="55" />
<hkern u1="Y" u2="&#x2f;" k="131" />
<hkern u1="Y" u2="&#x2a;" k="-43" />
<hkern u1="Y" u2="&#x26;" k="66" />
<hkern u1="Z" u2="&#x135;" k="-78" />
<hkern u1="Z" u2="&#x12d;" k="-66" />
<hkern u1="Z" u2="&#x12b;" k="-61" />
<hkern u1="Z" u2="&#x129;" k="-102" />
<hkern u1="Z" u2="&#xef;" k="-74" />
<hkern u1="Z" u2="&#xee;" k="-100" />
<hkern u1="Z" u2="&#xec;" k="-137" />
<hkern u1="Z" u2="&#x29;" k="25" />
<hkern u1="[" u2="&#x21b;" k="37" />
<hkern u1="[" u2="&#x219;" k="51" />
<hkern u1="[" u2="&#x218;" k="41" />
<hkern u1="[" u2="&#x1ff;" k="63" />
<hkern u1="[" u2="&#x1fe;" k="66" />
<hkern u1="[" u2="&#x1fd;" k="63" />
<hkern u1="[" u2="&#x1fb;" k="63" />
<hkern u1="[" u2="&#x17e;" k="20" />
<hkern u1="[" u2="&#x17c;" k="20" />
<hkern u1="[" u2="&#x17a;" k="20" />
<hkern u1="[" u2="&#x178;" k="-18" />
<hkern u1="[" u2="&#x177;" k="37" />
<hkern u1="[" u2="&#x176;" k="-18" />
<hkern u1="[" u2="&#x175;" k="43" />
<hkern u1="[" u2="&#x173;" k="53" />
<hkern u1="[" u2="&#x172;" k="20" />
<hkern u1="[" u2="&#x171;" k="53" />
<hkern u1="[" u2="&#x170;" k="20" />
<hkern u1="[" u2="&#x16f;" k="53" />
<hkern u1="[" u2="&#x16e;" k="20" />
<hkern u1="[" u2="&#x16d;" k="53" />
<hkern u1="[" u2="&#x16c;" k="20" />
<hkern u1="[" u2="&#x16b;" k="53" />
<hkern u1="[" u2="&#x16a;" k="20" />
<hkern u1="[" u2="&#x169;" k="53" />
<hkern u1="[" u2="&#x168;" k="20" />
<hkern u1="[" u2="&#x167;" k="37" />
<hkern u1="[" u2="&#x165;" k="37" />
<hkern u1="[" u2="&#x161;" k="51" />
<hkern u1="[" u2="&#x160;" k="41" />
<hkern u1="[" u2="&#x15f;" k="51" />
<hkern u1="[" u2="&#x15e;" k="41" />
<hkern u1="[" u2="&#x15d;" k="51" />
<hkern u1="[" u2="&#x15c;" k="41" />
<hkern u1="[" u2="&#x15b;" k="51" />
<hkern u1="[" u2="&#x15a;" k="41" />
<hkern u1="[" u2="&#x159;" k="-14" />
<hkern u1="[" u2="&#x158;" k="23" />
<hkern u1="[" u2="&#x157;" k="31" />
<hkern u1="[" u2="&#x156;" k="23" />
<hkern u1="[" u2="&#x155;" k="31" />
<hkern u1="[" u2="&#x154;" k="23" />
<hkern u1="[" u2="&#x153;" k="63" />
<hkern u1="[" u2="&#x152;" k="66" />
<hkern u1="[" u2="&#x151;" k="63" />
<hkern u1="[" u2="&#x150;" k="66" />
<hkern u1="[" u2="&#x14f;" k="63" />
<hkern u1="[" u2="&#x14e;" k="66" />
<hkern u1="[" u2="&#x14d;" k="63" />
<hkern u1="[" u2="&#x14c;" k="66" />
<hkern u1="[" u2="&#x14b;" k="31" />
<hkern u1="[" u2="&#x14a;" k="23" />
<hkern u1="[" u2="&#x148;" k="31" />
<hkern u1="[" u2="&#x147;" k="23" />
<hkern u1="[" u2="&#x146;" k="31" />
<hkern u1="[" u2="&#x145;" k="23" />
<hkern u1="[" u2="&#x144;" k="31" />
<hkern u1="[" u2="&#x143;" k="23" />
<hkern u1="[" u2="&#x141;" k="23" />
<hkern u1="[" u2="&#x13d;" k="23" />
<hkern u1="[" u2="&#x13b;" k="23" />
<hkern u1="[" u2="&#x139;" k="23" />
<hkern u1="[" u2="&#x136;" k="23" />
<hkern u1="[" u2="&#x135;" k="-80" />
<hkern u1="[" u2="&#x130;" k="23" />
<hkern u1="[" u2="&#x12f;" k="-23" />
<hkern u1="[" u2="&#x12e;" k="23" />
<hkern u1="[" u2="&#x12d;" k="-137" />
<hkern u1="[" u2="&#x12c;" k="23" />
<hkern u1="[" u2="&#x12b;" k="-104" />
<hkern u1="[" u2="&#x12a;" k="23" />
<hkern u1="[" u2="&#x129;" k="-147" />
<hkern u1="[" u2="&#x128;" k="23" />
<hkern u1="[" u2="&#x126;" k="23" />
<hkern u1="[" u2="&#x124;" k="23" />
<hkern u1="[" u2="&#x122;" k="66" />
<hkern u1="[" u2="&#x120;" k="66" />
<hkern u1="[" u2="&#x11e;" k="66" />
<hkern u1="[" u2="&#x11c;" k="66" />
<hkern u1="[" u2="&#x11b;" k="63" />
<hkern u1="[" u2="&#x11a;" k="23" />
<hkern u1="[" u2="&#x119;" k="63" />
<hkern u1="[" u2="&#x118;" k="23" />
<hkern u1="[" u2="&#x117;" k="63" />
<hkern u1="[" u2="&#x116;" k="23" />
<hkern u1="[" u2="&#x115;" k="63" />
<hkern u1="[" u2="&#x114;" k="23" />
<hkern u1="[" u2="&#x113;" k="63" />
<hkern u1="[" u2="&#x112;" k="23" />
<hkern u1="[" u2="&#x111;" k="63" />
<hkern u1="[" u2="&#x110;" k="23" />
<hkern u1="[" u2="&#x10f;" k="63" />
<hkern u1="[" u2="&#x10e;" k="23" />
<hkern u1="[" u2="&#x10d;" k="63" />
<hkern u1="[" u2="&#x10c;" k="66" />
<hkern u1="[" u2="&#x10b;" k="63" />
<hkern u1="[" u2="&#x10a;" k="66" />
<hkern u1="[" u2="&#x109;" k="63" />
<hkern u1="[" u2="&#x108;" k="66" />
<hkern u1="[" u2="&#x107;" k="63" />
<hkern u1="[" u2="&#x106;" k="66" />
<hkern u1="[" u2="&#x105;" k="63" />
<hkern u1="[" u2="&#x103;" k="63" />
<hkern u1="[" u2="&#x101;" k="63" />
<hkern u1="[" u2="&#xff;" k="37" />
<hkern u1="[" u2="&#xfd;" k="37" />
<hkern u1="[" u2="&#xfc;" k="53" />
<hkern u1="[" u2="&#xfb;" k="53" />
<hkern u1="[" u2="&#xfa;" k="53" />
<hkern u1="[" u2="&#xf9;" k="53" />
<hkern u1="[" u2="&#xf8;" k="63" />
<hkern u1="[" u2="&#xf6;" k="63" />
<hkern u1="[" u2="&#xf5;" k="63" />
<hkern u1="[" u2="&#xf4;" k="63" />
<hkern u1="[" u2="&#xf3;" k="63" />
<hkern u1="[" u2="&#xf2;" k="63" />
<hkern u1="[" u2="&#xf1;" k="31" />
<hkern u1="[" u2="&#xef;" k="-117" />
<hkern u1="[" u2="&#xee;" k="-51" />
<hkern u1="[" u2="&#xec;" k="-180" />
<hkern u1="[" u2="&#xeb;" k="63" />
<hkern u1="[" u2="&#xea;" k="63" />
<hkern u1="[" u2="&#xe9;" k="63" />
<hkern u1="[" u2="&#xe8;" k="63" />
<hkern u1="[" u2="&#xe7;" k="63" />
<hkern u1="[" u2="&#xe6;" k="63" />
<hkern u1="[" u2="&#xe5;" k="63" />
<hkern u1="[" u2="&#xe4;" k="63" />
<hkern u1="[" u2="&#xe3;" k="63" />
<hkern u1="[" u2="&#xe2;" k="63" />
<hkern u1="[" u2="&#xe1;" k="63" />
<hkern u1="[" u2="&#xe0;" k="63" />
<hkern u1="[" u2="&#xdd;" k="-18" />
<hkern u1="[" u2="&#xdc;" k="20" />
<hkern u1="[" u2="&#xdb;" k="20" />
<hkern u1="[" u2="&#xda;" k="20" />
<hkern u1="[" u2="&#xd9;" k="20" />
<hkern u1="[" u2="&#xd8;" k="66" />
<hkern u1="[" u2="&#xd6;" k="66" />
<hkern u1="[" u2="&#xd5;" k="66" />
<hkern u1="[" u2="&#xd4;" k="66" />
<hkern u1="[" u2="&#xd3;" k="66" />
<hkern u1="[" u2="&#xd2;" k="66" />
<hkern u1="[" u2="&#xd1;" k="23" />
<hkern u1="[" u2="&#xcf;" k="23" />
<hkern u1="[" u2="&#xce;" k="23" />
<hkern u1="[" u2="&#xcd;" k="23" />
<hkern u1="[" u2="&#xcc;" k="23" />
<hkern u1="[" u2="&#xcb;" k="23" />
<hkern u1="[" u2="&#xca;" k="23" />
<hkern u1="[" u2="&#xc9;" k="23" />
<hkern u1="[" u2="&#xc8;" k="23" />
<hkern u1="[" u2="&#xc7;" k="66" />
<hkern u1="[" u2="&#x7b;" k="53" />
<hkern u1="[" u2="z" k="20" />
<hkern u1="[" u2="y" k="37" />
<hkern u1="[" u2="w" k="43" />
<hkern u1="[" u2="v" k="37" />
<hkern u1="[" u2="u" k="53" />
<hkern u1="[" u2="t" k="37" />
<hkern u1="[" u2="s" k="51" />
<hkern u1="[" u2="r" k="31" />
<hkern u1="[" u2="q" k="63" />
<hkern u1="[" u2="p" k="31" />
<hkern u1="[" u2="o" k="63" />
<hkern u1="[" u2="n" k="31" />
<hkern u1="[" u2="m" k="31" />
<hkern u1="[" u2="j" k="-80" />
<hkern u1="[" u2="f" k="35" />
<hkern u1="[" u2="e" k="63" />
<hkern u1="[" u2="d" k="63" />
<hkern u1="[" u2="c" k="63" />
<hkern u1="[" u2="a" k="63" />
<hkern u1="[" u2="Y" k="-18" />
<hkern u1="[" u2="U" k="20" />
<hkern u1="[" u2="S" k="41" />
<hkern u1="[" u2="R" k="23" />
<hkern u1="[" u2="Q" k="66" />
<hkern u1="[" u2="P" k="23" />
<hkern u1="[" u2="O" k="66" />
<hkern u1="[" u2="N" k="23" />
<hkern u1="[" u2="M" k="23" />
<hkern u1="[" u2="L" k="23" />
<hkern u1="[" u2="K" k="23" />
<hkern u1="[" u2="I" k="23" />
<hkern u1="[" u2="H" k="23" />
<hkern u1="[" u2="G" k="66" />
<hkern u1="[" u2="F" k="23" />
<hkern u1="[" u2="E" k="23" />
<hkern u1="[" u2="D" k="23" />
<hkern u1="[" u2="C" k="66" />
<hkern u1="[" u2="B" k="23" />
<hkern u1="[" u2="&#x28;" k="41" />
<hkern u1="\" u2="&#x201d;" k="117" />
<hkern u1="\" u2="&#x2019;" k="117" />
<hkern u1="\" u2="&#x21b;" k="27" />
<hkern u1="\" u2="&#x21a;" k="84" />
<hkern u1="\" u2="&#x1ff;" k="20" />
<hkern u1="\" u2="&#x1fe;" k="41" />
<hkern u1="\" u2="&#x178;" k="127" />
<hkern u1="\" u2="&#x177;" k="35" />
<hkern u1="\" u2="&#x176;" k="127" />
<hkern u1="\" u2="&#x175;" k="27" />
<hkern u1="\" u2="&#x174;" k="70" />
<hkern u1="\" u2="&#x172;" k="37" />
<hkern u1="\" u2="&#x170;" k="37" />
<hkern u1="\" u2="&#x16e;" k="37" />
<hkern u1="\" u2="&#x16c;" k="37" />
<hkern u1="\" u2="&#x16a;" k="37" />
<hkern u1="\" u2="&#x168;" k="37" />
<hkern u1="\" u2="&#x167;" k="27" />
<hkern u1="\" u2="&#x166;" k="84" />
<hkern u1="\" u2="&#x165;" k="27" />
<hkern u1="\" u2="&#x164;" k="84" />
<hkern u1="\" u2="&#x153;" k="20" />
<hkern u1="\" u2="&#x152;" k="41" />
<hkern u1="\" u2="&#x151;" k="20" />
<hkern u1="\" u2="&#x150;" k="41" />
<hkern u1="\" u2="&#x14f;" k="20" />
<hkern u1="\" u2="&#x14e;" k="41" />
<hkern u1="\" u2="&#x14d;" k="20" />
<hkern u1="\" u2="&#x14c;" k="41" />
<hkern u1="\" u2="&#x122;" k="41" />
<hkern u1="\" u2="&#x120;" k="41" />
<hkern u1="\" u2="&#x11e;" k="41" />
<hkern u1="\" u2="&#x11c;" k="41" />
<hkern u1="\" u2="&#x11b;" k="20" />
<hkern u1="\" u2="&#x119;" k="20" />
<hkern u1="\" u2="&#x117;" k="20" />
<hkern u1="\" u2="&#x115;" k="20" />
<hkern u1="\" u2="&#x113;" k="20" />
<hkern u1="\" u2="&#x10d;" k="20" />
<hkern u1="\" u2="&#x10c;" k="41" />
<hkern u1="\" u2="&#x10b;" k="20" />
<hkern u1="\" u2="&#x10a;" k="41" />
<hkern u1="\" u2="&#x109;" k="20" />
<hkern u1="\" u2="&#x108;" k="41" />
<hkern u1="\" u2="&#x107;" k="20" />
<hkern u1="\" u2="&#x106;" k="41" />
<hkern u1="\" u2="&#xff;" k="35" />
<hkern u1="\" u2="&#xfd;" k="35" />
<hkern u1="\" u2="&#xf8;" k="20" />
<hkern u1="\" u2="&#xf6;" k="20" />
<hkern u1="\" u2="&#xf5;" k="20" />
<hkern u1="\" u2="&#xf4;" k="20" />
<hkern u1="\" u2="&#xf3;" k="20" />
<hkern u1="\" u2="&#xf2;" k="20" />
<hkern u1="\" u2="&#xeb;" k="20" />
<hkern u1="\" u2="&#xea;" k="20" />
<hkern u1="\" u2="&#xe9;" k="20" />
<hkern u1="\" u2="&#xe8;" k="20" />
<hkern u1="\" u2="&#xe7;" k="20" />
<hkern u1="\" u2="&#xdd;" k="127" />
<hkern u1="\" u2="&#xdc;" k="37" />
<hkern u1="\" u2="&#xdb;" k="37" />
<hkern u1="\" u2="&#xda;" k="37" />
<hkern u1="\" u2="&#xd9;" k="37" />
<hkern u1="\" u2="&#xd8;" k="41" />
<hkern u1="\" u2="&#xd6;" k="41" />
<hkern u1="\" u2="&#xd5;" k="41" />
<hkern u1="\" u2="&#xd4;" k="41" />
<hkern u1="\" u2="&#xd3;" k="41" />
<hkern u1="\" u2="&#xd2;" k="41" />
<hkern u1="\" u2="&#xc7;" k="41" />
<hkern u1="\" u2="y" k="35" />
<hkern u1="\" u2="w" k="27" />
<hkern u1="\" u2="v" k="35" />
<hkern u1="\" u2="t" k="27" />
<hkern u1="\" u2="o" k="20" />
<hkern u1="\" u2="e" k="20" />
<hkern u1="\" u2="c" k="20" />
<hkern u1="\" u2="Y" k="127" />
<hkern u1="\" u2="W" k="70" />
<hkern u1="\" u2="V" k="82" />
<hkern u1="\" u2="U" k="37" />
<hkern u1="\" u2="T" k="84" />
<hkern u1="\" u2="Q" k="41" />
<hkern u1="\" u2="O" k="41" />
<hkern u1="\" u2="G" k="41" />
<hkern u1="\" u2="C" k="41" />
<hkern u1="\" u2="&#x27;" k="123" />
<hkern u1="\" u2="&#x22;" k="123" />
<hkern u1="a" u2="&#x2122;" k="43" />
<hkern u1="a" u2="&#x7d;" k="41" />
<hkern u1="a" u2="&#x7c;" k="55" />
<hkern u1="a" u2="]" k="31" />
<hkern u1="a" u2="\" k="63" />
<hkern u1="a" u2="V" k="51" />
<hkern u1="a" u2="&#x3f;" k="29" />
<hkern u1="a" u2="&#x2a;" k="16" />
<hkern u1="a" u2="&#x29;" k="45" />
<hkern u1="b" u2="&#x2122;" k="66" />
<hkern u1="b" u2="&#xae;" k="35" />
<hkern u1="b" u2="&#x7d;" k="55" />
<hkern u1="b" u2="&#x7c;" k="70" />
<hkern u1="b" u2="x" k="23" />
<hkern u1="b" u2="v" k="14" />
<hkern u1="b" u2="f" k="8" />
<hkern u1="b" u2="]" k="61" />
<hkern u1="b" u2="\" k="92" />
<hkern u1="b" u2="X" k="18" />
<hkern u1="b" u2="V" k="66" />
<hkern u1="b" u2="&#x3f;" k="41" />
<hkern u1="b" u2="&#x2a;" k="43" />
<hkern u1="b" u2="&#x29;" k="59" />
<hkern u1="c" u2="&#x2122;" k="33" />
<hkern u1="c" u2="&#x7d;" k="31" />
<hkern u1="c" u2="&#x7c;" k="43" />
<hkern u1="c" u2="]" k="23" />
<hkern u1="c" u2="\" k="49" />
<hkern u1="c" u2="V" k="27" />
<hkern u1="c" u2="&#x29;" k="33" />
<hkern u1="d" u2="&#x135;" k="-39" />
<hkern u1="d" u2="&#x12d;" k="-41" />
<hkern u1="d" u2="&#x12b;" k="-25" />
<hkern u1="d" u2="&#x129;" k="-66" />
<hkern u1="d" u2="&#xef;" k="-37" />
<hkern u1="d" u2="&#xee;" k="-61" />
<hkern u1="d" u2="&#xec;" k="-100" />
<hkern u1="d" u2="&#x29;" k="31" />
<hkern u1="e" u2="&#x2122;" k="51" />
<hkern u1="e" u2="&#x142;" k="-27" />
<hkern u1="e" u2="&#x7d;" k="47" />
<hkern u1="e" u2="&#x7c;" k="59" />
<hkern u1="e" u2="v" k="10" />
<hkern u1="e" u2="]" k="39" />
<hkern u1="e" u2="\" k="76" />
<hkern u1="e" u2="V" k="49" />
<hkern u1="e" u2="&#x3f;" k="31" />
<hkern u1="e" u2="&#x2a;" k="29" />
<hkern u1="e" u2="&#x29;" k="47" />
<hkern u1="f" u2="&#x2039;" k="70" />
<hkern u1="f" u2="&#x2026;" k="68" />
<hkern u1="f" u2="&#x201e;" k="68" />
<hkern u1="f" u2="&#x201a;" k="68" />
<hkern u1="f" u2="&#x2014;" k="57" />
<hkern u1="f" u2="&#x2013;" k="57" />
<hkern u1="f" u2="&#x1fc;" k="29" />
<hkern u1="f" u2="&#x1fa;" k="29" />
<hkern u1="f" u2="&#x178;" k="-16" />
<hkern u1="f" u2="&#x176;" k="-16" />
<hkern u1="f" u2="&#x135;" k="-96" />
<hkern u1="f" u2="&#x12d;" k="-117" />
<hkern u1="f" u2="&#x12b;" k="-106" />
<hkern u1="f" u2="&#x129;" k="-150" />
<hkern u1="f" u2="&#x104;" k="29" />
<hkern u1="f" u2="&#x102;" k="29" />
<hkern u1="f" u2="&#x100;" k="29" />
<hkern u1="f" u2="&#xef;" k="-119" />
<hkern u1="f" u2="&#xee;" k="-119" />
<hkern u1="f" u2="&#xec;" k="-182" />
<hkern u1="f" u2="&#xdd;" k="-16" />
<hkern u1="f" u2="&#xc6;" k="29" />
<hkern u1="f" u2="&#xc5;" k="29" />
<hkern u1="f" u2="&#xc4;" k="29" />
<hkern u1="f" u2="&#xc3;" k="29" />
<hkern u1="f" u2="&#xc2;" k="29" />
<hkern u1="f" u2="&#xc1;" k="29" />
<hkern u1="f" u2="&#xc0;" k="29" />
<hkern u1="f" u2="&#xab;" k="70" />
<hkern u1="f" u2="Y" k="-16" />
<hkern u1="f" u2="A" k="29" />
<hkern u1="f" u2="&#x2f;" k="33" />
<hkern u1="f" u2="&#x2e;" k="68" />
<hkern u1="f" u2="&#x2d;" k="57" />
<hkern u1="f" u2="&#x2c;" k="68" />
<hkern u1="f" u2="&#x2a;" k="-16" />
<hkern u1="g" u2="&#x2122;" k="25" />
<hkern u1="g" u2="&#x201e;" k="-76" />
<hkern u1="g" u2="&#x201a;" k="-45" />
<hkern u1="g" u2="&#x135;" k="-109" />
<hkern u1="g" u2="&#x12f;" k="-49" />
<hkern u1="g" u2="&#x7d;" k="-10" />
<hkern u1="g" u2="&#x7c;" k="35" />
<hkern u1="g" u2="j" k="-109" />
<hkern u1="g" u2="]" k="-10" />
<hkern u1="g" u2="\" k="39" />
<hkern u1="g" u2="V" k="16" />
<hkern u1="g" u2="&#x3b;" k="-49" />
<hkern u1="g" u2="&#x2c;" k="-47" />
<hkern u1="g" u2="&#x29;" k="-16" />
<hkern u1="h" u2="&#x2122;" k="59" />
<hkern u1="h" u2="&#xae;" k="31" />
<hkern u1="h" u2="&#x7d;" k="41" />
<hkern u1="h" u2="&#x7c;" k="70" />
<hkern u1="h" u2="v" k="10" />
<hkern u1="h" u2="f" k="8" />
<hkern u1="h" u2="]" k="20" />
<hkern u1="h" u2="\" k="88" />
<hkern u1="h" u2="V" k="61" />
<hkern u1="h" u2="&#x3f;" k="37" />
<hkern u1="h" u2="&#x2a;" k="35" />
<hkern u1="h" u2="&#x29;" k="43" />
<hkern u1="i" u2="&#x135;" k="-41" />
<hkern u1="i" u2="&#x12d;" k="-35" />
<hkern u1="i" u2="&#x12b;" k="-25" />
<hkern u1="i" u2="&#x129;" k="-68" />
<hkern u1="i" u2="&#xef;" k="-37" />
<hkern u1="i" u2="&#xee;" k="-63" />
<hkern u1="i" u2="&#xec;" k="-100" />
<hkern u1="i" u2="&#x29;" k="33" />
<hkern u1="j" u2="&#x135;" k="-41" />
<hkern u1="j" u2="&#x12d;" k="-29" />
<hkern u1="j" u2="&#x12b;" k="-25" />
<hkern u1="j" u2="&#x129;" k="-68" />
<hkern u1="j" u2="&#xef;" k="-37" />
<hkern u1="j" u2="&#xee;" k="-63" />
<hkern u1="j" u2="&#xec;" k="-100" />
<hkern u1="j" u2="&#x29;" k="33" />
<hkern u1="k" u2="&#x2122;" k="27" />
<hkern u1="k" u2="&#x7c;" k="31" />
<hkern u1="k" u2="\" k="37" />
<hkern u1="k" u2="V" k="16" />
<hkern u1="l" u2="&#x135;" k="-39" />
<hkern u1="l" u2="&#x12d;" k="-41" />
<hkern u1="l" u2="&#x12b;" k="-25" />
<hkern u1="l" u2="&#x129;" k="-66" />
<hkern u1="l" u2="&#xef;" k="-37" />
<hkern u1="l" u2="&#xee;" k="-61" />
<hkern u1="l" u2="&#xec;" k="-100" />
<hkern u1="l" u2="&#x29;" k="31" />
<hkern u1="m" u2="&#x2122;" k="59" />
<hkern u1="m" u2="&#xae;" k="31" />
<hkern u1="m" u2="&#x7d;" k="41" />
<hkern u1="m" u2="&#x7c;" k="70" />
<hkern u1="m" u2="v" k="10" />
<hkern u1="m" u2="f" k="8" />
<hkern u1="m" u2="]" k="20" />
<hkern u1="m" u2="\" k="88" />
<hkern u1="m" u2="V" k="61" />
<hkern u1="m" u2="&#x3f;" k="37" />
<hkern u1="m" u2="&#x2a;" k="35" />
<hkern u1="m" u2="&#x29;" k="43" />
<hkern u1="n" u2="&#x2122;" k="59" />
<hkern u1="n" u2="&#xae;" k="31" />
<hkern u1="n" u2="&#x7d;" k="41" />
<hkern u1="n" u2="&#x7c;" k="70" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="f" k="8" />
<hkern u1="n" u2="]" k="20" />
<hkern u1="n" u2="\" k="88" />
<hkern u1="n" u2="V" k="61" />
<hkern u1="n" u2="&#x3f;" k="37" />
<hkern u1="n" u2="&#x2a;" k="35" />
<hkern u1="n" u2="&#x29;" k="43" />
<hkern u1="o" u2="&#x2122;" k="63" />
<hkern u1="o" u2="&#xae;" k="39" />
<hkern u1="o" u2="&#x7d;" k="55" />
<hkern u1="o" u2="&#x7c;" k="70" />
<hkern u1="o" u2="x" k="23" />
<hkern u1="o" u2="v" k="16" />
<hkern u1="o" u2="f" k="12" />
<hkern u1="o" u2="]" k="61" />
<hkern u1="o" u2="\" k="94" />
<hkern u1="o" u2="X" k="23" />
<hkern u1="o" u2="V" k="70" />
<hkern u1="o" u2="&#x3f;" k="39" />
<hkern u1="o" u2="&#x2a;" k="45" />
<hkern u1="o" u2="&#x29;" k="59" />
<hkern u1="p" u2="&#x2122;" k="66" />
<hkern u1="p" u2="&#xae;" k="35" />
<hkern u1="p" u2="&#x7d;" k="55" />
<hkern u1="p" u2="&#x7c;" k="70" />
<hkern u1="p" u2="x" k="23" />
<hkern u1="p" u2="v" k="14" />
<hkern u1="p" u2="f" k="8" />
<hkern u1="p" u2="]" k="61" />
<hkern u1="p" u2="\" k="92" />
<hkern u1="p" u2="X" k="18" />
<hkern u1="p" u2="V" k="66" />
<hkern u1="p" u2="&#x3f;" k="41" />
<hkern u1="p" u2="&#x2a;" k="43" />
<hkern u1="p" u2="&#x29;" k="59" />
<hkern u1="q" u2="&#x2122;" k="37" />
<hkern u1="q" u2="&#x201e;" k="-16" />
<hkern u1="q" u2="&#x7d;" k="45" />
<hkern u1="q" u2="&#x7c;" k="53" />
<hkern u1="q" u2="]" k="25" />
<hkern u1="q" u2="\" k="57" />
<hkern u1="q" u2="V" k="45" />
<hkern u1="q" u2="&#x3f;" k="25" />
<hkern u1="q" u2="&#x29;" k="47" />
<hkern u1="r" u2="&#x7d;" k="45" />
<hkern u1="r" u2="&#x7c;" k="23" />
<hkern u1="r" u2="]" k="57" />
<hkern u1="r" u2="\" k="25" />
<hkern u1="r" u2="X" k="47" />
<hkern u1="r" u2="&#x2f;" k="45" />
<hkern u1="r" u2="&#x29;" k="47" />
<hkern u1="s" u2="&#x2122;" k="45" />
<hkern u1="s" u2="&#x7d;" k="51" />
<hkern u1="s" u2="&#x7c;" k="57" />
<hkern u1="s" u2="v" k="10" />
<hkern u1="s" u2="]" k="47" />
<hkern u1="s" u2="\" k="63" />
<hkern u1="s" u2="V" k="45" />
<hkern u1="s" u2="&#x3f;" k="25" />
<hkern u1="s" u2="&#x2a;" k="16" />
<hkern u1="s" u2="&#x29;" k="47" />
<hkern u1="t" u2="&#x2122;" k="23" />
<hkern u1="t" u2="&#x7d;" k="20" />
<hkern u1="t" u2="&#x7c;" k="37" />
<hkern u1="t" u2="\" k="35" />
<hkern u1="t" u2="V" k="12" />
<hkern u1="t" u2="&#x29;" k="23" />
<hkern u1="u" u2="&#x2122;" k="37" />
<hkern u1="u" u2="&#x7d;" k="45" />
<hkern u1="u" u2="&#x7c;" k="53" />
<hkern u1="u" u2="]" k="25" />
<hkern u1="u" u2="\" k="57" />
<hkern u1="u" u2="V" k="45" />
<hkern u1="u" u2="&#x3f;" k="25" />
<hkern u1="u" u2="&#x29;" k="47" />
<hkern u1="v" u2="&#x2122;" k="23" />
<hkern u1="v" u2="&#x203a;" k="25" />
<hkern u1="v" u2="&#x2039;" k="51" />
<hkern u1="v" u2="&#x2026;" k="72" />
<hkern u1="v" u2="&#x201e;" k="72" />
<hkern u1="v" u2="&#x201a;" k="72" />
<hkern u1="v" u2="&#x2014;" k="39" />
<hkern u1="v" u2="&#x2013;" k="39" />
<hkern u1="v" u2="&#x21a;" k="111" />
<hkern u1="v" u2="&#x219;" k="16" />
<hkern u1="v" u2="&#x1ff;" k="23" />
<hkern u1="v" u2="&#x1fd;" k="23" />
<hkern u1="v" u2="&#x1fc;" k="20" />
<hkern u1="v" u2="&#x1fb;" k="23" />
<hkern u1="v" u2="&#x1fa;" k="20" />
<hkern u1="v" u2="&#x17d;" k="16" />
<hkern u1="v" u2="&#x17b;" k="16" />
<hkern u1="v" u2="&#x179;" k="16" />
<hkern u1="v" u2="&#x178;" k="63" />
<hkern u1="v" u2="&#x176;" k="63" />
<hkern u1="v" u2="&#x174;" k="10" />
<hkern u1="v" u2="&#x166;" k="111" />
<hkern u1="v" u2="&#x164;" k="111" />
<hkern u1="v" u2="&#x161;" k="16" />
<hkern u1="v" u2="&#x15f;" k="16" />
<hkern u1="v" u2="&#x15d;" k="16" />
<hkern u1="v" u2="&#x15b;" k="16" />
<hkern u1="v" u2="&#x153;" k="23" />
<hkern u1="v" u2="&#x151;" k="23" />
<hkern u1="v" u2="&#x14f;" k="23" />
<hkern u1="v" u2="&#x14d;" k="23" />
<hkern u1="v" u2="&#x134;" k="20" />
<hkern u1="v" u2="&#x123;" k="12" />
<hkern u1="v" u2="&#x121;" k="12" />
<hkern u1="v" u2="&#x11f;" k="12" />
<hkern u1="v" u2="&#x11d;" k="12" />
<hkern u1="v" u2="&#x11b;" k="23" />
<hkern u1="v" u2="&#x119;" k="23" />
<hkern u1="v" u2="&#x117;" k="23" />
<hkern u1="v" u2="&#x115;" k="23" />
<hkern u1="v" u2="&#x113;" k="23" />
<hkern u1="v" u2="&#x111;" k="23" />
<hkern u1="v" u2="&#x10f;" k="23" />
<hkern u1="v" u2="&#x10d;" k="23" />
<hkern u1="v" u2="&#x10b;" k="23" />
<hkern u1="v" u2="&#x109;" k="23" />
<hkern u1="v" u2="&#x107;" k="23" />
<hkern u1="v" u2="&#x105;" k="23" />
<hkern u1="v" u2="&#x104;" k="20" />
<hkern u1="v" u2="&#x103;" k="23" />
<hkern u1="v" u2="&#x102;" k="20" />
<hkern u1="v" u2="&#x101;" k="23" />
<hkern u1="v" u2="&#x100;" k="20" />
<hkern u1="v" u2="&#xf8;" k="23" />
<hkern u1="v" u2="&#xf6;" k="23" />
<hkern u1="v" u2="&#xf5;" k="23" />
<hkern u1="v" u2="&#xf4;" k="23" />
<hkern u1="v" u2="&#xf3;" k="23" />
<hkern u1="v" u2="&#xf2;" k="23" />
<hkern u1="v" u2="&#xeb;" k="23" />
<hkern u1="v" u2="&#xea;" k="23" />
<hkern u1="v" u2="&#xe9;" k="23" />
<hkern u1="v" u2="&#xe8;" k="23" />
<hkern u1="v" u2="&#xe7;" k="23" />
<hkern u1="v" u2="&#xe6;" k="23" />
<hkern u1="v" u2="&#xe5;" k="23" />
<hkern u1="v" u2="&#xe4;" k="23" />
<hkern u1="v" u2="&#xe3;" k="23" />
<hkern u1="v" u2="&#xe2;" k="23" />
<hkern u1="v" u2="&#xe1;" k="23" />
<hkern u1="v" u2="&#xe0;" k="23" />
<hkern u1="v" u2="&#xdd;" k="63" />
<hkern u1="v" u2="&#xc6;" k="20" />
<hkern u1="v" u2="&#xc5;" k="20" />
<hkern u1="v" u2="&#xc4;" k="20" />
<hkern u1="v" u2="&#xc3;" k="20" />
<hkern u1="v" u2="&#xc2;" k="20" />
<hkern u1="v" u2="&#xc1;" k="20" />
<hkern u1="v" u2="&#xc0;" k="20" />
<hkern u1="v" u2="&#xbb;" k="25" />
<hkern u1="v" u2="&#xab;" k="51" />
<hkern u1="v" u2="&#x7d;" k="51" />
<hkern u1="v" u2="&#x7c;" k="37" />
<hkern u1="v" u2="s" k="16" />
<hkern u1="v" u2="q" k="23" />
<hkern u1="v" u2="o" k="23" />
<hkern u1="v" u2="g" k="12" />
<hkern u1="v" u2="e" k="23" />
<hkern u1="v" u2="d" k="23" />
<hkern u1="v" u2="c" k="23" />
<hkern u1="v" u2="a" k="23" />
<hkern u1="v" u2="]" k="63" />
<hkern u1="v" u2="\" k="43" />
<hkern u1="v" u2="Z" k="16" />
<hkern u1="v" u2="Y" k="63" />
<hkern u1="v" u2="X" k="31" />
<hkern u1="v" u2="W" k="10" />
<hkern u1="v" u2="V" k="14" />
<hkern u1="v" u2="T" k="111" />
<hkern u1="v" u2="J" k="20" />
<hkern u1="v" u2="A" k="20" />
<hkern u1="v" u2="&#x2f;" k="37" />
<hkern u1="v" u2="&#x2e;" k="72" />
<hkern u1="v" u2="&#x2d;" k="39" />
<hkern u1="v" u2="&#x2c;" k="72" />
<hkern u1="v" u2="&#x29;" k="53" />
<hkern u1="w" u2="&#x2122;" k="25" />
<hkern u1="w" u2="&#x7d;" k="51" />
<hkern u1="w" u2="&#x7c;" k="33" />
<hkern u1="w" u2="]" k="61" />
<hkern u1="w" u2="\" k="35" />
<hkern u1="w" u2="X" k="27" />
<hkern u1="w" u2="V" k="16" />
<hkern u1="w" u2="&#x2f;" k="23" />
<hkern u1="w" u2="&#x29;" k="53" />
<hkern u1="x" u2="&#x2122;" k="18" />
<hkern u1="x" u2="&#x2039;" k="72" />
<hkern u1="x" u2="&#x2014;" k="59" />
<hkern u1="x" u2="&#x2013;" k="59" />
<hkern u1="x" u2="&#x21a;" k="111" />
<hkern u1="x" u2="&#x1ff;" k="23" />
<hkern u1="x" u2="&#x1fd;" k="23" />
<hkern u1="x" u2="&#x1fb;" k="23" />
<hkern u1="x" u2="&#x178;" k="55" />
<hkern u1="x" u2="&#x176;" k="55" />
<hkern u1="x" u2="&#x166;" k="111" />
<hkern u1="x" u2="&#x164;" k="111" />
<hkern u1="x" u2="&#x153;" k="23" />
<hkern u1="x" u2="&#x151;" k="23" />
<hkern u1="x" u2="&#x14f;" k="23" />
<hkern u1="x" u2="&#x14d;" k="23" />
<hkern u1="x" u2="&#x11b;" k="23" />
<hkern u1="x" u2="&#x119;" k="23" />
<hkern u1="x" u2="&#x117;" k="23" />
<hkern u1="x" u2="&#x115;" k="23" />
<hkern u1="x" u2="&#x113;" k="23" />
<hkern u1="x" u2="&#x111;" k="23" />
<hkern u1="x" u2="&#x10f;" k="23" />
<hkern u1="x" u2="&#x10d;" k="23" />
<hkern u1="x" u2="&#x10b;" k="23" />
<hkern u1="x" u2="&#x109;" k="23" />
<hkern u1="x" u2="&#x107;" k="23" />
<hkern u1="x" u2="&#x105;" k="23" />
<hkern u1="x" u2="&#x103;" k="23" />
<hkern u1="x" u2="&#x101;" k="23" />
<hkern u1="x" u2="&#xf8;" k="23" />
<hkern u1="x" u2="&#xf6;" k="23" />
<hkern u1="x" u2="&#xf5;" k="23" />
<hkern u1="x" u2="&#xf4;" k="23" />
<hkern u1="x" u2="&#xf3;" k="23" />
<hkern u1="x" u2="&#xf2;" k="23" />
<hkern u1="x" u2="&#xeb;" k="23" />
<hkern u1="x" u2="&#xea;" k="23" />
<hkern u1="x" u2="&#xe9;" k="23" />
<hkern u1="x" u2="&#xe8;" k="23" />
<hkern u1="x" u2="&#xe7;" k="23" />
<hkern u1="x" u2="&#xe6;" k="23" />
<hkern u1="x" u2="&#xe5;" k="23" />
<hkern u1="x" u2="&#xe4;" k="23" />
<hkern u1="x" u2="&#xe3;" k="23" />
<hkern u1="x" u2="&#xe2;" k="23" />
<hkern u1="x" u2="&#xe1;" k="23" />
<hkern u1="x" u2="&#xe0;" k="23" />
<hkern u1="x" u2="&#xdd;" k="55" />
<hkern u1="x" u2="&#xab;" k="72" />
<hkern u1="x" u2="&#x7c;" k="25" />
<hkern u1="x" u2="q" k="23" />
<hkern u1="x" u2="o" k="23" />
<hkern u1="x" u2="e" k="23" />
<hkern u1="x" u2="d" k="23" />
<hkern u1="x" u2="c" k="23" />
<hkern u1="x" u2="a" k="23" />
<hkern u1="x" u2="\" k="27" />
<hkern u1="x" u2="Y" k="55" />
<hkern u1="x" u2="V" k="10" />
<hkern u1="x" u2="T" k="111" />
<hkern u1="x" u2="&#x2d;" k="59" />
<hkern u1="y" u2="&#x2122;" k="23" />
<hkern u1="y" u2="&#x7d;" k="51" />
<hkern u1="y" u2="&#x7c;" k="37" />
<hkern u1="y" u2="]" k="63" />
<hkern u1="y" u2="\" k="43" />
<hkern u1="y" u2="X" k="29" />
<hkern u1="y" u2="V" k="14" />
<hkern u1="y" u2="&#x2f;" k="37" />
<hkern u1="y" u2="&#x29;" k="51" />
<hkern u1="z" u2="&#x2122;" k="33" />
<hkern u1="z" u2="&#x7d;" k="37" />
<hkern u1="z" u2="&#x7c;" k="45" />
<hkern u1="z" u2="\" k="49" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="z" u2="&#x29;" k="39" />
<hkern u1="&#x7b;" u2="&#x21b;" k="35" />
<hkern u1="&#x7b;" u2="&#x219;" k="53" />
<hkern u1="&#x7b;" u2="&#x218;" k="39" />
<hkern u1="&#x7b;" u2="&#x1ff;" k="59" />
<hkern u1="&#x7b;" u2="&#x1fe;" k="59" />
<hkern u1="&#x7b;" u2="&#x1fd;" k="59" />
<hkern u1="&#x7b;" u2="&#x1fc;" k="43" />
<hkern u1="&#x7b;" u2="&#x1fb;" k="59" />
<hkern u1="&#x7b;" u2="&#x1fa;" k="43" />
<hkern u1="&#x7b;" u2="&#x17e;" k="45" />
<hkern u1="&#x7b;" u2="&#x17c;" k="45" />
<hkern u1="&#x7b;" u2="&#x17a;" k="45" />
<hkern u1="&#x7b;" u2="&#x178;" k="-18" />
<hkern u1="&#x7b;" u2="&#x177;" k="33" />
<hkern u1="&#x7b;" u2="&#x176;" k="-18" />
<hkern u1="&#x7b;" u2="&#x175;" k="43" />
<hkern u1="&#x7b;" u2="&#x173;" k="51" />
<hkern u1="&#x7b;" u2="&#x171;" k="51" />
<hkern u1="&#x7b;" u2="&#x16f;" k="51" />
<hkern u1="&#x7b;" u2="&#x16d;" k="51" />
<hkern u1="&#x7b;" u2="&#x16b;" k="51" />
<hkern u1="&#x7b;" u2="&#x169;" k="51" />
<hkern u1="&#x7b;" u2="&#x167;" k="35" />
<hkern u1="&#x7b;" u2="&#x165;" k="35" />
<hkern u1="&#x7b;" u2="&#x161;" k="53" />
<hkern u1="&#x7b;" u2="&#x160;" k="39" />
<hkern u1="&#x7b;" u2="&#x15f;" k="53" />
<hkern u1="&#x7b;" u2="&#x15e;" k="39" />
<hkern u1="&#x7b;" u2="&#x15d;" k="53" />
<hkern u1="&#x7b;" u2="&#x15c;" k="39" />
<hkern u1="&#x7b;" u2="&#x15b;" k="53" />
<hkern u1="&#x7b;" u2="&#x15a;" k="39" />
<hkern u1="&#x7b;" u2="&#x159;" k="-16" />
<hkern u1="&#x7b;" u2="&#x158;" k="23" />
<hkern u1="&#x7b;" u2="&#x157;" k="51" />
<hkern u1="&#x7b;" u2="&#x156;" k="23" />
<hkern u1="&#x7b;" u2="&#x155;" k="25" />
<hkern u1="&#x7b;" u2="&#x154;" k="23" />
<hkern u1="&#x7b;" u2="&#x153;" k="59" />
<hkern u1="&#x7b;" u2="&#x152;" k="59" />
<hkern u1="&#x7b;" u2="&#x151;" k="59" />
<hkern u1="&#x7b;" u2="&#x150;" k="59" />
<hkern u1="&#x7b;" u2="&#x14f;" k="59" />
<hkern u1="&#x7b;" u2="&#x14e;" k="59" />
<hkern u1="&#x7b;" u2="&#x14d;" k="59" />
<hkern u1="&#x7b;" u2="&#x14c;" k="59" />
<hkern u1="&#x7b;" u2="&#x14b;" k="51" />
<hkern u1="&#x7b;" u2="&#x14a;" k="23" />
<hkern u1="&#x7b;" u2="&#x148;" k="51" />
<hkern u1="&#x7b;" u2="&#x147;" k="23" />
<hkern u1="&#x7b;" u2="&#x146;" k="51" />
<hkern u1="&#x7b;" u2="&#x145;" k="23" />
<hkern u1="&#x7b;" u2="&#x144;" k="51" />
<hkern u1="&#x7b;" u2="&#x143;" k="23" />
<hkern u1="&#x7b;" u2="&#x141;" k="23" />
<hkern u1="&#x7b;" u2="&#x13d;" k="23" />
<hkern u1="&#x7b;" u2="&#x13b;" k="23" />
<hkern u1="&#x7b;" u2="&#x139;" k="23" />
<hkern u1="&#x7b;" u2="&#x136;" k="23" />
<hkern u1="&#x7b;" u2="&#x135;" k="-84" />
<hkern u1="&#x7b;" u2="&#x130;" k="23" />
<hkern u1="&#x7b;" u2="&#x12f;" k="-16" />
<hkern u1="&#x7b;" u2="&#x12e;" k="23" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-133" />
<hkern u1="&#x7b;" u2="&#x12c;" k="23" />
<hkern u1="&#x7b;" u2="&#x12b;" k="-109" />
<hkern u1="&#x7b;" u2="&#x12a;" k="23" />
<hkern u1="&#x7b;" u2="&#x129;" k="-150" />
<hkern u1="&#x7b;" u2="&#x128;" k="23" />
<hkern u1="&#x7b;" u2="&#x127;" k="-12" />
<hkern u1="&#x7b;" u2="&#x126;" k="23" />
<hkern u1="&#x7b;" u2="&#x124;" k="23" />
<hkern u1="&#x7b;" u2="&#x122;" k="59" />
<hkern u1="&#x7b;" u2="&#x120;" k="59" />
<hkern u1="&#x7b;" u2="&#x11e;" k="59" />
<hkern u1="&#x7b;" u2="&#x11c;" k="59" />
<hkern u1="&#x7b;" u2="&#x11b;" k="59" />
<hkern u1="&#x7b;" u2="&#x11a;" k="23" />
<hkern u1="&#x7b;" u2="&#x119;" k="59" />
<hkern u1="&#x7b;" u2="&#x118;" k="23" />
<hkern u1="&#x7b;" u2="&#x117;" k="59" />
<hkern u1="&#x7b;" u2="&#x116;" k="23" />
<hkern u1="&#x7b;" u2="&#x115;" k="59" />
<hkern u1="&#x7b;" u2="&#x114;" k="23" />
<hkern u1="&#x7b;" u2="&#x113;" k="59" />
<hkern u1="&#x7b;" u2="&#x112;" k="23" />
<hkern u1="&#x7b;" u2="&#x111;" k="59" />
<hkern u1="&#x7b;" u2="&#x110;" k="23" />
<hkern u1="&#x7b;" u2="&#x10f;" k="59" />
<hkern u1="&#x7b;" u2="&#x10e;" k="23" />
<hkern u1="&#x7b;" u2="&#x10d;" k="59" />
<hkern u1="&#x7b;" u2="&#x10c;" k="59" />
<hkern u1="&#x7b;" u2="&#x10b;" k="59" />
<hkern u1="&#x7b;" u2="&#x10a;" k="59" />
<hkern u1="&#x7b;" u2="&#x109;" k="59" />
<hkern u1="&#x7b;" u2="&#x108;" k="59" />
<hkern u1="&#x7b;" u2="&#x107;" k="59" />
<hkern u1="&#x7b;" u2="&#x106;" k="59" />
<hkern u1="&#x7b;" u2="&#x105;" k="59" />
<hkern u1="&#x7b;" u2="&#x104;" k="43" />
<hkern u1="&#x7b;" u2="&#x103;" k="59" />
<hkern u1="&#x7b;" u2="&#x102;" k="43" />
<hkern u1="&#x7b;" u2="&#x101;" k="59" />
<hkern u1="&#x7b;" u2="&#x100;" k="43" />
<hkern u1="&#x7b;" u2="&#xff;" k="33" />
<hkern u1="&#x7b;" u2="&#xfd;" k="33" />
<hkern u1="&#x7b;" u2="&#xfc;" k="51" />
<hkern u1="&#x7b;" u2="&#xfb;" k="51" />
<hkern u1="&#x7b;" u2="&#xfa;" k="51" />
<hkern u1="&#x7b;" u2="&#xf9;" k="51" />
<hkern u1="&#x7b;" u2="&#xf8;" k="59" />
<hkern u1="&#x7b;" u2="&#xf6;" k="59" />
<hkern u1="&#x7b;" u2="&#xf5;" k="59" />
<hkern u1="&#x7b;" u2="&#xf4;" k="59" />
<hkern u1="&#x7b;" u2="&#xf3;" k="59" />
<hkern u1="&#x7b;" u2="&#xf2;" k="59" />
<hkern u1="&#x7b;" u2="&#xf1;" k="51" />
<hkern u1="&#x7b;" u2="&#xef;" k="-121" />
<hkern u1="&#x7b;" u2="&#xee;" k="-47" />
<hkern u1="&#x7b;" u2="&#xec;" k="-184" />
<hkern u1="&#x7b;" u2="&#xeb;" k="59" />
<hkern u1="&#x7b;" u2="&#xea;" k="59" />
<hkern u1="&#x7b;" u2="&#xe9;" k="59" />
<hkern u1="&#x7b;" u2="&#xe8;" k="59" />
<hkern u1="&#x7b;" u2="&#xe7;" k="59" />
<hkern u1="&#x7b;" u2="&#xe6;" k="59" />
<hkern u1="&#x7b;" u2="&#xe5;" k="59" />
<hkern u1="&#x7b;" u2="&#xe4;" k="59" />
<hkern u1="&#x7b;" u2="&#xe3;" k="59" />
<hkern u1="&#x7b;" u2="&#xe2;" k="59" />
<hkern u1="&#x7b;" u2="&#xe1;" k="59" />
<hkern u1="&#x7b;" u2="&#xe0;" k="59" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-18" />
<hkern u1="&#x7b;" u2="&#xd8;" k="59" />
<hkern u1="&#x7b;" u2="&#xd6;" k="59" />
<hkern u1="&#x7b;" u2="&#xd5;" k="59" />
<hkern u1="&#x7b;" u2="&#xd4;" k="59" />
<hkern u1="&#x7b;" u2="&#xd3;" k="59" />
<hkern u1="&#x7b;" u2="&#xd2;" k="59" />
<hkern u1="&#x7b;" u2="&#xd1;" k="23" />
<hkern u1="&#x7b;" u2="&#xcf;" k="23" />
<hkern u1="&#x7b;" u2="&#xce;" k="23" />
<hkern u1="&#x7b;" u2="&#xcd;" k="23" />
<hkern u1="&#x7b;" u2="&#xcc;" k="23" />
<hkern u1="&#x7b;" u2="&#xcb;" k="23" />
<hkern u1="&#x7b;" u2="&#xca;" k="23" />
<hkern u1="&#x7b;" u2="&#xc9;" k="23" />
<hkern u1="&#x7b;" u2="&#xc8;" k="23" />
<hkern u1="&#x7b;" u2="&#xc7;" k="59" />
<hkern u1="&#x7b;" u2="&#xc6;" k="43" />
<hkern u1="&#x7b;" u2="&#xc5;" k="43" />
<hkern u1="&#x7b;" u2="&#xc4;" k="43" />
<hkern u1="&#x7b;" u2="&#xc3;" k="43" />
<hkern u1="&#x7b;" u2="&#xc2;" k="43" />
<hkern u1="&#x7b;" u2="&#xc1;" k="43" />
<hkern u1="&#x7b;" u2="&#xc0;" k="43" />
<hkern u1="&#x7b;" u2="&#x7b;" k="53" />
<hkern u1="&#x7b;" u2="z" k="45" />
<hkern u1="&#x7b;" u2="y" k="33" />
<hkern u1="&#x7b;" u2="w" k="43" />
<hkern u1="&#x7b;" u2="v" k="35" />
<hkern u1="&#x7b;" u2="u" k="51" />
<hkern u1="&#x7b;" u2="t" k="35" />
<hkern u1="&#x7b;" u2="s" k="53" />
<hkern u1="&#x7b;" u2="r" k="51" />
<hkern u1="&#x7b;" u2="q" k="59" />
<hkern u1="&#x7b;" u2="p" k="51" />
<hkern u1="&#x7b;" u2="o" k="59" />
<hkern u1="&#x7b;" u2="n" k="51" />
<hkern u1="&#x7b;" u2="m" k="51" />
<hkern u1="&#x7b;" u2="j" k="-84" />
<hkern u1="&#x7b;" u2="f" k="35" />
<hkern u1="&#x7b;" u2="e" k="59" />
<hkern u1="&#x7b;" u2="d" k="59" />
<hkern u1="&#x7b;" u2="c" k="59" />
<hkern u1="&#x7b;" u2="a" k="59" />
<hkern u1="&#x7b;" u2="Y" k="-18" />
<hkern u1="&#x7b;" u2="S" k="39" />
<hkern u1="&#x7b;" u2="R" k="23" />
<hkern u1="&#x7b;" u2="Q" k="59" />
<hkern u1="&#x7b;" u2="P" k="23" />
<hkern u1="&#x7b;" u2="O" k="59" />
<hkern u1="&#x7b;" u2="N" k="23" />
<hkern u1="&#x7b;" u2="M" k="23" />
<hkern u1="&#x7b;" u2="L" k="23" />
<hkern u1="&#x7b;" u2="K" k="23" />
<hkern u1="&#x7b;" u2="I" k="23" />
<hkern u1="&#x7b;" u2="H" k="23" />
<hkern u1="&#x7b;" u2="G" k="59" />
<hkern u1="&#x7b;" u2="F" k="23" />
<hkern u1="&#x7b;" u2="E" k="23" />
<hkern u1="&#x7b;" u2="D" k="23" />
<hkern u1="&#x7b;" u2="C" k="59" />
<hkern u1="&#x7b;" u2="B" k="23" />
<hkern u1="&#x7b;" u2="A" k="43" />
<hkern u1="&#x7b;" u2="&#x28;" k="41" />
<hkern u1="&#x7c;" u2="&#x201d;" k="88" />
<hkern u1="&#x7c;" u2="&#x2019;" k="88" />
<hkern u1="&#x7c;" u2="&#x21b;" k="82" />
<hkern u1="&#x7c;" u2="&#x21a;" k="111" />
<hkern u1="&#x7c;" u2="&#x219;" k="55" />
<hkern u1="&#x7c;" u2="&#x218;" k="59" />
<hkern u1="&#x7c;" u2="&#x1ff;" k="72" />
<hkern u1="&#x7c;" u2="&#x1fe;" k="84" />
<hkern u1="&#x7c;" u2="&#x1fd;" k="70" />
<hkern u1="&#x7c;" u2="&#x1fc;" k="45" />
<hkern u1="&#x7c;" u2="&#x1fb;" k="70" />
<hkern u1="&#x7c;" u2="&#x1fa;" k="45" />
<hkern u1="&#x7c;" u2="&#x17e;" k="47" />
<hkern u1="&#x7c;" u2="&#x17d;" k="49" />
<hkern u1="&#x7c;" u2="&#x17c;" k="47" />
<hkern u1="&#x7c;" u2="&#x17b;" k="49" />
<hkern u1="&#x7c;" u2="&#x17a;" k="47" />
<hkern u1="&#x7c;" u2="&#x179;" k="49" />
<hkern u1="&#x7c;" u2="&#x178;" k="147" />
<hkern u1="&#x7c;" u2="&#x177;" k="86" />
<hkern u1="&#x7c;" u2="&#x176;" k="147" />
<hkern u1="&#x7c;" u2="&#x175;" k="84" />
<hkern u1="&#x7c;" u2="&#x174;" k="115" />
<hkern u1="&#x7c;" u2="&#x173;" k="70" />
<hkern u1="&#x7c;" u2="&#x172;" k="82" />
<hkern u1="&#x7c;" u2="&#x171;" k="70" />
<hkern u1="&#x7c;" u2="&#x170;" k="82" />
<hkern u1="&#x7c;" u2="&#x16f;" k="70" />
<hkern u1="&#x7c;" u2="&#x16e;" k="82" />
<hkern u1="&#x7c;" u2="&#x16d;" k="70" />
<hkern u1="&#x7c;" u2="&#x16c;" k="82" />
<hkern u1="&#x7c;" u2="&#x16b;" k="70" />
<hkern u1="&#x7c;" u2="&#x16a;" k="82" />
<hkern u1="&#x7c;" u2="&#x169;" k="70" />
<hkern u1="&#x7c;" u2="&#x168;" k="82" />
<hkern u1="&#x7c;" u2="&#x167;" k="82" />
<hkern u1="&#x7c;" u2="&#x166;" k="111" />
<hkern u1="&#x7c;" u2="&#x165;" k="82" />
<hkern u1="&#x7c;" u2="&#x164;" k="111" />
<hkern u1="&#x7c;" u2="&#x161;" k="55" />
<hkern u1="&#x7c;" u2="&#x160;" k="59" />
<hkern u1="&#x7c;" u2="&#x15f;" k="55" />
<hkern u1="&#x7c;" u2="&#x15e;" k="59" />
<hkern u1="&#x7c;" u2="&#x15d;" k="55" />
<hkern u1="&#x7c;" u2="&#x15c;" k="59" />
<hkern u1="&#x7c;" u2="&#x15b;" k="55" />
<hkern u1="&#x7c;" u2="&#x15a;" k="59" />
<hkern u1="&#x7c;" u2="&#x159;" k="53" />
<hkern u1="&#x7c;" u2="&#x158;" k="61" />
<hkern u1="&#x7c;" u2="&#x157;" k="53" />
<hkern u1="&#x7c;" u2="&#x156;" k="61" />
<hkern u1="&#x7c;" u2="&#x155;" k="53" />
<hkern u1="&#x7c;" u2="&#x154;" k="61" />
<hkern u1="&#x7c;" u2="&#x153;" k="72" />
<hkern u1="&#x7c;" u2="&#x152;" k="84" />
<hkern u1="&#x7c;" u2="&#x151;" k="72" />
<hkern u1="&#x7c;" u2="&#x150;" k="84" />
<hkern u1="&#x7c;" u2="&#x14f;" k="72" />
<hkern u1="&#x7c;" u2="&#x14e;" k="84" />
<hkern u1="&#x7c;" u2="&#x14d;" k="72" />
<hkern u1="&#x7c;" u2="&#x14c;" k="84" />
<hkern u1="&#x7c;" u2="&#x14b;" k="53" />
<hkern u1="&#x7c;" u2="&#x14a;" k="61" />
<hkern u1="&#x7c;" u2="&#x148;" k="53" />
<hkern u1="&#x7c;" u2="&#x147;" k="61" />
<hkern u1="&#x7c;" u2="&#x146;" k="53" />
<hkern u1="&#x7c;" u2="&#x145;" k="61" />
<hkern u1="&#x7c;" u2="&#x144;" k="53" />
<hkern u1="&#x7c;" u2="&#x143;" k="61" />
<hkern u1="&#x7c;" u2="&#x142;" k="53" />
<hkern u1="&#x7c;" u2="&#x141;" k="61" />
<hkern u1="&#x7c;" u2="&#x13e;" k="53" />
<hkern u1="&#x7c;" u2="&#x13d;" k="61" />
<hkern u1="&#x7c;" u2="&#x13c;" k="53" />
<hkern u1="&#x7c;" u2="&#x13b;" k="61" />
<hkern u1="&#x7c;" u2="&#x13a;" k="53" />
<hkern u1="&#x7c;" u2="&#x139;" k="61" />
<hkern u1="&#x7c;" u2="&#x137;" k="53" />
<hkern u1="&#x7c;" u2="&#x136;" k="61" />
<hkern u1="&#x7c;" u2="&#x135;" k="53" />
<hkern u1="&#x7c;" u2="&#x131;" k="53" />
<hkern u1="&#x7c;" u2="&#x130;" k="61" />
<hkern u1="&#x7c;" u2="&#x12f;" k="-8" />
<hkern u1="&#x7c;" u2="&#x12e;" k="29" />
<hkern u1="&#x7c;" u2="&#x12d;" k="53" />
<hkern u1="&#x7c;" u2="&#x12c;" k="61" />
<hkern u1="&#x7c;" u2="&#x12b;" k="53" />
<hkern u1="&#x7c;" u2="&#x12a;" k="61" />
<hkern u1="&#x7c;" u2="&#x129;" k="53" />
<hkern u1="&#x7c;" u2="&#x128;" k="61" />
<hkern u1="&#x7c;" u2="&#x127;" k="53" />
<hkern u1="&#x7c;" u2="&#x126;" k="61" />
<hkern u1="&#x7c;" u2="&#x125;" k="53" />
<hkern u1="&#x7c;" u2="&#x124;" k="61" />
<hkern u1="&#x7c;" u2="&#x123;" k="23" />
<hkern u1="&#x7c;" u2="&#x122;" k="84" />
<hkern u1="&#x7c;" u2="&#x121;" k="23" />
<hkern u1="&#x7c;" u2="&#x120;" k="84" />
<hkern u1="&#x7c;" u2="&#x11f;" k="23" />
<hkern u1="&#x7c;" u2="&#x11e;" k="84" />
<hkern u1="&#x7c;" u2="&#x11d;" k="23" />
<hkern u1="&#x7c;" u2="&#x11c;" k="84" />
<hkern u1="&#x7c;" u2="&#x11b;" k="72" />
<hkern u1="&#x7c;" u2="&#x11a;" k="61" />
<hkern u1="&#x7c;" u2="&#x119;" k="72" />
<hkern u1="&#x7c;" u2="&#x118;" k="61" />
<hkern u1="&#x7c;" u2="&#x117;" k="72" />
<hkern u1="&#x7c;" u2="&#x116;" k="61" />
<hkern u1="&#x7c;" u2="&#x115;" k="72" />
<hkern u1="&#x7c;" u2="&#x114;" k="61" />
<hkern u1="&#x7c;" u2="&#x113;" k="72" />
<hkern u1="&#x7c;" u2="&#x112;" k="61" />
<hkern u1="&#x7c;" u2="&#x111;" k="70" />
<hkern u1="&#x7c;" u2="&#x110;" k="61" />
<hkern u1="&#x7c;" u2="&#x10f;" k="70" />
<hkern u1="&#x7c;" u2="&#x10e;" k="61" />
<hkern u1="&#x7c;" u2="&#x10d;" k="72" />
<hkern u1="&#x7c;" u2="&#x10c;" k="84" />
<hkern u1="&#x7c;" u2="&#x10b;" k="72" />
<hkern u1="&#x7c;" u2="&#x10a;" k="84" />
<hkern u1="&#x7c;" u2="&#x109;" k="72" />
<hkern u1="&#x7c;" u2="&#x108;" k="84" />
<hkern u1="&#x7c;" u2="&#x107;" k="72" />
<hkern u1="&#x7c;" u2="&#x106;" k="84" />
<hkern u1="&#x7c;" u2="&#x105;" k="70" />
<hkern u1="&#x7c;" u2="&#x104;" k="45" />
<hkern u1="&#x7c;" u2="&#x103;" k="70" />
<hkern u1="&#x7c;" u2="&#x102;" k="45" />
<hkern u1="&#x7c;" u2="&#x101;" k="70" />
<hkern u1="&#x7c;" u2="&#x100;" k="45" />
<hkern u1="&#x7c;" u2="&#xff;" k="86" />
<hkern u1="&#x7c;" u2="&#xfd;" k="86" />
<hkern u1="&#x7c;" u2="&#xfc;" k="70" />
<hkern u1="&#x7c;" u2="&#xfb;" k="70" />
<hkern u1="&#x7c;" u2="&#xfa;" k="70" />
<hkern u1="&#x7c;" u2="&#xf9;" k="70" />
<hkern u1="&#x7c;" u2="&#xf8;" k="72" />
<hkern u1="&#x7c;" u2="&#xf6;" k="72" />
<hkern u1="&#x7c;" u2="&#xf5;" k="72" />
<hkern u1="&#x7c;" u2="&#xf4;" k="72" />
<hkern u1="&#x7c;" u2="&#xf3;" k="72" />
<hkern u1="&#x7c;" u2="&#xf2;" k="72" />
<hkern u1="&#x7c;" u2="&#xf1;" k="53" />
<hkern u1="&#x7c;" u2="&#xef;" k="53" />
<hkern u1="&#x7c;" u2="&#xee;" k="53" />
<hkern u1="&#x7c;" u2="&#xed;" k="53" />
<hkern u1="&#x7c;" u2="&#xec;" k="53" />
<hkern u1="&#x7c;" u2="&#xeb;" k="72" />
<hkern u1="&#x7c;" u2="&#xea;" k="72" />
<hkern u1="&#x7c;" u2="&#xe9;" k="72" />
<hkern u1="&#x7c;" u2="&#xe8;" k="72" />
<hkern u1="&#x7c;" u2="&#xe7;" k="72" />
<hkern u1="&#x7c;" u2="&#xe6;" k="70" />
<hkern u1="&#x7c;" u2="&#xe5;" k="70" />
<hkern u1="&#x7c;" u2="&#xe4;" k="70" />
<hkern u1="&#x7c;" u2="&#xe3;" k="70" />
<hkern u1="&#x7c;" u2="&#xe2;" k="70" />
<hkern u1="&#x7c;" u2="&#xe1;" k="70" />
<hkern u1="&#x7c;" u2="&#xe0;" k="70" />
<hkern u1="&#x7c;" u2="&#xdf;" k="53" />
<hkern u1="&#x7c;" u2="&#xdd;" k="147" />
<hkern u1="&#x7c;" u2="&#xdc;" k="82" />
<hkern u1="&#x7c;" u2="&#xdb;" k="82" />
<hkern u1="&#x7c;" u2="&#xda;" k="82" />
<hkern u1="&#x7c;" u2="&#xd9;" k="82" />
<hkern u1="&#x7c;" u2="&#xd8;" k="84" />
<hkern u1="&#x7c;" u2="&#xd6;" k="84" />
<hkern u1="&#x7c;" u2="&#xd5;" k="84" />
<hkern u1="&#x7c;" u2="&#xd4;" k="84" />
<hkern u1="&#x7c;" u2="&#xd3;" k="84" />
<hkern u1="&#x7c;" u2="&#xd2;" k="84" />
<hkern u1="&#x7c;" u2="&#xd1;" k="61" />
<hkern u1="&#x7c;" u2="&#xcf;" k="61" />
<hkern u1="&#x7c;" u2="&#xce;" k="61" />
<hkern u1="&#x7c;" u2="&#xcd;" k="61" />
<hkern u1="&#x7c;" u2="&#xcc;" k="61" />
<hkern u1="&#x7c;" u2="&#xcb;" k="61" />
<hkern u1="&#x7c;" u2="&#xca;" k="61" />
<hkern u1="&#x7c;" u2="&#xc9;" k="61" />
<hkern u1="&#x7c;" u2="&#xc8;" k="61" />
<hkern u1="&#x7c;" u2="&#xc7;" k="84" />
<hkern u1="&#x7c;" u2="&#xc6;" k="45" />
<hkern u1="&#x7c;" u2="&#xc5;" k="45" />
<hkern u1="&#x7c;" u2="&#xc4;" k="45" />
<hkern u1="&#x7c;" u2="&#xc3;" k="45" />
<hkern u1="&#x7c;" u2="&#xc2;" k="45" />
<hkern u1="&#x7c;" u2="&#xc1;" k="45" />
<hkern u1="&#x7c;" u2="&#xc0;" k="45" />
<hkern u1="&#x7c;" u2="z" k="47" />
<hkern u1="&#x7c;" u2="y" k="86" />
<hkern u1="&#x7c;" u2="x" k="23" />
<hkern u1="&#x7c;" u2="w" k="84" />
<hkern u1="&#x7c;" u2="v" k="90" />
<hkern u1="&#x7c;" u2="u" k="70" />
<hkern u1="&#x7c;" u2="t" k="82" />
<hkern u1="&#x7c;" u2="s" k="55" />
<hkern u1="&#x7c;" u2="r" k="53" />
<hkern u1="&#x7c;" u2="q" k="70" />
<hkern u1="&#x7c;" u2="p" k="53" />
<hkern u1="&#x7c;" u2="o" k="72" />
<hkern u1="&#x7c;" u2="n" k="53" />
<hkern u1="&#x7c;" u2="m" k="53" />
<hkern u1="&#x7c;" u2="l" k="53" />
<hkern u1="&#x7c;" u2="k" k="53" />
<hkern u1="&#x7c;" u2="j" k="53" />
<hkern u1="&#x7c;" u2="i" k="53" />
<hkern u1="&#x7c;" u2="h" k="53" />
<hkern u1="&#x7c;" u2="g" k="23" />
<hkern u1="&#x7c;" u2="f" k="37" />
<hkern u1="&#x7c;" u2="e" k="72" />
<hkern u1="&#x7c;" u2="d" k="70" />
<hkern u1="&#x7c;" u2="c" k="72" />
<hkern u1="&#x7c;" u2="b" k="57" />
<hkern u1="&#x7c;" u2="a" k="70" />
<hkern u1="&#x7c;" u2="Z" k="49" />
<hkern u1="&#x7c;" u2="Y" k="147" />
<hkern u1="&#x7c;" u2="W" k="115" />
<hkern u1="&#x7c;" u2="V" k="125" />
<hkern u1="&#x7c;" u2="U" k="82" />
<hkern u1="&#x7c;" u2="T" k="111" />
<hkern u1="&#x7c;" u2="S" k="59" />
<hkern u1="&#x7c;" u2="R" k="61" />
<hkern u1="&#x7c;" u2="Q" k="84" />
<hkern u1="&#x7c;" u2="P" k="61" />
<hkern u1="&#x7c;" u2="O" k="84" />
<hkern u1="&#x7c;" u2="N" k="61" />
<hkern u1="&#x7c;" u2="M" k="61" />
<hkern u1="&#x7c;" u2="L" k="61" />
<hkern u1="&#x7c;" u2="K" k="61" />
<hkern u1="&#x7c;" u2="I" k="61" />
<hkern u1="&#x7c;" u2="H" k="61" />
<hkern u1="&#x7c;" u2="G" k="84" />
<hkern u1="&#x7c;" u2="F" k="61" />
<hkern u1="&#x7c;" u2="E" k="61" />
<hkern u1="&#x7c;" u2="D" k="61" />
<hkern u1="&#x7c;" u2="C" k="84" />
<hkern u1="&#x7c;" u2="B" k="61" />
<hkern u1="&#x7c;" u2="A" k="45" />
<hkern u1="&#x7c;" u2="&#x27;" k="94" />
<hkern u1="&#x7c;" u2="&#x22;" k="94" />
<hkern u1="&#x7d;" u2="&#x7d;" k="51" />
<hkern u1="&#x7d;" u2="]" k="53" />
<hkern u1="&#x7d;" u2="&#x29;" k="53" />
<hkern u1="&#xa1;" u2="&#x21a;" k="70" />
<hkern u1="&#xa1;" u2="&#x178;" k="76" />
<hkern u1="&#xa1;" u2="&#x176;" k="76" />
<hkern u1="&#xa1;" u2="&#x174;" k="33" />
<hkern u1="&#xa1;" u2="&#x166;" k="70" />
<hkern u1="&#xa1;" u2="&#x164;" k="70" />
<hkern u1="&#xa1;" u2="&#x135;" k="-82" />
<hkern u1="&#xa1;" u2="&#x12f;" k="-14" />
<hkern u1="&#xa1;" u2="&#xdd;" k="76" />
<hkern u1="&#xa1;" u2="j" k="-82" />
<hkern u1="&#xa1;" u2="Y" k="76" />
<hkern u1="&#xa1;" u2="W" k="33" />
<hkern u1="&#xa1;" u2="V" k="39" />
<hkern u1="&#xa1;" u2="T" k="70" />
<hkern u1="&#xab;" u2="V" k="43" />
<hkern u1="&#xae;" u2="&#x21a;" k="27" />
<hkern u1="&#xae;" u2="&#x1fc;" k="37" />
<hkern u1="&#xae;" u2="&#x1fa;" k="37" />
<hkern u1="&#xae;" u2="&#x17d;" k="31" />
<hkern u1="&#xae;" u2="&#x17b;" k="31" />
<hkern u1="&#xae;" u2="&#x179;" k="31" />
<hkern u1="&#xae;" u2="&#x178;" k="72" />
<hkern u1="&#xae;" u2="&#x176;" k="72" />
<hkern u1="&#xae;" u2="&#x174;" k="23" />
<hkern u1="&#xae;" u2="&#x166;" k="27" />
<hkern u1="&#xae;" u2="&#x164;" k="27" />
<hkern u1="&#xae;" u2="&#x141;" k="-14" />
<hkern u1="&#xae;" u2="&#x134;" k="20" />
<hkern u1="&#xae;" u2="&#x104;" k="37" />
<hkern u1="&#xae;" u2="&#x102;" k="37" />
<hkern u1="&#xae;" u2="&#x100;" k="37" />
<hkern u1="&#xae;" u2="&#xdd;" k="72" />
<hkern u1="&#xae;" u2="&#xc6;" k="37" />
<hkern u1="&#xae;" u2="&#xc5;" k="37" />
<hkern u1="&#xae;" u2="&#xc4;" k="37" />
<hkern u1="&#xae;" u2="&#xc3;" k="37" />
<hkern u1="&#xae;" u2="&#xc2;" k="37" />
<hkern u1="&#xae;" u2="&#xc1;" k="37" />
<hkern u1="&#xae;" u2="&#xc0;" k="37" />
<hkern u1="&#xae;" u2="Z" k="31" />
<hkern u1="&#xae;" u2="Y" k="72" />
<hkern u1="&#xae;" u2="X" k="43" />
<hkern u1="&#xae;" u2="W" k="23" />
<hkern u1="&#xae;" u2="V" k="29" />
<hkern u1="&#xae;" u2="T" k="27" />
<hkern u1="&#xae;" u2="J" k="20" />
<hkern u1="&#xae;" u2="A" k="37" />
<hkern u1="&#xae;" u2="&#x27;" k="23" />
<hkern u1="&#xae;" u2="&#x22;" k="23" />
<hkern u1="&#xbb;" u2="&#x166;" k="70" />
<hkern u1="&#xbb;" u2="&#x142;" k="-14" />
<hkern u1="&#xbb;" u2="&#x141;" k="-29" />
<hkern u1="&#xbb;" u2="x" k="70" />
<hkern u1="&#xbb;" u2="v" k="37" />
<hkern u1="&#xbb;" u2="f" k="39" />
<hkern u1="&#xbb;" u2="X" k="37" />
<hkern u1="&#xbb;" u2="V" k="72" />
<hkern u1="&#xbf;" u2="&#x21b;" k="37" />
<hkern u1="&#xbf;" u2="&#x21a;" k="109" />
<hkern u1="&#xbf;" u2="&#x219;" k="41" />
<hkern u1="&#xbf;" u2="&#x218;" k="37" />
<hkern u1="&#xbf;" u2="&#x1ff;" k="43" />
<hkern u1="&#xbf;" u2="&#x1fe;" k="41" />
<hkern u1="&#xbf;" u2="&#x1fd;" k="41" />
<hkern u1="&#xbf;" u2="&#x1fc;" k="41" />
<hkern u1="&#xbf;" u2="&#x1fb;" k="41" />
<hkern u1="&#xbf;" u2="&#x1fa;" k="41" />
<hkern u1="&#xbf;" u2="&#x17e;" k="37" />
<hkern u1="&#xbf;" u2="&#x17d;" k="39" />
<hkern u1="&#xbf;" u2="&#x17c;" k="37" />
<hkern u1="&#xbf;" u2="&#x17b;" k="39" />
<hkern u1="&#xbf;" u2="&#x17a;" k="37" />
<hkern u1="&#xbf;" u2="&#x179;" k="39" />
<hkern u1="&#xbf;" u2="&#x178;" k="106" />
<hkern u1="&#xbf;" u2="&#x177;" k="45" />
<hkern u1="&#xbf;" u2="&#x176;" k="106" />
<hkern u1="&#xbf;" u2="&#x175;" k="41" />
<hkern u1="&#xbf;" u2="&#x174;" k="63" />
<hkern u1="&#xbf;" u2="&#x173;" k="41" />
<hkern u1="&#xbf;" u2="&#x172;" k="43" />
<hkern u1="&#xbf;" u2="&#x171;" k="41" />
<hkern u1="&#xbf;" u2="&#x170;" k="43" />
<hkern u1="&#xbf;" u2="&#x16f;" k="41" />
<hkern u1="&#xbf;" u2="&#x16e;" k="43" />
<hkern u1="&#xbf;" u2="&#x16d;" k="41" />
<hkern u1="&#xbf;" u2="&#x16c;" k="43" />
<hkern u1="&#xbf;" u2="&#x16b;" k="41" />
<hkern u1="&#xbf;" u2="&#x16a;" k="43" />
<hkern u1="&#xbf;" u2="&#x169;" k="41" />
<hkern u1="&#xbf;" u2="&#x168;" k="43" />
<hkern u1="&#xbf;" u2="&#x167;" k="37" />
<hkern u1="&#xbf;" u2="&#x166;" k="109" />
<hkern u1="&#xbf;" u2="&#x165;" k="37" />
<hkern u1="&#xbf;" u2="&#x164;" k="109" />
<hkern u1="&#xbf;" u2="&#x161;" k="41" />
<hkern u1="&#xbf;" u2="&#x160;" k="37" />
<hkern u1="&#xbf;" u2="&#x15f;" k="41" />
<hkern u1="&#xbf;" u2="&#x15e;" k="37" />
<hkern u1="&#xbf;" u2="&#x15d;" k="41" />
<hkern u1="&#xbf;" u2="&#x15c;" k="37" />
<hkern u1="&#xbf;" u2="&#x15b;" k="41" />
<hkern u1="&#xbf;" u2="&#x15a;" k="37" />
<hkern u1="&#xbf;" u2="&#x159;" k="35" />
<hkern u1="&#xbf;" u2="&#x158;" k="35" />
<hkern u1="&#xbf;" u2="&#x157;" k="35" />
<hkern u1="&#xbf;" u2="&#x156;" k="35" />
<hkern u1="&#xbf;" u2="&#x155;" k="35" />
<hkern u1="&#xbf;" u2="&#x154;" k="35" />
<hkern u1="&#xbf;" u2="&#x153;" k="43" />
<hkern u1="&#xbf;" u2="&#x152;" k="41" />
<hkern u1="&#xbf;" u2="&#x151;" k="43" />
<hkern u1="&#xbf;" u2="&#x150;" k="41" />
<hkern u1="&#xbf;" u2="&#x14f;" k="43" />
<hkern u1="&#xbf;" u2="&#x14e;" k="41" />
<hkern u1="&#xbf;" u2="&#x14d;" k="43" />
<hkern u1="&#xbf;" u2="&#x14c;" k="41" />
<hkern u1="&#xbf;" u2="&#x14b;" k="35" />
<hkern u1="&#xbf;" u2="&#x14a;" k="35" />
<hkern u1="&#xbf;" u2="&#x148;" k="35" />
<hkern u1="&#xbf;" u2="&#x147;" k="35" />
<hkern u1="&#xbf;" u2="&#x146;" k="35" />
<hkern u1="&#xbf;" u2="&#x145;" k="35" />
<hkern u1="&#xbf;" u2="&#x144;" k="35" />
<hkern u1="&#xbf;" u2="&#x143;" k="35" />
<hkern u1="&#xbf;" u2="&#x142;" k="35" />
<hkern u1="&#xbf;" u2="&#x141;" k="35" />
<hkern u1="&#xbf;" u2="&#x13e;" k="35" />
<hkern u1="&#xbf;" u2="&#x13d;" k="35" />
<hkern u1="&#xbf;" u2="&#x13c;" k="35" />
<hkern u1="&#xbf;" u2="&#x13b;" k="35" />
<hkern u1="&#xbf;" u2="&#x13a;" k="35" />
<hkern u1="&#xbf;" u2="&#x139;" k="35" />
<hkern u1="&#xbf;" u2="&#x137;" k="35" />
<hkern u1="&#xbf;" u2="&#x136;" k="35" />
<hkern u1="&#xbf;" u2="&#x135;" k="35" />
<hkern u1="&#xbf;" u2="&#x131;" k="35" />
<hkern u1="&#xbf;" u2="&#x130;" k="35" />
<hkern u1="&#xbf;" u2="&#x12f;" k="-6" />
<hkern u1="&#xbf;" u2="&#x12e;" k="35" />
<hkern u1="&#xbf;" u2="&#x12d;" k="35" />
<hkern u1="&#xbf;" u2="&#x12c;" k="35" />
<hkern u1="&#xbf;" u2="&#x12b;" k="35" />
<hkern u1="&#xbf;" u2="&#x12a;" k="35" />
<hkern u1="&#xbf;" u2="&#x129;" k="35" />
<hkern u1="&#xbf;" u2="&#x128;" k="35" />
<hkern u1="&#xbf;" u2="&#x127;" k="35" />
<hkern u1="&#xbf;" u2="&#x126;" k="35" />
<hkern u1="&#xbf;" u2="&#x125;" k="35" />
<hkern u1="&#xbf;" u2="&#x124;" k="35" />
<hkern u1="&#xbf;" u2="&#x122;" k="41" />
<hkern u1="&#xbf;" u2="&#x120;" k="41" />
<hkern u1="&#xbf;" u2="&#x11e;" k="41" />
<hkern u1="&#xbf;" u2="&#x11c;" k="41" />
<hkern u1="&#xbf;" u2="&#x11b;" k="43" />
<hkern u1="&#xbf;" u2="&#x11a;" k="35" />
<hkern u1="&#xbf;" u2="&#x119;" k="43" />
<hkern u1="&#xbf;" u2="&#x118;" k="35" />
<hkern u1="&#xbf;" u2="&#x117;" k="43" />
<hkern u1="&#xbf;" u2="&#x116;" k="35" />
<hkern u1="&#xbf;" u2="&#x115;" k="43" />
<hkern u1="&#xbf;" u2="&#x114;" k="35" />
<hkern u1="&#xbf;" u2="&#x113;" k="43" />
<hkern u1="&#xbf;" u2="&#x112;" k="35" />
<hkern u1="&#xbf;" u2="&#x111;" k="41" />
<hkern u1="&#xbf;" u2="&#x110;" k="35" />
<hkern u1="&#xbf;" u2="&#x10f;" k="41" />
<hkern u1="&#xbf;" u2="&#x10e;" k="35" />
<hkern u1="&#xbf;" u2="&#x10d;" k="43" />
<hkern u1="&#xbf;" u2="&#x10c;" k="41" />
<hkern u1="&#xbf;" u2="&#x10b;" k="43" />
<hkern u1="&#xbf;" u2="&#x10a;" k="41" />
<hkern u1="&#xbf;" u2="&#x109;" k="43" />
<hkern u1="&#xbf;" u2="&#x108;" k="41" />
<hkern u1="&#xbf;" u2="&#x107;" k="43" />
<hkern u1="&#xbf;" u2="&#x106;" k="41" />
<hkern u1="&#xbf;" u2="&#x105;" k="41" />
<hkern u1="&#xbf;" u2="&#x104;" k="41" />
<hkern u1="&#xbf;" u2="&#x103;" k="41" />
<hkern u1="&#xbf;" u2="&#x102;" k="41" />
<hkern u1="&#xbf;" u2="&#x101;" k="41" />
<hkern u1="&#xbf;" u2="&#x100;" k="41" />
<hkern u1="&#xbf;" u2="&#xff;" k="45" />
<hkern u1="&#xbf;" u2="&#xfd;" k="45" />
<hkern u1="&#xbf;" u2="&#xfc;" k="41" />
<hkern u1="&#xbf;" u2="&#xfb;" k="41" />
<hkern u1="&#xbf;" u2="&#xfa;" k="41" />
<hkern u1="&#xbf;" u2="&#xf9;" k="41" />
<hkern u1="&#xbf;" u2="&#xf8;" k="43" />
<hkern u1="&#xbf;" u2="&#xf6;" k="43" />
<hkern u1="&#xbf;" u2="&#xf5;" k="43" />
<hkern u1="&#xbf;" u2="&#xf4;" k="43" />
<hkern u1="&#xbf;" u2="&#xf3;" k="43" />
<hkern u1="&#xbf;" u2="&#xf2;" k="43" />
<hkern u1="&#xbf;" u2="&#xf1;" k="35" />
<hkern u1="&#xbf;" u2="&#xef;" k="35" />
<hkern u1="&#xbf;" u2="&#xee;" k="35" />
<hkern u1="&#xbf;" u2="&#xed;" k="35" />
<hkern u1="&#xbf;" u2="&#xec;" k="35" />
<hkern u1="&#xbf;" u2="&#xeb;" k="43" />
<hkern u1="&#xbf;" u2="&#xea;" k="43" />
<hkern u1="&#xbf;" u2="&#xe9;" k="43" />
<hkern u1="&#xbf;" u2="&#xe8;" k="43" />
<hkern u1="&#xbf;" u2="&#xe7;" k="43" />
<hkern u1="&#xbf;" u2="&#xe6;" k="41" />
<hkern u1="&#xbf;" u2="&#xe5;" k="41" />
<hkern u1="&#xbf;" u2="&#xe4;" k="41" />
<hkern u1="&#xbf;" u2="&#xe3;" k="41" />
<hkern u1="&#xbf;" u2="&#xe2;" k="41" />
<hkern u1="&#xbf;" u2="&#xe1;" k="41" />
<hkern u1="&#xbf;" u2="&#xe0;" k="41" />
<hkern u1="&#xbf;" u2="&#xdf;" k="35" />
<hkern u1="&#xbf;" u2="&#xdd;" k="106" />
<hkern u1="&#xbf;" u2="&#xdc;" k="43" />
<hkern u1="&#xbf;" u2="&#xdb;" k="43" />
<hkern u1="&#xbf;" u2="&#xda;" k="43" />
<hkern u1="&#xbf;" u2="&#xd9;" k="43" />
<hkern u1="&#xbf;" u2="&#xd8;" k="41" />
<hkern u1="&#xbf;" u2="&#xd6;" k="41" />
<hkern u1="&#xbf;" u2="&#xd5;" k="41" />
<hkern u1="&#xbf;" u2="&#xd4;" k="41" />
<hkern u1="&#xbf;" u2="&#xd3;" k="41" />
<hkern u1="&#xbf;" u2="&#xd2;" k="41" />
<hkern u1="&#xbf;" u2="&#xd1;" k="35" />
<hkern u1="&#xbf;" u2="&#xcf;" k="35" />
<hkern u1="&#xbf;" u2="&#xce;" k="35" />
<hkern u1="&#xbf;" u2="&#xcd;" k="35" />
<hkern u1="&#xbf;" u2="&#xcc;" k="35" />
<hkern u1="&#xbf;" u2="&#xcb;" k="35" />
<hkern u1="&#xbf;" u2="&#xca;" k="35" />
<hkern u1="&#xbf;" u2="&#xc9;" k="35" />
<hkern u1="&#xbf;" u2="&#xc8;" k="35" />
<hkern u1="&#xbf;" u2="&#xc7;" k="41" />
<hkern u1="&#xbf;" u2="&#xc6;" k="41" />
<hkern u1="&#xbf;" u2="&#xc5;" k="41" />
<hkern u1="&#xbf;" u2="&#xc4;" k="41" />
<hkern u1="&#xbf;" u2="&#xc3;" k="41" />
<hkern u1="&#xbf;" u2="&#xc2;" k="41" />
<hkern u1="&#xbf;" u2="&#xc1;" k="41" />
<hkern u1="&#xbf;" u2="&#xc0;" k="41" />
<hkern u1="&#xbf;" u2="z" k="37" />
<hkern u1="&#xbf;" u2="y" k="45" />
<hkern u1="&#xbf;" u2="x" k="37" />
<hkern u1="&#xbf;" u2="w" k="41" />
<hkern u1="&#xbf;" u2="v" k="45" />
<hkern u1="&#xbf;" u2="u" k="41" />
<hkern u1="&#xbf;" u2="t" k="37" />
<hkern u1="&#xbf;" u2="s" k="41" />
<hkern u1="&#xbf;" u2="r" k="35" />
<hkern u1="&#xbf;" u2="q" k="41" />
<hkern u1="&#xbf;" u2="p" k="35" />
<hkern u1="&#xbf;" u2="o" k="43" />
<hkern u1="&#xbf;" u2="n" k="35" />
<hkern u1="&#xbf;" u2="m" k="35" />
<hkern u1="&#xbf;" u2="l" k="35" />
<hkern u1="&#xbf;" u2="k" k="35" />
<hkern u1="&#xbf;" u2="j" k="35" />
<hkern u1="&#xbf;" u2="i" k="35" />
<hkern u1="&#xbf;" u2="h" k="35" />
<hkern u1="&#xbf;" u2="e" k="43" />
<hkern u1="&#xbf;" u2="d" k="41" />
<hkern u1="&#xbf;" u2="c" k="43" />
<hkern u1="&#xbf;" u2="b" k="39" />
<hkern u1="&#xbf;" u2="a" k="41" />
<hkern u1="&#xbf;" u2="Z" k="39" />
<hkern u1="&#xbf;" u2="Y" k="106" />
<hkern u1="&#xbf;" u2="X" k="39" />
<hkern u1="&#xbf;" u2="W" k="63" />
<hkern u1="&#xbf;" u2="V" k="70" />
<hkern u1="&#xbf;" u2="U" k="43" />
<hkern u1="&#xbf;" u2="T" k="109" />
<hkern u1="&#xbf;" u2="S" k="37" />
<hkern u1="&#xbf;" u2="R" k="35" />
<hkern u1="&#xbf;" u2="Q" k="41" />
<hkern u1="&#xbf;" u2="P" k="35" />
<hkern u1="&#xbf;" u2="O" k="41" />
<hkern u1="&#xbf;" u2="N" k="35" />
<hkern u1="&#xbf;" u2="M" k="35" />
<hkern u1="&#xbf;" u2="L" k="35" />
<hkern u1="&#xbf;" u2="K" k="35" />
<hkern u1="&#xbf;" u2="I" k="35" />
<hkern u1="&#xbf;" u2="H" k="35" />
<hkern u1="&#xbf;" u2="G" k="41" />
<hkern u1="&#xbf;" u2="F" k="35" />
<hkern u1="&#xbf;" u2="E" k="35" />
<hkern u1="&#xbf;" u2="D" k="35" />
<hkern u1="&#xbf;" u2="C" k="41" />
<hkern u1="&#xbf;" u2="B" k="35" />
<hkern u1="&#xbf;" u2="A" k="41" />
<hkern u1="&#xc0;" u2="&#x2122;" k="68" />
<hkern u1="&#xc0;" u2="&#xae;" k="57" />
<hkern u1="&#xc0;" u2="&#x7d;" k="27" />
<hkern u1="&#xc0;" u2="&#x7c;" k="94" />
<hkern u1="&#xc0;" u2="v" k="31" />
<hkern u1="&#xc0;" u2="f" k="14" />
<hkern u1="&#xc0;" u2="\" k="84" />
<hkern u1="&#xc0;" u2="V" k="61" />
<hkern u1="&#xc0;" u2="&#x3f;" k="35" />
<hkern u1="&#xc0;" u2="&#x2a;" k="61" />
<hkern u1="&#xc0;" u2="&#x29;" k="33" />
<hkern u1="&#xc1;" u2="&#x2122;" k="68" />
<hkern u1="&#xc1;" u2="&#xae;" k="57" />
<hkern u1="&#xc1;" u2="&#x7d;" k="27" />
<hkern u1="&#xc1;" u2="&#x7c;" k="94" />
<hkern u1="&#xc1;" u2="v" k="31" />
<hkern u1="&#xc1;" u2="f" k="14" />
<hkern u1="&#xc1;" u2="\" k="84" />
<hkern u1="&#xc1;" u2="V" k="61" />
<hkern u1="&#xc1;" u2="&#x3f;" k="35" />
<hkern u1="&#xc1;" u2="&#x2a;" k="61" />
<hkern u1="&#xc1;" u2="&#x29;" k="33" />
<hkern u1="&#xc2;" u2="&#x2122;" k="68" />
<hkern u1="&#xc2;" u2="&#xae;" k="57" />
<hkern u1="&#xc2;" u2="&#x7d;" k="27" />
<hkern u1="&#xc2;" u2="&#x7c;" k="94" />
<hkern u1="&#xc2;" u2="v" k="31" />
<hkern u1="&#xc2;" u2="f" k="14" />
<hkern u1="&#xc2;" u2="\" k="84" />
<hkern u1="&#xc2;" u2="V" k="61" />
<hkern u1="&#xc2;" u2="&#x3f;" k="35" />
<hkern u1="&#xc2;" u2="&#x2a;" k="61" />
<hkern u1="&#xc2;" u2="&#x29;" k="33" />
<hkern u1="&#xc3;" u2="&#x2122;" k="68" />
<hkern u1="&#xc3;" u2="&#xae;" k="57" />
<hkern u1="&#xc3;" u2="&#x7d;" k="27" />
<hkern u1="&#xc3;" u2="&#x7c;" k="94" />
<hkern u1="&#xc3;" u2="v" k="31" />
<hkern u1="&#xc3;" u2="f" k="14" />
<hkern u1="&#xc3;" u2="\" k="84" />
<hkern u1="&#xc3;" u2="V" k="61" />
<hkern u1="&#xc3;" u2="&#x3f;" k="35" />
<hkern u1="&#xc3;" u2="&#x2a;" k="61" />
<hkern u1="&#xc3;" u2="&#x29;" k="33" />
<hkern u1="&#xc4;" u2="&#x2122;" k="68" />
<hkern u1="&#xc4;" u2="&#xae;" k="57" />
<hkern u1="&#xc4;" u2="&#x7d;" k="27" />
<hkern u1="&#xc4;" u2="&#x7c;" k="94" />
<hkern u1="&#xc4;" u2="v" k="31" />
<hkern u1="&#xc4;" u2="f" k="14" />
<hkern u1="&#xc4;" u2="\" k="84" />
<hkern u1="&#xc4;" u2="V" k="61" />
<hkern u1="&#xc4;" u2="&#x3f;" k="35" />
<hkern u1="&#xc4;" u2="&#x2a;" k="61" />
<hkern u1="&#xc4;" u2="&#x29;" k="33" />
<hkern u1="&#xc5;" u2="&#x2122;" k="68" />
<hkern u1="&#xc5;" u2="&#xae;" k="57" />
<hkern u1="&#xc5;" u2="&#x7d;" k="27" />
<hkern u1="&#xc5;" u2="&#x7c;" k="94" />
<hkern u1="&#xc5;" u2="v" k="31" />
<hkern u1="&#xc5;" u2="f" k="14" />
<hkern u1="&#xc5;" u2="\" k="84" />
<hkern u1="&#xc5;" u2="V" k="61" />
<hkern u1="&#xc5;" u2="&#x3f;" k="35" />
<hkern u1="&#xc5;" u2="&#x2a;" k="61" />
<hkern u1="&#xc5;" u2="&#x29;" k="33" />
<hkern u1="&#xc6;" u2="&#x135;" k="-82" />
<hkern u1="&#xc6;" u2="&#x12d;" k="-70" />
<hkern u1="&#xc6;" u2="&#x12b;" k="-63" />
<hkern u1="&#xc6;" u2="&#x129;" k="-106" />
<hkern u1="&#xc6;" u2="&#xef;" k="-78" />
<hkern u1="&#xc6;" u2="&#xee;" k="-104" />
<hkern u1="&#xc6;" u2="&#xec;" k="-141" />
<hkern u1="&#xc6;" u2="v" k="14" />
<hkern u1="&#xc6;" u2="&#x29;" k="20" />
<hkern u1="&#xc7;" u2="&#x135;" k="-82" />
<hkern u1="&#xc7;" u2="&#x12d;" k="-63" />
<hkern u1="&#xc7;" u2="&#x12b;" k="-80" />
<hkern u1="&#xc7;" u2="&#x129;" k="-109" />
<hkern u1="&#xc7;" u2="&#xef;" k="-90" />
<hkern u1="&#xc7;" u2="&#xee;" k="-104" />
<hkern u1="&#xc7;" u2="&#xec;" k="-111" />
<hkern u1="&#xc7;" u2="&#x2a;" k="-33" />
<hkern u1="&#xc7;" u2="&#x29;" k="25" />
<hkern u1="&#xc8;" u2="&#x135;" k="-82" />
<hkern u1="&#xc8;" u2="&#x12d;" k="-70" />
<hkern u1="&#xc8;" u2="&#x12b;" k="-63" />
<hkern u1="&#xc8;" u2="&#x129;" k="-106" />
<hkern u1="&#xc8;" u2="&#xef;" k="-78" />
<hkern u1="&#xc8;" u2="&#xee;" k="-104" />
<hkern u1="&#xc8;" u2="&#xec;" k="-141" />
<hkern u1="&#xc8;" u2="v" k="14" />
<hkern u1="&#xc8;" u2="&#x29;" k="20" />
<hkern u1="&#xc9;" u2="&#x135;" k="-82" />
<hkern u1="&#xc9;" u2="&#x12d;" k="-70" />
<hkern u1="&#xc9;" u2="&#x12b;" k="-63" />
<hkern u1="&#xc9;" u2="&#x129;" k="-106" />
<hkern u1="&#xc9;" u2="&#xef;" k="-78" />
<hkern u1="&#xc9;" u2="&#xee;" k="-104" />
<hkern u1="&#xc9;" u2="&#xec;" k="-141" />
<hkern u1="&#xc9;" u2="v" k="14" />
<hkern u1="&#xc9;" u2="&#x29;" k="20" />
<hkern u1="&#xca;" u2="&#x135;" k="-82" />
<hkern u1="&#xca;" u2="&#x12d;" k="-70" />
<hkern u1="&#xca;" u2="&#x12b;" k="-63" />
<hkern u1="&#xca;" u2="&#x129;" k="-106" />
<hkern u1="&#xca;" u2="&#xef;" k="-78" />
<hkern u1="&#xca;" u2="&#xee;" k="-104" />
<hkern u1="&#xca;" u2="&#xec;" k="-141" />
<hkern u1="&#xca;" u2="v" k="14" />
<hkern u1="&#xca;" u2="&#x29;" k="20" />
<hkern u1="&#xcb;" u2="&#x135;" k="-82" />
<hkern u1="&#xcb;" u2="&#x12d;" k="-70" />
<hkern u1="&#xcb;" u2="&#x12b;" k="-63" />
<hkern u1="&#xcb;" u2="&#x129;" k="-106" />
<hkern u1="&#xcb;" u2="&#xef;" k="-78" />
<hkern u1="&#xcb;" u2="&#xee;" k="-104" />
<hkern u1="&#xcb;" u2="&#xec;" k="-141" />
<hkern u1="&#xcb;" u2="v" k="14" />
<hkern u1="&#xcb;" u2="&#x29;" k="20" />
<hkern u1="&#xcc;" u2="&#x135;" k="-18" />
<hkern u1="&#xcc;" u2="&#x129;" k="-45" />
<hkern u1="&#xcc;" u2="&#xef;" k="-16" />
<hkern u1="&#xcc;" u2="&#xee;" k="-41" />
<hkern u1="&#xcc;" u2="&#xec;" k="-78" />
<hkern u1="&#xcc;" u2="&#x7d;" k="27" />
<hkern u1="&#xcc;" u2="&#x7c;" k="25" />
<hkern u1="&#xcc;" u2="f" k="12" />
<hkern u1="&#xcc;" u2="]" k="25" />
<hkern u1="&#xcc;" u2="&#x29;" k="41" />
<hkern u1="&#xcd;" u2="&#x135;" k="-18" />
<hkern u1="&#xcd;" u2="&#x129;" k="-45" />
<hkern u1="&#xcd;" u2="&#xef;" k="-16" />
<hkern u1="&#xcd;" u2="&#xee;" k="-41" />
<hkern u1="&#xcd;" u2="&#xec;" k="-78" />
<hkern u1="&#xcd;" u2="&#x7d;" k="27" />
<hkern u1="&#xcd;" u2="&#x7c;" k="25" />
<hkern u1="&#xcd;" u2="f" k="12" />
<hkern u1="&#xcd;" u2="]" k="25" />
<hkern u1="&#xcd;" u2="&#x29;" k="41" />
<hkern u1="&#xce;" u2="&#x135;" k="-18" />
<hkern u1="&#xce;" u2="&#x129;" k="-45" />
<hkern u1="&#xce;" u2="&#xef;" k="-16" />
<hkern u1="&#xce;" u2="&#xee;" k="-41" />
<hkern u1="&#xce;" u2="&#xec;" k="-78" />
<hkern u1="&#xce;" u2="&#x7d;" k="27" />
<hkern u1="&#xce;" u2="&#x7c;" k="25" />
<hkern u1="&#xce;" u2="f" k="12" />
<hkern u1="&#xce;" u2="]" k="25" />
<hkern u1="&#xce;" u2="&#x29;" k="41" />
<hkern u1="&#xcf;" u2="&#x135;" k="-18" />
<hkern u1="&#xcf;" u2="&#x129;" k="-45" />
<hkern u1="&#xcf;" u2="&#xef;" k="-16" />
<hkern u1="&#xcf;" u2="&#xee;" k="-41" />
<hkern u1="&#xcf;" u2="&#xec;" k="-78" />
<hkern u1="&#xcf;" u2="&#x7d;" k="27" />
<hkern u1="&#xcf;" u2="&#x7c;" k="25" />
<hkern u1="&#xcf;" u2="f" k="12" />
<hkern u1="&#xcf;" u2="]" k="25" />
<hkern u1="&#xcf;" u2="&#x29;" k="41" />
<hkern u1="&#xd1;" u2="&#x135;" k="-18" />
<hkern u1="&#xd1;" u2="&#x129;" k="-45" />
<hkern u1="&#xd1;" u2="&#xef;" k="-16" />
<hkern u1="&#xd1;" u2="&#xee;" k="-41" />
<hkern u1="&#xd1;" u2="&#xec;" k="-78" />
<hkern u1="&#xd1;" u2="&#x7d;" k="27" />
<hkern u1="&#xd1;" u2="&#x7c;" k="25" />
<hkern u1="&#xd1;" u2="f" k="12" />
<hkern u1="&#xd1;" u2="]" k="25" />
<hkern u1="&#xd1;" u2="&#x29;" k="41" />
<hkern u1="&#xd2;" u2="&#x2122;" k="29" />
<hkern u1="&#xd2;" u2="&#x7d;" k="61" />
<hkern u1="&#xd2;" u2="&#x7c;" k="57" />
<hkern u1="&#xd2;" u2="x" k="10" />
<hkern u1="&#xd2;" u2="]" k="70" />
<hkern u1="&#xd2;" u2="\" k="49" />
<hkern u1="&#xd2;" u2="X" k="31" />
<hkern u1="&#xd2;" u2="V" k="29" />
<hkern u1="&#xd2;" u2="&#x2f;" k="33" />
<hkern u1="&#xd2;" u2="&#x29;" k="66" />
<hkern u1="&#xd3;" u2="&#x2122;" k="29" />
<hkern u1="&#xd3;" u2="&#x7d;" k="61" />
<hkern u1="&#xd3;" u2="&#x7c;" k="57" />
<hkern u1="&#xd3;" u2="x" k="10" />
<hkern u1="&#xd3;" u2="]" k="70" />
<hkern u1="&#xd3;" u2="\" k="49" />
<hkern u1="&#xd3;" u2="X" k="31" />
<hkern u1="&#xd3;" u2="V" k="29" />
<hkern u1="&#xd3;" u2="&#x2f;" k="33" />
<hkern u1="&#xd3;" u2="&#x29;" k="66" />
<hkern u1="&#xd4;" u2="&#x2122;" k="29" />
<hkern u1="&#xd4;" u2="&#x7d;" k="61" />
<hkern u1="&#xd4;" u2="&#x7c;" k="57" />
<hkern u1="&#xd4;" u2="x" k="10" />
<hkern u1="&#xd4;" u2="]" k="70" />
<hkern u1="&#xd4;" u2="\" k="49" />
<hkern u1="&#xd4;" u2="X" k="31" />
<hkern u1="&#xd4;" u2="V" k="29" />
<hkern u1="&#xd4;" u2="&#x2f;" k="33" />
<hkern u1="&#xd4;" u2="&#x29;" k="66" />
<hkern u1="&#xd5;" u2="&#x2122;" k="29" />
<hkern u1="&#xd5;" u2="&#x7d;" k="61" />
<hkern u1="&#xd5;" u2="&#x7c;" k="57" />
<hkern u1="&#xd5;" u2="x" k="10" />
<hkern u1="&#xd5;" u2="]" k="70" />
<hkern u1="&#xd5;" u2="\" k="49" />
<hkern u1="&#xd5;" u2="X" k="31" />
<hkern u1="&#xd5;" u2="V" k="29" />
<hkern u1="&#xd5;" u2="&#x2f;" k="33" />
<hkern u1="&#xd5;" u2="&#x29;" k="66" />
<hkern u1="&#xd6;" u2="&#x2122;" k="29" />
<hkern u1="&#xd6;" u2="&#x7d;" k="61" />
<hkern u1="&#xd6;" u2="&#x7c;" k="57" />
<hkern u1="&#xd6;" u2="x" k="10" />
<hkern u1="&#xd6;" u2="]" k="70" />
<hkern u1="&#xd6;" u2="\" k="49" />
<hkern u1="&#xd6;" u2="X" k="31" />
<hkern u1="&#xd6;" u2="V" k="29" />
<hkern u1="&#xd6;" u2="&#x2f;" k="33" />
<hkern u1="&#xd6;" u2="&#x29;" k="66" />
<hkern u1="&#xd8;" u2="&#x2122;" k="29" />
<hkern u1="&#xd8;" u2="&#x7d;" k="61" />
<hkern u1="&#xd8;" u2="&#x7c;" k="57" />
<hkern u1="&#xd8;" u2="x" k="10" />
<hkern u1="&#xd8;" u2="]" k="70" />
<hkern u1="&#xd8;" u2="\" k="49" />
<hkern u1="&#xd8;" u2="X" k="31" />
<hkern u1="&#xd8;" u2="V" k="29" />
<hkern u1="&#xd8;" u2="&#x2f;" k="33" />
<hkern u1="&#xd8;" u2="&#x29;" k="66" />
<hkern u1="&#xd9;" u2="&#x135;" k="-31" />
<hkern u1="&#xd9;" u2="&#x12d;" k="-18" />
<hkern u1="&#xd9;" u2="&#x12b;" k="-14" />
<hkern u1="&#xd9;" u2="&#x129;" k="-55" />
<hkern u1="&#xd9;" u2="&#xef;" k="-27" />
<hkern u1="&#xd9;" u2="&#xee;" k="-53" />
<hkern u1="&#xd9;" u2="&#xec;" k="-90" />
<hkern u1="&#xd9;" u2="&#x7d;" k="25" />
<hkern u1="&#xd9;" u2="&#x7c;" k="20" />
<hkern u1="&#xd9;" u2="f" k="10" />
<hkern u1="&#xd9;" u2="]" k="23" />
<hkern u1="&#xd9;" u2="&#x2f;" k="31" />
<hkern u1="&#xd9;" u2="&#x29;" k="41" />
<hkern u1="&#xda;" u2="&#x135;" k="-31" />
<hkern u1="&#xda;" u2="&#x12d;" k="-18" />
<hkern u1="&#xda;" u2="&#x12b;" k="-14" />
<hkern u1="&#xda;" u2="&#x129;" k="-55" />
<hkern u1="&#xda;" u2="&#xef;" k="-27" />
<hkern u1="&#xda;" u2="&#xee;" k="-53" />
<hkern u1="&#xda;" u2="&#xec;" k="-90" />
<hkern u1="&#xda;" u2="&#x7d;" k="25" />
<hkern u1="&#xda;" u2="&#x7c;" k="20" />
<hkern u1="&#xda;" u2="f" k="10" />
<hkern u1="&#xda;" u2="]" k="23" />
<hkern u1="&#xda;" u2="&#x2f;" k="31" />
<hkern u1="&#xda;" u2="&#x29;" k="41" />
<hkern u1="&#xdb;" u2="&#x135;" k="-31" />
<hkern u1="&#xdb;" u2="&#x12d;" k="-18" />
<hkern u1="&#xdb;" u2="&#x12b;" k="-14" />
<hkern u1="&#xdb;" u2="&#x129;" k="-55" />
<hkern u1="&#xdb;" u2="&#xef;" k="-27" />
<hkern u1="&#xdb;" u2="&#xee;" k="-53" />
<hkern u1="&#xdb;" u2="&#xec;" k="-90" />
<hkern u1="&#xdb;" u2="&#x7d;" k="25" />
<hkern u1="&#xdb;" u2="&#x7c;" k="20" />
<hkern u1="&#xdb;" u2="f" k="10" />
<hkern u1="&#xdb;" u2="]" k="23" />
<hkern u1="&#xdb;" u2="&#x2f;" k="31" />
<hkern u1="&#xdb;" u2="&#x29;" k="41" />
<hkern u1="&#xdc;" u2="&#x135;" k="-31" />
<hkern u1="&#xdc;" u2="&#x12d;" k="-18" />
<hkern u1="&#xdc;" u2="&#x12b;" k="-14" />
<hkern u1="&#xdc;" u2="&#x129;" k="-55" />
<hkern u1="&#xdc;" u2="&#xef;" k="-27" />
<hkern u1="&#xdc;" u2="&#xee;" k="-53" />
<hkern u1="&#xdc;" u2="&#xec;" k="-90" />
<hkern u1="&#xdc;" u2="&#x7d;" k="25" />
<hkern u1="&#xdc;" u2="&#x7c;" k="20" />
<hkern u1="&#xdc;" u2="f" k="10" />
<hkern u1="&#xdc;" u2="]" k="23" />
<hkern u1="&#xdc;" u2="&#x2f;" k="31" />
<hkern u1="&#xdc;" u2="&#x29;" k="41" />
<hkern u1="&#xdd;" u2="&#x17a;" k="80" />
<hkern u1="&#xdd;" u2="&#x177;" k="41" />
<hkern u1="&#xdd;" u2="&#x171;" k="88" />
<hkern u1="&#xdd;" u2="&#x16b;" k="88" />
<hkern u1="&#xdd;" u2="&#x169;" k="80" />
<hkern u1="&#xdd;" u2="&#x159;" k="47" />
<hkern u1="&#xdd;" u2="&#x155;" k="-6" />
<hkern u1="&#xdd;" u2="&#x151;" k="82" />
<hkern u1="&#xdd;" u2="&#x14f;" k="88" />
<hkern u1="&#xdd;" u2="&#x14d;" k="117" />
<hkern u1="&#xdd;" u2="&#x135;" k="-59" />
<hkern u1="&#xdd;" u2="&#x131;" k="115" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-141" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-135" />
<hkern u1="&#xdd;" u2="&#x129;" k="-178" />
<hkern u1="&#xdd;" u2="&#x127;" k="-18" />
<hkern u1="&#xdd;" u2="&#x11f;" k="88" />
<hkern u1="&#xdd;" u2="&#x113;" k="102" />
<hkern u1="&#xdd;" u2="&#xff;" k="37" />
<hkern u1="&#xdd;" u2="&#xfc;" k="82" />
<hkern u1="&#xdd;" u2="&#xf5;" k="88" />
<hkern u1="&#xdd;" u2="&#xf2;" k="111" />
<hkern u1="&#xdd;" u2="&#xf1;" k="115" />
<hkern u1="&#xdd;" u2="&#xef;" k="-150" />
<hkern u1="&#xdd;" u2="&#xee;" k="-78" />
<hkern u1="&#xdd;" u2="&#xed;" k="-14" />
<hkern u1="&#xdd;" u2="&#xec;" k="-182" />
<hkern u1="&#xdd;" u2="&#xeb;" k="100" />
<hkern u1="&#xdd;" u2="&#xea;" k="121" />
<hkern u1="&#xdd;" u2="&#xe8;" k="51" />
<hkern u1="&#xdd;" u2="&#xae;" k="35" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-12" />
<hkern u1="&#xdd;" u2="&#x7c;" k="-25" />
<hkern u1="&#xdd;" u2="x" k="66" />
<hkern u1="&#xdd;" u2="v" k="63" />
<hkern u1="&#xdd;" u2="f" k="33" />
<hkern u1="&#xdd;" u2="]" k="-12" />
<hkern u1="&#xdd;" u2="&#x40;" k="55" />
<hkern u1="&#xdd;" u2="&#x2f;" k="131" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-43" />
<hkern u1="&#xdd;" u2="&#x26;" k="66" />
<hkern u1="&#xdf;" u2="&#x2122;" k="16" />
<hkern u1="&#xdf;" u2="&#x201d;" k="27" />
<hkern u1="&#xdf;" u2="&#x201c;" k="31" />
<hkern u1="&#xdf;" u2="&#x2019;" k="27" />
<hkern u1="&#xdf;" u2="&#x2018;" k="31" />
<hkern u1="&#xdf;" u2="&#x21a;" k="27" />
<hkern u1="&#xdf;" u2="&#x1fe;" k="12" />
<hkern u1="&#xdf;" u2="&#x178;" k="76" />
<hkern u1="&#xdf;" u2="&#x177;" k="18" />
<hkern u1="&#xdf;" u2="&#x176;" k="76" />
<hkern u1="&#xdf;" u2="&#x175;" k="10" />
<hkern u1="&#xdf;" u2="&#x174;" k="37" />
<hkern u1="&#xdf;" u2="&#x172;" k="14" />
<hkern u1="&#xdf;" u2="&#x170;" k="14" />
<hkern u1="&#xdf;" u2="&#x16e;" k="14" />
<hkern u1="&#xdf;" u2="&#x16c;" k="14" />
<hkern u1="&#xdf;" u2="&#x16a;" k="14" />
<hkern u1="&#xdf;" u2="&#x168;" k="14" />
<hkern u1="&#xdf;" u2="&#x166;" k="27" />
<hkern u1="&#xdf;" u2="&#x164;" k="27" />
<hkern u1="&#xdf;" u2="&#x152;" k="12" />
<hkern u1="&#xdf;" u2="&#x150;" k="12" />
<hkern u1="&#xdf;" u2="&#x14e;" k="12" />
<hkern u1="&#xdf;" u2="&#x14c;" k="12" />
<hkern u1="&#xdf;" u2="&#x123;" k="14" />
<hkern u1="&#xdf;" u2="&#x122;" k="12" />
<hkern u1="&#xdf;" u2="&#x121;" k="14" />
<hkern u1="&#xdf;" u2="&#x120;" k="12" />
<hkern u1="&#xdf;" u2="&#x11f;" k="14" />
<hkern u1="&#xdf;" u2="&#x11e;" k="12" />
<hkern u1="&#xdf;" u2="&#x11d;" k="14" />
<hkern u1="&#xdf;" u2="&#x11c;" k="12" />
<hkern u1="&#xdf;" u2="&#x10c;" k="12" />
<hkern u1="&#xdf;" u2="&#x10a;" k="12" />
<hkern u1="&#xdf;" u2="&#x108;" k="12" />
<hkern u1="&#xdf;" u2="&#x106;" k="12" />
<hkern u1="&#xdf;" u2="&#xff;" k="18" />
<hkern u1="&#xdf;" u2="&#xfd;" k="18" />
<hkern u1="&#xdf;" u2="&#xee;" k="-18" />
<hkern u1="&#xdf;" u2="&#xdd;" k="76" />
<hkern u1="&#xdf;" u2="&#xdc;" k="14" />
<hkern u1="&#xdf;" u2="&#xdb;" k="14" />
<hkern u1="&#xdf;" u2="&#xda;" k="14" />
<hkern u1="&#xdf;" u2="&#xd9;" k="14" />
<hkern u1="&#xdf;" u2="&#xd8;" k="12" />
<hkern u1="&#xdf;" u2="&#xd6;" k="12" />
<hkern u1="&#xdf;" u2="&#xd5;" k="12" />
<hkern u1="&#xdf;" u2="&#xd4;" k="12" />
<hkern u1="&#xdf;" u2="&#xd3;" k="12" />
<hkern u1="&#xdf;" u2="&#xd2;" k="12" />
<hkern u1="&#xdf;" u2="&#xc7;" k="12" />
<hkern u1="&#xdf;" u2="&#xae;" k="20" />
<hkern u1="&#xdf;" u2="&#x7d;" k="45" />
<hkern u1="&#xdf;" u2="&#x7c;" k="57" />
<hkern u1="&#xdf;" u2="y" k="18" />
<hkern u1="&#xdf;" u2="w" k="10" />
<hkern u1="&#xdf;" u2="v" k="18" />
<hkern u1="&#xdf;" u2="g" k="14" />
<hkern u1="&#xdf;" u2="f" k="8" />
<hkern u1="&#xdf;" u2="]" k="43" />
<hkern u1="&#xdf;" u2="\" k="45" />
<hkern u1="&#xdf;" u2="Y" k="76" />
<hkern u1="&#xdf;" u2="W" k="37" />
<hkern u1="&#xdf;" u2="V" k="43" />
<hkern u1="&#xdf;" u2="U" k="14" />
<hkern u1="&#xdf;" u2="T" k="27" />
<hkern u1="&#xdf;" u2="Q" k="12" />
<hkern u1="&#xdf;" u2="O" k="12" />
<hkern u1="&#xdf;" u2="G" k="12" />
<hkern u1="&#xdf;" u2="C" k="12" />
<hkern u1="&#xdf;" u2="&#x2a;" k="18" />
<hkern u1="&#xdf;" u2="&#x29;" k="43" />
<hkern u1="&#xdf;" u2="&#x27;" k="23" />
<hkern u1="&#xdf;" u2="&#x22;" k="23" />
<hkern u1="&#xe0;" u2="&#x2122;" k="43" />
<hkern u1="&#xe0;" u2="&#x7d;" k="41" />
<hkern u1="&#xe0;" u2="&#x7c;" k="55" />
<hkern u1="&#xe0;" u2="]" k="31" />
<hkern u1="&#xe0;" u2="\" k="63" />
<hkern u1="&#xe0;" u2="V" k="51" />
<hkern u1="&#xe0;" u2="&#x3f;" k="29" />
<hkern u1="&#xe0;" u2="&#x2a;" k="16" />
<hkern u1="&#xe0;" u2="&#x29;" k="45" />
<hkern u1="&#xe1;" u2="&#x2122;" k="43" />
<hkern u1="&#xe1;" u2="&#x7d;" k="41" />
<hkern u1="&#xe1;" u2="&#x7c;" k="55" />
<hkern u1="&#xe1;" u2="]" k="31" />
<hkern u1="&#xe1;" u2="\" k="63" />
<hkern u1="&#xe1;" u2="V" k="51" />
<hkern u1="&#xe1;" u2="&#x3f;" k="29" />
<hkern u1="&#xe1;" u2="&#x2a;" k="16" />
<hkern u1="&#xe1;" u2="&#x29;" k="45" />
<hkern u1="&#xe2;" u2="&#x2122;" k="43" />
<hkern u1="&#xe2;" u2="&#x7d;" k="41" />
<hkern u1="&#xe2;" u2="&#x7c;" k="55" />
<hkern u1="&#xe2;" u2="]" k="31" />
<hkern u1="&#xe2;" u2="\" k="63" />
<hkern u1="&#xe2;" u2="V" k="51" />
<hkern u1="&#xe2;" u2="&#x3f;" k="29" />
<hkern u1="&#xe2;" u2="&#x2a;" k="16" />
<hkern u1="&#xe2;" u2="&#x29;" k="45" />
<hkern u1="&#xe3;" u2="&#x2122;" k="43" />
<hkern u1="&#xe3;" u2="&#x7d;" k="41" />
<hkern u1="&#xe3;" u2="&#x7c;" k="55" />
<hkern u1="&#xe3;" u2="]" k="31" />
<hkern u1="&#xe3;" u2="\" k="63" />
<hkern u1="&#xe3;" u2="V" k="51" />
<hkern u1="&#xe3;" u2="&#x3f;" k="29" />
<hkern u1="&#xe3;" u2="&#x2a;" k="16" />
<hkern u1="&#xe3;" u2="&#x29;" k="45" />
<hkern u1="&#xe4;" u2="&#x2122;" k="43" />
<hkern u1="&#xe4;" u2="&#x7d;" k="41" />
<hkern u1="&#xe4;" u2="&#x7c;" k="55" />
<hkern u1="&#xe4;" u2="]" k="31" />
<hkern u1="&#xe4;" u2="\" k="63" />
<hkern u1="&#xe4;" u2="V" k="51" />
<hkern u1="&#xe4;" u2="&#x3f;" k="29" />
<hkern u1="&#xe4;" u2="&#x2a;" k="16" />
<hkern u1="&#xe4;" u2="&#x29;" k="45" />
<hkern u1="&#xe5;" u2="&#x2122;" k="43" />
<hkern u1="&#xe5;" u2="&#x7d;" k="41" />
<hkern u1="&#xe5;" u2="&#x7c;" k="55" />
<hkern u1="&#xe5;" u2="]" k="31" />
<hkern u1="&#xe5;" u2="\" k="63" />
<hkern u1="&#xe5;" u2="V" k="51" />
<hkern u1="&#xe5;" u2="&#x3f;" k="29" />
<hkern u1="&#xe5;" u2="&#x2a;" k="16" />
<hkern u1="&#xe5;" u2="&#x29;" k="45" />
<hkern u1="&#xe6;" u2="&#x2122;" k="51" />
<hkern u1="&#xe6;" u2="&#x142;" k="-16" />
<hkern u1="&#xe6;" u2="&#x7d;" k="47" />
<hkern u1="&#xe6;" u2="&#x7c;" k="59" />
<hkern u1="&#xe6;" u2="v" k="10" />
<hkern u1="&#xe6;" u2="]" k="39" />
<hkern u1="&#xe6;" u2="\" k="76" />
<hkern u1="&#xe6;" u2="V" k="49" />
<hkern u1="&#xe6;" u2="&#x3f;" k="31" />
<hkern u1="&#xe6;" u2="&#x2a;" k="29" />
<hkern u1="&#xe6;" u2="&#x29;" k="47" />
<hkern u1="&#xe7;" u2="&#x2122;" k="33" />
<hkern u1="&#xe7;" u2="&#x201e;" k="-49" />
<hkern u1="&#xe7;" u2="&#x7d;" k="31" />
<hkern u1="&#xe7;" u2="&#x7c;" k="43" />
<hkern u1="&#xe7;" u2="j" k="-66" />
<hkern u1="&#xe7;" u2="]" k="23" />
<hkern u1="&#xe7;" u2="\" k="49" />
<hkern u1="&#xe7;" u2="V" k="27" />
<hkern u1="&#xe7;" u2="&#x29;" k="33" />
<hkern u1="&#xe8;" u2="&#x2122;" k="51" />
<hkern u1="&#xe8;" u2="&#x142;" k="-27" />
<hkern u1="&#xe8;" u2="&#x7d;" k="47" />
<hkern u1="&#xe8;" u2="&#x7c;" k="59" />
<hkern u1="&#xe8;" u2="v" k="10" />
<hkern u1="&#xe8;" u2="]" k="39" />
<hkern u1="&#xe8;" u2="\" k="76" />
<hkern u1="&#xe8;" u2="V" k="49" />
<hkern u1="&#xe8;" u2="&#x3f;" k="31" />
<hkern u1="&#xe8;" u2="&#x2a;" k="29" />
<hkern u1="&#xe8;" u2="&#x29;" k="47" />
<hkern u1="&#xe9;" u2="&#x2122;" k="51" />
<hkern u1="&#xe9;" u2="&#x142;" k="-27" />
<hkern u1="&#xe9;" u2="&#x7d;" k="47" />
<hkern u1="&#xe9;" u2="&#x7c;" k="59" />
<hkern u1="&#xe9;" u2="v" k="10" />
<hkern u1="&#xe9;" u2="]" k="39" />
<hkern u1="&#xe9;" u2="\" k="76" />
<hkern u1="&#xe9;" u2="V" k="49" />
<hkern u1="&#xe9;" u2="&#x3f;" k="31" />
<hkern u1="&#xe9;" u2="&#x2a;" k="29" />
<hkern u1="&#xe9;" u2="&#x29;" k="47" />
<hkern u1="&#xea;" u2="&#x2122;" k="51" />
<hkern u1="&#xea;" u2="&#x142;" k="-27" />
<hkern u1="&#xea;" u2="&#x7d;" k="47" />
<hkern u1="&#xea;" u2="&#x7c;" k="59" />
<hkern u1="&#xea;" u2="v" k="10" />
<hkern u1="&#xea;" u2="]" k="39" />
<hkern u1="&#xea;" u2="\" k="76" />
<hkern u1="&#xea;" u2="V" k="49" />
<hkern u1="&#xea;" u2="&#x3f;" k="31" />
<hkern u1="&#xea;" u2="&#x2a;" k="29" />
<hkern u1="&#xea;" u2="&#x29;" k="47" />
<hkern u1="&#xeb;" u2="&#x2122;" k="51" />
<hkern u1="&#xeb;" u2="&#x142;" k="-27" />
<hkern u1="&#xeb;" u2="&#x7d;" k="47" />
<hkern u1="&#xeb;" u2="&#x7c;" k="59" />
<hkern u1="&#xeb;" u2="v" k="10" />
<hkern u1="&#xeb;" u2="]" k="39" />
<hkern u1="&#xeb;" u2="\" k="76" />
<hkern u1="&#xeb;" u2="V" k="49" />
<hkern u1="&#xeb;" u2="&#x3f;" k="31" />
<hkern u1="&#xeb;" u2="&#x2a;" k="29" />
<hkern u1="&#xeb;" u2="&#x29;" k="47" />
<hkern u1="&#xec;" u2="&#x135;" k="-41" />
<hkern u1="&#xec;" u2="&#x12d;" k="-35" />
<hkern u1="&#xec;" u2="&#x12b;" k="-25" />
<hkern u1="&#xec;" u2="&#x129;" k="-68" />
<hkern u1="&#xec;" u2="&#xef;" k="-37" />
<hkern u1="&#xec;" u2="&#xee;" k="-63" />
<hkern u1="&#xec;" u2="&#xec;" k="-100" />
<hkern u1="&#xec;" u2="&#x29;" k="33" />
<hkern u1="&#xed;" u2="&#x2122;" k="-51" />
<hkern u1="&#xed;" u2="&#x201d;" k="-45" />
<hkern u1="&#xed;" u2="&#x2019;" k="-45" />
<hkern u1="&#xed;" u2="&#x159;" k="-96" />
<hkern u1="&#xed;" u2="&#x142;" k="-39" />
<hkern u1="&#xed;" u2="&#x13e;" k="-39" />
<hkern u1="&#xed;" u2="&#x13c;" k="-39" />
<hkern u1="&#xed;" u2="&#x13a;" k="-39" />
<hkern u1="&#xed;" u2="&#x137;" k="-39" />
<hkern u1="&#xed;" u2="&#x135;" k="-41" />
<hkern u1="&#xed;" u2="&#x131;" k="-41" />
<hkern u1="&#xed;" u2="&#x12f;" k="-41" />
<hkern u1="&#xed;" u2="&#x12d;" k="-35" />
<hkern u1="&#xed;" u2="&#x12b;" k="-25" />
<hkern u1="&#xed;" u2="&#x129;" k="-41" />
<hkern u1="&#xed;" u2="&#x127;" k="-39" />
<hkern u1="&#xed;" u2="&#x125;" k="-39" />
<hkern u1="&#xed;" u2="&#xef;" k="-37" />
<hkern u1="&#xed;" u2="&#xee;" k="-41" />
<hkern u1="&#xed;" u2="&#xed;" k="-41" />
<hkern u1="&#xed;" u2="&#xec;" k="-41" />
<hkern u1="&#xed;" u2="&#xdf;" k="-39" />
<hkern u1="&#xed;" u2="&#x7d;" k="-113" />
<hkern u1="&#xed;" u2="&#x7c;" k="-113" />
<hkern u1="&#xed;" u2="l" k="-39" />
<hkern u1="&#xed;" u2="k" k="-39" />
<hkern u1="&#xed;" u2="j" k="-41" />
<hkern u1="&#xed;" u2="i" k="-41" />
<hkern u1="&#xed;" u2="h" k="-39" />
<hkern u1="&#xed;" u2="b" k="-43" />
<hkern u1="&#xed;" u2="]" k="-113" />
<hkern u1="&#xed;" u2="\" k="-74" />
<hkern u1="&#xed;" u2="&#x3f;" k="-102" />
<hkern u1="&#xed;" u2="&#x2a;" k="-109" />
<hkern u1="&#xed;" u2="&#x29;" k="33" />
<hkern u1="&#xed;" u2="&#x27;" k="-106" />
<hkern u1="&#xed;" u2="&#x22;" k="-106" />
<hkern u1="&#xed;" u2="&#x21;" k="-102" />
<hkern u1="&#xee;" u2="&#x2122;" k="-29" />
<hkern u1="&#xee;" u2="&#x201d;" k="-66" />
<hkern u1="&#xee;" u2="&#x201c;" k="-25" />
<hkern u1="&#xee;" u2="&#x2019;" k="-66" />
<hkern u1="&#xee;" u2="&#x2018;" k="-25" />
<hkern u1="&#xee;" u2="&#x142;" k="-31" />
<hkern u1="&#xee;" u2="&#x13e;" k="-31" />
<hkern u1="&#xee;" u2="&#x13c;" k="-31" />
<hkern u1="&#xee;" u2="&#x13a;" k="-31" />
<hkern u1="&#xee;" u2="&#x137;" k="-31" />
<hkern u1="&#xee;" u2="&#x135;" k="-31" />
<hkern u1="&#xee;" u2="&#x131;" k="-31" />
<hkern u1="&#xee;" u2="&#x12f;" k="-31" />
<hkern u1="&#xee;" u2="&#x12d;" k="-31" />
<hkern u1="&#xee;" u2="&#x12b;" k="-25" />
<hkern u1="&#xee;" u2="&#x129;" k="-31" />
<hkern u1="&#xee;" u2="&#x127;" k="-31" />
<hkern u1="&#xee;" u2="&#x125;" k="-31" />
<hkern u1="&#xee;" u2="&#xef;" k="-31" />
<hkern u1="&#xee;" u2="&#xee;" k="-31" />
<hkern u1="&#xee;" u2="&#xed;" k="-31" />
<hkern u1="&#xee;" u2="&#xec;" k="-31" />
<hkern u1="&#xee;" u2="&#xdf;" k="-16" />
<hkern u1="&#xee;" u2="&#xae;" k="-51" />
<hkern u1="&#xee;" u2="&#x7c;" k="-66" />
<hkern u1="&#xee;" u2="l" k="-31" />
<hkern u1="&#xee;" u2="k" k="-31" />
<hkern u1="&#xee;" u2="j" k="-31" />
<hkern u1="&#xee;" u2="i" k="-31" />
<hkern u1="&#xee;" u2="h" k="-31" />
<hkern u1="&#xee;" u2="b" k="-35" />
<hkern u1="&#xee;" u2="]" k="-14" />
<hkern u1="&#xee;" u2="&#x3f;" k="-78" />
<hkern u1="&#xee;" u2="&#x2a;" k="-96" />
<hkern u1="&#xee;" u2="&#x29;" k="-6" />
<hkern u1="&#xee;" u2="&#x27;" k="-98" />
<hkern u1="&#xee;" u2="&#x22;" k="-98" />
<hkern u1="&#xee;" u2="&#x21;" k="-92" />
<hkern u1="&#xef;" u2="&#x2122;" k="-29" />
<hkern u1="&#xef;" u2="&#x201d;" k="-23" />
<hkern u1="&#xef;" u2="&#x2019;" k="-23" />
<hkern u1="&#xef;" u2="&#x135;" k="-41" />
<hkern u1="&#xef;" u2="&#x12d;" k="-35" />
<hkern u1="&#xef;" u2="&#x12b;" k="-25" />
<hkern u1="&#xef;" u2="&#x129;" k="-68" />
<hkern u1="&#xef;" u2="&#xef;" k="-37" />
<hkern u1="&#xef;" u2="&#xee;" k="-63" />
<hkern u1="&#xef;" u2="&#xec;" k="-100" />
<hkern u1="&#xef;" u2="&#xe8;" k="-18" />
<hkern u1="&#xef;" u2="&#x7d;" k="-72" />
<hkern u1="&#xef;" u2="&#x7c;" k="-92" />
<hkern u1="&#xef;" u2="]" k="-74" />
<hkern u1="&#xef;" u2="\" k="-61" />
<hkern u1="&#xef;" u2="&#x3f;" k="-63" />
<hkern u1="&#xef;" u2="&#x2a;" k="-113" />
<hkern u1="&#xef;" u2="&#x29;" k="33" />
<hkern u1="&#xef;" u2="&#x27;" k="-68" />
<hkern u1="&#xef;" u2="&#x22;" k="-68" />
<hkern u1="&#xef;" u2="&#x21;" k="-66" />
<hkern u1="&#xf1;" u2="&#x2122;" k="59" />
<hkern u1="&#xf1;" u2="&#xae;" k="31" />
<hkern u1="&#xf1;" u2="&#x7d;" k="41" />
<hkern u1="&#xf1;" u2="&#x7c;" k="70" />
<hkern u1="&#xf1;" u2="v" k="10" />
<hkern u1="&#xf1;" u2="f" k="8" />
<hkern u1="&#xf1;" u2="]" k="20" />
<hkern u1="&#xf1;" u2="\" k="88" />
<hkern u1="&#xf1;" u2="V" k="61" />
<hkern u1="&#xf1;" u2="&#x3f;" k="37" />
<hkern u1="&#xf1;" u2="&#x2a;" k="35" />
<hkern u1="&#xf1;" u2="&#x29;" k="43" />
<hkern u1="&#xf2;" u2="&#x2122;" k="63" />
<hkern u1="&#xf2;" u2="&#xae;" k="39" />
<hkern u1="&#xf2;" u2="&#x7d;" k="55" />
<hkern u1="&#xf2;" u2="&#x7c;" k="70" />
<hkern u1="&#xf2;" u2="x" k="23" />
<hkern u1="&#xf2;" u2="v" k="16" />
<hkern u1="&#xf2;" u2="f" k="12" />
<hkern u1="&#xf2;" u2="]" k="61" />
<hkern u1="&#xf2;" u2="\" k="94" />
<hkern u1="&#xf2;" u2="X" k="23" />
<hkern u1="&#xf2;" u2="V" k="70" />
<hkern u1="&#xf2;" u2="&#x3f;" k="39" />
<hkern u1="&#xf2;" u2="&#x2a;" k="45" />
<hkern u1="&#xf2;" u2="&#x29;" k="59" />
<hkern u1="&#xf3;" u2="&#x2122;" k="63" />
<hkern u1="&#xf3;" u2="&#xae;" k="39" />
<hkern u1="&#xf3;" u2="&#x7d;" k="55" />
<hkern u1="&#xf3;" u2="&#x7c;" k="70" />
<hkern u1="&#xf3;" u2="x" k="23" />
<hkern u1="&#xf3;" u2="v" k="16" />
<hkern u1="&#xf3;" u2="f" k="12" />
<hkern u1="&#xf3;" u2="]" k="61" />
<hkern u1="&#xf3;" u2="\" k="94" />
<hkern u1="&#xf3;" u2="X" k="23" />
<hkern u1="&#xf3;" u2="V" k="70" />
<hkern u1="&#xf3;" u2="&#x3f;" k="39" />
<hkern u1="&#xf3;" u2="&#x2a;" k="45" />
<hkern u1="&#xf3;" u2="&#x29;" k="59" />
<hkern u1="&#xf4;" u2="&#x2122;" k="63" />
<hkern u1="&#xf4;" u2="&#xae;" k="39" />
<hkern u1="&#xf4;" u2="&#x7d;" k="55" />
<hkern u1="&#xf4;" u2="&#x7c;" k="70" />
<hkern u1="&#xf4;" u2="x" k="23" />
<hkern u1="&#xf4;" u2="v" k="16" />
<hkern u1="&#xf4;" u2="f" k="12" />
<hkern u1="&#xf4;" u2="]" k="61" />
<hkern u1="&#xf4;" u2="\" k="94" />
<hkern u1="&#xf4;" u2="X" k="23" />
<hkern u1="&#xf4;" u2="V" k="70" />
<hkern u1="&#xf4;" u2="&#x3f;" k="39" />
<hkern u1="&#xf4;" u2="&#x2a;" k="45" />
<hkern u1="&#xf4;" u2="&#x29;" k="59" />
<hkern u1="&#xf5;" u2="&#x2122;" k="63" />
<hkern u1="&#xf5;" u2="&#xae;" k="39" />
<hkern u1="&#xf5;" u2="&#x7d;" k="55" />
<hkern u1="&#xf5;" u2="&#x7c;" k="70" />
<hkern u1="&#xf5;" u2="x" k="23" />
<hkern u1="&#xf5;" u2="v" k="16" />
<hkern u1="&#xf5;" u2="f" k="12" />
<hkern u1="&#xf5;" u2="]" k="61" />
<hkern u1="&#xf5;" u2="\" k="94" />
<hkern u1="&#xf5;" u2="X" k="23" />
<hkern u1="&#xf5;" u2="V" k="70" />
<hkern u1="&#xf5;" u2="&#x3f;" k="39" />
<hkern u1="&#xf5;" u2="&#x2a;" k="45" />
<hkern u1="&#xf5;" u2="&#x29;" k="59" />
<hkern u1="&#xf6;" u2="&#x2122;" k="63" />
<hkern u1="&#xf6;" u2="&#xae;" k="39" />
<hkern u1="&#xf6;" u2="&#x7d;" k="55" />
<hkern u1="&#xf6;" u2="&#x7c;" k="70" />
<hkern u1="&#xf6;" u2="x" k="23" />
<hkern u1="&#xf6;" u2="v" k="16" />
<hkern u1="&#xf6;" u2="f" k="12" />
<hkern u1="&#xf6;" u2="]" k="61" />
<hkern u1="&#xf6;" u2="\" k="94" />
<hkern u1="&#xf6;" u2="X" k="23" />
<hkern u1="&#xf6;" u2="V" k="70" />
<hkern u1="&#xf6;" u2="&#x3f;" k="39" />
<hkern u1="&#xf6;" u2="&#x2a;" k="45" />
<hkern u1="&#xf6;" u2="&#x29;" k="59" />
<hkern u1="&#xf8;" u2="&#x2122;" k="63" />
<hkern u1="&#xf8;" u2="&#xae;" k="39" />
<hkern u1="&#xf8;" u2="&#x7d;" k="55" />
<hkern u1="&#xf8;" u2="&#x7c;" k="70" />
<hkern u1="&#xf8;" u2="x" k="23" />
<hkern u1="&#xf8;" u2="v" k="16" />
<hkern u1="&#xf8;" u2="f" k="12" />
<hkern u1="&#xf8;" u2="]" k="61" />
<hkern u1="&#xf8;" u2="\" k="94" />
<hkern u1="&#xf8;" u2="X" k="23" />
<hkern u1="&#xf8;" u2="V" k="70" />
<hkern u1="&#xf8;" u2="&#x3f;" k="39" />
<hkern u1="&#xf8;" u2="&#x2a;" k="45" />
<hkern u1="&#xf8;" u2="&#x29;" k="59" />
<hkern u1="&#xf9;" u2="&#x2122;" k="37" />
<hkern u1="&#xf9;" u2="&#x7d;" k="45" />
<hkern u1="&#xf9;" u2="&#x7c;" k="53" />
<hkern u1="&#xf9;" u2="]" k="25" />
<hkern u1="&#xf9;" u2="\" k="57" />
<hkern u1="&#xf9;" u2="V" k="45" />
<hkern u1="&#xf9;" u2="&#x3f;" k="25" />
<hkern u1="&#xf9;" u2="&#x29;" k="47" />
<hkern u1="&#xfa;" u2="&#x2122;" k="37" />
<hkern u1="&#xfa;" u2="&#x7d;" k="45" />
<hkern u1="&#xfa;" u2="&#x7c;" k="53" />
<hkern u1="&#xfa;" u2="]" k="25" />
<hkern u1="&#xfa;" u2="\" k="57" />
<hkern u1="&#xfa;" u2="V" k="45" />
<hkern u1="&#xfa;" u2="&#x3f;" k="25" />
<hkern u1="&#xfa;" u2="&#x29;" k="47" />
<hkern u1="&#xfb;" u2="&#x2122;" k="37" />
<hkern u1="&#xfb;" u2="&#x7d;" k="45" />
<hkern u1="&#xfb;" u2="&#x7c;" k="53" />
<hkern u1="&#xfb;" u2="]" k="25" />
<hkern u1="&#xfb;" u2="\" k="57" />
<hkern u1="&#xfb;" u2="V" k="45" />
<hkern u1="&#xfb;" u2="&#x3f;" k="25" />
<hkern u1="&#xfb;" u2="&#x29;" k="47" />
<hkern u1="&#xfc;" u2="&#x2122;" k="37" />
<hkern u1="&#xfc;" u2="&#x7d;" k="45" />
<hkern u1="&#xfc;" u2="&#x7c;" k="53" />
<hkern u1="&#xfc;" u2="]" k="25" />
<hkern u1="&#xfc;" u2="\" k="57" />
<hkern u1="&#xfc;" u2="V" k="45" />
<hkern u1="&#xfc;" u2="&#x3f;" k="25" />
<hkern u1="&#xfc;" u2="&#x29;" k="47" />
<hkern u1="&#xfd;" u2="&#x2122;" k="23" />
<hkern u1="&#xfd;" u2="&#x7d;" k="51" />
<hkern u1="&#xfd;" u2="&#x7c;" k="37" />
<hkern u1="&#xfd;" u2="]" k="63" />
<hkern u1="&#xfd;" u2="\" k="43" />
<hkern u1="&#xfd;" u2="X" k="29" />
<hkern u1="&#xfd;" u2="V" k="14" />
<hkern u1="&#xfd;" u2="&#x2f;" k="37" />
<hkern u1="&#xfd;" u2="&#x29;" k="51" />
<hkern u1="&#xff;" u2="&#x2122;" k="23" />
<hkern u1="&#xff;" u2="&#x7d;" k="51" />
<hkern u1="&#xff;" u2="&#x7c;" k="37" />
<hkern u1="&#xff;" u2="]" k="63" />
<hkern u1="&#xff;" u2="\" k="43" />
<hkern u1="&#xff;" u2="X" k="29" />
<hkern u1="&#xff;" u2="V" k="14" />
<hkern u1="&#xff;" u2="&#x2f;" k="37" />
<hkern u1="&#xff;" u2="&#x29;" k="51" />
<hkern u1="&#x100;" u2="&#x2122;" k="68" />
<hkern u1="&#x100;" u2="&#xae;" k="57" />
<hkern u1="&#x100;" u2="&#x7d;" k="27" />
<hkern u1="&#x100;" u2="&#x7c;" k="94" />
<hkern u1="&#x100;" u2="v" k="31" />
<hkern u1="&#x100;" u2="f" k="14" />
<hkern u1="&#x100;" u2="\" k="84" />
<hkern u1="&#x100;" u2="V" k="61" />
<hkern u1="&#x100;" u2="&#x3f;" k="35" />
<hkern u1="&#x100;" u2="&#x2a;" k="61" />
<hkern u1="&#x100;" u2="&#x29;" k="33" />
<hkern u1="&#x101;" u2="&#x2122;" k="43" />
<hkern u1="&#x101;" u2="&#x7d;" k="41" />
<hkern u1="&#x101;" u2="&#x7c;" k="55" />
<hkern u1="&#x101;" u2="]" k="31" />
<hkern u1="&#x101;" u2="\" k="63" />
<hkern u1="&#x101;" u2="V" k="51" />
<hkern u1="&#x101;" u2="&#x3f;" k="29" />
<hkern u1="&#x101;" u2="&#x2a;" k="16" />
<hkern u1="&#x101;" u2="&#x29;" k="45" />
<hkern u1="&#x102;" u2="&#x2122;" k="68" />
<hkern u1="&#x102;" u2="&#xae;" k="57" />
<hkern u1="&#x102;" u2="&#x7d;" k="27" />
<hkern u1="&#x102;" u2="&#x7c;" k="94" />
<hkern u1="&#x102;" u2="v" k="31" />
<hkern u1="&#x102;" u2="f" k="14" />
<hkern u1="&#x102;" u2="\" k="84" />
<hkern u1="&#x102;" u2="V" k="61" />
<hkern u1="&#x102;" u2="&#x3f;" k="35" />
<hkern u1="&#x102;" u2="&#x2a;" k="61" />
<hkern u1="&#x102;" u2="&#x29;" k="33" />
<hkern u1="&#x103;" u2="&#x2122;" k="43" />
<hkern u1="&#x103;" u2="&#x7d;" k="41" />
<hkern u1="&#x103;" u2="&#x7c;" k="55" />
<hkern u1="&#x103;" u2="]" k="31" />
<hkern u1="&#x103;" u2="\" k="63" />
<hkern u1="&#x103;" u2="V" k="51" />
<hkern u1="&#x103;" u2="&#x3f;" k="29" />
<hkern u1="&#x103;" u2="&#x2a;" k="16" />
<hkern u1="&#x103;" u2="&#x29;" k="45" />
<hkern u1="&#x104;" u2="&#x2122;" k="68" />
<hkern u1="&#x104;" u2="&#x201e;" k="-106" />
<hkern u1="&#x104;" u2="&#x201a;" k="-59" />
<hkern u1="&#x104;" u2="&#xae;" k="57" />
<hkern u1="&#x104;" u2="&#x7d;" k="12" />
<hkern u1="&#x104;" u2="&#x7c;" k="94" />
<hkern u1="&#x104;" u2="v" k="31" />
<hkern u1="&#x104;" u2="j" k="-100" />
<hkern u1="&#x104;" u2="f" k="14" />
<hkern u1="&#x104;" u2="\" k="84" />
<hkern u1="&#x104;" u2="V" k="61" />
<hkern u1="&#x104;" u2="&#x3f;" k="35" />
<hkern u1="&#x104;" u2="&#x3b;" k="-41" />
<hkern u1="&#x104;" u2="&#x2c;" k="-39" />
<hkern u1="&#x104;" u2="&#x2a;" k="61" />
<hkern u1="&#x104;" u2="&#x29;" k="33" />
<hkern u1="&#x105;" u2="&#x2122;" k="43" />
<hkern u1="&#x105;" u2="&#x201e;" k="-33" />
<hkern u1="&#x105;" u2="&#x7d;" k="41" />
<hkern u1="&#x105;" u2="&#x7c;" k="55" />
<hkern u1="&#x105;" u2="j" k="-25" />
<hkern u1="&#x105;" u2="]" k="31" />
<hkern u1="&#x105;" u2="\" k="63" />
<hkern u1="&#x105;" u2="V" k="51" />
<hkern u1="&#x105;" u2="&#x3f;" k="29" />
<hkern u1="&#x105;" u2="&#x2a;" k="16" />
<hkern u1="&#x105;" u2="&#x29;" k="45" />
<hkern u1="&#x106;" u2="&#x135;" k="-82" />
<hkern u1="&#x106;" u2="&#x12d;" k="-63" />
<hkern u1="&#x106;" u2="&#x12b;" k="-80" />
<hkern u1="&#x106;" u2="&#x129;" k="-109" />
<hkern u1="&#x106;" u2="&#xef;" k="-90" />
<hkern u1="&#x106;" u2="&#xee;" k="-104" />
<hkern u1="&#x106;" u2="&#xec;" k="-111" />
<hkern u1="&#x106;" u2="&#x2a;" k="-33" />
<hkern u1="&#x106;" u2="&#x29;" k="25" />
<hkern u1="&#x107;" u2="&#x2122;" k="33" />
<hkern u1="&#x107;" u2="&#x7d;" k="31" />
<hkern u1="&#x107;" u2="&#x7c;" k="43" />
<hkern u1="&#x107;" u2="]" k="23" />
<hkern u1="&#x107;" u2="\" k="49" />
<hkern u1="&#x107;" u2="V" k="27" />
<hkern u1="&#x107;" u2="&#x29;" k="33" />
<hkern u1="&#x108;" u2="&#x135;" k="-82" />
<hkern u1="&#x108;" u2="&#x12d;" k="-63" />
<hkern u1="&#x108;" u2="&#x12b;" k="-80" />
<hkern u1="&#x108;" u2="&#x129;" k="-109" />
<hkern u1="&#x108;" u2="&#xef;" k="-90" />
<hkern u1="&#x108;" u2="&#xee;" k="-104" />
<hkern u1="&#x108;" u2="&#xec;" k="-111" />
<hkern u1="&#x108;" u2="&#x2a;" k="-33" />
<hkern u1="&#x108;" u2="&#x29;" k="25" />
<hkern u1="&#x109;" u2="&#x2122;" k="33" />
<hkern u1="&#x109;" u2="&#x7d;" k="31" />
<hkern u1="&#x109;" u2="&#x7c;" k="43" />
<hkern u1="&#x109;" u2="]" k="23" />
<hkern u1="&#x109;" u2="\" k="49" />
<hkern u1="&#x109;" u2="V" k="27" />
<hkern u1="&#x109;" u2="&#x29;" k="33" />
<hkern u1="&#x10a;" u2="&#x135;" k="-82" />
<hkern u1="&#x10a;" u2="&#x12d;" k="-63" />
<hkern u1="&#x10a;" u2="&#x12b;" k="-80" />
<hkern u1="&#x10a;" u2="&#x129;" k="-109" />
<hkern u1="&#x10a;" u2="&#xef;" k="-90" />
<hkern u1="&#x10a;" u2="&#xee;" k="-104" />
<hkern u1="&#x10a;" u2="&#xec;" k="-111" />
<hkern u1="&#x10a;" u2="&#x2a;" k="-33" />
<hkern u1="&#x10a;" u2="&#x29;" k="25" />
<hkern u1="&#x10b;" u2="&#x2122;" k="33" />
<hkern u1="&#x10b;" u2="&#x7d;" k="31" />
<hkern u1="&#x10b;" u2="&#x7c;" k="43" />
<hkern u1="&#x10b;" u2="]" k="23" />
<hkern u1="&#x10b;" u2="\" k="49" />
<hkern u1="&#x10b;" u2="V" k="27" />
<hkern u1="&#x10b;" u2="&#x29;" k="33" />
<hkern u1="&#x10c;" u2="&#x135;" k="-82" />
<hkern u1="&#x10c;" u2="&#x12d;" k="-63" />
<hkern u1="&#x10c;" u2="&#x12b;" k="-80" />
<hkern u1="&#x10c;" u2="&#x129;" k="-109" />
<hkern u1="&#x10c;" u2="&#xef;" k="-90" />
<hkern u1="&#x10c;" u2="&#xee;" k="-104" />
<hkern u1="&#x10c;" u2="&#xec;" k="-111" />
<hkern u1="&#x10c;" u2="&#x2a;" k="-33" />
<hkern u1="&#x10c;" u2="&#x29;" k="25" />
<hkern u1="&#x10d;" u2="&#x2122;" k="33" />
<hkern u1="&#x10d;" u2="&#x12b;" k="-106" />
<hkern u1="&#x10d;" u2="&#x7d;" k="8" />
<hkern u1="&#x10d;" u2="&#x7c;" k="43" />
<hkern u1="&#x10d;" u2="]" k="8" />
<hkern u1="&#x10d;" u2="\" k="49" />
<hkern u1="&#x10d;" u2="V" k="27" />
<hkern u1="&#x10d;" u2="&#x29;" k="-10" />
<hkern u1="&#x10e;" u2="&#x2122;" k="31" />
<hkern u1="&#x10e;" u2="&#x7d;" k="61" />
<hkern u1="&#x10e;" u2="&#x7c;" k="57" />
<hkern u1="&#x10e;" u2="x" k="10" />
<hkern u1="&#x10e;" u2="]" k="70" />
<hkern u1="&#x10e;" u2="\" k="49" />
<hkern u1="&#x10e;" u2="X" k="37" />
<hkern u1="&#x10e;" u2="V" k="29" />
<hkern u1="&#x10e;" u2="&#x2f;" k="35" />
<hkern u1="&#x10e;" u2="&#x29;" k="66" />
<hkern u1="&#x10f;" u2="&#x7d;" k="-29" />
<hkern u1="&#x10f;" u2="&#x7c;" k="-41" />
<hkern u1="&#x10f;" u2="x" k="-43" />
<hkern u1="&#x10f;" u2="v" k="-41" />
<hkern u1="&#x10f;" u2="]" k="-29" />
<hkern u1="&#x10f;" u2="&#x3f;" k="-18" />
<hkern u1="&#x10f;" u2="&#x2f;" k="66" />
<hkern u1="&#x10f;" u2="&#x2a;" k="-68" />
<hkern u1="&#x10f;" u2="&#x21;" k="-18" />
<hkern u1="&#x110;" u2="&#x2122;" k="31" />
<hkern u1="&#x110;" u2="&#x7d;" k="61" />
<hkern u1="&#x110;" u2="&#x7c;" k="57" />
<hkern u1="&#x110;" u2="x" k="10" />
<hkern u1="&#x110;" u2="]" k="70" />
<hkern u1="&#x110;" u2="\" k="49" />
<hkern u1="&#x110;" u2="X" k="37" />
<hkern u1="&#x110;" u2="V" k="29" />
<hkern u1="&#x110;" u2="&#x2f;" k="35" />
<hkern u1="&#x110;" u2="&#x29;" k="66" />
<hkern u1="&#x111;" u2="&#x135;" k="-39" />
<hkern u1="&#x111;" u2="&#x12d;" k="-41" />
<hkern u1="&#x111;" u2="&#x12b;" k="-25" />
<hkern u1="&#x111;" u2="&#x129;" k="-66" />
<hkern u1="&#x111;" u2="&#xef;" k="-37" />
<hkern u1="&#x111;" u2="&#xee;" k="-61" />
<hkern u1="&#x111;" u2="&#xec;" k="-100" />
<hkern u1="&#x111;" u2="&#x7c;" k="-12" />
<hkern u1="&#x111;" u2="&#x2a;" k="-45" />
<hkern u1="&#x111;" u2="&#x29;" k="31" />
<hkern u1="&#x112;" u2="&#x135;" k="-82" />
<hkern u1="&#x112;" u2="&#x12d;" k="-70" />
<hkern u1="&#x112;" u2="&#x12b;" k="-63" />
<hkern u1="&#x112;" u2="&#x129;" k="-106" />
<hkern u1="&#x112;" u2="&#xef;" k="-78" />
<hkern u1="&#x112;" u2="&#xee;" k="-104" />
<hkern u1="&#x112;" u2="&#xec;" k="-141" />
<hkern u1="&#x112;" u2="v" k="14" />
<hkern u1="&#x112;" u2="&#x29;" k="20" />
<hkern u1="&#x113;" u2="&#x2122;" k="51" />
<hkern u1="&#x113;" u2="&#x142;" k="-27" />
<hkern u1="&#x113;" u2="&#x7d;" k="47" />
<hkern u1="&#x113;" u2="&#x7c;" k="59" />
<hkern u1="&#x113;" u2="v" k="10" />
<hkern u1="&#x113;" u2="]" k="39" />
<hkern u1="&#x113;" u2="\" k="76" />
<hkern u1="&#x113;" u2="V" k="49" />
<hkern u1="&#x113;" u2="&#x3f;" k="31" />
<hkern u1="&#x113;" u2="&#x2a;" k="29" />
<hkern u1="&#x113;" u2="&#x29;" k="47" />
<hkern u1="&#x114;" u2="&#x135;" k="-82" />
<hkern u1="&#x114;" u2="&#x12d;" k="-70" />
<hkern u1="&#x114;" u2="&#x12b;" k="-63" />
<hkern u1="&#x114;" u2="&#x129;" k="-106" />
<hkern u1="&#x114;" u2="&#xef;" k="-78" />
<hkern u1="&#x114;" u2="&#xee;" k="-104" />
<hkern u1="&#x114;" u2="&#xec;" k="-141" />
<hkern u1="&#x114;" u2="v" k="14" />
<hkern u1="&#x114;" u2="&#x29;" k="20" />
<hkern u1="&#x115;" u2="&#x2122;" k="51" />
<hkern u1="&#x115;" u2="&#x142;" k="-27" />
<hkern u1="&#x115;" u2="&#x7d;" k="47" />
<hkern u1="&#x115;" u2="&#x7c;" k="59" />
<hkern u1="&#x115;" u2="v" k="10" />
<hkern u1="&#x115;" u2="]" k="39" />
<hkern u1="&#x115;" u2="\" k="76" />
<hkern u1="&#x115;" u2="V" k="49" />
<hkern u1="&#x115;" u2="&#x3f;" k="31" />
<hkern u1="&#x115;" u2="&#x2a;" k="29" />
<hkern u1="&#x115;" u2="&#x29;" k="47" />
<hkern u1="&#x116;" u2="&#x135;" k="-82" />
<hkern u1="&#x116;" u2="&#x12d;" k="-70" />
<hkern u1="&#x116;" u2="&#x12b;" k="-63" />
<hkern u1="&#x116;" u2="&#x129;" k="-106" />
<hkern u1="&#x116;" u2="&#xef;" k="-78" />
<hkern u1="&#x116;" u2="&#xee;" k="-104" />
<hkern u1="&#x116;" u2="&#xec;" k="-141" />
<hkern u1="&#x116;" u2="v" k="14" />
<hkern u1="&#x116;" u2="&#x29;" k="20" />
<hkern u1="&#x117;" u2="&#x2122;" k="51" />
<hkern u1="&#x117;" u2="&#x142;" k="-27" />
<hkern u1="&#x117;" u2="&#x7d;" k="47" />
<hkern u1="&#x117;" u2="&#x7c;" k="59" />
<hkern u1="&#x117;" u2="v" k="10" />
<hkern u1="&#x117;" u2="]" k="39" />
<hkern u1="&#x117;" u2="\" k="76" />
<hkern u1="&#x117;" u2="V" k="49" />
<hkern u1="&#x117;" u2="&#x3f;" k="31" />
<hkern u1="&#x117;" u2="&#x2a;" k="29" />
<hkern u1="&#x117;" u2="&#x29;" k="47" />
<hkern u1="&#x118;" u2="&#x201e;" k="-63" />
<hkern u1="&#x118;" u2="&#x201a;" k="-16" />
<hkern u1="&#x118;" u2="&#x135;" k="-82" />
<hkern u1="&#x118;" u2="&#x12d;" k="-70" />
<hkern u1="&#x118;" u2="&#x12b;" k="-63" />
<hkern u1="&#x118;" u2="&#x129;" k="-106" />
<hkern u1="&#x118;" u2="&#xef;" k="-78" />
<hkern u1="&#x118;" u2="&#xee;" k="-104" />
<hkern u1="&#x118;" u2="&#xec;" k="-141" />
<hkern u1="&#x118;" u2="v" k="14" />
<hkern u1="&#x118;" u2="j" k="-57" />
<hkern u1="&#x118;" u2="&#x29;" k="20" />
<hkern u1="&#x119;" u2="&#x2122;" k="51" />
<hkern u1="&#x119;" u2="&#x201e;" k="-57" />
<hkern u1="&#x119;" u2="&#x142;" k="-27" />
<hkern u1="&#x119;" u2="&#x7d;" k="47" />
<hkern u1="&#x119;" u2="&#x7c;" k="59" />
<hkern u1="&#x119;" u2="v" k="10" />
<hkern u1="&#x119;" u2="]" k="39" />
<hkern u1="&#x119;" u2="\" k="76" />
<hkern u1="&#x119;" u2="V" k="49" />
<hkern u1="&#x119;" u2="&#x3f;" k="31" />
<hkern u1="&#x119;" u2="&#x2a;" k="29" />
<hkern u1="&#x119;" u2="&#x29;" k="47" />
<hkern u1="&#x11a;" u2="&#x135;" k="-82" />
<hkern u1="&#x11a;" u2="&#x12d;" k="-70" />
<hkern u1="&#x11a;" u2="&#x12b;" k="-63" />
<hkern u1="&#x11a;" u2="&#x129;" k="-106" />
<hkern u1="&#x11a;" u2="&#xef;" k="-78" />
<hkern u1="&#x11a;" u2="&#xee;" k="-104" />
<hkern u1="&#x11a;" u2="&#xec;" k="-141" />
<hkern u1="&#x11a;" u2="v" k="14" />
<hkern u1="&#x11a;" u2="&#x29;" k="20" />
<hkern u1="&#x11b;" u2="&#x2122;" k="51" />
<hkern u1="&#x11b;" u2="&#x142;" k="-27" />
<hkern u1="&#x11b;" u2="&#x7d;" k="47" />
<hkern u1="&#x11b;" u2="&#x7c;" k="59" />
<hkern u1="&#x11b;" u2="v" k="10" />
<hkern u1="&#x11b;" u2="]" k="39" />
<hkern u1="&#x11b;" u2="\" k="76" />
<hkern u1="&#x11b;" u2="V" k="49" />
<hkern u1="&#x11b;" u2="&#x3f;" k="31" />
<hkern u1="&#x11b;" u2="&#x2a;" k="29" />
<hkern u1="&#x11b;" u2="&#x29;" k="47" />
<hkern u1="&#x11c;" u2="&#x135;" k="-45" />
<hkern u1="&#x11c;" u2="&#x12d;" k="-29" />
<hkern u1="&#x11c;" u2="&#x12b;" k="-41" />
<hkern u1="&#x11c;" u2="&#x129;" k="-68" />
<hkern u1="&#x11c;" u2="&#xef;" k="-53" />
<hkern u1="&#x11c;" u2="&#xee;" k="-68" />
<hkern u1="&#x11c;" u2="&#xec;" k="-68" />
<hkern u1="&#x11c;" u2="&#x7d;" k="25" />
<hkern u1="&#x11c;" u2="&#x7c;" k="25" />
<hkern u1="&#x11c;" u2="f" k="10" />
<hkern u1="&#x11c;" u2="]" k="23" />
<hkern u1="&#x11c;" u2="\" k="20" />
<hkern u1="&#x11c;" u2="V" k="23" />
<hkern u1="&#x11c;" u2="&#x29;" k="43" />
<hkern u1="&#x11d;" u2="&#x2122;" k="25" />
<hkern u1="&#x11d;" u2="&#x201e;" k="-76" />
<hkern u1="&#x11d;" u2="&#x201a;" k="-45" />
<hkern u1="&#x11d;" u2="&#x135;" k="-109" />
<hkern u1="&#x11d;" u2="&#x12f;" k="-49" />
<hkern u1="&#x11d;" u2="&#x7d;" k="-10" />
<hkern u1="&#x11d;" u2="&#x7c;" k="35" />
<hkern u1="&#x11d;" u2="j" k="-109" />
<hkern u1="&#x11d;" u2="]" k="-10" />
<hkern u1="&#x11d;" u2="\" k="39" />
<hkern u1="&#x11d;" u2="V" k="16" />
<hkern u1="&#x11d;" u2="&#x3b;" k="-49" />
<hkern u1="&#x11d;" u2="&#x2c;" k="-47" />
<hkern u1="&#x11d;" u2="&#x29;" k="-16" />
<hkern u1="&#x11e;" u2="&#x135;" k="-45" />
<hkern u1="&#x11e;" u2="&#x12d;" k="-29" />
<hkern u1="&#x11e;" u2="&#x12b;" k="-41" />
<hkern u1="&#x11e;" u2="&#x129;" k="-68" />
<hkern u1="&#x11e;" u2="&#xef;" k="-53" />
<hkern u1="&#x11e;" u2="&#xee;" k="-68" />
<hkern u1="&#x11e;" u2="&#xec;" k="-68" />
<hkern u1="&#x11e;" u2="&#x7d;" k="25" />
<hkern u1="&#x11e;" u2="&#x7c;" k="25" />
<hkern u1="&#x11e;" u2="f" k="10" />
<hkern u1="&#x11e;" u2="]" k="23" />
<hkern u1="&#x11e;" u2="\" k="20" />
<hkern u1="&#x11e;" u2="V" k="23" />
<hkern u1="&#x11e;" u2="&#x29;" k="43" />
<hkern u1="&#x11f;" u2="&#x2122;" k="25" />
<hkern u1="&#x11f;" u2="&#x201e;" k="-76" />
<hkern u1="&#x11f;" u2="&#x201a;" k="-45" />
<hkern u1="&#x11f;" u2="&#x135;" k="-109" />
<hkern u1="&#x11f;" u2="&#x12f;" k="-49" />
<hkern u1="&#x11f;" u2="&#x7d;" k="-10" />
<hkern u1="&#x11f;" u2="&#x7c;" k="35" />
<hkern u1="&#x11f;" u2="j" k="-109" />
<hkern u1="&#x11f;" u2="]" k="-10" />
<hkern u1="&#x11f;" u2="\" k="39" />
<hkern u1="&#x11f;" u2="V" k="16" />
<hkern u1="&#x11f;" u2="&#x3b;" k="-49" />
<hkern u1="&#x11f;" u2="&#x2c;" k="-47" />
<hkern u1="&#x11f;" u2="&#x29;" k="-16" />
<hkern u1="&#x120;" u2="&#x135;" k="-45" />
<hkern u1="&#x120;" u2="&#x12d;" k="-29" />
<hkern u1="&#x120;" u2="&#x12b;" k="-41" />
<hkern u1="&#x120;" u2="&#x129;" k="-68" />
<hkern u1="&#x120;" u2="&#xef;" k="-53" />
<hkern u1="&#x120;" u2="&#xee;" k="-68" />
<hkern u1="&#x120;" u2="&#xec;" k="-68" />
<hkern u1="&#x120;" u2="&#x7d;" k="25" />
<hkern u1="&#x120;" u2="&#x7c;" k="25" />
<hkern u1="&#x120;" u2="f" k="10" />
<hkern u1="&#x120;" u2="]" k="23" />
<hkern u1="&#x120;" u2="\" k="20" />
<hkern u1="&#x120;" u2="V" k="23" />
<hkern u1="&#x120;" u2="&#x29;" k="43" />
<hkern u1="&#x121;" u2="&#x2122;" k="25" />
<hkern u1="&#x121;" u2="&#x201e;" k="-76" />
<hkern u1="&#x121;" u2="&#x201a;" k="-45" />
<hkern u1="&#x121;" u2="&#x135;" k="-109" />
<hkern u1="&#x121;" u2="&#x12f;" k="-49" />
<hkern u1="&#x121;" u2="&#x7d;" k="-10" />
<hkern u1="&#x121;" u2="&#x7c;" k="35" />
<hkern u1="&#x121;" u2="j" k="-109" />
<hkern u1="&#x121;" u2="]" k="-10" />
<hkern u1="&#x121;" u2="\" k="39" />
<hkern u1="&#x121;" u2="V" k="16" />
<hkern u1="&#x121;" u2="&#x3b;" k="-49" />
<hkern u1="&#x121;" u2="&#x2c;" k="-47" />
<hkern u1="&#x121;" u2="&#x29;" k="-16" />
<hkern u1="&#x122;" u2="&#x135;" k="-45" />
<hkern u1="&#x122;" u2="&#x12d;" k="-29" />
<hkern u1="&#x122;" u2="&#x12b;" k="-41" />
<hkern u1="&#x122;" u2="&#x129;" k="-68" />
<hkern u1="&#x122;" u2="&#xef;" k="-53" />
<hkern u1="&#x122;" u2="&#xee;" k="-68" />
<hkern u1="&#x122;" u2="&#xec;" k="-68" />
<hkern u1="&#x122;" u2="&#x7d;" k="25" />
<hkern u1="&#x122;" u2="&#x7c;" k="25" />
<hkern u1="&#x122;" u2="f" k="10" />
<hkern u1="&#x122;" u2="]" k="23" />
<hkern u1="&#x122;" u2="\" k="20" />
<hkern u1="&#x122;" u2="V" k="23" />
<hkern u1="&#x122;" u2="&#x29;" k="43" />
<hkern u1="&#x123;" u2="&#x2122;" k="25" />
<hkern u1="&#x123;" u2="&#x201e;" k="-76" />
<hkern u1="&#x123;" u2="&#x201a;" k="-45" />
<hkern u1="&#x123;" u2="&#x135;" k="-109" />
<hkern u1="&#x123;" u2="&#x12f;" k="-49" />
<hkern u1="&#x123;" u2="&#x7d;" k="-10" />
<hkern u1="&#x123;" u2="&#x7c;" k="35" />
<hkern u1="&#x123;" u2="j" k="-109" />
<hkern u1="&#x123;" u2="]" k="-10" />
<hkern u1="&#x123;" u2="\" k="39" />
<hkern u1="&#x123;" u2="V" k="16" />
<hkern u1="&#x123;" u2="&#x3b;" k="-49" />
<hkern u1="&#x123;" u2="&#x2c;" k="-47" />
<hkern u1="&#x123;" u2="&#x29;" k="-16" />
<hkern u1="&#x124;" u2="&#x135;" k="-18" />
<hkern u1="&#x124;" u2="&#x129;" k="-45" />
<hkern u1="&#x124;" u2="&#xef;" k="-16" />
<hkern u1="&#x124;" u2="&#xee;" k="-41" />
<hkern u1="&#x124;" u2="&#xec;" k="-78" />
<hkern u1="&#x124;" u2="&#x7d;" k="27" />
<hkern u1="&#x124;" u2="&#x7c;" k="25" />
<hkern u1="&#x124;" u2="f" k="12" />
<hkern u1="&#x124;" u2="]" k="25" />
<hkern u1="&#x124;" u2="&#x29;" k="41" />
<hkern u1="&#x125;" u2="&#x2122;" k="59" />
<hkern u1="&#x125;" u2="&#xae;" k="31" />
<hkern u1="&#x125;" u2="&#x7d;" k="41" />
<hkern u1="&#x125;" u2="&#x7c;" k="70" />
<hkern u1="&#x125;" u2="v" k="10" />
<hkern u1="&#x125;" u2="f" k="8" />
<hkern u1="&#x125;" u2="]" k="20" />
<hkern u1="&#x125;" u2="\" k="88" />
<hkern u1="&#x125;" u2="V" k="61" />
<hkern u1="&#x125;" u2="&#x3f;" k="37" />
<hkern u1="&#x125;" u2="&#x2a;" k="35" />
<hkern u1="&#x125;" u2="&#x29;" k="43" />
<hkern u1="&#x126;" u2="&#x135;" k="-18" />
<hkern u1="&#x126;" u2="&#x129;" k="-45" />
<hkern u1="&#x126;" u2="&#xef;" k="-16" />
<hkern u1="&#x126;" u2="&#xee;" k="-41" />
<hkern u1="&#x126;" u2="&#xec;" k="-78" />
<hkern u1="&#x126;" u2="&#x7d;" k="27" />
<hkern u1="&#x126;" u2="&#x7c;" k="25" />
<hkern u1="&#x126;" u2="f" k="12" />
<hkern u1="&#x126;" u2="]" k="25" />
<hkern u1="&#x126;" u2="&#x2a;" k="-68" />
<hkern u1="&#x126;" u2="&#x29;" k="41" />
<hkern u1="&#x127;" u2="&#x2122;" k="59" />
<hkern u1="&#x127;" u2="&#xae;" k="31" />
<hkern u1="&#x127;" u2="&#x7d;" k="41" />
<hkern u1="&#x127;" u2="&#x7c;" k="70" />
<hkern u1="&#x127;" u2="v" k="10" />
<hkern u1="&#x127;" u2="f" k="8" />
<hkern u1="&#x127;" u2="]" k="20" />
<hkern u1="&#x127;" u2="\" k="88" />
<hkern u1="&#x127;" u2="V" k="61" />
<hkern u1="&#x127;" u2="&#x3f;" k="37" />
<hkern u1="&#x127;" u2="&#x2a;" k="35" />
<hkern u1="&#x127;" u2="&#x29;" k="43" />
<hkern u1="&#x128;" u2="&#x135;" k="-18" />
<hkern u1="&#x128;" u2="&#x129;" k="-45" />
<hkern u1="&#x128;" u2="&#xef;" k="-16" />
<hkern u1="&#x128;" u2="&#xee;" k="-41" />
<hkern u1="&#x128;" u2="&#xec;" k="-78" />
<hkern u1="&#x128;" u2="&#x7d;" k="27" />
<hkern u1="&#x128;" u2="&#x7c;" k="25" />
<hkern u1="&#x128;" u2="f" k="12" />
<hkern u1="&#x128;" u2="]" k="25" />
<hkern u1="&#x128;" u2="&#x29;" k="41" />
<hkern u1="&#x129;" u2="&#x2122;" k="-18" />
<hkern u1="&#x129;" u2="&#x135;" k="-41" />
<hkern u1="&#x129;" u2="&#x12d;" k="-35" />
<hkern u1="&#x129;" u2="&#x12b;" k="-25" />
<hkern u1="&#x129;" u2="&#x129;" k="-68" />
<hkern u1="&#x129;" u2="&#xef;" k="-37" />
<hkern u1="&#x129;" u2="&#xee;" k="-63" />
<hkern u1="&#x129;" u2="&#xec;" k="-100" />
<hkern u1="&#x129;" u2="&#x7d;" k="-76" />
<hkern u1="&#x129;" u2="&#x7c;" k="-76" />
<hkern u1="&#x129;" u2="]" k="-76" />
<hkern u1="&#x129;" u2="\" k="-41" />
<hkern u1="&#x129;" u2="&#x3f;" k="-66" />
<hkern u1="&#x129;" u2="&#x2a;" k="-80" />
<hkern u1="&#x129;" u2="&#x29;" k="33" />
<hkern u1="&#x129;" u2="&#x27;" k="-70" />
<hkern u1="&#x129;" u2="&#x22;" k="-70" />
<hkern u1="&#x129;" u2="&#x21;" k="-66" />
<hkern u1="&#x12a;" u2="&#x135;" k="-18" />
<hkern u1="&#x12a;" u2="&#x129;" k="-45" />
<hkern u1="&#x12a;" u2="&#xef;" k="-16" />
<hkern u1="&#x12a;" u2="&#xee;" k="-41" />
<hkern u1="&#x12a;" u2="&#xec;" k="-78" />
<hkern u1="&#x12a;" u2="&#x7d;" k="27" />
<hkern u1="&#x12a;" u2="&#x7c;" k="25" />
<hkern u1="&#x12a;" u2="f" k="12" />
<hkern u1="&#x12a;" u2="]" k="25" />
<hkern u1="&#x12a;" u2="&#x29;" k="41" />
<hkern u1="&#x12b;" u2="&#x135;" k="-41" />
<hkern u1="&#x12b;" u2="&#x12d;" k="-35" />
<hkern u1="&#x12b;" u2="&#x12b;" k="-25" />
<hkern u1="&#x12b;" u2="&#x129;" k="-68" />
<hkern u1="&#x12b;" u2="&#xef;" k="-37" />
<hkern u1="&#x12b;" u2="&#xee;" k="-63" />
<hkern u1="&#x12b;" u2="&#xec;" k="-100" />
<hkern u1="&#x12b;" u2="&#x7d;" k="-51" />
<hkern u1="&#x12b;" u2="&#x7c;" k="-72" />
<hkern u1="&#x12b;" u2="]" k="-53" />
<hkern u1="&#x12b;" u2="\" k="-41" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-43" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-74" />
<hkern u1="&#x12b;" u2="&#x29;" k="33" />
<hkern u1="&#x12b;" u2="&#x27;" k="-47" />
<hkern u1="&#x12b;" u2="&#x22;" k="-47" />
<hkern u1="&#x12b;" u2="&#x21;" k="-45" />
<hkern u1="&#x12c;" u2="&#x135;" k="-18" />
<hkern u1="&#x12c;" u2="&#x129;" k="-45" />
<hkern u1="&#x12c;" u2="&#xef;" k="-16" />
<hkern u1="&#x12c;" u2="&#xee;" k="-41" />
<hkern u1="&#x12c;" u2="&#xec;" k="-78" />
<hkern u1="&#x12c;" u2="&#x7d;" k="27" />
<hkern u1="&#x12c;" u2="&#x7c;" k="25" />
<hkern u1="&#x12c;" u2="f" k="12" />
<hkern u1="&#x12c;" u2="]" k="25" />
<hkern u1="&#x12c;" u2="&#x29;" k="41" />
<hkern u1="&#x12d;" u2="&#x2122;" k="-14" />
<hkern u1="&#x12d;" u2="&#x135;" k="-41" />
<hkern u1="&#x12d;" u2="&#x12d;" k="-35" />
<hkern u1="&#x12d;" u2="&#x12b;" k="-25" />
<hkern u1="&#x12d;" u2="&#x129;" k="-68" />
<hkern u1="&#x12d;" u2="&#xef;" k="-37" />
<hkern u1="&#x12d;" u2="&#xee;" k="-63" />
<hkern u1="&#x12d;" u2="&#xec;" k="-100" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-88" />
<hkern u1="&#x12d;" u2="&#x7c;" k="-98" />
<hkern u1="&#x12d;" u2="]" k="-94" />
<hkern u1="&#x12d;" u2="\" k="-68" />
<hkern u1="&#x12d;" u2="&#x3f;" k="-39" />
<hkern u1="&#x12d;" u2="&#x2a;" k="-55" />
<hkern u1="&#x12d;" u2="&#x29;" k="33" />
<hkern u1="&#x12d;" u2="&#x27;" k="-63" />
<hkern u1="&#x12d;" u2="&#x22;" k="-63" />
<hkern u1="&#x12d;" u2="&#x21;" k="-70" />
<hkern u1="&#x12e;" u2="&#x135;" k="-18" />
<hkern u1="&#x12e;" u2="&#x129;" k="-45" />
<hkern u1="&#x12e;" u2="&#xef;" k="-16" />
<hkern u1="&#x12e;" u2="&#xee;" k="-41" />
<hkern u1="&#x12e;" u2="&#xec;" k="-78" />
<hkern u1="&#x12e;" u2="&#x7d;" k="27" />
<hkern u1="&#x12e;" u2="&#x7c;" k="25" />
<hkern u1="&#x12e;" u2="f" k="12" />
<hkern u1="&#x12e;" u2="]" k="25" />
<hkern u1="&#x12e;" u2="&#x29;" k="41" />
<hkern u1="&#x12f;" u2="&#x135;" k="-41" />
<hkern u1="&#x12f;" u2="&#x12d;" k="-35" />
<hkern u1="&#x12f;" u2="&#x12b;" k="-25" />
<hkern u1="&#x12f;" u2="&#x129;" k="-68" />
<hkern u1="&#x12f;" u2="&#xef;" k="-37" />
<hkern u1="&#x12f;" u2="&#xee;" k="-63" />
<hkern u1="&#x12f;" u2="&#xec;" k="-100" />
<hkern u1="&#x12f;" u2="&#x29;" k="33" />
<hkern u1="&#x130;" u2="&#x135;" k="-18" />
<hkern u1="&#x130;" u2="&#x129;" k="-45" />
<hkern u1="&#x130;" u2="&#xef;" k="-16" />
<hkern u1="&#x130;" u2="&#xee;" k="-41" />
<hkern u1="&#x130;" u2="&#xec;" k="-78" />
<hkern u1="&#x130;" u2="&#x7d;" k="27" />
<hkern u1="&#x130;" u2="&#x7c;" k="25" />
<hkern u1="&#x130;" u2="f" k="12" />
<hkern u1="&#x130;" u2="]" k="25" />
<hkern u1="&#x130;" u2="&#x29;" k="41" />
<hkern u1="&#x131;" u2="&#x135;" k="-41" />
<hkern u1="&#x131;" u2="&#x12d;" k="-35" />
<hkern u1="&#x131;" u2="&#x12b;" k="-25" />
<hkern u1="&#x131;" u2="&#x129;" k="-68" />
<hkern u1="&#x131;" u2="&#xef;" k="-37" />
<hkern u1="&#x131;" u2="&#xee;" k="-63" />
<hkern u1="&#x131;" u2="&#xec;" k="-100" />
<hkern u1="&#x131;" u2="&#x29;" k="33" />
<hkern u1="&#x134;" u2="&#x135;" k="-27" />
<hkern u1="&#x134;" u2="&#x12d;" k="-12" />
<hkern u1="&#x134;" u2="&#x129;" k="-51" />
<hkern u1="&#x134;" u2="&#xef;" k="-23" />
<hkern u1="&#x134;" u2="&#xee;" k="-49" />
<hkern u1="&#x134;" u2="&#xec;" k="-84" />
<hkern u1="&#x134;" u2="&#x7d;" k="23" />
<hkern u1="&#x134;" u2="&#x7c;" k="23" />
<hkern u1="&#x134;" u2="f" k="10" />
<hkern u1="&#x134;" u2="]" k="23" />
<hkern u1="&#x134;" u2="&#x29;" k="39" />
<hkern u1="&#x135;" u2="&#x201d;" k="-47" />
<hkern u1="&#x135;" u2="&#x201c;" k="-12" />
<hkern u1="&#x135;" u2="&#x2019;" k="-47" />
<hkern u1="&#x135;" u2="&#x142;" k="-12" />
<hkern u1="&#x135;" u2="&#x13e;" k="-12" />
<hkern u1="&#x135;" u2="&#x13c;" k="-12" />
<hkern u1="&#x135;" u2="&#x13a;" k="-12" />
<hkern u1="&#x135;" u2="&#x137;" k="-12" />
<hkern u1="&#x135;" u2="&#x135;" k="-12" />
<hkern u1="&#x135;" u2="&#x131;" k="-12" />
<hkern u1="&#x135;" u2="&#x12f;" k="-12" />
<hkern u1="&#x135;" u2="&#x12d;" k="-12" />
<hkern u1="&#x135;" u2="&#x12b;" k="-12" />
<hkern u1="&#x135;" u2="&#x129;" k="-12" />
<hkern u1="&#x135;" u2="&#x127;" k="-12" />
<hkern u1="&#x135;" u2="&#x125;" k="-12" />
<hkern u1="&#x135;" u2="&#xef;" k="-12" />
<hkern u1="&#x135;" u2="&#xee;" k="-12" />
<hkern u1="&#x135;" u2="&#xed;" k="-12" />
<hkern u1="&#x135;" u2="&#xec;" k="-12" />
<hkern u1="&#x135;" u2="&#xdf;" k="-12" />
<hkern u1="&#x135;" u2="&#xae;" k="-33" />
<hkern u1="&#x135;" u2="&#x7c;" k="-47" />
<hkern u1="&#x135;" u2="l" k="-12" />
<hkern u1="&#x135;" u2="k" k="-12" />
<hkern u1="&#x135;" u2="j" k="-12" />
<hkern u1="&#x135;" u2="i" k="-12" />
<hkern u1="&#x135;" u2="h" k="-12" />
<hkern u1="&#x135;" u2="b" k="-16" />
<hkern u1="&#x135;" u2="&#x3f;" k="-59" />
<hkern u1="&#x135;" u2="&#x2a;" k="-80" />
<hkern u1="&#x135;" u2="&#x29;" k="12" />
<hkern u1="&#x135;" u2="&#x27;" k="-80" />
<hkern u1="&#x135;" u2="&#x22;" k="-80" />
<hkern u1="&#x135;" u2="&#x21;" k="-74" />
<hkern u1="&#x136;" u2="&#x135;" k="-49" />
<hkern u1="&#x136;" u2="&#x12d;" k="-111" />
<hkern u1="&#x136;" u2="&#x12b;" k="-109" />
<hkern u1="&#x136;" u2="&#x129;" k="-147" />
<hkern u1="&#x136;" u2="&#xef;" k="-119" />
<hkern u1="&#x136;" u2="&#xee;" k="-68" />
<hkern u1="&#x136;" u2="&#xec;" k="-156" />
<hkern u1="&#x136;" u2="v" k="25" />
<hkern u1="&#x136;" u2="f" k="14" />
<hkern u1="&#x136;" u2="&#x2a;" k="-18" />
<hkern u1="&#x137;" u2="&#x2122;" k="27" />
<hkern u1="&#x137;" u2="&#x7c;" k="31" />
<hkern u1="&#x137;" u2="\" k="37" />
<hkern u1="&#x137;" u2="V" k="16" />
<hkern u1="&#x139;" u2="&#x2122;" k="129" />
<hkern u1="&#x139;" u2="&#x201e;" k="-14" />
<hkern u1="&#x139;" u2="&#xae;" k="131" />
<hkern u1="&#x139;" u2="&#x7c;" k="82" />
<hkern u1="&#x139;" u2="v" k="70" />
<hkern u1="&#x139;" u2="f" k="12" />
<hkern u1="&#x139;" u2="\" k="131" />
<hkern u1="&#x139;" u2="V" k="115" />
<hkern u1="&#x139;" u2="&#x3f;" k="20" />
<hkern u1="&#x139;" u2="&#x3b;" k="-14" />
<hkern u1="&#x139;" u2="&#x2c;" k="-12" />
<hkern u1="&#x139;" u2="&#x2a;" k="129" />
<hkern u1="&#x13a;" u2="&#x135;" k="-39" />
<hkern u1="&#x13a;" u2="&#x12d;" k="-41" />
<hkern u1="&#x13a;" u2="&#x12b;" k="-25" />
<hkern u1="&#x13a;" u2="&#x129;" k="-66" />
<hkern u1="&#x13a;" u2="&#xef;" k="-37" />
<hkern u1="&#x13a;" u2="&#xee;" k="-61" />
<hkern u1="&#x13a;" u2="&#xec;" k="-100" />
<hkern u1="&#x13a;" u2="&#x29;" k="31" />
<hkern u1="&#x13b;" u2="&#x2122;" k="129" />
<hkern u1="&#x13b;" u2="&#x201e;" k="-14" />
<hkern u1="&#x13b;" u2="&#xae;" k="131" />
<hkern u1="&#x13b;" u2="&#x7c;" k="82" />
<hkern u1="&#x13b;" u2="v" k="70" />
<hkern u1="&#x13b;" u2="f" k="12" />
<hkern u1="&#x13b;" u2="\" k="131" />
<hkern u1="&#x13b;" u2="V" k="115" />
<hkern u1="&#x13b;" u2="&#x3f;" k="20" />
<hkern u1="&#x13b;" u2="&#x3b;" k="-14" />
<hkern u1="&#x13b;" u2="&#x2c;" k="-12" />
<hkern u1="&#x13b;" u2="&#x2a;" k="129" />
<hkern u1="&#x13c;" u2="&#x135;" k="-39" />
<hkern u1="&#x13c;" u2="&#x12d;" k="-41" />
<hkern u1="&#x13c;" u2="&#x12b;" k="-25" />
<hkern u1="&#x13c;" u2="&#x129;" k="-66" />
<hkern u1="&#x13c;" u2="&#xef;" k="-37" />
<hkern u1="&#x13c;" u2="&#xee;" k="-61" />
<hkern u1="&#x13c;" u2="&#xec;" k="-100" />
<hkern u1="&#x13c;" u2="&#x29;" k="31" />
<hkern u1="&#x13d;" u2="&#x2122;" k="86" />
<hkern u1="&#x13d;" u2="&#x201e;" k="-14" />
<hkern u1="&#x13d;" u2="&#x201d;" k="43" />
<hkern u1="&#x13d;" u2="&#x201c;" k="41" />
<hkern u1="&#x13d;" u2="&#x2019;" k="43" />
<hkern u1="&#x13d;" u2="&#x2018;" k="45" />
<hkern u1="&#x13d;" u2="&#x21a;" k="25" />
<hkern u1="&#x13d;" u2="&#x178;" k="8" />
<hkern u1="&#x13d;" u2="&#x177;" k="43" />
<hkern u1="&#x13d;" u2="&#x176;" k="8" />
<hkern u1="&#x13d;" u2="&#x174;" k="41" />
<hkern u1="&#x13d;" u2="&#x166;" k="25" />
<hkern u1="&#x13d;" u2="&#x164;" k="25" />
<hkern u1="&#x13d;" u2="&#xff;" k="43" />
<hkern u1="&#x13d;" u2="&#xfd;" k="43" />
<hkern u1="&#x13d;" u2="&#xdd;" k="8" />
<hkern u1="&#x13d;" u2="&#xae;" k="84" />
<hkern u1="&#x13d;" u2="&#x7c;" k="39" />
<hkern u1="&#x13d;" u2="y" k="43" />
<hkern u1="&#x13d;" u2="v" k="43" />
<hkern u1="&#x13d;" u2="f" k="12" />
<hkern u1="&#x13d;" u2="\" k="74" />
<hkern u1="&#x13d;" u2="Y" k="8" />
<hkern u1="&#x13d;" u2="W" k="41" />
<hkern u1="&#x13d;" u2="V" k="35" />
<hkern u1="&#x13d;" u2="T" k="25" />
<hkern u1="&#x13d;" u2="&#x3f;" k="20" />
<hkern u1="&#x13d;" u2="&#x3b;" k="-14" />
<hkern u1="&#x13d;" u2="&#x2c;" k="-12" />
<hkern u1="&#x13d;" u2="&#x2a;" k="129" />
<hkern u1="&#x13d;" u2="&#x27;" k="57" />
<hkern u1="&#x13d;" u2="&#x22;" k="57" />
<hkern u1="&#x13e;" u2="&#x7d;" k="-29" />
<hkern u1="&#x13e;" u2="&#x7c;" k="-41" />
<hkern u1="&#x13e;" u2="x" k="-43" />
<hkern u1="&#x13e;" u2="v" k="-41" />
<hkern u1="&#x13e;" u2="]" k="-29" />
<hkern u1="&#x13e;" u2="&#x3f;" k="-18" />
<hkern u1="&#x13e;" u2="&#x2f;" k="66" />
<hkern u1="&#x13e;" u2="&#x2a;" k="-68" />
<hkern u1="&#x13e;" u2="&#x21;" k="-18" />
<hkern u1="&#x141;" u2="&#x2122;" k="129" />
<hkern u1="&#x141;" u2="&#x201e;" k="-14" />
<hkern u1="&#x141;" u2="&#x201a;" k="-12" />
<hkern u1="&#x141;" u2="&#xae;" k="131" />
<hkern u1="&#x141;" u2="&#x7c;" k="82" />
<hkern u1="&#x141;" u2="v" k="70" />
<hkern u1="&#x141;" u2="f" k="12" />
<hkern u1="&#x141;" u2="\" k="131" />
<hkern u1="&#x141;" u2="V" k="115" />
<hkern u1="&#x141;" u2="&#x3f;" k="20" />
<hkern u1="&#x141;" u2="&#x3b;" k="-14" />
<hkern u1="&#x141;" u2="&#x2c;" k="-12" />
<hkern u1="&#x141;" u2="&#x2a;" k="129" />
<hkern u1="&#x142;" u2="&#x21b;" k="-41" />
<hkern u1="&#x142;" u2="&#x219;" k="-12" />
<hkern u1="&#x142;" u2="&#x167;" k="-41" />
<hkern u1="&#x142;" u2="&#x165;" k="-41" />
<hkern u1="&#x142;" u2="&#x161;" k="-12" />
<hkern u1="&#x142;" u2="&#x15f;" k="-12" />
<hkern u1="&#x142;" u2="&#x15d;" k="-12" />
<hkern u1="&#x142;" u2="&#x15b;" k="-12" />
<hkern u1="&#x142;" u2="&#x135;" k="-39" />
<hkern u1="&#x142;" u2="&#x12d;" k="-41" />
<hkern u1="&#x142;" u2="&#x12b;" k="-25" />
<hkern u1="&#x142;" u2="&#x129;" k="-66" />
<hkern u1="&#x142;" u2="&#x123;" k="-20" />
<hkern u1="&#x142;" u2="&#x121;" k="-20" />
<hkern u1="&#x142;" u2="&#x11f;" k="-20" />
<hkern u1="&#x142;" u2="&#x11d;" k="-20" />
<hkern u1="&#x142;" u2="&#xef;" k="-37" />
<hkern u1="&#x142;" u2="&#xee;" k="-61" />
<hkern u1="&#x142;" u2="&#xec;" k="-100" />
<hkern u1="&#x142;" u2="t" k="-41" />
<hkern u1="&#x142;" u2="s" k="-12" />
<hkern u1="&#x142;" u2="g" k="-20" />
<hkern u1="&#x142;" u2="&#x3b;" k="-25" />
<hkern u1="&#x142;" u2="&#x3a;" k="-25" />
<hkern u1="&#x142;" u2="&#x29;" k="31" />
<hkern u1="&#x142;" u2="&#x21;" k="-16" />
<hkern u1="&#x143;" u2="&#x135;" k="-18" />
<hkern u1="&#x143;" u2="&#x129;" k="-45" />
<hkern u1="&#x143;" u2="&#xef;" k="-16" />
<hkern u1="&#x143;" u2="&#xee;" k="-41" />
<hkern u1="&#x143;" u2="&#xec;" k="-78" />
<hkern u1="&#x143;" u2="&#x7d;" k="27" />
<hkern u1="&#x143;" u2="&#x7c;" k="25" />
<hkern u1="&#x143;" u2="f" k="12" />
<hkern u1="&#x143;" u2="]" k="25" />
<hkern u1="&#x143;" u2="&#x29;" k="41" />
<hkern u1="&#x144;" u2="&#x2122;" k="59" />
<hkern u1="&#x144;" u2="&#xae;" k="31" />
<hkern u1="&#x144;" u2="&#x7d;" k="41" />
<hkern u1="&#x144;" u2="&#x7c;" k="70" />
<hkern u1="&#x144;" u2="v" k="10" />
<hkern u1="&#x144;" u2="f" k="8" />
<hkern u1="&#x144;" u2="]" k="20" />
<hkern u1="&#x144;" u2="\" k="88" />
<hkern u1="&#x144;" u2="V" k="61" />
<hkern u1="&#x144;" u2="&#x3f;" k="37" />
<hkern u1="&#x144;" u2="&#x2a;" k="35" />
<hkern u1="&#x144;" u2="&#x29;" k="43" />
<hkern u1="&#x145;" u2="&#x135;" k="-18" />
<hkern u1="&#x145;" u2="&#x129;" k="-45" />
<hkern u1="&#x145;" u2="&#xef;" k="-16" />
<hkern u1="&#x145;" u2="&#xee;" k="-41" />
<hkern u1="&#x145;" u2="&#xec;" k="-78" />
<hkern u1="&#x145;" u2="&#x7d;" k="27" />
<hkern u1="&#x145;" u2="&#x7c;" k="25" />
<hkern u1="&#x145;" u2="f" k="12" />
<hkern u1="&#x145;" u2="]" k="25" />
<hkern u1="&#x145;" u2="&#x29;" k="41" />
<hkern u1="&#x146;" u2="&#x2122;" k="59" />
<hkern u1="&#x146;" u2="&#xae;" k="31" />
<hkern u1="&#x146;" u2="&#x7d;" k="41" />
<hkern u1="&#x146;" u2="&#x7c;" k="70" />
<hkern u1="&#x146;" u2="v" k="10" />
<hkern u1="&#x146;" u2="f" k="8" />
<hkern u1="&#x146;" u2="]" k="20" />
<hkern u1="&#x146;" u2="\" k="88" />
<hkern u1="&#x146;" u2="V" k="61" />
<hkern u1="&#x146;" u2="&#x3f;" k="37" />
<hkern u1="&#x146;" u2="&#x2a;" k="35" />
<hkern u1="&#x146;" u2="&#x29;" k="43" />
<hkern u1="&#x147;" u2="&#x135;" k="-18" />
<hkern u1="&#x147;" u2="&#x129;" k="-45" />
<hkern u1="&#x147;" u2="&#xef;" k="-16" />
<hkern u1="&#x147;" u2="&#xee;" k="-41" />
<hkern u1="&#x147;" u2="&#xec;" k="-78" />
<hkern u1="&#x147;" u2="&#x7d;" k="27" />
<hkern u1="&#x147;" u2="&#x7c;" k="25" />
<hkern u1="&#x147;" u2="f" k="12" />
<hkern u1="&#x147;" u2="]" k="25" />
<hkern u1="&#x147;" u2="&#x29;" k="41" />
<hkern u1="&#x148;" u2="&#x2122;" k="59" />
<hkern u1="&#x148;" u2="&#xae;" k="31" />
<hkern u1="&#x148;" u2="&#x7d;" k="41" />
<hkern u1="&#x148;" u2="&#x7c;" k="70" />
<hkern u1="&#x148;" u2="v" k="10" />
<hkern u1="&#x148;" u2="f" k="8" />
<hkern u1="&#x148;" u2="]" k="20" />
<hkern u1="&#x148;" u2="\" k="88" />
<hkern u1="&#x148;" u2="V" k="61" />
<hkern u1="&#x148;" u2="&#x3f;" k="37" />
<hkern u1="&#x148;" u2="&#x2a;" k="35" />
<hkern u1="&#x148;" u2="&#x29;" k="43" />
<hkern u1="&#x14a;" u2="&#x135;" k="-18" />
<hkern u1="&#x14a;" u2="&#x129;" k="-45" />
<hkern u1="&#x14a;" u2="&#xef;" k="-16" />
<hkern u1="&#x14a;" u2="&#xee;" k="-41" />
<hkern u1="&#x14a;" u2="&#xec;" k="-78" />
<hkern u1="&#x14a;" u2="&#x7d;" k="27" />
<hkern u1="&#x14a;" u2="&#x7c;" k="25" />
<hkern u1="&#x14a;" u2="f" k="12" />
<hkern u1="&#x14a;" u2="]" k="25" />
<hkern u1="&#x14a;" u2="&#x29;" k="41" />
<hkern u1="&#x14b;" u2="&#x2122;" k="59" />
<hkern u1="&#x14b;" u2="&#xae;" k="31" />
<hkern u1="&#x14b;" u2="&#x7d;" k="41" />
<hkern u1="&#x14b;" u2="&#x7c;" k="70" />
<hkern u1="&#x14b;" u2="v" k="10" />
<hkern u1="&#x14b;" u2="f" k="8" />
<hkern u1="&#x14b;" u2="]" k="20" />
<hkern u1="&#x14b;" u2="\" k="88" />
<hkern u1="&#x14b;" u2="V" k="61" />
<hkern u1="&#x14b;" u2="&#x3f;" k="37" />
<hkern u1="&#x14b;" u2="&#x2a;" k="35" />
<hkern u1="&#x14b;" u2="&#x29;" k="43" />
<hkern u1="&#x14c;" u2="&#x2122;" k="29" />
<hkern u1="&#x14c;" u2="&#x7d;" k="61" />
<hkern u1="&#x14c;" u2="&#x7c;" k="57" />
<hkern u1="&#x14c;" u2="x" k="10" />
<hkern u1="&#x14c;" u2="]" k="70" />
<hkern u1="&#x14c;" u2="\" k="49" />
<hkern u1="&#x14c;" u2="X" k="31" />
<hkern u1="&#x14c;" u2="V" k="29" />
<hkern u1="&#x14c;" u2="&#x2f;" k="33" />
<hkern u1="&#x14c;" u2="&#x29;" k="66" />
<hkern u1="&#x14d;" u2="&#x2122;" k="63" />
<hkern u1="&#x14d;" u2="&#xae;" k="39" />
<hkern u1="&#x14d;" u2="&#x7d;" k="55" />
<hkern u1="&#x14d;" u2="&#x7c;" k="70" />
<hkern u1="&#x14d;" u2="x" k="23" />
<hkern u1="&#x14d;" u2="v" k="16" />
<hkern u1="&#x14d;" u2="f" k="12" />
<hkern u1="&#x14d;" u2="]" k="61" />
<hkern u1="&#x14d;" u2="\" k="94" />
<hkern u1="&#x14d;" u2="X" k="23" />
<hkern u1="&#x14d;" u2="V" k="70" />
<hkern u1="&#x14d;" u2="&#x3f;" k="39" />
<hkern u1="&#x14d;" u2="&#x2a;" k="45" />
<hkern u1="&#x14d;" u2="&#x29;" k="59" />
<hkern u1="&#x14e;" u2="&#x2122;" k="29" />
<hkern u1="&#x14e;" u2="&#x7d;" k="61" />
<hkern u1="&#x14e;" u2="&#x7c;" k="57" />
<hkern u1="&#x14e;" u2="x" k="10" />
<hkern u1="&#x14e;" u2="]" k="70" />
<hkern u1="&#x14e;" u2="\" k="49" />
<hkern u1="&#x14e;" u2="X" k="31" />
<hkern u1="&#x14e;" u2="V" k="29" />
<hkern u1="&#x14e;" u2="&#x2f;" k="33" />
<hkern u1="&#x14e;" u2="&#x29;" k="66" />
<hkern u1="&#x14f;" u2="&#x2122;" k="63" />
<hkern u1="&#x14f;" u2="&#xae;" k="39" />
<hkern u1="&#x14f;" u2="&#x7d;" k="55" />
<hkern u1="&#x14f;" u2="&#x7c;" k="70" />
<hkern u1="&#x14f;" u2="x" k="23" />
<hkern u1="&#x14f;" u2="v" k="16" />
<hkern u1="&#x14f;" u2="f" k="12" />
<hkern u1="&#x14f;" u2="]" k="61" />
<hkern u1="&#x14f;" u2="\" k="94" />
<hkern u1="&#x14f;" u2="X" k="23" />
<hkern u1="&#x14f;" u2="V" k="70" />
<hkern u1="&#x14f;" u2="&#x3f;" k="39" />
<hkern u1="&#x14f;" u2="&#x2a;" k="45" />
<hkern u1="&#x14f;" u2="&#x29;" k="59" />
<hkern u1="&#x150;" u2="&#x2122;" k="29" />
<hkern u1="&#x150;" u2="&#x7d;" k="61" />
<hkern u1="&#x150;" u2="&#x7c;" k="57" />
<hkern u1="&#x150;" u2="x" k="10" />
<hkern u1="&#x150;" u2="]" k="70" />
<hkern u1="&#x150;" u2="\" k="49" />
<hkern u1="&#x150;" u2="X" k="31" />
<hkern u1="&#x150;" u2="V" k="29" />
<hkern u1="&#x150;" u2="&#x2f;" k="33" />
<hkern u1="&#x150;" u2="&#x29;" k="66" />
<hkern u1="&#x151;" u2="&#x2122;" k="63" />
<hkern u1="&#x151;" u2="&#xae;" k="39" />
<hkern u1="&#x151;" u2="&#x7d;" k="55" />
<hkern u1="&#x151;" u2="&#x7c;" k="70" />
<hkern u1="&#x151;" u2="x" k="23" />
<hkern u1="&#x151;" u2="v" k="16" />
<hkern u1="&#x151;" u2="f" k="12" />
<hkern u1="&#x151;" u2="]" k="61" />
<hkern u1="&#x151;" u2="\" k="94" />
<hkern u1="&#x151;" u2="X" k="23" />
<hkern u1="&#x151;" u2="V" k="70" />
<hkern u1="&#x151;" u2="&#x3f;" k="39" />
<hkern u1="&#x151;" u2="&#x2a;" k="45" />
<hkern u1="&#x151;" u2="&#x29;" k="59" />
<hkern u1="&#x152;" u2="&#x135;" k="-82" />
<hkern u1="&#x152;" u2="&#x12d;" k="-70" />
<hkern u1="&#x152;" u2="&#x12b;" k="-63" />
<hkern u1="&#x152;" u2="&#x129;" k="-106" />
<hkern u1="&#x152;" u2="&#xef;" k="-78" />
<hkern u1="&#x152;" u2="&#xee;" k="-104" />
<hkern u1="&#x152;" u2="&#xec;" k="-141" />
<hkern u1="&#x152;" u2="v" k="14" />
<hkern u1="&#x152;" u2="&#x29;" k="20" />
<hkern u1="&#x153;" u2="&#x2122;" k="51" />
<hkern u1="&#x153;" u2="&#x142;" k="-18" />
<hkern u1="&#x153;" u2="&#x7d;" k="47" />
<hkern u1="&#x153;" u2="&#x7c;" k="59" />
<hkern u1="&#x153;" u2="v" k="10" />
<hkern u1="&#x153;" u2="]" k="39" />
<hkern u1="&#x153;" u2="\" k="76" />
<hkern u1="&#x153;" u2="V" k="49" />
<hkern u1="&#x153;" u2="&#x3f;" k="31" />
<hkern u1="&#x153;" u2="&#x2a;" k="29" />
<hkern u1="&#x153;" u2="&#x29;" k="47" />
<hkern u1="&#x154;" u2="&#xee;" k="-16" />
<hkern u1="&#x154;" u2="&#x7d;" k="31" />
<hkern u1="&#x154;" u2="&#x7c;" k="51" />
<hkern u1="&#x154;" u2="\" k="39" />
<hkern u1="&#x154;" u2="V" k="27" />
<hkern u1="&#x154;" u2="&#x2f;" k="27" />
<hkern u1="&#x154;" u2="&#x29;" k="35" />
<hkern u1="&#x155;" u2="&#x7d;" k="45" />
<hkern u1="&#x155;" u2="&#x7c;" k="23" />
<hkern u1="&#x155;" u2="]" k="57" />
<hkern u1="&#x155;" u2="\" k="25" />
<hkern u1="&#x155;" u2="X" k="47" />
<hkern u1="&#x155;" u2="&#x2f;" k="45" />
<hkern u1="&#x155;" u2="&#x29;" k="47" />
<hkern u1="&#x156;" u2="&#xee;" k="-16" />
<hkern u1="&#x156;" u2="&#x7d;" k="31" />
<hkern u1="&#x156;" u2="&#x7c;" k="51" />
<hkern u1="&#x156;" u2="\" k="39" />
<hkern u1="&#x156;" u2="V" k="27" />
<hkern u1="&#x156;" u2="&#x2f;" k="27" />
<hkern u1="&#x156;" u2="&#x29;" k="35" />
<hkern u1="&#x157;" u2="&#x7d;" k="45" />
<hkern u1="&#x157;" u2="&#x7c;" k="23" />
<hkern u1="&#x157;" u2="]" k="57" />
<hkern u1="&#x157;" u2="\" k="25" />
<hkern u1="&#x157;" u2="X" k="47" />
<hkern u1="&#x157;" u2="&#x2f;" k="45" />
<hkern u1="&#x157;" u2="&#x29;" k="47" />
<hkern u1="&#x158;" u2="&#xee;" k="-16" />
<hkern u1="&#x158;" u2="&#x7d;" k="31" />
<hkern u1="&#x158;" u2="&#x7c;" k="51" />
<hkern u1="&#x158;" u2="\" k="39" />
<hkern u1="&#x158;" u2="V" k="27" />
<hkern u1="&#x158;" u2="&#x2f;" k="27" />
<hkern u1="&#x158;" u2="&#x29;" k="35" />
<hkern u1="&#x159;" u2="&#x159;" k="-39" />
<hkern u1="&#x159;" u2="&#x7d;" k="10" />
<hkern u1="&#x159;" u2="&#x7c;" k="23" />
<hkern u1="&#x159;" u2="]" k="10" />
<hkern u1="&#x159;" u2="\" k="25" />
<hkern u1="&#x159;" u2="X" k="47" />
<hkern u1="&#x159;" u2="&#x2f;" k="45" />
<hkern u1="&#x159;" u2="&#x29;" k="-8" />
<hkern u1="&#x15a;" u2="&#x135;" k="-25" />
<hkern u1="&#x15a;" u2="&#x129;" k="-37" />
<hkern u1="&#x15a;" u2="&#xef;" k="-23" />
<hkern u1="&#x15a;" u2="&#xee;" k="-47" />
<hkern u1="&#x15a;" u2="&#xec;" k="-57" />
<hkern u1="&#x15a;" u2="&#x7d;" k="35" />
<hkern u1="&#x15a;" u2="&#x7c;" k="37" />
<hkern u1="&#x15a;" u2="v" k="10" />
<hkern u1="&#x15a;" u2="f" k="12" />
<hkern u1="&#x15a;" u2="]" k="35" />
<hkern u1="&#x15a;" u2="\" k="25" />
<hkern u1="&#x15a;" u2="V" k="25" />
<hkern u1="&#x15a;" u2="&#x29;" k="47" />
<hkern u1="&#x15b;" u2="&#x2122;" k="45" />
<hkern u1="&#x15b;" u2="&#x7d;" k="51" />
<hkern u1="&#x15b;" u2="&#x7c;" k="57" />
<hkern u1="&#x15b;" u2="v" k="10" />
<hkern u1="&#x15b;" u2="]" k="47" />
<hkern u1="&#x15b;" u2="\" k="63" />
<hkern u1="&#x15b;" u2="V" k="45" />
<hkern u1="&#x15b;" u2="&#x3f;" k="25" />
<hkern u1="&#x15b;" u2="&#x2a;" k="16" />
<hkern u1="&#x15b;" u2="&#x29;" k="47" />
<hkern u1="&#x15c;" u2="&#x135;" k="-25" />
<hkern u1="&#x15c;" u2="&#x129;" k="-37" />
<hkern u1="&#x15c;" u2="&#xef;" k="-23" />
<hkern u1="&#x15c;" u2="&#xee;" k="-47" />
<hkern u1="&#x15c;" u2="&#xec;" k="-57" />
<hkern u1="&#x15c;" u2="&#x7d;" k="35" />
<hkern u1="&#x15c;" u2="&#x7c;" k="37" />
<hkern u1="&#x15c;" u2="v" k="10" />
<hkern u1="&#x15c;" u2="f" k="12" />
<hkern u1="&#x15c;" u2="]" k="35" />
<hkern u1="&#x15c;" u2="\" k="25" />
<hkern u1="&#x15c;" u2="V" k="25" />
<hkern u1="&#x15c;" u2="&#x29;" k="47" />
<hkern u1="&#x15d;" u2="&#x2122;" k="45" />
<hkern u1="&#x15d;" u2="&#x7d;" k="51" />
<hkern u1="&#x15d;" u2="&#x7c;" k="57" />
<hkern u1="&#x15d;" u2="v" k="10" />
<hkern u1="&#x15d;" u2="]" k="47" />
<hkern u1="&#x15d;" u2="\" k="63" />
<hkern u1="&#x15d;" u2="V" k="45" />
<hkern u1="&#x15d;" u2="&#x3f;" k="25" />
<hkern u1="&#x15d;" u2="&#x2a;" k="16" />
<hkern u1="&#x15d;" u2="&#x29;" k="47" />
<hkern u1="&#x15e;" u2="&#x135;" k="-25" />
<hkern u1="&#x15e;" u2="&#x129;" k="-37" />
<hkern u1="&#x15e;" u2="&#xef;" k="-23" />
<hkern u1="&#x15e;" u2="&#xee;" k="-47" />
<hkern u1="&#x15e;" u2="&#xec;" k="-57" />
<hkern u1="&#x15e;" u2="&#x7d;" k="35" />
<hkern u1="&#x15e;" u2="&#x7c;" k="37" />
<hkern u1="&#x15e;" u2="v" k="10" />
<hkern u1="&#x15e;" u2="f" k="12" />
<hkern u1="&#x15e;" u2="]" k="35" />
<hkern u1="&#x15e;" u2="\" k="25" />
<hkern u1="&#x15e;" u2="V" k="25" />
<hkern u1="&#x15e;" u2="&#x29;" k="47" />
<hkern u1="&#x15f;" u2="&#x2122;" k="45" />
<hkern u1="&#x15f;" u2="&#x7d;" k="51" />
<hkern u1="&#x15f;" u2="&#x7c;" k="57" />
<hkern u1="&#x15f;" u2="v" k="10" />
<hkern u1="&#x15f;" u2="]" k="47" />
<hkern u1="&#x15f;" u2="\" k="63" />
<hkern u1="&#x15f;" u2="V" k="45" />
<hkern u1="&#x15f;" u2="&#x3f;" k="25" />
<hkern u1="&#x15f;" u2="&#x2a;" k="16" />
<hkern u1="&#x15f;" u2="&#x29;" k="47" />
<hkern u1="&#x160;" u2="&#x135;" k="-25" />
<hkern u1="&#x160;" u2="&#x129;" k="-37" />
<hkern u1="&#x160;" u2="&#xef;" k="-23" />
<hkern u1="&#x160;" u2="&#xee;" k="-47" />
<hkern u1="&#x160;" u2="&#xec;" k="-57" />
<hkern u1="&#x160;" u2="&#x7d;" k="35" />
<hkern u1="&#x160;" u2="&#x7c;" k="37" />
<hkern u1="&#x160;" u2="v" k="10" />
<hkern u1="&#x160;" u2="f" k="12" />
<hkern u1="&#x160;" u2="]" k="35" />
<hkern u1="&#x160;" u2="\" k="25" />
<hkern u1="&#x160;" u2="V" k="25" />
<hkern u1="&#x160;" u2="&#x29;" k="47" />
<hkern u1="&#x161;" u2="&#x2122;" k="45" />
<hkern u1="&#x161;" u2="&#x7d;" k="51" />
<hkern u1="&#x161;" u2="&#x7c;" k="57" />
<hkern u1="&#x161;" u2="v" k="10" />
<hkern u1="&#x161;" u2="]" k="47" />
<hkern u1="&#x161;" u2="\" k="63" />
<hkern u1="&#x161;" u2="V" k="45" />
<hkern u1="&#x161;" u2="&#x3f;" k="25" />
<hkern u1="&#x161;" u2="&#x2a;" k="16" />
<hkern u1="&#x161;" u2="&#x29;" k="47" />
<hkern u1="&#x164;" u2="&#x15d;" k="68" />
<hkern u1="&#x164;" u2="&#x159;" k="66" />
<hkern u1="&#x164;" u2="&#x155;" k="14" />
<hkern u1="&#x164;" u2="&#x151;" k="74" />
<hkern u1="&#x164;" u2="&#x135;" k="-135" />
<hkern u1="&#x164;" u2="&#x131;" k="94" />
<hkern u1="&#x164;" u2="&#x12d;" k="-121" />
<hkern u1="&#x164;" u2="&#x12b;" k="-117" />
<hkern u1="&#x164;" u2="&#x129;" k="-158" />
<hkern u1="&#x164;" u2="&#x127;" k="-23" />
<hkern u1="&#x164;" u2="&#x11f;" k="104" />
<hkern u1="&#x164;" u2="&#x11d;" k="72" />
<hkern u1="&#x164;" u2="&#x109;" k="100" />
<hkern u1="&#x164;" u2="&#xf5;" k="113" />
<hkern u1="&#x164;" u2="&#xef;" k="-131" />
<hkern u1="&#x164;" u2="&#xee;" k="-158" />
<hkern u1="&#x164;" u2="&#xec;" k="-195" />
<hkern u1="&#x164;" u2="&#xea;" k="80" />
<hkern u1="&#x164;" u2="&#xe8;" k="63" />
<hkern u1="&#x164;" u2="x" k="74" />
<hkern u1="&#x164;" u2="v" k="80" />
<hkern u1="&#x164;" u2="f" k="18" />
<hkern u1="&#x164;" u2="&#x2f;" k="86" />
<hkern u1="&#x164;" u2="&#x2a;" k="-43" />
<hkern u1="&#x164;" u2="&#x26;" k="25" />
<hkern u1="&#x165;" u2="&#x2039;" k="86" />
<hkern u1="&#x165;" u2="&#x2026;" k="92" />
<hkern u1="&#x165;" u2="&#x201e;" k="92" />
<hkern u1="&#x165;" u2="&#x201d;" k="-39" />
<hkern u1="&#x165;" u2="&#x201c;" k="-37" />
<hkern u1="&#x165;" u2="&#x201a;" k="92" />
<hkern u1="&#x165;" u2="&#x2019;" k="-39" />
<hkern u1="&#x165;" u2="&#x2018;" k="-37" />
<hkern u1="&#x165;" u2="&#x2014;" k="68" />
<hkern u1="&#x165;" u2="&#x2013;" k="68" />
<hkern u1="&#x165;" u2="&#x21b;" k="-27" />
<hkern u1="&#x165;" u2="&#x1ff;" k="16" />
<hkern u1="&#x165;" u2="&#x1fd;" k="14" />
<hkern u1="&#x165;" u2="&#x1fb;" k="14" />
<hkern u1="&#x165;" u2="&#x177;" k="-41" />
<hkern u1="&#x165;" u2="&#x175;" k="-23" />
<hkern u1="&#x165;" u2="&#x167;" k="-27" />
<hkern u1="&#x165;" u2="&#x165;" k="-27" />
<hkern u1="&#x165;" u2="&#x153;" k="16" />
<hkern u1="&#x165;" u2="&#x151;" k="16" />
<hkern u1="&#x165;" u2="&#x14f;" k="16" />
<hkern u1="&#x165;" u2="&#x14d;" k="16" />
<hkern u1="&#x165;" u2="&#x142;" k="-18" />
<hkern u1="&#x165;" u2="&#x13e;" k="-18" />
<hkern u1="&#x165;" u2="&#x13c;" k="-18" />
<hkern u1="&#x165;" u2="&#x13a;" k="-18" />
<hkern u1="&#x165;" u2="&#x137;" k="-18" />
<hkern u1="&#x165;" u2="&#x135;" k="-18" />
<hkern u1="&#x165;" u2="&#x131;" k="-18" />
<hkern u1="&#x165;" u2="&#x12f;" k="-18" />
<hkern u1="&#x165;" u2="&#x12d;" k="-18" />
<hkern u1="&#x165;" u2="&#x12b;" k="-18" />
<hkern u1="&#x165;" u2="&#x129;" k="-18" />
<hkern u1="&#x165;" u2="&#x127;" k="-18" />
<hkern u1="&#x165;" u2="&#x125;" k="-18" />
<hkern u1="&#x165;" u2="&#x11b;" k="16" />
<hkern u1="&#x165;" u2="&#x119;" k="16" />
<hkern u1="&#x165;" u2="&#x117;" k="16" />
<hkern u1="&#x165;" u2="&#x115;" k="16" />
<hkern u1="&#x165;" u2="&#x113;" k="16" />
<hkern u1="&#x165;" u2="&#x111;" k="14" />
<hkern u1="&#x165;" u2="&#x10f;" k="14" />
<hkern u1="&#x165;" u2="&#x10d;" k="16" />
<hkern u1="&#x165;" u2="&#x10b;" k="16" />
<hkern u1="&#x165;" u2="&#x109;" k="16" />
<hkern u1="&#x165;" u2="&#x107;" k="16" />
<hkern u1="&#x165;" u2="&#x105;" k="14" />
<hkern u1="&#x165;" u2="&#x103;" k="14" />
<hkern u1="&#x165;" u2="&#x101;" k="14" />
<hkern u1="&#x165;" u2="&#xff;" k="-41" />
<hkern u1="&#x165;" u2="&#xfd;" k="-41" />
<hkern u1="&#x165;" u2="&#xf8;" k="16" />
<hkern u1="&#x165;" u2="&#xf6;" k="16" />
<hkern u1="&#x165;" u2="&#xf5;" k="16" />
<hkern u1="&#x165;" u2="&#xf4;" k="16" />
<hkern u1="&#x165;" u2="&#xf3;" k="16" />
<hkern u1="&#x165;" u2="&#xf2;" k="16" />
<hkern u1="&#x165;" u2="&#xef;" k="-18" />
<hkern u1="&#x165;" u2="&#xee;" k="-18" />
<hkern u1="&#x165;" u2="&#xed;" k="-18" />
<hkern u1="&#x165;" u2="&#xec;" k="-18" />
<hkern u1="&#x165;" u2="&#xeb;" k="16" />
<hkern u1="&#x165;" u2="&#xea;" k="16" />
<hkern u1="&#x165;" u2="&#xe9;" k="16" />
<hkern u1="&#x165;" u2="&#xe8;" k="16" />
<hkern u1="&#x165;" u2="&#xe7;" k="16" />
<hkern u1="&#x165;" u2="&#xe6;" k="14" />
<hkern u1="&#x165;" u2="&#xe5;" k="14" />
<hkern u1="&#x165;" u2="&#xe4;" k="14" />
<hkern u1="&#x165;" u2="&#xe3;" k="14" />
<hkern u1="&#x165;" u2="&#xe2;" k="14" />
<hkern u1="&#x165;" u2="&#xe1;" k="14" />
<hkern u1="&#x165;" u2="&#xe0;" k="14" />
<hkern u1="&#x165;" u2="&#xdf;" k="-18" />
<hkern u1="&#x165;" u2="&#xab;" k="86" />
<hkern u1="&#x165;" u2="&#x7d;" k="-29" />
<hkern u1="&#x165;" u2="&#x7c;" k="-41" />
<hkern u1="&#x165;" u2="y" k="-41" />
<hkern u1="&#x165;" u2="x" k="-45" />
<hkern u1="&#x165;" u2="w" k="-23" />
<hkern u1="&#x165;" u2="v" k="-41" />
<hkern u1="&#x165;" u2="t" k="-27" />
<hkern u1="&#x165;" u2="q" k="14" />
<hkern u1="&#x165;" u2="o" k="16" />
<hkern u1="&#x165;" u2="l" k="-18" />
<hkern u1="&#x165;" u2="k" k="-18" />
<hkern u1="&#x165;" u2="j" k="-18" />
<hkern u1="&#x165;" u2="i" k="-18" />
<hkern u1="&#x165;" u2="h" k="-18" />
<hkern u1="&#x165;" u2="e" k="16" />
<hkern u1="&#x165;" u2="d" k="14" />
<hkern u1="&#x165;" u2="c" k="16" />
<hkern u1="&#x165;" u2="b" k="-23" />
<hkern u1="&#x165;" u2="a" k="14" />
<hkern u1="&#x165;" u2="]" k="-29" />
<hkern u1="&#x165;" u2="&#x3f;" k="-18" />
<hkern u1="&#x165;" u2="&#x3b;" k="-12" />
<hkern u1="&#x165;" u2="&#x3a;" k="-12" />
<hkern u1="&#x165;" u2="&#x2f;" k="66" />
<hkern u1="&#x165;" u2="&#x2e;" k="92" />
<hkern u1="&#x165;" u2="&#x2d;" k="68" />
<hkern u1="&#x165;" u2="&#x2c;" k="92" />
<hkern u1="&#x165;" u2="&#x2a;" k="-68" />
<hkern u1="&#x165;" u2="&#x27;" k="-25" />
<hkern u1="&#x165;" u2="&#x22;" k="-25" />
<hkern u1="&#x165;" u2="&#x21;" k="-18" />
<hkern u1="&#x166;" u2="&#x2039;" k="74" />
<hkern u1="&#x166;" u2="&#x2014;" k="53" />
<hkern u1="&#x166;" u2="&#x2013;" k="53" />
<hkern u1="&#x166;" u2="&#x1ff;" k="113" />
<hkern u1="&#x166;" u2="&#x15d;" k="68" />
<hkern u1="&#x166;" u2="&#x159;" k="66" />
<hkern u1="&#x166;" u2="&#x155;" k="14" />
<hkern u1="&#x166;" u2="&#x153;" k="113" />
<hkern u1="&#x166;" u2="&#x151;" k="74" />
<hkern u1="&#x166;" u2="&#x14f;" k="113" />
<hkern u1="&#x166;" u2="&#x14d;" k="113" />
<hkern u1="&#x166;" u2="&#x141;" k="-16" />
<hkern u1="&#x166;" u2="&#x135;" k="-135" />
<hkern u1="&#x166;" u2="&#x131;" k="94" />
<hkern u1="&#x166;" u2="&#x12d;" k="-121" />
<hkern u1="&#x166;" u2="&#x12b;" k="-117" />
<hkern u1="&#x166;" u2="&#x129;" k="-158" />
<hkern u1="&#x166;" u2="&#x127;" k="-23" />
<hkern u1="&#x166;" u2="&#x11f;" k="104" />
<hkern u1="&#x166;" u2="&#x11d;" k="72" />
<hkern u1="&#x166;" u2="&#x11b;" k="113" />
<hkern u1="&#x166;" u2="&#x119;" k="113" />
<hkern u1="&#x166;" u2="&#x117;" k="113" />
<hkern u1="&#x166;" u2="&#x115;" k="113" />
<hkern u1="&#x166;" u2="&#x113;" k="113" />
<hkern u1="&#x166;" u2="&#x10d;" k="113" />
<hkern u1="&#x166;" u2="&#x10b;" k="113" />
<hkern u1="&#x166;" u2="&#x109;" k="100" />
<hkern u1="&#x166;" u2="&#x107;" k="113" />
<hkern u1="&#x166;" u2="&#xf8;" k="113" />
<hkern u1="&#x166;" u2="&#xf6;" k="113" />
<hkern u1="&#x166;" u2="&#xf5;" k="113" />
<hkern u1="&#x166;" u2="&#xf4;" k="113" />
<hkern u1="&#x166;" u2="&#xf3;" k="113" />
<hkern u1="&#x166;" u2="&#xf2;" k="113" />
<hkern u1="&#x166;" u2="&#xef;" k="-131" />
<hkern u1="&#x166;" u2="&#xee;" k="-158" />
<hkern u1="&#x166;" u2="&#xec;" k="-195" />
<hkern u1="&#x166;" u2="&#xeb;" k="113" />
<hkern u1="&#x166;" u2="&#xea;" k="80" />
<hkern u1="&#x166;" u2="&#xe9;" k="113" />
<hkern u1="&#x166;" u2="&#xe8;" k="63" />
<hkern u1="&#x166;" u2="&#xe7;" k="113" />
<hkern u1="&#x166;" u2="&#xab;" k="74" />
<hkern u1="&#x166;" u2="x" k="74" />
<hkern u1="&#x166;" u2="v" k="80" />
<hkern u1="&#x166;" u2="o" k="113" />
<hkern u1="&#x166;" u2="f" k="18" />
<hkern u1="&#x166;" u2="e" k="113" />
<hkern u1="&#x166;" u2="c" k="113" />
<hkern u1="&#x166;" u2="&#x2f;" k="86" />
<hkern u1="&#x166;" u2="&#x2d;" k="68" />
<hkern u1="&#x166;" u2="&#x2a;" k="-43" />
<hkern u1="&#x166;" u2="&#x26;" k="25" />
<hkern u1="&#x167;" u2="&#x2122;" k="23" />
<hkern u1="&#x167;" u2="&#x7d;" k="20" />
<hkern u1="&#x167;" u2="&#x7c;" k="37" />
<hkern u1="&#x167;" u2="\" k="35" />
<hkern u1="&#x167;" u2="V" k="12" />
<hkern u1="&#x167;" u2="&#x29;" k="23" />
<hkern u1="&#x168;" u2="&#x135;" k="-31" />
<hkern u1="&#x168;" u2="&#x12d;" k="-18" />
<hkern u1="&#x168;" u2="&#x12b;" k="-14" />
<hkern u1="&#x168;" u2="&#x129;" k="-55" />
<hkern u1="&#x168;" u2="&#xef;" k="-27" />
<hkern u1="&#x168;" u2="&#xee;" k="-53" />
<hkern u1="&#x168;" u2="&#xec;" k="-90" />
<hkern u1="&#x168;" u2="&#x7d;" k="25" />
<hkern u1="&#x168;" u2="&#x7c;" k="20" />
<hkern u1="&#x168;" u2="f" k="10" />
<hkern u1="&#x168;" u2="]" k="23" />
<hkern u1="&#x168;" u2="&#x2f;" k="31" />
<hkern u1="&#x168;" u2="&#x29;" k="41" />
<hkern u1="&#x169;" u2="&#x2122;" k="37" />
<hkern u1="&#x169;" u2="&#x7d;" k="45" />
<hkern u1="&#x169;" u2="&#x7c;" k="53" />
<hkern u1="&#x169;" u2="]" k="25" />
<hkern u1="&#x169;" u2="\" k="57" />
<hkern u1="&#x169;" u2="V" k="45" />
<hkern u1="&#x169;" u2="&#x3f;" k="25" />
<hkern u1="&#x169;" u2="&#x29;" k="47" />
<hkern u1="&#x16a;" u2="&#x135;" k="-31" />
<hkern u1="&#x16a;" u2="&#x12d;" k="-18" />
<hkern u1="&#x16a;" u2="&#x12b;" k="-14" />
<hkern u1="&#x16a;" u2="&#x129;" k="-55" />
<hkern u1="&#x16a;" u2="&#xef;" k="-27" />
<hkern u1="&#x16a;" u2="&#xee;" k="-53" />
<hkern u1="&#x16a;" u2="&#xec;" k="-90" />
<hkern u1="&#x16a;" u2="&#x7d;" k="25" />
<hkern u1="&#x16a;" u2="&#x7c;" k="20" />
<hkern u1="&#x16a;" u2="f" k="10" />
<hkern u1="&#x16a;" u2="]" k="23" />
<hkern u1="&#x16a;" u2="&#x2f;" k="31" />
<hkern u1="&#x16a;" u2="&#x29;" k="41" />
<hkern u1="&#x16b;" u2="&#x2122;" k="37" />
<hkern u1="&#x16b;" u2="&#x7d;" k="45" />
<hkern u1="&#x16b;" u2="&#x7c;" k="53" />
<hkern u1="&#x16b;" u2="]" k="25" />
<hkern u1="&#x16b;" u2="\" k="57" />
<hkern u1="&#x16b;" u2="V" k="45" />
<hkern u1="&#x16b;" u2="&#x3f;" k="25" />
<hkern u1="&#x16b;" u2="&#x29;" k="47" />
<hkern u1="&#x16c;" u2="&#x135;" k="-31" />
<hkern u1="&#x16c;" u2="&#x12d;" k="-18" />
<hkern u1="&#x16c;" u2="&#x12b;" k="-14" />
<hkern u1="&#x16c;" u2="&#x129;" k="-55" />
<hkern u1="&#x16c;" u2="&#xef;" k="-27" />
<hkern u1="&#x16c;" u2="&#xee;" k="-53" />
<hkern u1="&#x16c;" u2="&#xec;" k="-90" />
<hkern u1="&#x16c;" u2="&#x7d;" k="25" />
<hkern u1="&#x16c;" u2="&#x7c;" k="20" />
<hkern u1="&#x16c;" u2="f" k="10" />
<hkern u1="&#x16c;" u2="]" k="23" />
<hkern u1="&#x16c;" u2="&#x2f;" k="31" />
<hkern u1="&#x16c;" u2="&#x29;" k="41" />
<hkern u1="&#x16d;" u2="&#x2122;" k="37" />
<hkern u1="&#x16d;" u2="&#x7d;" k="45" />
<hkern u1="&#x16d;" u2="&#x7c;" k="53" />
<hkern u1="&#x16d;" u2="]" k="25" />
<hkern u1="&#x16d;" u2="\" k="57" />
<hkern u1="&#x16d;" u2="V" k="45" />
<hkern u1="&#x16d;" u2="&#x3f;" k="25" />
<hkern u1="&#x16d;" u2="&#x29;" k="47" />
<hkern u1="&#x16e;" u2="&#x135;" k="-31" />
<hkern u1="&#x16e;" u2="&#x12d;" k="-18" />
<hkern u1="&#x16e;" u2="&#x12b;" k="-14" />
<hkern u1="&#x16e;" u2="&#x129;" k="-55" />
<hkern u1="&#x16e;" u2="&#xef;" k="-27" />
<hkern u1="&#x16e;" u2="&#xee;" k="-53" />
<hkern u1="&#x16e;" u2="&#xec;" k="-90" />
<hkern u1="&#x16e;" u2="&#x7d;" k="25" />
<hkern u1="&#x16e;" u2="&#x7c;" k="20" />
<hkern u1="&#x16e;" u2="f" k="10" />
<hkern u1="&#x16e;" u2="]" k="23" />
<hkern u1="&#x16e;" u2="&#x2f;" k="31" />
<hkern u1="&#x16e;" u2="&#x29;" k="41" />
<hkern u1="&#x16f;" u2="&#x2122;" k="37" />
<hkern u1="&#x16f;" u2="&#x7d;" k="45" />
<hkern u1="&#x16f;" u2="&#x7c;" k="53" />
<hkern u1="&#x16f;" u2="]" k="25" />
<hkern u1="&#x16f;" u2="\" k="57" />
<hkern u1="&#x16f;" u2="V" k="45" />
<hkern u1="&#x16f;" u2="&#x3f;" k="25" />
<hkern u1="&#x16f;" u2="&#x29;" k="47" />
<hkern u1="&#x170;" u2="&#x135;" k="-31" />
<hkern u1="&#x170;" u2="&#x12d;" k="-18" />
<hkern u1="&#x170;" u2="&#x12b;" k="-14" />
<hkern u1="&#x170;" u2="&#x129;" k="-55" />
<hkern u1="&#x170;" u2="&#xef;" k="-27" />
<hkern u1="&#x170;" u2="&#xee;" k="-53" />
<hkern u1="&#x170;" u2="&#xec;" k="-90" />
<hkern u1="&#x170;" u2="&#x7d;" k="25" />
<hkern u1="&#x170;" u2="&#x7c;" k="20" />
<hkern u1="&#x170;" u2="f" k="10" />
<hkern u1="&#x170;" u2="]" k="23" />
<hkern u1="&#x170;" u2="&#x2f;" k="31" />
<hkern u1="&#x170;" u2="&#x29;" k="41" />
<hkern u1="&#x171;" u2="&#x2122;" k="37" />
<hkern u1="&#x171;" u2="&#x7d;" k="45" />
<hkern u1="&#x171;" u2="&#x7c;" k="53" />
<hkern u1="&#x171;" u2="]" k="25" />
<hkern u1="&#x171;" u2="\" k="57" />
<hkern u1="&#x171;" u2="V" k="45" />
<hkern u1="&#x171;" u2="&#x3f;" k="25" />
<hkern u1="&#x171;" u2="&#x29;" k="47" />
<hkern u1="&#x172;" u2="&#x135;" k="-31" />
<hkern u1="&#x172;" u2="&#x12d;" k="-18" />
<hkern u1="&#x172;" u2="&#x12b;" k="-14" />
<hkern u1="&#x172;" u2="&#x129;" k="-55" />
<hkern u1="&#x172;" u2="&#xef;" k="-27" />
<hkern u1="&#x172;" u2="&#xee;" k="-53" />
<hkern u1="&#x172;" u2="&#xec;" k="-90" />
<hkern u1="&#x172;" u2="&#x7d;" k="25" />
<hkern u1="&#x172;" u2="&#x7c;" k="20" />
<hkern u1="&#x172;" u2="f" k="10" />
<hkern u1="&#x172;" u2="]" k="23" />
<hkern u1="&#x172;" u2="&#x2f;" k="31" />
<hkern u1="&#x172;" u2="&#x29;" k="41" />
<hkern u1="&#x173;" u2="&#x2122;" k="37" />
<hkern u1="&#x173;" u2="&#x201e;" k="-23" />
<hkern u1="&#x173;" u2="&#x7d;" k="45" />
<hkern u1="&#x173;" u2="&#x7c;" k="53" />
<hkern u1="&#x173;" u2="j" k="-18" />
<hkern u1="&#x173;" u2="]" k="25" />
<hkern u1="&#x173;" u2="\" k="57" />
<hkern u1="&#x173;" u2="V" k="45" />
<hkern u1="&#x173;" u2="&#x3f;" k="25" />
<hkern u1="&#x173;" u2="&#x29;" k="47" />
<hkern u1="&#x174;" u2="&#x159;" k="18" />
<hkern u1="&#x174;" u2="&#x155;" k="18" />
<hkern u1="&#x174;" u2="&#x135;" k="-76" />
<hkern u1="&#x174;" u2="&#x131;" k="31" />
<hkern u1="&#x174;" u2="&#x12d;" k="-104" />
<hkern u1="&#x174;" u2="&#x12b;" k="-100" />
<hkern u1="&#x174;" u2="&#x129;" k="-141" />
<hkern u1="&#x174;" u2="&#xef;" k="-115" />
<hkern u1="&#x174;" u2="&#xee;" k="-96" />
<hkern u1="&#x174;" u2="&#xec;" k="-156" />
<hkern u1="&#x174;" u2="x" k="10" />
<hkern u1="&#x174;" u2="v" k="10" />
<hkern u1="&#x174;" u2="&#x40;" k="29" />
<hkern u1="&#x174;" u2="&#x2f;" k="74" />
<hkern u1="&#x174;" u2="&#x2a;" k="-37" />
<hkern u1="&#x174;" u2="&#x29;" k="25" />
<hkern u1="&#x174;" u2="&#x26;" k="35" />
<hkern u1="&#x175;" u2="&#x2122;" k="25" />
<hkern u1="&#x175;" u2="&#x7d;" k="51" />
<hkern u1="&#x175;" u2="&#x7c;" k="33" />
<hkern u1="&#x175;" u2="]" k="61" />
<hkern u1="&#x175;" u2="\" k="35" />
<hkern u1="&#x175;" u2="X" k="27" />
<hkern u1="&#x175;" u2="V" k="16" />
<hkern u1="&#x175;" u2="&#x2f;" k="23" />
<hkern u1="&#x175;" u2="&#x29;" k="53" />
<hkern u1="&#x176;" u2="&#x17a;" k="80" />
<hkern u1="&#x176;" u2="&#x177;" k="41" />
<hkern u1="&#x176;" u2="&#x171;" k="88" />
<hkern u1="&#x176;" u2="&#x16b;" k="88" />
<hkern u1="&#x176;" u2="&#x169;" k="80" />
<hkern u1="&#x176;" u2="&#x159;" k="47" />
<hkern u1="&#x176;" u2="&#x155;" k="-6" />
<hkern u1="&#x176;" u2="&#x151;" k="82" />
<hkern u1="&#x176;" u2="&#x14f;" k="88" />
<hkern u1="&#x176;" u2="&#x14d;" k="117" />
<hkern u1="&#x176;" u2="&#x135;" k="-59" />
<hkern u1="&#x176;" u2="&#x131;" k="115" />
<hkern u1="&#x176;" u2="&#x12d;" k="-141" />
<hkern u1="&#x176;" u2="&#x12b;" k="-135" />
<hkern u1="&#x176;" u2="&#x129;" k="-178" />
<hkern u1="&#x176;" u2="&#x127;" k="-18" />
<hkern u1="&#x176;" u2="&#x11f;" k="88" />
<hkern u1="&#x176;" u2="&#x113;" k="102" />
<hkern u1="&#x176;" u2="&#xff;" k="37" />
<hkern u1="&#x176;" u2="&#xfc;" k="82" />
<hkern u1="&#x176;" u2="&#xf5;" k="88" />
<hkern u1="&#x176;" u2="&#xf2;" k="111" />
<hkern u1="&#x176;" u2="&#xf1;" k="115" />
<hkern u1="&#x176;" u2="&#xef;" k="-150" />
<hkern u1="&#x176;" u2="&#xee;" k="-78" />
<hkern u1="&#x176;" u2="&#xed;" k="-14" />
<hkern u1="&#x176;" u2="&#xec;" k="-182" />
<hkern u1="&#x176;" u2="&#xeb;" k="100" />
<hkern u1="&#x176;" u2="&#xea;" k="121" />
<hkern u1="&#x176;" u2="&#xe8;" k="51" />
<hkern u1="&#x176;" u2="&#xae;" k="35" />
<hkern u1="&#x176;" u2="&#x7d;" k="-12" />
<hkern u1="&#x176;" u2="&#x7c;" k="-25" />
<hkern u1="&#x176;" u2="x" k="66" />
<hkern u1="&#x176;" u2="v" k="63" />
<hkern u1="&#x176;" u2="f" k="33" />
<hkern u1="&#x176;" u2="]" k="-12" />
<hkern u1="&#x176;" u2="&#x40;" k="55" />
<hkern u1="&#x176;" u2="&#x2f;" k="131" />
<hkern u1="&#x176;" u2="&#x2a;" k="-43" />
<hkern u1="&#x176;" u2="&#x26;" k="66" />
<hkern u1="&#x177;" u2="&#x2122;" k="23" />
<hkern u1="&#x177;" u2="&#x7d;" k="51" />
<hkern u1="&#x177;" u2="&#x7c;" k="37" />
<hkern u1="&#x177;" u2="]" k="63" />
<hkern u1="&#x177;" u2="\" k="43" />
<hkern u1="&#x177;" u2="X" k="29" />
<hkern u1="&#x177;" u2="V" k="14" />
<hkern u1="&#x177;" u2="&#x2f;" k="37" />
<hkern u1="&#x177;" u2="&#x29;" k="51" />
<hkern u1="&#x178;" u2="&#x17a;" k="80" />
<hkern u1="&#x178;" u2="&#x177;" k="41" />
<hkern u1="&#x178;" u2="&#x171;" k="88" />
<hkern u1="&#x178;" u2="&#x16b;" k="88" />
<hkern u1="&#x178;" u2="&#x169;" k="80" />
<hkern u1="&#x178;" u2="&#x159;" k="47" />
<hkern u1="&#x178;" u2="&#x155;" k="-6" />
<hkern u1="&#x178;" u2="&#x151;" k="82" />
<hkern u1="&#x178;" u2="&#x14f;" k="88" />
<hkern u1="&#x178;" u2="&#x14d;" k="117" />
<hkern u1="&#x178;" u2="&#x135;" k="-59" />
<hkern u1="&#x178;" u2="&#x131;" k="115" />
<hkern u1="&#x178;" u2="&#x12d;" k="-141" />
<hkern u1="&#x178;" u2="&#x12b;" k="-135" />
<hkern u1="&#x178;" u2="&#x129;" k="-178" />
<hkern u1="&#x178;" u2="&#x127;" k="-18" />
<hkern u1="&#x178;" u2="&#x11f;" k="88" />
<hkern u1="&#x178;" u2="&#x113;" k="102" />
<hkern u1="&#x178;" u2="&#xff;" k="37" />
<hkern u1="&#x178;" u2="&#xfc;" k="82" />
<hkern u1="&#x178;" u2="&#xf5;" k="88" />
<hkern u1="&#x178;" u2="&#xf2;" k="111" />
<hkern u1="&#x178;" u2="&#xf1;" k="115" />
<hkern u1="&#x178;" u2="&#xef;" k="-150" />
<hkern u1="&#x178;" u2="&#xee;" k="-78" />
<hkern u1="&#x178;" u2="&#xed;" k="-14" />
<hkern u1="&#x178;" u2="&#xec;" k="-182" />
<hkern u1="&#x178;" u2="&#xeb;" k="100" />
<hkern u1="&#x178;" u2="&#xea;" k="121" />
<hkern u1="&#x178;" u2="&#xe8;" k="51" />
<hkern u1="&#x178;" u2="&#xae;" k="35" />
<hkern u1="&#x178;" u2="&#x7d;" k="-12" />
<hkern u1="&#x178;" u2="&#x7c;" k="-25" />
<hkern u1="&#x178;" u2="x" k="66" />
<hkern u1="&#x178;" u2="v" k="63" />
<hkern u1="&#x178;" u2="f" k="33" />
<hkern u1="&#x178;" u2="]" k="-12" />
<hkern u1="&#x178;" u2="&#x40;" k="55" />
<hkern u1="&#x178;" u2="&#x2f;" k="131" />
<hkern u1="&#x178;" u2="&#x2a;" k="-43" />
<hkern u1="&#x178;" u2="&#x26;" k="66" />
<hkern u1="&#x179;" u2="&#x135;" k="-78" />
<hkern u1="&#x179;" u2="&#x12d;" k="-66" />
<hkern u1="&#x179;" u2="&#x12b;" k="-61" />
<hkern u1="&#x179;" u2="&#x129;" k="-102" />
<hkern u1="&#x179;" u2="&#xef;" k="-74" />
<hkern u1="&#x179;" u2="&#xee;" k="-100" />
<hkern u1="&#x179;" u2="&#xec;" k="-137" />
<hkern u1="&#x179;" u2="&#x29;" k="25" />
<hkern u1="&#x17a;" u2="&#x2122;" k="33" />
<hkern u1="&#x17a;" u2="&#x7d;" k="37" />
<hkern u1="&#x17a;" u2="&#x7c;" k="45" />
<hkern u1="&#x17a;" u2="\" k="49" />
<hkern u1="&#x17a;" u2="V" k="27" />
<hkern u1="&#x17a;" u2="&#x29;" k="39" />
<hkern u1="&#x17b;" u2="&#x135;" k="-78" />
<hkern u1="&#x17b;" u2="&#x12d;" k="-66" />
<hkern u1="&#x17b;" u2="&#x12b;" k="-61" />
<hkern u1="&#x17b;" u2="&#x129;" k="-102" />
<hkern u1="&#x17b;" u2="&#xef;" k="-74" />
<hkern u1="&#x17b;" u2="&#xee;" k="-100" />
<hkern u1="&#x17b;" u2="&#xec;" k="-137" />
<hkern u1="&#x17b;" u2="&#x29;" k="25" />
<hkern u1="&#x17c;" u2="&#x2122;" k="33" />
<hkern u1="&#x17c;" u2="&#x7d;" k="37" />
<hkern u1="&#x17c;" u2="&#x7c;" k="45" />
<hkern u1="&#x17c;" u2="\" k="49" />
<hkern u1="&#x17c;" u2="V" k="27" />
<hkern u1="&#x17c;" u2="&#x29;" k="39" />
<hkern u1="&#x17d;" u2="&#x135;" k="-78" />
<hkern u1="&#x17d;" u2="&#x12d;" k="-66" />
<hkern u1="&#x17d;" u2="&#x12b;" k="-61" />
<hkern u1="&#x17d;" u2="&#x129;" k="-102" />
<hkern u1="&#x17d;" u2="&#xef;" k="-74" />
<hkern u1="&#x17d;" u2="&#xee;" k="-100" />
<hkern u1="&#x17d;" u2="&#xec;" k="-137" />
<hkern u1="&#x17d;" u2="&#x29;" k="25" />
<hkern u1="&#x17e;" u2="&#x2122;" k="33" />
<hkern u1="&#x17e;" u2="&#x12b;" k="-27" />
<hkern u1="&#x17e;" u2="&#x7d;" k="37" />
<hkern u1="&#x17e;" u2="&#x7c;" k="45" />
<hkern u1="&#x17e;" u2="\" k="49" />
<hkern u1="&#x17e;" u2="V" k="27" />
<hkern u1="&#x17e;" u2="&#x29;" k="39" />
<hkern u1="&#x1fa;" u2="&#x2122;" k="68" />
<hkern u1="&#x1fa;" u2="&#xae;" k="57" />
<hkern u1="&#x1fa;" u2="&#x7d;" k="27" />
<hkern u1="&#x1fa;" u2="&#x7c;" k="94" />
<hkern u1="&#x1fa;" u2="v" k="31" />
<hkern u1="&#x1fa;" u2="f" k="14" />
<hkern u1="&#x1fa;" u2="\" k="84" />
<hkern u1="&#x1fa;" u2="V" k="61" />
<hkern u1="&#x1fa;" u2="&#x3f;" k="35" />
<hkern u1="&#x1fa;" u2="&#x2a;" k="61" />
<hkern u1="&#x1fa;" u2="&#x29;" k="33" />
<hkern u1="&#x1fb;" u2="&#x2122;" k="43" />
<hkern u1="&#x1fb;" u2="&#x7d;" k="41" />
<hkern u1="&#x1fb;" u2="&#x7c;" k="55" />
<hkern u1="&#x1fb;" u2="]" k="31" />
<hkern u1="&#x1fb;" u2="\" k="63" />
<hkern u1="&#x1fb;" u2="V" k="51" />
<hkern u1="&#x1fb;" u2="&#x3f;" k="29" />
<hkern u1="&#x1fb;" u2="&#x2a;" k="16" />
<hkern u1="&#x1fb;" u2="&#x29;" k="45" />
<hkern u1="&#x1fc;" u2="&#x135;" k="-82" />
<hkern u1="&#x1fc;" u2="&#x12d;" k="-70" />
<hkern u1="&#x1fc;" u2="&#x12b;" k="-63" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-106" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-78" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-104" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-141" />
<hkern u1="&#x1fc;" u2="v" k="14" />
<hkern u1="&#x1fc;" u2="&#x29;" k="20" />
<hkern u1="&#x1fd;" u2="&#x2122;" k="51" />
<hkern u1="&#x1fd;" u2="&#x142;" k="-27" />
<hkern u1="&#x1fd;" u2="&#x7d;" k="47" />
<hkern u1="&#x1fd;" u2="&#x7c;" k="59" />
<hkern u1="&#x1fd;" u2="v" k="10" />
<hkern u1="&#x1fd;" u2="]" k="39" />
<hkern u1="&#x1fd;" u2="\" k="76" />
<hkern u1="&#x1fd;" u2="V" k="49" />
<hkern u1="&#x1fd;" u2="&#x3f;" k="31" />
<hkern u1="&#x1fd;" u2="&#x2a;" k="29" />
<hkern u1="&#x1fd;" u2="&#x29;" k="47" />
<hkern u1="&#x1fe;" u2="&#x2122;" k="29" />
<hkern u1="&#x1fe;" u2="&#x7d;" k="61" />
<hkern u1="&#x1fe;" u2="&#x7c;" k="57" />
<hkern u1="&#x1fe;" u2="x" k="10" />
<hkern u1="&#x1fe;" u2="]" k="70" />
<hkern u1="&#x1fe;" u2="\" k="49" />
<hkern u1="&#x1fe;" u2="X" k="31" />
<hkern u1="&#x1fe;" u2="V" k="29" />
<hkern u1="&#x1fe;" u2="&#x2f;" k="33" />
<hkern u1="&#x1fe;" u2="&#x29;" k="66" />
<hkern u1="&#x1ff;" u2="&#x2122;" k="63" />
<hkern u1="&#x1ff;" u2="&#xae;" k="39" />
<hkern u1="&#x1ff;" u2="&#x7d;" k="55" />
<hkern u1="&#x1ff;" u2="&#x7c;" k="70" />
<hkern u1="&#x1ff;" u2="x" k="23" />
<hkern u1="&#x1ff;" u2="v" k="16" />
<hkern u1="&#x1ff;" u2="f" k="12" />
<hkern u1="&#x1ff;" u2="]" k="61" />
<hkern u1="&#x1ff;" u2="\" k="94" />
<hkern u1="&#x1ff;" u2="X" k="23" />
<hkern u1="&#x1ff;" u2="V" k="70" />
<hkern u1="&#x1ff;" u2="&#x3f;" k="39" />
<hkern u1="&#x1ff;" u2="&#x2a;" k="45" />
<hkern u1="&#x1ff;" u2="&#x29;" k="59" />
<hkern u1="&#x218;" u2="&#x135;" k="-25" />
<hkern u1="&#x218;" u2="&#x129;" k="-37" />
<hkern u1="&#x218;" u2="&#xef;" k="-23" />
<hkern u1="&#x218;" u2="&#xee;" k="-47" />
<hkern u1="&#x218;" u2="&#xec;" k="-57" />
<hkern u1="&#x218;" u2="&#x7d;" k="35" />
<hkern u1="&#x218;" u2="&#x7c;" k="37" />
<hkern u1="&#x218;" u2="v" k="10" />
<hkern u1="&#x218;" u2="f" k="12" />
<hkern u1="&#x218;" u2="]" k="35" />
<hkern u1="&#x218;" u2="\" k="25" />
<hkern u1="&#x218;" u2="V" k="25" />
<hkern u1="&#x218;" u2="&#x29;" k="47" />
<hkern u1="&#x219;" u2="&#x2122;" k="45" />
<hkern u1="&#x219;" u2="&#x7d;" k="51" />
<hkern u1="&#x219;" u2="&#x7c;" k="57" />
<hkern u1="&#x219;" u2="v" k="10" />
<hkern u1="&#x219;" u2="]" k="47" />
<hkern u1="&#x219;" u2="\" k="63" />
<hkern u1="&#x219;" u2="V" k="45" />
<hkern u1="&#x219;" u2="&#x3f;" k="25" />
<hkern u1="&#x219;" u2="&#x2a;" k="16" />
<hkern u1="&#x219;" u2="&#x29;" k="47" />
<hkern u1="&#x21a;" u2="&#x15d;" k="68" />
<hkern u1="&#x21a;" u2="&#x159;" k="66" />
<hkern u1="&#x21a;" u2="&#x155;" k="14" />
<hkern u1="&#x21a;" u2="&#x151;" k="74" />
<hkern u1="&#x21a;" u2="&#x135;" k="-135" />
<hkern u1="&#x21a;" u2="&#x131;" k="94" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-121" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-117" />
<hkern u1="&#x21a;" u2="&#x129;" k="-158" />
<hkern u1="&#x21a;" u2="&#x127;" k="-23" />
<hkern u1="&#x21a;" u2="&#x11f;" k="104" />
<hkern u1="&#x21a;" u2="&#x11d;" k="72" />
<hkern u1="&#x21a;" u2="&#x109;" k="100" />
<hkern u1="&#x21a;" u2="&#xf5;" k="113" />
<hkern u1="&#x21a;" u2="&#xef;" k="-131" />
<hkern u1="&#x21a;" u2="&#xee;" k="-158" />
<hkern u1="&#x21a;" u2="&#xec;" k="-195" />
<hkern u1="&#x21a;" u2="&#xea;" k="80" />
<hkern u1="&#x21a;" u2="&#xe8;" k="63" />
<hkern u1="&#x21a;" u2="x" k="74" />
<hkern u1="&#x21a;" u2="v" k="80" />
<hkern u1="&#x21a;" u2="f" k="18" />
<hkern u1="&#x21a;" u2="&#x2f;" k="86" />
<hkern u1="&#x21a;" u2="&#x2a;" k="-43" />
<hkern u1="&#x21a;" u2="&#x26;" k="25" />
<hkern u1="&#x21b;" u2="&#x2122;" k="23" />
<hkern u1="&#x21b;" u2="&#x7d;" k="20" />
<hkern u1="&#x21b;" u2="&#x7c;" k="37" />
<hkern u1="&#x21b;" u2="\" k="35" />
<hkern u1="&#x21b;" u2="V" k="12" />
<hkern u1="&#x21b;" u2="&#x29;" k="23" />
<hkern u1="&#x2013;" u2="&#x166;" k="72" />
<hkern u1="&#x2013;" u2="&#x142;" k="-16" />
<hkern u1="&#x2013;" u2="&#x141;" k="-27" />
<hkern u1="&#x2013;" u2="x" k="63" />
<hkern u1="&#x2013;" u2="v" k="29" />
<hkern u1="&#x2013;" u2="f" k="35" />
<hkern u1="&#x2013;" u2="X" k="51" />
<hkern u1="&#x2013;" u2="V" k="66" />
<hkern u1="&#x2014;" u2="&#x166;" k="72" />
<hkern u1="&#x2014;" u2="&#x142;" k="-16" />
<hkern u1="&#x2014;" u2="&#x141;" k="-27" />
<hkern u1="&#x2014;" u2="x" k="63" />
<hkern u1="&#x2014;" u2="v" k="29" />
<hkern u1="&#x2014;" u2="f" k="35" />
<hkern u1="&#x2014;" u2="X" k="51" />
<hkern u1="&#x2014;" u2="V" k="66" />
<hkern u1="&#x2018;" u2="&#x135;" k="-94" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-115" />
<hkern u1="&#x2018;" u2="&#x12b;" k="-115" />
<hkern u1="&#x2018;" u2="&#x129;" k="-154" />
<hkern u1="&#x2018;" u2="&#xef;" k="-127" />
<hkern u1="&#x2018;" u2="&#xee;" k="-117" />
<hkern u1="&#x2018;" u2="&#xec;" k="-186" />
<hkern u1="&#x2019;" u2="&#x155;" k="16" />
<hkern u1="&#x2019;" u2="&#x135;" k="-66" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-119" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-115" />
<hkern u1="&#x2019;" u2="&#x129;" k="-156" />
<hkern u1="&#x2019;" u2="&#xef;" k="-127" />
<hkern u1="&#x2019;" u2="&#xee;" k="-88" />
<hkern u1="&#x2019;" u2="&#xec;" k="-182" />
<hkern u1="&#x2019;" u2="&#xae;" k="29" />
<hkern u1="&#x2019;" u2="&#x7c;" k="-14" />
<hkern u1="&#x2019;" u2="x" k="16" />
<hkern u1="&#x2019;" u2="v" k="18" />
<hkern u1="&#x2019;" u2="f" k="27" />
<hkern u1="&#x2019;" u2="&#x40;" k="59" />
<hkern u1="&#x2019;" u2="&#x2f;" k="154" />
<hkern u1="&#x2019;" u2="&#x26;" k="78" />
<hkern u1="&#x201a;" u2="v" k="61" />
<hkern u1="&#x201a;" u2="f" k="33" />
<hkern u1="&#x201a;" u2="V" k="88" />
<hkern u1="&#x201c;" u2="&#x135;" k="-90" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-115" />
<hkern u1="&#x201c;" u2="&#x12b;" k="-115" />
<hkern u1="&#x201c;" u2="&#x129;" k="-154" />
<hkern u1="&#x201c;" u2="&#xef;" k="-127" />
<hkern u1="&#x201c;" u2="&#xee;" k="-113" />
<hkern u1="&#x201c;" u2="&#xec;" k="-182" />
<hkern u1="&#x201d;" u2="&#x155;" k="16" />
<hkern u1="&#x201d;" u2="&#x135;" k="-66" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-119" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-115" />
<hkern u1="&#x201d;" u2="&#x129;" k="-156" />
<hkern u1="&#x201d;" u2="&#xef;" k="-127" />
<hkern u1="&#x201d;" u2="&#xee;" k="-88" />
<hkern u1="&#x201d;" u2="&#xec;" k="-182" />
<hkern u1="&#x201d;" u2="&#xae;" k="29" />
<hkern u1="&#x201d;" u2="&#x7c;" k="-14" />
<hkern u1="&#x201d;" u2="x" k="16" />
<hkern u1="&#x201d;" u2="v" k="18" />
<hkern u1="&#x201d;" u2="f" k="27" />
<hkern u1="&#x201d;" u2="&#x40;" k="59" />
<hkern u1="&#x201d;" u2="&#x2f;" k="154" />
<hkern u1="&#x201d;" u2="&#x26;" k="78" />
<hkern u1="&#x201e;" u2="&#x135;" k="-27" />
<hkern u1="&#x201e;" u2="v" k="61" />
<hkern u1="&#x201e;" u2="j" k="-27" />
<hkern u1="&#x201e;" u2="f" k="33" />
<hkern u1="&#x201e;" u2="V" k="88" />
<hkern u1="&#x2039;" u2="V" k="43" />
<hkern u1="&#x203a;" u2="&#x166;" k="70" />
<hkern u1="&#x203a;" u2="&#x142;" k="-14" />
<hkern u1="&#x203a;" u2="&#x141;" k="-29" />
<hkern u1="&#x203a;" u2="x" k="70" />
<hkern u1="&#x203a;" u2="v" k="37" />
<hkern u1="&#x203a;" u2="f" k="39" />
<hkern u1="&#x203a;" u2="X" k="37" />
<hkern u1="&#x203a;" u2="V" k="72" />
<hkern u1="&#x2122;" u2="&#x1fc;" k="39" />
<hkern u1="&#x2122;" u2="&#x1fa;" k="39" />
<hkern u1="&#x2122;" u2="&#x17d;" k="18" />
<hkern u1="&#x2122;" u2="&#x17b;" k="18" />
<hkern u1="&#x2122;" u2="&#x179;" k="18" />
<hkern u1="&#x2122;" u2="&#x178;" k="49" />
<hkern u1="&#x2122;" u2="&#x176;" k="49" />
<hkern u1="&#x2122;" u2="&#x135;" k="-18" />
<hkern u1="&#x2122;" u2="&#x134;" k="16" />
<hkern u1="&#x2122;" u2="&#x104;" k="39" />
<hkern u1="&#x2122;" u2="&#x102;" k="39" />
<hkern u1="&#x2122;" u2="&#x100;" k="39" />
<hkern u1="&#x2122;" u2="&#xee;" k="-41" />
<hkern u1="&#x2122;" u2="&#xec;" k="-39" />
<hkern u1="&#x2122;" u2="&#xdd;" k="49" />
<hkern u1="&#x2122;" u2="&#xc6;" k="39" />
<hkern u1="&#x2122;" u2="&#xc5;" k="39" />
<hkern u1="&#x2122;" u2="&#xc4;" k="39" />
<hkern u1="&#x2122;" u2="&#xc3;" k="39" />
<hkern u1="&#x2122;" u2="&#xc2;" k="39" />
<hkern u1="&#x2122;" u2="&#xc1;" k="39" />
<hkern u1="&#x2122;" u2="&#xc0;" k="39" />
<hkern u1="&#x2122;" u2="Z" k="18" />
<hkern u1="&#x2122;" u2="Y" k="49" />
<hkern u1="&#x2122;" u2="X" k="31" />
<hkern u1="&#x2122;" u2="J" k="16" />
<hkern u1="&#x2122;" u2="A" k="39" />
<hkern u1="&#x2122;" u2="&#x27;" k="18" />
<hkern u1="&#x2122;" u2="&#x22;" k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="20" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="25" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="w,wcircumflex" 	k="14" />
<hkern g1="D,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="25" />
<hkern g1="D,Dcaron,Dcroat" 	g2="W,Wcircumflex" 	k="23" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="76" />
<hkern g1="D,Dcaron,Dcroat" 	g2="quotedbl,quotesingle" 	k="20" />
<hkern g1="D,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="31" />
<hkern g1="D,Dcaron,Dcroat" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="D,Dcaron,Dcroat" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="37" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="47" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex" 	k="29" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="W,Wcircumflex" 	k="14" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="quoteleft,quotedblleft" 	k="47" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="guillemotleft,guilsinglleft" 	k="23" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="J,Jcircumflex" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="J,Jcircumflex" 	g2="hyphen,endash,emdash" 	k="18" />
<hkern g1="J,Jcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="J,Jcircumflex" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="J,Jcircumflex" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="18" />
<hkern g1="K,Kcommaaccent" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="K,Kcommaaccent" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="43" />
<hkern g1="K,Kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="37" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="59" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="K,Kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="45" />
<hkern g1="K,Kcommaaccent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="K,Kcommaaccent" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="K,Kcommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="14" />
<hkern g1="K,Kcommaaccent" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="37" />
<hkern g1="K,Kcommaaccent" 	g2="w,wcircumflex" 	k="31" />
<hkern g1="K,Kcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="25" />
<hkern g1="K,Kcommaaccent" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="12" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="25" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="115" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="W,Wcircumflex" 	k="104" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="162" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="10" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="51" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="49" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteleft,quotedblleft" 	k="131" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteright,quotedblright" 	k="129" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quotedbl,quotesingle" 	k="127" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="23" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="w,wcircumflex" 	k="31" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="70" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="25" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="74" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="29" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="16" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="W,Wcircumflex" 	k="18" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="61" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="hyphen,endash,emdash" 	k="27" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="20" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="W,Wcircumflex" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="39" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="12" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="quoteleft,quotedblleft" 	k="55" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="111" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="123" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="96" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="92" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="113" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="98" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex" 	k="84" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex" 	k="80" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="63" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="92" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="94" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="74" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="84" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="129" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="106" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="quoteleft,quotedblleft" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="29" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="10" />
<hkern g1="W,Wcircumflex" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="27" />
<hkern g1="W,Wcircumflex" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="16" />
<hkern g1="W,Wcircumflex" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="45" />
<hkern g1="W,Wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="W,Wcircumflex" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="W,Wcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="45" />
<hkern g1="W,Wcircumflex" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="W,Wcircumflex" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="W,Wcircumflex" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="25" />
<hkern g1="W,Wcircumflex" 	g2="w,wcircumflex" 	k="16" />
<hkern g1="W,Wcircumflex" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="W,Wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="53" />
<hkern g1="W,Wcircumflex" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="W,Wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="86" />
<hkern g1="W,Wcircumflex" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="colon,semicolon" 	k="45" />
<hkern g1="W,Wcircumflex" 	g2="guillemotright,guilsinglright" 	k="53" />
<hkern g1="W,Wcircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="z,zacute,zdotaccent,zcaron" 	k="23" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="53" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="35" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="131" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="113" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="135" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="hyphen,endash,emdash" 	k="127" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="135" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="55" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="t,tcaron,tbar,uni021B" 	k="27" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="104" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="w,wcircumflex" 	k="80" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="y,yacute,ydieresis,ycircumflex" 	k="63" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="100" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="J,Jcircumflex" 	k="29" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="133" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="115" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="colon,semicolon" 	k="94" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="102" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="123" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="94" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="106" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="W,Wcircumflex" 	k="41" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="125" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="b,p" 	g2="T,Tcaron,Tbar,uni021A" 	k="111" />
<hkern g1="b,p" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="b,p" 	g2="W,Wcircumflex" 	k="53" />
<hkern g1="b,p" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="139" />
<hkern g1="b,p" 	g2="quoteleft,quotedblleft" 	k="29" />
<hkern g1="b,p" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="b,p" 	g2="quotedbl,quotesingle" 	k="39" />
<hkern g1="b,p" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="b,p" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="b,p" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="b,p" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="b,p" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="b,p" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="147" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex" 	k="20" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="96" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="hyphen,endash,emdash" 	k="57" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="86" />
<hkern g1="colon,semicolon" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="16" />
<hkern g1="colon,semicolon" 	g2="W,Wcircumflex" 	k="39" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="90" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="135" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex" 	k="39" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="156" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="125" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="W,Wcircumflex" 	k="10" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="74" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="88" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="43" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="94" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex" 	k="63" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="129" />
<hkern g1="guillemotright,guilsinglright" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="20" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="78" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="90" />
<hkern g1="guillemotright,guilsinglright" 	g2="t,tcaron,tbar,uni021B" 	k="37" />
<hkern g1="guillemotright,guilsinglright" 	g2="w,wcircumflex" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex" 	k="39" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="35" />
<hkern g1="guillemotright,guilsinglright" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="57" />
<hkern g1="guillemotright,guilsinglright" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="23" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="45" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="92" />
<hkern g1="hyphen,endash,emdash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex" 	k="57" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="125" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="90" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="102" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="33" />
<hkern g1="hyphen,endash,emdash" 	g2="w,wcircumflex" 	k="23" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="31" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="33" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="43" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="59" />
<hkern g1="hyphen,endash,emdash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="20" />
<hkern g1="k,kcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="115" />
<hkern g1="k,kcommaaccent" 	g2="W,Wcircumflex" 	k="12" />
<hkern g1="k,kcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="68" />
<hkern g1="k,kcommaaccent" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="16" />
<hkern g1="k,kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash" 	k="59" />
<hkern g1="k,kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="dcaron,lcaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="12" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="84" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="dcaron,lcaron" 	g2="quoteleft,quotedblleft" 	k="-37" />
<hkern g1="dcaron,lcaron" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-25" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex" 	k="-23" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="-41" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="100" />
<hkern g1="dcaron,lcaron" 	g2="colon,semicolon" 	k="-12" />
<hkern g1="dcaron,lcaron" 	g2="b" 	k="-23" />
<hkern g1="dcaron,lcaron" 	g2="h,k,germandbls,hcircumflex,hbar,kcommaaccent" 	k="-16" />
<hkern g1="dcaron,lcaron" 	g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	k="-16" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,lcommaaccent,lcaron,lslash" 	k="-16" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="111" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="20" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="W,Wcircumflex" 	k="51" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="143" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteright,quotedblright" 	k="27" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="113" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="145" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="45" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T,Tcaron,Tbar,uni021A" 	k="90" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W,Wcircumflex" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="127" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="53" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="213" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="219" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="229" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t,tcaron,tbar,uni021B" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w,wcircumflex" 	k="47" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis,ycircumflex" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="-10" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="-25" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="27" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="98" />
<hkern g1="quoteleft,quotedblleft" 	g2="J,Jcircumflex" 	k="41" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="162" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="51" />
<hkern g1="quoteright,quotedblright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="23" />
<hkern g1="quoteright,quotedblright" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="-29" />
<hkern g1="quoteright,quotedblright" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="55" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="127" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="106" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="55" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="t,tcaron,tbar,uni021B" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="w,wcircumflex" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis,ycircumflex" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="109" />
<hkern g1="quoteright,quotedblright" 	g2="J,Jcircumflex" 	k="43" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="184" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="47" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="z,zacute,zdotaccent,zcaron" 	k="33" />
<hkern g1="quoteright,quotedblright" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="quotedbl,quotesingle" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="-14" />
<hkern g1="quotedbl,quotesingle" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="94" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="90" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="39" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="152" />
<hkern g1="quotedbl,quotesingle" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="18" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="86" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="43" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="39" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="25" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="131" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="115" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="hyphen,endash,emdash" 	k="16" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="80" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="61" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="63" />
<hkern g1="t,tbar,uni021B" 	g2="hyphen,endash,emdash" 	k="53" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="98" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="119" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="w,wcircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="109" />
<hkern g1="w,wcircumflex" 	g2="W,Wcircumflex" 	k="12" />
<hkern g1="w,wcircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="72" />
<hkern g1="w,wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="w,wcircumflex" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="w,wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="16" />
<hkern g1="w,wcircumflex" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="w,wcircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="w,wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="45" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="111" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="W,Wcircumflex" 	k="10" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="63" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="23" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="12" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="hyphen,endash,emdash" 	k="39" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="20" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="guillemotright,guilsinglright" 	k="27" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="16" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="129" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex" 	k="23" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="92" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="51" />
</font>
</defs></svg> 