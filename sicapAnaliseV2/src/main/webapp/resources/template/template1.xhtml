<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" 
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html 
	xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:p="http://primefaces.org/ui"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions">

<h:head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="viewport"
		content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
	<meta name="apple-mobile-web-app-capable" content="yes" />

	<h:outputScript library="sentinel-layout" name="js/layout.js" />
	<h:outputScript name="locale-primefaces.js" library="js" />
	<h:outputScript name="css_browser_selector.js" library="js" />
	<h:outputScript name="countdown.js" library="js" />
	<h:outputScript name="timer.js" library="js" />

	<h:outputStylesheet library="primefaces-sentinel" name="theme.css"/>
	<h:outputStylesheet library="sentinel-layout" name="css/font-icon-layout.css" />			
	<h:outputStylesheet library="sentinel-layout" name="css/sentinel-layout.css" />
	<h:outputStylesheet library="sentinel-layout" name="css/core-layout.css" />

	<h:outputStylesheet library="css" name="index.css" />

	<title>.:: SICAP ::.</title>

	<link rel="icon" 	href="#{request.contextPath}/resources/imagens/logo_sicap.png" />

	<script type="text/javascript">
	
		function handleDialogRequest(xhr, status, args, dialog) {  
	        if( args.notValid || args.validationFailed )  
	           return;
	        dialog.hide();  
		}  

		function handleDialogOpenRequest(xhr, status, args, dialog) {  
	        if( args.notValid || args.validationFailed )  
	           return;
	        dialog.show();  
		}  

		$(document).ready(function(){
			ajustaHeights();
			$('#sm-topmenu li .ui-commandlink').removeClass('ui-commandlink');

			if ($(document).width() >= 641) {
				$('#layout-menubar').addClass('slimmenu');
			}
			if ($(document).width() >= 1200) {
				$('#layout-menubar').removeClass('slimmenu');
			}
			
			$(window).on('resize', function(){
				ajustaHeights();
				
				if ($(document).width() >= 641) {
					$('#layout-menubar').addClass('slimmenu');
				}
				if ($(document).width() >= 1200) {
					$('#layout-menubar').removeClass('slimmenu');
				}
				
			}).resize();
		});
		
		$(document).ajaxSuccess(function() {
			ajustaHeights();
		});

		ajustaHeights = function () {
			$('#layout-menubar').css('min-height', 100);
			$('#layout-menubar').css('height', 100);
			$('#corpo').css('min-height', 100);
			$('#corpo').css('min-height', $(document).height() - $('#layout-header').height() - $('#footer').height() - 10);
			$('#layout-menubar').css('height', $('#corpo').height() + $('#footer').height() + 5);
		};
		
	</script>

	<style type="text/css">
		#overlay {
			visibility: hidden;
			position: fixed;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			text-align: center;
			z-index: 200;
			background-color: black;
			opacity: 0.9;
		}
		
		#overlay div {
			width: 300px;
			margin: 100px auto;
			background-color: #fff;
			border: 1px solid #000;
			padding: 15px;
			text-align: center;
		}
	</style>

	<ui:insert name="head"/>

</h:head>

<h:body>

	<div id="layout-header" class="Unselectable fontRegular">
		<h:outputLink id="tceLink" value="http://www.tce.ac.gov.br" title="Tribunal de Contas do Estado do Acre">
			<h:graphicImage id="iconTce" library="imagens" name="icon_tce.png"/>
		</h:outputLink>

		<p:commandLink id="sicapLink" title="Página inicial">
			<h:graphicImage id="iconSistema" library="imagens" name="logo_sicap.png" />
			<h:outputText id="lblHeader" value="SICAP" />
		</p:commandLink>

		<ul id="sm-topmenu" class="layout-header-widgets white Fs14">
			<li class="Fleft BordRadHalf TexAlCenter" tabindex="0">
				<i class="icon-user-male Fs15"></i>
				<ul class="layout-header-widgets-submenu bigger BordRad5 shadows white Animated05">
					<li class="Animated05">
						<a class="white Unselectable">
							<i class="icon-user-male Fs26 OvHidden"></i>
							<span>
								<h:outputText value="jesse" styleClass="Fs12" />
								<br />
								<h:outputText value="cpd" styleClass="Fs10" />
							</span>
						</a>
					</li>
				</ul>
			</li>
			<li class="Fleft BordRadHalf TexAlCenter" tabindex="0">
					<p:commandLink id="logout" title="Sair do Sistema" styleClass="white Unselectable">
						<i class="icon-power Fs18"></i>
					</p:commandLink>
			</li>
		</ul>
		<!-- NEVER REMOVE FOLLOWING 'UL' TAG ! BECAUSE THIS CONTAINS RESPONSIVE MODE HEADER MENU OPEN-CLOSE BUTTON -->
		<ul id="sm-mobiletopmenu" class="layout-header-widgets white Fs14">
			<li 	class="Fleft BordRadHalf TexAlCenter Animated05 DisplayOnResponsive">
				<i class="icon-th-1"></i>
			</li>
		</ul>
		<!-- ****** -->
		<ui:insert name="header-widgets" />
	</div>

	<div id="layout-menubar" class="Unselectable fontRegular">

		<div class="layout-menubarinner-box" id="buttonArea">
			<a href="#" id="layout-menubar-resize" class="BordRad3" title="Menu Resize">
				<i class="icon-th-list-2"></i>
			</a>
			<a href="#" id="layout-menubar-resize2" class="BordRad3" title="Open Menu">
				<i class="icon-menu"></i>
			</a>
		</div>

		<h:form id="formListaTpsDocs">
			<ul id="areaNovoDocumento" class="layout-menubar-container" style="margin-top: 30px;">
				<li>
					<a href="#" onclick="Sentinel.toggleSubMenu(this);return false;" style="width: 225px !important;">
						<i class="icon-home yellow i"></i> Página Inicial
					</a>
				</li>
			</ul>
		</h:form>

	</div>

	<div id="layout-portlets-cover" class="fontRegular">
		<div id="corpo" class="Container100 BorBotLeaden ui-fluid">

			<div id="timer">
				<table id="tempoSessao" border="0px" width="auto" style="min-width: 200px; padding-top: 5px">
					<tr>
						<td>
							<h:outputText value="Sua sessão expira em: " styleClass="statusTop" />
							<span id="session-countdown"	class="statusTopTextLabel"></span>
						</td>
					</tr>
				</table>
			</div>

			<div id="mainContent">
				<ui:insert name="content" />
				<br />
			</div>
			
		</div>

		<div id="footer" class="Container100">
			<div class="textoRodape">
				<h:outputText value="Tribunal de Contas do Estado do Acre" />
				<br />
				<h:outputText value="Av. Ceará, 2994, 7° BEC - Rio Branco-Acre - CEP 69.918-111" />
				<br />
				<h:outputText value="Telefones: (68) 3025-2009 - 3025-2069 - Fax: (68) 3025-2041 " />
				<div style="float: right; opacity: 0.2; color: rgb(51, 102, 153);">
					<h:outputText value="v 1.0" />
				</div>
			</div>
		</div>

	</div>
</h:body>
</html>