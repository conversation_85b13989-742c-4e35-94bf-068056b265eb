<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.6.final using JasperReports Library version 6.20.6-5c96b6aa8a39ac1dc6b6bea4b81168e16dd39231  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="certidaoEntregaRemessa" pageWidth="595" pageHeight="842" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isFloatColumnFooter="true" uuid="1e3c0046-3cde-4689-9ec6-aa60bac24108">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="SQL SERVER - SICAP"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="510"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="476"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<import value="br.gov.ac.tce.sicapweb.modelo.remessa.TempestividadeRemessa"/>
	<parameter name="usuario" class="java.lang.String"/>
	<parameter name="exercicio" class="java.lang.Integer"/>
	<parameter name="REPORT_IMAGE_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["/home/<USER>/Aplicacoes/eclipse/workspace-atos-pessoal/sicapAnaliseV2/src/main/webapp/relatorios/"]]></defaultValueExpression>
	</parameter>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<parameterDescription><![CDATA[]]></parameterDescription>
		<defaultValueExpression><![CDATA["C:\\Users\\<USER>\\JaspersoftWorkspace\\sicapAnalise\\"]]></defaultValueExpression>
	</parameter>
	<parameter name="nomeEntidade" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["Tribunal de Contas do Estado do Acre"]]></defaultValueExpression>
	</parameter>
	<parameter name="remessas" class="br.gov.ac.tce.sicapweb.modelo.remessa.TempestividadeRemessa"/>
	<parameter name="idEntidadeCjur" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[SELECT a.cpfMask, a.nome, a.descricao 
FROM auditoria.vw_legendaAssinaturas a 
WHERE a.idEntidadeCjur = $P{idEntidadeCjur} AND a.ano = $P{exercicio} 
GROUP BY a.cpfMask, a.nome, a.descricao
ORDER BY Max(a.idRemessa)]]>
	</queryString>
	<field name="cpfMask" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="cpfMask"/>
		<property name="com.jaspersoft.studio.field.label" value="cpfMask"/>
	</field>
	<field name="nome" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="nome"/>
		<property name="com.jaspersoft.studio.field.label" value="nome"/>
	</field>
	<field name="descricao" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.name" value="descricao"/>
		<property name="com.jaspersoft.studio.field.label" value="descricao"/>
	</field>
	<pageHeader>
		<band height="540" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="0" y="0" width="75" height="75" uuid="19d8c8a6-5dab-4b4f-b91e-6171608defe8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<imageExpression><![CDATA[$P{REPORT_IMAGE_DIR}+"logotce.png"]]></imageExpression>
			</image>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="480" y="0" width="75" height="75" uuid="3610c532-d117-4353-a39f-4b17cb6df684"/>
				<imageExpression><![CDATA[$P{REPORT_IMAGE_DIR}+"brasao.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="0" y="2" width="555" height="38" uuid="8259e93a-76ce-4765-9138-dd3c1cb6c3c2">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[Tribunal de Contas do Estado do Acre]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="42" width="555" height="38" uuid="8f4f5dde-2c07-4cb8-a86c-41534e5e1c8b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13" isBold="true"/>
				</textElement>
				<text><![CDATA[SICAP - Sistema de Controle de Atos de Pessoal]]></text>
			</staticText>
			<rectangle radius="0">
				<reportElement x="0" y="78" width="555" height="1" uuid="fd2140f1-632e-403b-8721-bcb0b235688d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="82" width="555" height="13" uuid="48f11dfd-0aa6-46d3-9f1c-dee3fbcefb47">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="8" isItalic="true"/>
				</textElement>
				<text><![CDATA[Missão: Exercer o controle externo, orientando e fiscalizando a gestão pública, e incentivar a sociedade ao exercício do controle social.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="110" width="555" height="15" backcolor="#C0C0C0" uuid="15902bb4-e40f-454e-b890-340daf3d43a7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[CERTIDÃO DE ENTREGA DE REMESSAS]]></text>
			</staticText>
			<textField>
				<reportElement x="0" y="139" width="555" height="71" uuid="15e0ea1d-a009-4ebd-b6b3-12822a6dbced"/>
				<textElement textAlignment="Justified" markup="html">
					<font fontName="Arial" size="11"/>
					<paragraph firstLineIndent="30"/>
				</textElement>
				<textFieldExpression><![CDATA["A consulta realizada no SICAP, Sistema de Controle de Atos de Pessoal do Tribunal de Contas do Estado do Acre, demonstrou que até a presente data, o (a) <b>" + $P{nomeEntidade} +"</b>, relativamente ao exercício de " + $P{exercicio} + ", enviou as seguintes remessas de dados, em cumprimento à Resolução TCE/AC nº 102 de 28 de abril de 2016."]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="1" y="222" width="72" height="20" backcolor="#C0C0C0" uuid="21eaf338-56c1-4a12-93ac-79f0620b6580"/>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Competência]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="242" width="72" height="20" uuid="fabc143c-c587-4770-a5e1-4a4494489ca9">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Janeiro]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="262" width="72" height="20" uuid="77dfc41c-6480-463b-95b2-70bf607ef12e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Fevereiro]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="302" width="72" height="20" uuid="cae25c9a-8027-48aa-9e00-7af31e59ef6e">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Abril]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="322" width="72" height="20" uuid="b986269a-d4d3-467f-83c5-0ddbef07fb07">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Maio]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="282" width="72" height="20" uuid="36b54b39-de23-4be2-a1c2-9fc37445113c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Março]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="342" width="72" height="20" uuid="8ee78c94-ee6c-4a38-8bbf-4272e2d8dd2c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Junho]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="362" width="72" height="20" uuid="0dce302a-0bf2-4c09-85ae-492d5de17fbf">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Julho]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="382" width="72" height="20" uuid="ceeaa83d-fe1a-4549-be32-a6a52ee49c08">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Agosto]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="402" width="72" height="20" uuid="f7acefa6-a2ea-4574-9daa-a2e62f0deb37">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Setembro]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="422" width="72" height="20" uuid="8092b334-59ef-4b38-9499-0f5a30c349e8">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Outubro]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="442" width="72" height="20" uuid="3dc618ff-3f12-48c2-9db8-9179f95f714b">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Novembro]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="482" width="72" height="20" uuid="2a621345-78d4-4e2d-89f5-defd7e78f26c">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Décimo]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="462" width="72" height="20" uuid="90cf68df-924e-4af2-90bb-4b601ab3d8a8">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[Dezembro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="73" y="222" width="105" height="20" backcolor="#C0C0C0" uuid="dcf2c46d-20c3-404b-9967-350adceb6758">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Envio]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="178" y="222" width="105" height="20" backcolor="#C0C0C0" uuid="e6cb2328-aea6-4b96-9959-86c8e2fdfe67">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Confirmação]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="283" y="222" width="138" height="20" backcolor="#C0C0C0" uuid="70156689-b937-410a-b1fa-3141a4ac46de">
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Protocolo]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="421" y="222" width="75" height="20" backcolor="#C0C0C0" uuid="cf3b46b8-472d-4ad4-8008-28fdf674d288">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Status]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="496" y="222" width="58" height="20" backcolor="#C0C0C0" uuid="2c6bc0ee-cab7-452b-bfc9-dde426bf9ea4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Assinatura]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="242" width="105" height="20" uuid="4b03bc20-e78f-499c-9c60-1eade40a7722">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioJaneiro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="262" width="105" height="20" uuid="68e7af6b-788d-4d1d-8d09-af6ba3afe2b4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioFevereiro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="282" width="105" height="20" uuid="f5754543-776d-4404-a830-cdbefb31c1ac">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioMarco()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="302" width="105" height="20" uuid="c164c376-7cd1-481d-b3b7-7b562964a593">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioAbril()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="73" y="322" width="105" height="20" uuid="8161ff54-9803-40c6-9908-502f3c19b256">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioMaio()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="342" width="105" height="20" uuid="7ac2f617-0a2a-4815-a580-f669bfcd7d71">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioJunho()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="362" width="105" height="20" uuid="4fd8114b-d96a-45ab-a6d3-c582d08fab99">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioJulho()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="382" width="105" height="20" uuid="6b6e9e4e-e471-4013-aab0-d297bd83c2f4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioAgosto()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="402" width="105" height="20" uuid="c8b39384-dcfa-42cc-8e14-4b97018d49b0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioSetembro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="422" width="105" height="20" uuid="5432034d-44e3-4ed2-a94b-28a3887914bf">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioOutubro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="442" width="105" height="20" uuid="afab0917-94da-4531-a581-e7ae116d303c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioNovembro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="462" width="105" height="20" uuid="97d2e3b4-8672-4b2c-8dc9-dd8074629bff">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioDezembro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="73" y="482" width="105" height="20" uuid="6dd0e717-35e1-4fee-8c2f-bdc412eeb633">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataEnvioDecimo()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="178" y="242" width="105" height="20" uuid="25b4fb54-34bd-4afc-aa95-9523f7aba0b7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoJaneiro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="178" y="262" width="105" height="20" uuid="fbe71cf5-7f34-469f-be64-ada545cf3d36">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoFevereiro()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="178" y="282" width="105" height="20" uuid="*************-422a-9fc9-67b852bd7ee9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoMarco()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="178" y="302" width="105" height="20" uuid="d51b5686-435f-4de5-b4d3-eb32c901acda">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoAbril()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="178" y="322" width="105" height="20" uuid="2b9b35cf-0efd-4d66-8b82-af40ed017ea3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoMaio()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="342" width="105" height="20" uuid="62f03624-956f-4a27-9ed2-0a625c021f63">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoJunho()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="362" width="105" height="20" uuid="31f8455c-4554-417a-a1cc-2b1d6ff4b804">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoJulho()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="382" width="105" height="20" uuid="35779eae-1dee-47a1-b789-fec068b7e9ea">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoAgosto()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="402" width="105" height="20" uuid="ed67e94f-a76a-4968-9a57-b2e96de82b55">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoSetembro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="422" width="105" height="20" uuid="f76b6a54-1d6c-4079-9f49-bc94af74c2da">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoOutubro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="442" width="105" height="20" uuid="c910430e-c96e-494e-803d-d85964a1a950">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoNovembro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="462" width="105" height="20" uuid="d1175e31-7960-4a07-bd4b-b2cfd1751c77">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoDezembro()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy hh:mm:ss " isBlankWhenNull="true">
				<reportElement x="178" y="482" width="105" height="20" uuid="5444af0f-96a8-4221-8d2f-dac96e3fe8f9">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getDataConfirmacaoDecimo()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="242" width="138" height="20" uuid="fd858ad2-17d5-42ea-aa58-ca0d34b04dfb">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloJaneiro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="262" width="138" height="20" uuid="7ac2c7e4-1d3a-43c2-adb1-12c2d3c4393f">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloFevereiro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="282" width="138" height="20" uuid="56e3dd10-51ad-4d98-8c83-45c556e790df">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloMarco()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="302" width="138" height="20" uuid="e9763c8c-4837-4e50-b898-97a35dcaf27e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloAbril()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="322" width="138" height="20" uuid="3b0e1cda-5cd8-4815-b1ab-68951ca443db">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloMaio()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="342" width="138" height="20" uuid="3ca6cdf1-053d-48f9-9217-40ae9f580125">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloJunho()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="362" width="138" height="20" uuid="7bf864b5-e279-4ea0-801e-2597d5416601">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloJulho()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="382" width="138" height="20" uuid="2f0d3d28-04fe-4827-8760-977601438ebd">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloAgosto()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="402" width="138" height="20" uuid="9402f894-577c-476f-ad53-be2752d17b58">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloSetembro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="422" width="138" height="20" uuid="7ea25161-34ab-46cc-b9c7-4914edd5e0ac">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloOutubro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="442" width="138" height="20" uuid="97d8c682-1546-4e5b-9202-ca44241e5c5c">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloNovembro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="462" width="138" height="20" uuid="8a216ffe-3666-4887-ae5a-87c0b6d71716">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloDezembro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="283" y="482" width="138" height="20" uuid="4758e104-fb84-41a8-9683-9d62337dd3f1">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getProtocoloDecimo()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="242" width="75" height="20" uuid="42b3663d-0611-44b6-b046-973b1f74cf31">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioJan()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="262" width="75" height="20" uuid="28fa72f2-c345-440b-bbcc-100a139fac71">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioFev()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="282" width="75" height="20" uuid="b2a70b7e-4479-449d-bcb7-47821468b126">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioMar()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="302" width="75" height="20" uuid="db9ff56d-99fd-4eec-931d-31db2bbbe317">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioAbr()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="322" width="75" height="20" uuid="b1bdcca4-7f97-4107-a45a-c26a80b39365">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioMai()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="342" width="75" height="20" uuid="d1376fbf-dc65-45d5-95b9-0adc7c14a890">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioJun()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="362" width="75" height="20" uuid="dd15b0c0-3341-4dce-b96e-e8f5c9881e75">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioJul()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="382" width="75" height="20" uuid="144421ba-a5d4-446e-b4c1-f3f5a0c074b6">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioAgo()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="402" width="75" height="20" uuid="a9c37902-94f9-4fc4-a46b-560170f4297d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioSet()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="422" width="75" height="20" uuid="67ed6fb3-f19b-4be5-91ba-f88cd1534cde">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioOut()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="442" width="75" height="20" uuid="425ef1f6-0930-43fd-a118-07db9748c5d2">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioNov()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="462" width="75" height="20" uuid="5c3ac216-3336-4b8a-89a1-195430091260">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioDez()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="421" y="482" width="75" height="20" uuid="2b474967-f7cc-48ef-ae03-b1d2094a32c4">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="html">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getTextoRelatorioDecimo()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="242" width="58" height="20" uuid="ff64909e-72d0-458a-b899-3b0020aaeb89">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaJaneiro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="262" width="58" height="20" uuid="fbba3975-7dc4-410f-917e-fa995578c745">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaFevereiro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="282" width="58" height="20" uuid="367336ad-af3c-46a3-88a6-667e336ca85b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaMarco()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="302" width="58" height="20" uuid="84cb80d1-b031-43be-9773-3f22e60a0d94">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaAbril()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="322" width="58" height="20" uuid="97125d49-4eda-4d62-af69-03ae7cfc3c03">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaMaio()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="342" width="58" height="20" uuid="9ecbc38c-6a2e-447d-8392-8a2f8c87125f">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaJunho()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="362" width="58" height="20" uuid="fa02c206-4073-40da-9aaf-673aff8133d7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaJulho()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="382" width="58" height="20" uuid="b98e35c9-a298-4dcb-8d0e-bb398df463e3">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaAgosto()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="402" width="58" height="20" uuid="8dc7934d-b4d3-4e04-b2fb-6bc5455496e1">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaSetembro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="422" width="58" height="20" uuid="e715649b-bef9-40ea-a565-4453c29a3058">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaOutubro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="442" width="58" height="20" uuid="19187ec4-285a-479b-9a6c-1e5e20736603">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaNovembro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="462" width="58" height="20" uuid="40f80c5d-9f75-4c4b-81cf-925973165fce">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaDezembro()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement x="496" y="482" width="58" height="20" uuid="2b4a2c7d-4afc-4b21-b11a-45662e3a5d66">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" markup="none">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{remessas}.getAssinaturaDecimo()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="512" width="279" height="20" uuid="06e9acf6-2256-4efd-a337-a1acfaa7936b">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
					<paragraph lineSpacing="Double" lineSpacingSize="0.0" firstLineIndent="0"/>
				</textElement>
				<text><![CDATA[Legenda das assinaturas:]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="45">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="30" height="20" uuid="973337f3-9585-47d8-aaa6-3ac8ecdc5337">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{REPORT_COUNT} == 1L	? "A = "
	: $V{REPORT_COUNT} == 2L ? "B = " 
	: $V{REPORT_COUNT} == 3L ? " C = " 
	: $V{REPORT_COUNT} == 4L ? "D = " 
	: $V{REPORT_COUNT} == 5L ? "E = " 
	: $V{REPORT_COUNT} == 6L ? "F = "
    : $V{REPORT_COUNT} == 7L ? "G = "
    : $V{REPORT_COUNT} == 8L ? "H = "
    : $V{REPORT_COUNT} == 9L ? "I = "
    : $V{REPORT_COUNT} == 10L ? "J = "
    : $V{REPORT_COUNT} == 11L ? "K = "
    : $V{REPORT_COUNT} == 12L ? "L = "
    : ""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="69" y="0" width="282" height="20" uuid="a422bc09-543f-4ecf-80b6-f025ef429337">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="69" y="21" width="282" height="20" uuid="76e87097-6e7e-4e7a-8bf6-7ce246a77bce">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="31" y="0" width="38" height="20" uuid="2d598562-9670-45e9-9d10-e2fbc05a8f0e">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Nome: ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="383" y="0" width="130" height="20" uuid="d05a29c8-2c00-4702-9a05-d23cec243aed">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cpfMask}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="353" y="0" width="30" height="20" uuid="bbd6d7bd-6f0d-4cac-a70d-c6a6f95d01fe">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[CPF:]]></text>
			</staticText>
		</band>
	</detail>
	<pageFooter>
		<band height="37" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
			<staticText>
				<reportElement x="1" y="3" width="555" height="12" uuid="2f1d6b86-5b94-4b95-89e2-2308634a00fe">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Sistema de Controle de Atos de Pessoal do Tribunal de Contas do Estado do Acre. Resolução/TCE-AC n° 102/2016.]]></text>
			</staticText>
			<rectangle radius="0">
				<reportElement x="1" y="2" width="555" height="1" uuid="9c340cd3-b7e6-45f9-bd66-4f5500c23130">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="14" width="40" height="12" uuid="46dfb36b-15bf-4f36-b9a6-90417abbce74">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Usuário:]]></text>
			</staticText>
			<textField>
				<reportElement x="42" y="14" width="338" height="12" uuid="04474135-25ef-46e2-8eb1-bf460a607b06">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuario}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="25" width="40" height="12" uuid="d6311f84-f90a-4fd6-a7da-0b04fe82cbd6">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Emissão:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy hh:mm:ss">
				<reportElement x="42" y="25" width="100" height="12" uuid="c8b01ac1-e40a-43fc-951f-b12d4f7e35be">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="447" y="25" width="62" height="12" uuid="bc8a39a4-b2a9-4b1e-a426-cc4280917442">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Página " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="true">
				<reportElement x="509" y="25" width="46" height="12" uuid="2e55bc66-6b73-4712-8eec-9adaf852d8f7">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[" de " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
