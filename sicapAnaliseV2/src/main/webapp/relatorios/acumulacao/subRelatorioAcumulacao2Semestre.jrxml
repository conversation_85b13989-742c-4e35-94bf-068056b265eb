<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.3.1.final using JasperReports Library version 6.3.1  -->
<!-- 2017-08-18T13:40:36 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="subRelatorioAcumulacao1Semestre" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="800" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" isFloatColumnFooter="true" uuid="4ecf7ae5-1156-4d77-9b23-17820bd4036f">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="175"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="817"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="778"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="208"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="ano" class="java.lang.Integer"/>
	<parameter name="idEntidadeCjur" class="java.lang.Integer"/>
	<parameter name="idBeneficiario" class="java.lang.Long"/>
	<queryString>
		<![CDATA[SELECT 
	   r.codigo,
	   r.descricao,
       r.ano,
       r.folha,
       MAX(r.julho) AS julho,
       MAX(r.agosto) AS agosto,
       MAX(r.setembro) AS setembro,
       MAX(r.outubro) AS outubro,
       MAX(r.novembro) AS novembro,
       MAX(r.dezembro) AS dezembro,
       MAX(r.decimo) AS decimo, 
       (MAX(janeiro) + MAX(fevereiro) + MAX(marco) + MAX(abril) + MAX(maio) + MAX(junho) + MAX(julho) + MAX(agosto) + MAX(setembro) + MAX(outubro) + MAX(novembro) + MAX(dezembro) + MAX(decimo)) AS total
FROM (SELECT
	    v.codigo, 
		vcc.idContraCheque, 
        v.descricao, 
        cc.ano, 
        CASE WHEN cc.mes = 1 THEN vcc.valor ELSE 0 END AS janeiro, 
        CASE WHEN cc.mes = 2 THEN vcc.valor ELSE 0 END AS fevereiro, 
        CASE WHEN cc.mes = 3 THEN vcc.valor ELSE 0 END AS marco, 
        CASE WHEN cc.mes = 4 THEN vcc.valor ELSE 0 END AS abril, 
        CASE WHEN cc.mes = 5 THEN vcc.valor ELSE 0 END AS maio, 
        CASE WHEN cc.mes = 6 THEN vcc.valor ELSE 0 END AS junho, 
        CASE WHEN cc.mes = 7 THEN vcc.valor ELSE 0 END AS julho, 
        CASE WHEN cc.mes = 8 THEN vcc.valor ELSE 0 END AS agosto, 
        CASE WHEN cc.mes = 9 THEN vcc.valor ELSE 0 END AS setembro, 
        CASE WHEN cc.mes = 10 THEN vcc.valor ELSE 0 END AS outubro, 
        CASE WHEN cc.mes = 11 THEN vcc.valor ELSE 0 END AS novembro, 
        CASE WHEN cc.mes = 12 THEN vcc.valor ELSE 0 END AS dezembro, 
        CASE WHEN (cc.mes = 12) AND (tf.descricao like '%13%' OR tf.descricao like '%decimo%' OR tf.descricao like '%terceiro%') THEN vcc.valor ELSE 0 END AS decimo,
        tf.descricao as folha 
	 FROM ContraCheque cc 
     INNER JOIN VerbasContraCheque vcc ON vcc.idContraCheque = cc.id 
     INNER JOIN TipoFolha tf ON vcc.idTipoFolha = tf.id 
     INNER JOIN Verba v ON vcc.idVerba = v.id 
     INNER JOIN Beneficiario b ON cc.idBeneficiario = b.id 
     INNER JOIN CadastroUnico cadu ON b.idCadastroUnico = cadu.id 
     WHERE cc.ano = $P{ano}  AND cadu.cpf = $P{cpf}  AND cc.idEntidadeCjur = $P{idEntidadeCjur}  AND cc.idBeneficiario = $P{idBeneficiario} AND v.natureza = 'C' ) r
GROUP BY 
	r.codigo,
	r.descricao,
    r.ano,
    r.folha
ORDER BY 
	r.codigo asc]]>
	</queryString>
	<field name="codigo" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="ano" class="java.lang.Integer"/>
	<field name="folha" class="java.lang.String"/>
	<field name="julho" class="java.math.BigDecimal"/>
	<field name="agosto" class="java.math.BigDecimal"/>
	<field name="setembro" class="java.math.BigDecimal"/>
	<field name="outubro" class="java.math.BigDecimal"/>
	<field name="novembro" class="java.math.BigDecimal"/>
	<field name="dezembro" class="java.math.BigDecimal"/>
	<field name="decimo" class="java.math.BigDecimal"/>
	<field name="total" class="java.math.BigDecimal"/>
	<columnHeader>
		<band height="12" splitType="Prevent">
			<staticText>
				<reportElement mode="Opaque" x="0" y="1" width="37" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="517452c2-9cad-4e52-adeb-41818e12388a"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Código]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="259" y="1" width="103" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="47985ea1-6aaf-48f7-9686-839710b19241">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Tipo Folha]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="362" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="959c52cf-1025-48e2-afb1-f8a56f5135e6"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Julho]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="417" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="5cf30c6b-10d8-40a3-8827-e291d3aa296b"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Agosto]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="472" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="a51ef3a9-8d7b-4593-87e5-a0628979694a"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Setembro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="527" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="beda96e0-f46e-4691-afda-3f4e17469f3f">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Outubro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="582" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="d34fba49-e836-404b-b229-32893aae2672"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Novembro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="637" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="d9c9c184-6668-48b8-a933-8697e54a4d55">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Dezembro]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="692" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="bfb6a77a-2ae2-4494-b549-b3363f0012ad"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[13° Salário]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="747" y="1" width="55" height="11" isRemoveLineWhenBlank="true" backcolor="#C0C0C0" uuid="ee38582c-881b-4b49-aa1e-390aa54cbef3"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="37" y="1" width="222" height="11" backcolor="#C0C0C0" uuid="bac589e4-1140-43f4-9616-a7c7ce943f95">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Verba]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="11" splitType="Prevent">
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="37" height="11" isRemoveLineWhenBlank="true" uuid="8128eff7-5b39-45db-8ea9-a6d31368595c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="259" y="0" width="103" height="11" isRemoveLineWhenBlank="true" uuid="a348eeb2-1c46-4827-a92f-e592e58e9b49">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{folha}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="362" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="f81a0adf-c881-42c2-a62f-cabc9b27c1b5">
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{julho}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="417" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="5aa4fb90-4714-4ea3-87fa-bdea73e67fee">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agosto}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="472" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="78273e44-2419-4a2e-b7b1-4cc670a54611">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{setembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="527" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="a37a816a-4360-4d5e-8fde-6c968f1ce9b0">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outubro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="582" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="8f29e3e2-5acf-405c-bf26-1d54f26fb24d">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{novembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="637" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="f7457ff4-251c-49f4-b5a5-7931237bec06">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dezembro}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="692" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="9a25d331-ee80-4dc5-8f6d-77af08d1e9d6">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{decimo}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00" isBlankWhenNull="true">
				<reportElement x="747" y="0" width="55" height="11" isRemoveLineWhenBlank="true" uuid="7d2f00f0-e1de-45a1-b92b-8b2238427169">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph firstLineIndent="0" rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{total}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="37" y="0" width="222" height="11" uuid="457c8d20-abc7-481d-87e7-6ae8f8df5899">
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
					<paragraph leftIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricao}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
