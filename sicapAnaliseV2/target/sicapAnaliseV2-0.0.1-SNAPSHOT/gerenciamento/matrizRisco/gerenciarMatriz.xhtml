<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:p="http://primefaces.org/ui" xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions" template="/resources/template/template.xhtml">

	<ui:define name="content">
		<p:fieldset legend="Gerenciamento - Matriz de Risco">
			<h:form prependId="false">
				<p:growl id="idMsg" showDetail="false" autoUpdate="true" />
				<p:fieldset legend="Ações">
					<p:panelGrid columns="2" styleClass="semBorda">
						<p:commandButton icon="fa fa-fw fa-list white" value="Listar" actionListener="#{matrizRiscoBean.listarMatriz}" immediate="true" update="@form" />
						<p:commandButton icon="fa fa-fw fa-plus white" value="Nova matriz" actionListener="#{matrizRiscoBean.novaMatriz}" immediate="true"
							update="@form" oncomplete="PF('idWizard').loadStep('idMatriz', true)" />
					</p:panelGrid>
				</p:fieldset>
				<p:spacer />
				<p:fieldset id="idFieldsetMatriz" widgetVar="idFieldsetMatriz" legend="Cadastrar nova matriz"
					rendered="#{matrizRiscoBean.renderedCadastrarNovaMatriz}">
					<p:wizard id="idWizard" widgetVar="idWizard" nextLabel="próximo" backLabel="anterior" showNavBar="true"
						rendered="#{matrizRiscoBean.renderedCadastrarNovaMatriz}">
						<p:tab id="idMatriz" title="Matriz">
							<p:panel>
								<p:panelGrid id="idPanelMatriz" columns="2" styleClass="semBorda">
									<h:outputText value="Nome: *" styleClass="negrito" />
									<p:inputText value="#{matrizRiscoBean.matrizRisco.nome}" required="true" requiredMessage="O nome é obrigatório." size="100" />
									<p:calendar value="#{matrizRiscoBean.matrizRisco.dataCadastro}" pattern="dd/MM/yyyy">
										<f:converter converterId="dateConverter" />
									</p:calendar>
								</p:panelGrid>
							</p:panel>
						</p:tab>
						<p:tab id="idMetricas" title="Métricas">
							<p:panel>
								<p:panelGrid id="idPanelMetricas" columns="2" styleClass="semBorda">
									<h:outputText value="Nome: " styleClass="negrito" />
									<p:inputText value="#{matrizRiscoBean.metricasMatrizRisco.nome}" required="true" requiredMessage="O nome é obrigatório." size="150" />

									<h:outputText value="Montante de proventos: " styleClass="negrito" />
									<h:panelGrid>
										<p:inputNumber value="#{matrizRiscoBean.metricasMatrizRisco.montanteProventos}" decimalPlaces="5" thousandSeparator="." decimalSeparator=","
											size="12" required="true" requiredMessage="O campo montante de proventos é obrigatório." />
									</h:panelGrid>

									<h:outputText value="Carga horária total: " styleClass="negrito" />
									<h:panelGrid>
										<p:inputNumber value="#{matrizRiscoBean.metricasMatrizRisco.cargaHorariaTotal}" decimalPlaces="5" thousandSeparator="." decimalSeparator=","
											size="12" required="true" requiredMessage="O campo carga horária total é obrigatório." />
									</h:panelGrid>

									<h:outputText value="Agente político: " styleClass="negrito" />
									<h:panelGrid>
										<p:inputMask mask="9?9" value="#{matrizRiscoBean.metricasMatrizRisco.agentePolitico}" size="8" required="true"
											requiredMessage="O campo agente político é obrigatório." />
									</h:panelGrid>

									<h:outputText value="Quantidade de vínculos: " styleClass="negrito" />
									<h:panelGrid>
										<p:inputMask mask="9?9" value="#{matrizRiscoBean.metricasMatrizRisco.quantidadeVinculos}" size="8" required="true"
											requiredMessage="O campo quantidade de vínculos é obrigatório." />
									</h:panelGrid>

									<h:outputText value="Quantidade de vínculos não pensão: " styleClass="negrito" />
									<h:panelGrid>
										<p:inputMask mask="9?9" value="#{matrizRiscoBean.metricasMatrizRisco.quantidadeVinculosNaoPensao}" size="8" required="true"
											requiredMessage="O campo quantidade de vínculos não pensão é obrigatório." />
									</h:panelGrid>

									<h:outputText value="Quantidade de municípios de lotação: " styleClass="negrito" />
									<h:panelGrid>
										<p:inputMask mask="9?9" value="#{matrizRiscoBean.metricasMatrizRisco.quantidadeMunicipiosLotacao}" size="8" required="true"
											requiredMessage="O campo quantidade de municípios de lotação é obrigatório." />
									</h:panelGrid>
								</p:panelGrid>
							</p:panel>
						</p:tab>
						<p:tab id="idResumo" title="Confirmar dados">
							<p:spacer />
							<p:panel header="Dados da Matriz">
								<p:panelGrid columns="2" styleClass="semBorda">
									<h:outputText value="Matriz: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.matrizRisco.nome}" />
									<h:outputText value="Data Cadastro: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.matrizRisco.dataCadastro}" />
								</p:panelGrid>
							</p:panel>

							<p:spacer />
							<p:spacer />

							<p:panel header="Métricas">
								<p:panelGrid columns="2" styleClass="semBorda">
									<h:outputText value="Nome: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.nome}" />
									<h:outputText value="Data cadastro: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.dataCadastro}" />

									<h:outputText value="Montante de proventos: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.montanteProventos}">
										<f:convertNumber maxFractionDigits="5" />
									</h:outputText>

									<h:outputText value="Carga horária total: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.cargaHorariaTotal}">
										<f:convertNumber maxFractionDigits="5" />
									</h:outputText>

									<h:outputText value="Agente político: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.agentePolitico}" />

									<h:outputText value="Quantidade de vínculos: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.quantidadeVinculos}" />

									<h:outputText value="Quantidade de vínculos não pensão: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.quantidadeVinculosNaoPensao}" />

									<h:outputText value="Quantidade de municípios de lotação: " styleClass="negrito" />
									<h:outputText value="#{matrizRiscoBean.metricasMatrizRisco.quantidadeMunicipiosLotacao}" />

								</p:panelGrid>
								<br />
								<p:commandButton value="Salvar" action="#{matrizRiscoBean.salvarMatriz()}" update="@form" immediate="true" />
							</p:panel>

							<br />
							<br />

						</p:tab>
					</p:wizard>
				</p:fieldset>

				<p:spacer />

				<pe:masterDetail id="masterDetail" level="#{matrizRiscoBean.currentLevel}" showAllBreadcrumbItems="true"
					rendered="#{matrizRiscoBean.renderedListarMatrizesCadastradas}">

					<pe:masterDetailLevel id="listaMatrizes" level="1">
						<f:facet name="label">
							<h:outputText value="Matrizes de Risco" />
						</f:facet>

						<p:dataTable id="tabelaMatrizRisco" var="matriz" widgetVar="tabelaMatrizRisco" value="#{matrizRiscoBean.listaMatrizesRisco}"
							emptyMessage="Nenhuma matriz de risco encontrada." rows="20" paginator="true" paginatorPosition="bottom"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="20,30,40,50">

							<p:column headerText="Número" width="5%" styleClass="TexAlCenter">
								<h:outputText value="#{matriz.id}" />
							</p:column>

							<p:column headerText="Nome" width="50%" styleClass="TexAlLeft">
								<h:outputText value="#{matriz.nome}" />
							</p:column>

							<p:column headerText="Data de Cadastro" width="10%" styleClass="TexAlCenter">
								<h:outputText value="#{matriz.dataCadastro}">
									<f:converter converterId="localDateTimeConverter" />
								</h:outputText>
							</p:column>

							<p:column headerText="Ações" width="5%" styleClass="TexAlCenter">
								<p:commandLink value="" styleClass="icon-eye Fs14" process="@this" immediate="true" title="Visualizar métricas cadastradas">
									<pe:selectDetailLevel listener="#{matrizRiscoBean.visualizarMetricasCadastradas(matriz.id)}" />
								</p:commandLink>
							</p:column>

							<f:facet name="footer">
								<h:outputText value="Total de matrizes cadastradas: #{matrizRiscoBean.listaMatrizesRisco.size()}" />
							</f:facet>

						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailMetricas" level="2" contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Métricas" />
						</f:facet>

						<p:fieldset legend="Dados da matriz de risco">
							<h:panelGrid columns="2">
								<h:outputText value="Número: " styleClass="negrito" />
								<h:outputText value="#{matrizRiscoBean.matrizRiscoSelecionada.id}" />
								<h:outputText value="Nome: " styleClass="negrito" />
								<h:outputText value="#{matrizRiscoBean.matrizRiscoSelecionada.nome}" />
								<h:outputText value="Data de cadastro: " styleClass="negrito" />
								<h:outputText value="#{matrizRiscoBean.matrizRiscoSelecionada.dataCadastro}">
									<f:converter converterId="localDateTimeConverter" />
								</h:outputText>
								<h:outputText value="Usuário: " styleClass="negrito" />
								<h:outputText value="#{matrizRiscoBean.matrizRiscoSelecionada.usuarioInterno.nome}" />
							</h:panelGrid>
						</p:fieldset>
						<p:spacer />

						<p:fieldset legend="Métricas cadastradas">
							<p:dataTable id="tabelaMetricas" var="metricas" widgetVar="tabelaMetricas" value="#{matrizRiscoBean.matrizRiscoSelecionada.listaMetricas}"
								emptyMessage="Nenhuma métrica encontrada." rows="20" paginator="true" paginatorPosition="bottom"
								currentPageReportTemplate="página {currentPage} de {totalPages}"
								paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								rowsPerPageTemplate="20,30,40,50">

								<p:column headerText="Número" width="7%" styleClass="TexAlCenter">
									<h:outputText value="#{metricas.id}" />
								</p:column>

								<p:column headerText="nome" width="7%" styleClass="TexAlCenter">
									<h:outputText value="#{metricas.nome}" />
								</p:column>

								<p:column headerText="Data de Cadastro" width="10%" styleClass="TexAlCenter">
									<h:outputText value="#{matriz.dataCadastro}" />
								</p:column>

								<p:column headerText="Ações" width="30%" sortBy="#{acumulacao[2]}">
									<p:commandLink value="a" process="@this" immediate="true">
										<!-- 									<pe:selectDetailLevel listener="#{auditoriaAcumulacaoBean.detalharAcumulacao(acumulacao[0], acumulacao)}" /> -->
									</p:commandLink>
								</p:column>

							</p:dataTable>
						</p:fieldset>
						<p:spacer />
						<p:fieldset>
							<p:panel header="Acumulações">

							</p:panel>
							<h:panelGrid columns="2">
								<p:commandButton value="Voltar" style="margin-top: 10px;" icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
							</h:panelGrid>
						</p:fieldset>
					</pe:masterDetailLevel>
				</pe:masterDetail>
			</h:form>
		</p:fieldset>
	</ui:define>
</ui:composition>