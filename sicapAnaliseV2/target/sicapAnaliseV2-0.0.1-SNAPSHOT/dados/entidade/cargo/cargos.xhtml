<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
		<!-- 		<p:fieldset legend="Lista de Cargos"> -->
		<h:form prependId="false">
			<p:fieldset legend="Dados para a consulta por cargos">
			
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Tipo: " styleClass="FontBold" />
							<p:selectOneMenu value="#{cargoBean.tipoCargo}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{cargoBean.listaTipoCargo}" var="tipo"
									itemLabel="#{tipo.descricao}" itemValue="#{tipo.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Escolaridade: " styleClass="FontBold" />
							<p:selectOneMenu 
								value="#{cargoBean.tipoEscolaridade}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{cargoBean.listaEscolaridade}"
									var="escolaridade" itemLabel="#{escolaridade.descricao}"
									itemValue="#{escolaridade.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Tipo Acumulável: " styleClass="FontBold" />
							<p:selectOneMenu 
								value="#{cargoBean.tipoAcumulavel}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{cargoBean.listaTipoAcumulavel}"
									var="tipoAcumulavel" itemLabel="#{tipoAcumulavel.descricao}"
									itemValue="#{tipoAcumulavel.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Situação: " styleClass="FontBold" />
							<p:selectOneRadio value="#{cargoBean.situacao}">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItem itemLabel="Ativo" itemValue="#{1}" />
								<f:selectItem itemLabel="Inativo" itemValue="#{2}" />
							</p:selectOneRadio>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Nome: " styleClass="FontBold" />
							<p:inputText value="#{cargoBean.nome}" style="width: 100%;" />
						</h:panelGroup>
					</div>
				</div>
				
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width: auto;" icon="fa fa-fw fa-search white"
								actionListener="#{cargoBean.pesquisar()}" update="masterDetail" />
					</div>
				</div>
				

				
			</p:fieldset>

			<div class="EmptyBox10"></div>
			<p:fieldset legend="Resultados da consulta">
				<pe:masterDetail id="masterDetail" level="#{cargoBean.currentLevel}"
					showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tabelaCargos" var="cargo"
							widgetVar="tabelaCargos" value="#{cargoBean.listaCargos}"
							emptyMessage="Nenhum cargo encontrado." rows="10"
							paginator="true"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50,100"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column filterBy="#{cargo.codigo}" headerText="Código"
								width="15%" filterMatchMode="contains"
								filterStyle="width: 60px;">
								<h:outputText value="#{cargo.codigo}" />
							</p:column>

							<p:column filterBy="#{cargo.nome}" headerText="Nome" width="40%"
								filterMatchMode="contains" filterStyle="width: 50%!important;">
								<p:commandLink value="#{cargo.nome}" process="@this"
									immediate="true">
									<pe:selectDetailLevel
										listener="#{cargoBean.buscarDadosCargo(cargo)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Tipo" width="10%" styleClass="TexAlCenter">
								<h:outputText value="#{cargo.tipo.descricao}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de cargos: #{cargoBean.listaCargos.size()}" />
							</f:facet>

						</p:dataTable>

					</pe:masterDetailLevel>
					<pe:masterDetailLevel id="detailDadosServidor" level="2"
						contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Dados do Cargo" />
						</f:facet>
<!-- 						<p:fieldset> -->
						<p:panel header="#{cargoBean.cargo.codigo} - #{cargoBean.cargo.nome}">
							<h:panelGrid columns="2">
								<h:outputText value="Código: " styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.codigo}"
									styleClass="Fs12" />

								<h:outputText value="Nome: " styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.nome}" styleClass="Fs12" />

								<h:outputText value="Tipo: " styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.tipo.descricao}"
									styleClass="Fs12" />

								<h:outputText value="Carga Horária Mensal: "
									styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.cargaHorariaMensal}"
									styleClass="Fs12" />

								<h:outputText value="Escolaridade: " styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.escolaridade.descricao}"
									styleClass="Fs12" />

								<h:outputText value="Intervalo de Promoção: "
									styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.intervaloPromocao}"
									styleClass="Fs12" />

								<h:outputText value="Tipo Acumulável: "
									styleClass="Fs12 FontBold" />
								<h:outputText
									value="#{cargoBean.cargo.tipoAcumulavel.descricao}"
									styleClass="Fs12" />

								<h:outputText value="Data de Criação: "
									styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.dataCriacao}"
									styleClass="Fs12">
									<f:converter converterId="dateConverter" />
								</h:outputText>

								<h:outputText value="Situação: " styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.situacao.descricao}"
									styleClass="Fs12" />

								<h:outputText value="Data de Extincão: "
									styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.dataExtincao}"
									styleClass="Fs12">
									<f:converter converterId="dateConverter" />
								</h:outputText>

								<h:outputText value="Registro Ativo: "
									styleClass="Fs12 FontBold" />
								<h:outputText
									value="#{cargoBean.formatUtil.formatBoolean(cargoBean.cargo.registroAtivo)}"
									styleClass="Fs12" />

								<h:outputText value="Data de Inativação: "
									styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.dataInativacao}"
									styleClass="Fs12">
									<f:converter converterId="dateTimeConverter" />
								</h:outputText>

								<h:outputText value="Id da Remessa: " styleClass="Fs12 FontBold" />
								<h:outputText value="#{cargoBean.cargo.remessaEventual.id}"
									styleClass="Fs12" />

								<h:outputText value="Data de Envio: " styleClass="Fs12 FontBold" />
								<h:outputText
									value="#{cargoBean.cargo.remessaEventual.dataEnvio}"
									styleClass="Fs12">
									<f:converter converterId="dateTimeConverter" />
								</h:outputText>

							</h:panelGrid>
<!-- 						</p:fieldset> -->
</p:panel>
						<h:panelGrid columns="1">
								<p:commandButton value="Voltar" style="margin-top: 10px;"
									icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
							</h:panelGrid>
					</pe:masterDetailLevel>
				</pe:masterDetail>
			</p:fieldset>
		</h:form>
		<!-- 		</p:fieldset> -->
	</ui:define>
</ui:composition>