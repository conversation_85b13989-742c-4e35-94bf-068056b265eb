<ui:composition 
	xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:p="http://primefaces.org/ui" xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions" template="/resources/template/template.xhtml">

	<ui:define name="content">
		<p:fieldset legend="Lista de Verbas">
			<h:form prependId="false">
				<p:fieldset>

					<h:panelGrid columns="4" style="margin-bottom:10px;" cellpadding="5">

						<h:outputText value="Base Previdenciária: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{verbaBean.basePrevidenciaria}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="T"/>
								<f:selectItems value="#{verbaBean.status}" var="status" itemLabel="#{status.descricao}" itemValue="#{status.id}" />
							</p:selectOneMenu>
						</h:panelGrid>


						<h:outputText value="Base IRPF: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{verbaBean.baseIRPF}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="T" />
								<f:selectItems value="#{verbaBean.status}" var="status" itemLabel="#{status.descricao}" itemValue="#{status.id}" />
							</p:selectOneMenu>
						</h:panelGrid>

						<h:outputText value="Base FGTS: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{verbaBean.baseFGTS}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="T" />
								<f:selectItems value="#{verbaBean.status}" var="status" itemLabel="#{status.descricao}" itemValue="#{status.id}" />
							</p:selectOneMenu>
						</h:panelGrid>

						<h:outputText value="Natureza: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{verbaBean.natureza}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="T" />
								<f:selectItems value="#{verbaBean.tipoNatureza}" var="natureza" itemLabel="#{natureza.descricao}" itemValue="#{natureza.id}" />
							</p:selectOneMenu>
						</h:panelGrid>

						<h:outputText value="Referência: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{verbaBean.tipoReferencia}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{verbaBean.listaTipoReferencia}" var="referencia" itemLabel="#{referencia.descricao}" itemValue="#{referencia.id}" />
							</p:selectOneMenu>
						</h:panelGrid>

						<h:outputText value="Compõe Vencimento Padrão: " styleClass="FontBold" />
						<h:panelGrid columns="1" style="width: 30%;">
							<p:selectOneMenu value="#{verbaBean.compoeVencimentoPadrao}" autoWidth="false" style="width: 200px!important;">
								<f:selectItem itemLabel="Todos" itemValue="T" />
								<f:selectItems value="#{verbaBean.status}" var="status" itemLabel="#{status.descricao}" itemValue="#{status.id}" />
							</p:selectOneMenu>
						</h:panelGrid>

						<h:outputText value="Categoria Econômica: " styleClass="FontBold" />
						<p:inputText value="#{verbaBean.categoriaEconomica}" size="40" />

						<h:outputText value="Grupo de Natureza de Despesa: " styleClass="FontBold" />
						<p:inputText value="#{verbaBean.grupoNaturezaDespesa}" size="40" />

						<h:outputText value="Modalidade de Aplicação: " styleClass="FontBold" />
						<p:inputText value="#{verbaBean.modalidadeAplicacao}" size="40" />

						<h:outputText value="Elemento de Despesa: " styleClass="FontBold" />
						<p:inputText value="#{verbaBean.elementoDespesa}" size="40" />

						<h:outputText value="Nome: " styleClass="FontBold" />
						<p:inputText value="#{verbaBean.nome}" size="40" />

						<p:commandButton value="Pesquisar" icon="fa fa-fw fa-search white" actionListener="#{verbaBean.pesquisar()}" update="masterDetail" />
					</h:panelGrid>
				</p:fieldset>

				<pe:masterDetail id="masterDetail" level="#{verbaBean.currentLevel}" showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tabelaVerbas" var="verba" widgetVar="tabelaVerbas" value="#{verbaBean.listaVerba}" emptyMessage="Nenhuma verba encontrada."
							rows="10" paginator="true"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="10,20,30,40,50,100,300,500" currentPageReportTemplate="página {currentPage} de {totalPages}" paginatorPosition="bottom">

							<p:column headerText="Código" width="5%">
								<h:outputText value="#{verba.codigo}" />
							</p:column>

							<p:column headerText="Nome" width="15%">
								<p:commandLink value="#{verba.descricao}" process="@this" immediate="true">
									<pe:selectDetailLevel listener="#{verbaBean.buscaDadosVerba(verba)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Natureza" width="15%">
								<h:outputText value="#{verba.natureza.descricao}" />
							</p:column>

							<f:facet name="footer">
								<h:outputLabel value="Total de verbas: #{verbaBean.listaVerba.size()}" />
							</f:facet>

						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailDadosVerba" level="2" contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Dados do Verba" />
						</f:facet>
						<p:fieldset>
							<p:panel header="Verba">
								<h:panelGrid columns="2">
									<h:outputText value="Código: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.codigo}" />

									<h:outputText value="Nome: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.descricao}" />

									<h:outputText value="Base Legal: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.baseLegal}" />

									<h:outputText value="Base Previdenciária: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.basePrevidencia.descricao}" />

									<h:outputText value="Base IRPF: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.baseIRPF}" />

									<h:outputText value="Base FGTS: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.baseFGTS}" />

									<h:outputText value="Natureza: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.natureza.descricao}" />

									<h:outputText value="Referência: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.tipoReferencia.descricao}" />

									<h:outputText value="Compõe Vencimento  Padrão: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.tipoReferencia.descricao}" />

									<h:outputText value="Categoria Econômica: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.categoriaEconomica}" />

									<h:outputText value="Grupo da Natureza de Despesa: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.grupoNaturezaDespesa}" />

									<h:outputText value="Modalidade de Aplicação: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.modalidadeAplicacao}" />

									<h:outputText value="Elemento de Despesa: " styleClass="FontBold" />
									<h:outputText value="#{verbaBean.verba.elementoDespesa}" />

								</h:panelGrid>
							</p:panel>
							<h:panelGrid columns="2">
								<p:commandButton value="Voltar" style="margin-top: 10px;" icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
							</h:panelGrid>
						</p:fieldset>
					</pe:masterDetailLevel>
				</pe:masterDetail>
			</h:form>
		</p:fieldset>
	</ui:define>
</ui:composition>