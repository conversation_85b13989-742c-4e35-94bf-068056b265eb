<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.8.0.final using JasperReports Library version 6.8.0-2ed8dfabb690ff337a5797129f2cd92902b0c87b  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorioFinanceiro" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isSummaryWithPageHeaderAndFooter="true" uuid="78ee0887-7a69-4118-bdd8-2eadd495251d">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="sicap"/>
	<parameter name="competencia" class="java.lang.String"/>
	<parameter name="cpf" class="java.lang.String"/>
	<parameter name="usuario" class="java.lang.String"/>
	<parameter name="ano" class="java.lang.Integer"/>
	<parameter name="mes" class="java.lang.Integer"/>
	<parameter name="REPORT_IMAGE_DIR" class="java.lang.String" isForPrompting="false"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false"/>
	<queryString language="SQL">
		<![CDATA[SELECT auditoria."vw_consultaPessoaPorCPF".cpf,
	auditoria."vw_consultaPessoaPorCPF".nome,
	auditoria."vw_consultaPessoaPorCPF"."dataNascimento",
	auditoria."vw_consultaPessoaPorCPF"."nomeMae",
	auditoria."vw_consultaPessoaPorCPF".sexo,
	auditoria."vw_consultaPessoaPorCPF"."cpfMask"
FROM auditoria."vw_consultaPessoaPorCPF"
WHERE 
	 auditoria."vw_consultaPessoaPorCPF".cpf = $P{cpf}]]>
	</queryString>
	<field name="cpf" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="cpf"/>
	</field>
	<field name="nome" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nome"/>
	</field>
	<field name="dataNascimento" class="java.util.Date">
		<property name="com.jaspersoft.studio.field.label" value="dataNascimento"/>
	</field>
	<field name="nomeMae" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="nomeMae"/>
	</field>
	<field name="sexo" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="sexo"/>
	</field>
	<field name="cpfMask" class="java.lang.String">
		<property name="com.jaspersoft.studio.field.label" value="cpfMask"/>
	</field>
	<pageHeader>
		<band height="148" splitType="Stretch">
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="0" y="0" width="60" height="60" uuid="0d52f54f-eb2e-43a2-9a9b-e7cedd0712e8"/>
				<imageExpression><![CDATA[$P{REPORT_IMAGE_DIR} + "logotce.png"]]></imageExpression>
			</image>
			<image hAlign="Center" vAlign="Middle">
				<reportElement x="742" y="0" width="60" height="60" uuid="d42a6db1-efd5-44cb-861c-05e330cd7b39"/>
				<imageExpression><![CDATA[$P{REPORT_IMAGE_DIR} + "brasao.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="0" y="1" width="802" height="28" uuid="f676d569-7f2a-4c78-a256-40076763692a"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="24" isBold="true"/>
				</textElement>
				<text><![CDATA[Tribunal de Contas do Estado do Acre]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="30" width="802" height="28" uuid="8e91ebe8-ce08-46a4-8933-0a5644dcb859"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="16" isBold="true"/>
				</textElement>
				<text><![CDATA[SICAP - Sistema de Controle de Atos de Pessoal]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="66" width="802" height="1" uuid="0d1a85f6-5422-4008-bb9e-ca003c237892"/>
			</rectangle>
			<staticText>
				<reportElement x="0" y="70" width="802" height="13" uuid="98ecc341-d614-441e-a939-6227cb8b5c0e"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isItalic="true"/>
				</textElement>
				<text><![CDATA[Missão: Exercer o controle externo, orientando e fiscalizando a gestão pública, e incentivar a sociedade ao exercício do controle social.]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="90" width="802" height="15" backcolor="#C0C0C0" uuid="64e979f3-8651-4738-864e-1615b3bebad2"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[RELATÓRIO FINANCEIRO ANUAL]]></text>
			</staticText>
			<frame>
				<reportElement x="0" y="110" width="802" height="31" uuid="d00a0170-a6c9-4ec8-aec2-ce9db929fe08"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<staticText>
					<reportElement x="1" y="2" width="54" height="12" uuid="ef618b28-75da-4cc5-9c76-ae3ebc188d27"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="10" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="71" y="2" width="320" height="12" uuid="1692f9b8-0cc8-4855-a4a0-f4551bf7bbec"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nome}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="1" y="16" width="68" height="12" uuid="3cde48cc-4781-432f-bce8-eba4046ca883"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[Nome da Mãe:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="71" y="16" width="320" height="12" uuid="a85cb221-1982-48d8-b2a8-d255697832e5"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{nomeMae}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="401" y="2" width="28" height="12" uuid="f05a0776-8d21-4a4a-97ed-116464cb4c7a"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[CPF:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="430" y="2" width="122" height="12" uuid="10b151dd-6b1a-4584-9086-18886d1bdfe8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{cpfMask}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="401" y="16" width="28" height="12" uuid="2c91d94c-ed3a-4900-9a9c-3beedaa178b1"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[Sexo:]]></text>
				</staticText>
				<textField isBlankWhenNull="true">
					<reportElement x="430" y="16" width="122" height="12" uuid="b43517d8-4cbf-4d8f-84fb-ecf078a09d06"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{sexo}]]></textFieldExpression>
				</textField>
				<staticText>
					<reportElement x="599" y="2" width="100" height="12" uuid="3eae4b5d-02dc-4669-9e36-a50d0c17a3b2"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" isBold="true"/>
					</textElement>
					<text><![CDATA[Data de Nascimento:]]></text>
				</staticText>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="700" y="2" width="100" height="12" uuid="bce2a14d-293d-4836-8870-4f3818d6d692"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{dataNascimento}]]></textFieldExpression>
				</textField>
			</frame>
			<textField isBlankWhenNull="true">
				<reportElement x="668" y="90" width="133" height="15" uuid="1c6fe4de-cf8d-4579-b78c-b1547ed596a6"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
					<paragraph rightIndent="2"/>
				</textElement>
				<textFieldExpression><![CDATA["Ano: " + $P{ano}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="30" splitType="Stretch">
			<subreport>
				<reportElement x="0" y="0" width="802" height="20" isRemoveLineWhenBlank="true" uuid="5c16dfc9-ccb6-4dc4-9cfc-9abc7611da19"/>
				<subreportParameter name="cpf">
					<subreportParameterExpression><![CDATA[$F{cpf}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="ano">
					<subreportParameterExpression><![CDATA[$P{ano}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="SUBREPORT_DIR">
					<subreportParameterExpression><![CDATA[$P{SUBREPORT_DIR}]]></subreportParameterExpression>
				</subreportParameter>
				<connectionExpression><![CDATA[$P{REPORT_CONNECTION}]]></connectionExpression>
				<subreportExpression><![CDATA[$P{SUBREPORT_DIR} + "relatorioFinanceiroVinculos.jasper"]]></subreportExpression>
			</subreport>
			<break>
				<reportElement x="0" y="29" width="802" height="1" uuid="ca5cfc63-bdac-4b5b-b67a-866d1afc18a5"/>
			</break>
		</band>
	</detail>
	<pageFooter>
		<band height="46" splitType="Stretch">
			<staticText>
				<reportElement x="1" y="3" width="580" height="12" uuid="03ea0fad-06b8-4a8f-bdfd-1062d10a68dc"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Sistema de Controle de Atos de Pessoal do Tribunal de Contas do Estado do Acre. Resolução/TCE-AC n° 102/2016.]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="2" width="802" height="1" uuid="c437fd85-38f1-46be-88a6-4ce4f6777aa6"/>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="1" y="14" width="40" height="12" uuid="96b3ce2a-ba90-48cd-8098-09bde882f89e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Usuário:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="42" y="14" width="338" height="12" uuid="11626348-529d-4e82-8df4-b01a348e804f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuario}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="25" width="40" height="12" uuid="4a37815f-4d42-49fe-b7d1-3b648b1ee16a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<text><![CDATA[Emissão:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy hh:mm:ss" isBlankWhenNull="true">
				<reportElement x="42" y="25" width="100" height="12" uuid="fa4a639e-6fcd-45df-8a77-c847b1ad5fb7"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="692" y="25" width="62" height="12" uuid="0d9b7c52-10cd-46b9-9875-7dc11b641ce7"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Página " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="true">
				<reportElement x="756" y="25" width="46" height="12" uuid="e4f927ed-df88-4de9-957e-a4053a08e69a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[" de " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
