<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:c="http://java.sun.com/jsp/jstl/core">

<h:body>

	<ui:composition template="/resources/template/template.xhtml">

		<ui:define name="content">


			<style type="text/css">
.corTextoVerde td {
	color: #5cb85c !important;
}

.corTextoLaranja td {
	color: #f0ad4e !important;
}

.corTextoVermelho td {
	color: #d9534f !important;
}

.ui-growl-image-info {
	background-image:
		url("#{resource['primefaces-sentinel:images/warn-blue.svg']}")
		!important; /* test url */
}

.ui-growl-image-info+div.ui-growl-message {
	border-color: #cee4f5 !important;
	color: #63bce2 !important;
}

.ui-growl-item {
	border-color: #c4c9cc !important;
}

#tblAnalise\:filtroNome\:filter {
	display: block;
	width: 100% !important;
}
</style>

			<!-- 		<p:panel header="Acumulações em Análise"> -->
			<h:form id="frmAnalises" prependId="false">
				<!-- 				<div class="EmptyBox10" /> -->
				<p:fieldset legend="Dados para a consulta por análises">
					<div class="ui-g">
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<h:panelGroup>
								<h:outputText value="Situação da Análise: "
									styleClass="FontBold" />
								<p:selectOneMenu
									value="#{analiseAcumulacaoBean.tipoSituacaoAnalise}"
									converter="tipoSituacaoAnaliseConverter">
									<f:selectItem itemLabel="Selecione a Situação da Análise"
										itemValue="#{null}" />
									<f:selectItems
										value="#{analiseAcumulacaoBean.listaTipoSituacaoAnalise}"
										var="situacao" itemLabel="#{situacao.descricao}"
										itemValue="#{situacao}" />
								</p:selectOneMenu>
							</h:panelGroup>
						</div>
					</div>
					<div class="ui-g">
						<div class="ui-g-12">
							<p:commandButton process="@form" style="width: auto;"
								value="Pesquisar"
								action="#{analiseAcumulacaoBean.carregaAnalises()}"
								icon="fa fa-fw fa-search white" update=":tblAnalise" />
						</div>
					</div>
				</p:fieldset>
			</h:form>

			<div class="EmptyBox5" />

			<p:messages id="messages" globalOnly="true" showDetail="true"
				showSummary="true" />

			<div class="EmptyBox5" />
			<h:form prependId="false">

				<p:fieldset legend="Resultados da consulta">

					<c:if
						test="#{loginBean.getPermissao('SICAP_ADMINISTRADOR') or loginBean.perfilAuditoria}">
						<p:commandButton update="tblAnalise dlgNotifAnl messages"
							style="width: auto;" styleClass="OrangeButton"
							process="@this tblAnalise"
							actionListener="#{analiseAcumulacaoBean.checaAcumulacoesParaNotificar()}"
							icon="fa fa-fw fa-exclamation-triangle white" value="Notificar">
							<f:setPropertyActionListener
								value="#{analiseAcumulacaoBean.listaAcumulacaoSelecionadas}"
								target="#{notificarAnaliseBean.acumulacoes}" />
						</p:commandButton>

						<p:commandButton update="tblAnalise dlgConclAnl messages"
							style="width: auto;"
							actionListener="#{analiseAcumulacaoBean.checaAcumulacoesParaConcluir()}"
							value="Concluir Análise" process="@this tblAnalise"
							icon="fa fa-fw fa-check-square-o white">
							<f:setPropertyActionListener
								value="#{analiseAcumulacaoBean.listaAcumulacaoSelecionadas}"
								target="#{concluirAnaliseBean.listaAnaliseAcumulacao}" />

							<f:setPropertyActionListener value="#{loginBean.usuario}"
								target="#{concluirAnaliseBean.resultadoAnalise.usuario}" />
						</p:commandButton>
					</c:if>




					<div class="EmptyBox10" />
					<p:dataTable id="tblAnalise" widgetVar="tblAnaliseVar"
						tableStyle="table-layout: auto" reflow="true"
						value="#{analiseAcumulacaoBean.listaAcumulacao}" var="acumulacao"
						paginatorAlwaysVisible="false" paginator="true" rows="10"
						rowKey="#{acumulacao.acumulacaoId}"
						selection="#{analiseAcumulacaoBean.listaAcumulacaoSelecionadas}"
						paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
						rowsPerPageTemplate="10,20,30,40,50"
						currentPageReportTemplate="página {currentPage} de {totalPages}"
						emptyMessage="Nenhuma Acumulação encontrada">


						<p:column selectionMode="multiple"
							style="width:30px;text-align:center;" />

						<p:column headerText="Cód. Acumulação"
							filterBy="#{acumulacao.acumulacaoId}" filterMatchMode="contains"
							styleClass="TexAlCenter">
							<h:outputText value="#{acumulacao.acumulacaoId}" />
						</p:column>

						<p:column headerText="Competência" styleClass="TexAlCenter">
							<h:outputText
								value="#{acumulacao.acumulacaoMes}/#{acumulacao.acumulacaoAno}" />
						</p:column>

						<p:column headerText="CPF" width="8%" styleClass="TexAlCenter">
							<h:outputText value="#{acumulacao.acumulacaoCpf}">
								<f:converter converterId="converter.CpfConverter" />
							</h:outputText>
						</p:column>
						<p:column id="filtroNome" headerText="Nome"
							filterBy="#{acumulacao.acumulacaoNome}"
							filterMatchMode="contains" width="25%">
							<h:outputText value="#{acumulacao.acumulacaoNome}" />
						</p:column>
						<p:column headerText="Pontuação" styleClass="TexAlCenter">
							<h:outputText value="#{acumulacao.acumulacaoPontuacao}">
								<f:convertNumber pattern="#0.00" locale="pt_br" />
							</h:outputText>
						</p:column>

						<p:column headerText="Situação da Acumulação"
							styleClass="TexAlCenter">
							<h:outputText value="#{acumulacao.acumulacaoSituacaoDescricao}" />
						</p:column>
						<p:column headerText="Início da Análise" styleClass="TexAlCenter">
							<h:outputText
								value="#{acumulacao.analiseDataCriacao.toLocalDate()}">
								<f:converter converterId="converter.DateConverter" />
							</h:outputText>
						</p:column>
						<p:column headerText="Processo Eletrônico"
							filterBy="#{acumulacao.analiseNumeroProcesso}"
							filterMatchMode="contains" styleClass="TexAlCenter">
							<h:outputText value="#{acumulacao.analiseNumeroProcesso}" />
						</p:column>
						<p:column headerText="Ações" style="white-space: nowrap"
							styleClass="TexAlCenter">
							<p:commandButton id="btnVerHst" title="Histórico da Análise"
								style="width: auto;" process="@this"
								disabled="#{acumulacao.analiseId == null}"
								icon="fa fa-fw fa-list white" update="dlgAcmHst"
								oncomplete="PF('dlgAcmHstVar').show()">
								<f:setPropertyActionListener value="#{acumulacao.analiseId}"
									target="#{analiseHistoricoBean.idAnalise}" />
							</p:commandButton>

							<p:commandButton id="btnVerDet" title="Vinculos"
								style="width: auto;" icon="fa fa-fw fa-search white" update="dlgAcmVinc"
								process="@this" oncomplete="PF('dlgAcmVincVar').show()">
								<f:setPropertyActionListener value="#{acumulacao.acumulacaoId}"
									target="#{detalhamentoAcumulacaoBean.idAcumulacao}" />
							</p:commandButton>

							<p:commandButton style="width: auto;"
								disabled="#{acumulacao.analiseDocumentosEntregues == 0 and acumulacao.analiseRelatorios == 0}"
								id="btnVerDoc" title="Documentos"
								icon="fa fa-fw fa-files-o white"
								actionListener="#{analiseAcumulacaoBean.viewDocumentos(acumulacao)}">

								<f:setPropertyActionListener value="2"
									target="#{analiseAcumulacaoBean.nivelAtual}" />
							</p:commandButton>
							<p:commandButton style="width: auto;" id="btnProt"
								title="Protocolo" icon="fa fa-fw fa-file-text-o white"
								oncomplete="PF('dlgProtocolo').show()"
								disabled="#{acumulacao.analiseNumeroProcesso != null or !acumulacao.acumulacaoSituacaoDescricao.contains('In')}">
								<f:setPropertyActionListener value="#{acumlacao}"
									target="#{acumulacaoProtocoloBean.analiseAcumulacao}" />
							</p:commandButton>

						</p:column>


						<f:facet name="footer">
							<h:outputLabel
								value="Total de acumulações: #{analiseAcumulacaoBean.listaAcumulacao.size()}" />
						</f:facet>
					</p:dataTable>
					<p:spacer />
					<!-- 		<p:fieldset legend="Legenda"> -->
					<h:panelGrid columns="8">
						<div class="box corVerde"></div>
						<h:outputText value="Disponível para análise (3ª fase)"
							styleClass="FontBold" />
						<div class="box corLaranja"></div>
						<h:outputText value=" Parcialmente enviado, dentro do prazo"
							styleClass="FontBold" />
						<div class="box corVermelho"></div>
						<h:outputText
							value="Prazo vencido e não enviado por alguma das entidades"
							styleClass="FontBold" />
					</h:panelGrid>

				</p:fieldset>
			</h:form>



			<p:dialog id="dlgAcmHst" position="top"
				style="margin: 25px 0px 25px;" fitViewport="true"
				widgetVar="dlgAcmHstVar" modal="true" header="Histórico da Análise"
				showEffect="fade" hideEffect="fade" resizable="false"
				closeOnEscape="true" responsive="true" width="80%">
				<ui:include src="historico.xhtml" />
			</p:dialog>
			<p:dialog id="dlgAcmVinc" position="top"
				style="margin: 25px 0px 25px;" fitViewport="true"
				widgetVar="dlgAcmVincVar" modal="true"
				header="Detalhamento da Acumulação" showEffect="fade"
				hideEffect="fade" resizable="false" closeOnEscape="true"
				responsive="true" width="80%">
				<ui:include src="detalhamento.xhtml" />
			</p:dialog>
			<p:dialog id="dlgNotifAnl" position="top" widgetVar="dlgNotifAnlVar"
				modal="true" header="Notificar Acumulação" showEffect="fade"
				hideEffect="fade"
				visible="#{not empty notificarAnaliseBean.acumulacoes}"
				style="margin: 25px 0px 25px;" fitViewport="true" resizable="false"
				closeOnEscape="true" responsive="true" width="80%">
				<ui:include src="notificar.xhtml" />

			</p:dialog>
			<p:dialog id="dlgConclAnl" position="top"
				style="margin: 25px 0px 25px;" fitViewport="true"
				widgetVar="dlgConclAnlVar" modal="true" header="Concluir Análise"
				showEffect="fade" hideEffect="fade"
				visible="#{not empty concluirAnaliseBean.listaAnaliseAcumulacao}"
				resizable="false" closeOnEscape="true" responsive="true" width="70%">
				<ui:include src="concluir.xhtml" />

			</p:dialog>

			<p:dialog id="dlgProtocoloId" position="top"
				style="margin: 25px 0px 25px;" fitViewport="true"
				widgetVar="dlgProtocolo" blockScroll="true" modal="true"
				header="Criar Protocolo" showEffect="fade" hideEffect="fade"
				closeOnEscape="true" resizable="false" responsive="true" width="80%">
				<ui:include src="protocolo.xhtml" />
			</p:dialog>



		</ui:define>
	</ui:composition>
</h:body>
</html>