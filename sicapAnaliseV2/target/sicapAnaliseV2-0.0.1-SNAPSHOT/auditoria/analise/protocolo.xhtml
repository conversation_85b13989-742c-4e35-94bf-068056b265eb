<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">

<h:body>
	<h:form prependId="false" id="formProtocolo">
		<p:messages id="mensagens" />
		<p:fieldset legend="Informações para abertura do protocolo">
			<div class="ui-g">
				<div class="ui-g-12 ui-md-6 ui-lg-3">
					<h:panelGroup>
						<h:outputText value="CPF Usuário responsável:"
							styleClass="FontBold" />
						<p:inputText readonly="true"
							value="#{acumulacaoProtocoloBean.protocolo.cpfUsuarioSistema}" />
					</h:panelGroup>
				</div>
				<div class="ui-g-12 ui-md-6 ui-lg-9">
					<h:panelGroup>
						<h:outputText value="Nome Usuário responsável:"
							styleClass="FontBold" />
						<p:inputText readonly="true" value="#{loginBean.usuario.nome}" />

					</h:panelGroup>
				</div>
			</div>
			<div class="EmptyBox5"/>
			<p:tabView activeIndex="#{acumulacaoProtocoloBean.tabIndex}">
				<p:tab title="Dados Gerais">
					<div class="ui-g">
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<h:panelGroup>
								<h:outputText value="Tipo do Processo:" styleClass="FontBold" />
								<p:selectOneMenu required="true" autoWidth="false"
									requiredMessage="Informe o tipo do processo"
									value="#{acumulacaoProtocoloBean.protocolo.nomeTipoProcesso}">
									<f:selectItem itemLabel="Selecione o Tipo do Processo"
										itemValue="" />
									<f:selectItems
										value="#{acumulacaoProtocoloBean.listaTipoProcesso()}"
										var="tipoProcesso" itemLabel="#{tipoProcesso}"
										itemValue="#{tipoProcesso}" />
								</p:selectOneMenu>
							</h:panelGroup>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<h:panelGroup>
								<h:outputText value="Assunto do Processo:" styleClass="FontBold" />
								<p:inputText style="text-transform: uppercase" readonly="true"
									value="#{acumulacaoProtocoloBean.protocolo.assunto}" />
							</h:panelGroup>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<h:panelGroup>
								<h:outputText value="Classe do Processo:" styleClass="FontBold" />
								<p:inputText required="true" requiredMessage="Informe a classe"
									style="text-transform: uppercase" readonly="true"
									value="#{acumulacaoProtocoloBean.protocolo.classe}" />
							</h:panelGroup>
						</div>
					</div>
					<div class="ui-g">
						<div class="ui-g-12 ui-md-6 ui-lg-4">
							<h:panelGroup>
								<h:outputText value="Ano de Exercício do Protocolo e Processo:"
									styleClass="FontBold" />
								<p:inputNumber required="true" decimalPlaces="0"
									thousandSeparator="" requiredMessage="Informe o exercício"
									value="#{acumulacaoProtocoloBean.protocolo.exercicio}" />
							</h:panelGroup>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-8">
							<h:panelGroup>
								<h:outputText value="Objeto/Observação do Protocolo:"
									styleClass="FontBold" />
								<p:inputText required="true" style="text-transform: uppercase"
									requiredMessage="Informe o objeto/observação"
									value="#{acumulacaoProtocoloBean.protocolo.objeto}" />
							</h:panelGroup>
						</div>
					</div>
					<div class="ui-g">
						<div class="ui-g-12 ui-md-6 ui-lg-3">
							<h:panelGroup>
								<h:outputText value="Ente associado ao Protocolo:"
									styleClass="FontBold" />

								<p:selectOneMenu required="true"
									requiredMessage="Informe o Ente"
									value="#{acumulacaoProtocoloBean.protocolo.ente}">
									<f:selectItem itemLabel="Selecione o Ente" />
									<f:selectItems value="#{acumulacaoProtocoloBean.entes}"
										var="ente" itemValue="#{ente}" itemLabel="#{ente.descricao}" />
								</p:selectOneMenu>
							</h:panelGroup>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-5">
							<h:panelGroup>
								<h:outputText value="Entidade associada ao Protocolo:"
									styleClass="FontBold" />


								<p:selectOneMenu converter="entidadeConverter" required="true"
									autoWidth="false" requiredMessage="Informe a Entidade"
									value="#{acumulacaoProtocoloBean.protocolo.entidade}">
									<f:selectItem itemLabel="Selecione a Entidade" />
									<f:selectItems value="#{acumulacaoProtocoloBean.entidades}"
										var="entidade" itemValue="#{entidade}"
										itemLabel="#{entidade.nome}" />
								</p:selectOneMenu>
							</h:panelGroup>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-3">
							<h:panelGroup>
								<h:panelGroup>
									<h:outputText value="Tipo do Documento do Protocolo:"
										styleClass="FontBold" />
									<p:selectOneMenu required="true" autoWidth="false"
										requiredMessage="Informe o tipo do documento"
										value="#{acumulacaoProtocoloBean.protocolo.tipoDocumento}">
										<f:selectItem itemLabel="Selecione o Tipo do Documento"
											itemValue="" />
										<f:selectItems
											value="#{acumulacaoProtocoloBean.listaTipoDocumento()}"
											var="tipoDocumento" itemLabel="#{tipoDocumento}"
											itemValue="#{tipoDocumento}" />
									</p:selectOneMenu>
								</h:panelGroup>
							</h:panelGroup>
						</div>
						<div class="ui-g-12 ui-md-6 ui-lg-1">
							<h:panelGroup>
								<h:outputText value="Nº Remessa:" styleClass="FontBold" />
								<p:inputNumber readonly="true" decimalPlaces="0"
									thousandSeparator=""
									value="#{acumulacaoProtocoloBean.protocolo.numRemessa}" />
							</h:panelGroup>
						</div>
					</div>
				</p:tab>
				<p:tab title="Arquivos">
					<p:dataTable tableStyle="table-layout: auto" reflow="true"
						rows="10" paginator="true" paginatorPosition="bottom"
						id="tblArquivos" paginatorAlwaysVisible="false" rowIndexVar="idx"
						value="#{acumulacaoProtocoloBean.documentos}" var="documento" emptyMessage="Nenhum documento selecionado">
						<p:column headerText="#">
							<h:outputText value="#{idx + 1}" />
						</p:column>
						<p:column headerText="Nome">
							<h:outputText value="#{documento.nomeDocumento}" />
						</p:column>
						<p:column headerText="Caminho">
							<h:outputText value="#{documento.caminhoDocumento}" />
						</p:column>
						<p:column headerText="Tipo Arquivo">
							<h:outputText value="application/pdf" />
						</p:column>
					</p:dataTable>
				</p:tab>
				<p:tab title="Interessado/Responsáveis">
					<h:outputText value="Interessado" styleClass="Fs20 FontBold" />
					<p:fieldset>
						<div class="ui-g">

							<div class="ui-g-12 ui-md-6 ui-lg-2">
								<h:panelGroup>
									<h:outputText value="CPF/CNPJ:" styleClass="FontBold" />
									<p:inputText id="cpfCnpjInteressado"
										value="#{acumulacaoProtocoloBean.protocolo.interessado.cpfCnpj}">
										<f:ajax event="blur"
											listener="#{acumulacaoProtocoloBean.mascaraCpfCnpj()}"
											render="cpfCnpjInteressado" />
									</p:inputText>
								</h:panelGroup>
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-5">
								<h:panelGroup>
									<h:outputText value="Nome/Razão Social:" styleClass="FontBold" />
									<p:inputText
										value="#{acumulacaoProtocoloBean.protocolo.interessado.nome}" />
								</h:panelGroup>
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-3">
								<h:panelGroup>
									<h:outputText value="E-mail:" styleClass="FontBold" />
									<p:inputText
										value="#{acumulacaoProtocoloBean.protocolo.interessado.email}" />
								</h:panelGroup>
							</div>
							<div class="ui-g-12 ui-md-6 ui-lg-2"></div>

						</div>
					</p:fieldset>
					<div class="EmptyBox5" />

					<h:outputText value="Responsáveis" styleClass="Fs20 FontBold" />
					<p:fieldset>

						<p:panelGrid id="cadResp" columns="4" layout="grid"
							styleClass="showcase-text-align-left"
							columnClasses="ui-g-12 ui-md-6 ui-lg-2, ui-g-12 ui-md-6 ui-lg-5, ui-g-12 ui-md-6 ui-lg-3, ui-g-12 ui-md-6 ui-lg-2">
							<h:panelGroup>
								<h:outputText value="CPF/CNPJ:" styleClass="FontBold" />
								<p:inputText id="cpfCnpjResponsavel"
									value="#{acumulacaoProtocoloBean.responsavel.cpfCnpj}">
									<f:ajax event="blur"
										listener="#{acumulacaoProtocoloBean.mascaraCpfCnpj()}"
										render="cpfCnpjResponsavel" />
								</p:inputText>
							</h:panelGroup>

							<h:panelGroup>
								<h:outputText value="Nome/Razão Social:" styleClass="FontBold" />
								<p:inputText id="respNomRaz"
									value="#{acumulacaoProtocoloBean.responsavel.nome}" />

							</h:panelGroup>

							<h:panelGroup>
								<h:outputText value="E-mail:" styleClass="FontBold" />
								<p:inputText id="respEmail"
									value="#{acumulacaoProtocoloBean.responsavel.email}" />

							</h:panelGroup>

							<h:panelGroup>
								<p:spacer />
								<p:commandButton value="Adicionar Responsável" process="cadResp"
									action="#{acumulacaoProtocoloBean.addResponsavel()}"
									update=":mensagens cadResp tblResp" />
							</h:panelGroup>

						</p:panelGrid>

						<div class="EmptyBox5" />
						<div class="ui-g">
							<div class="ui-g-12">
								<p:dataTable id="tblResp" paginatorPosition="bottom" emptyMessage="Nenhum responsável adicionado"
									value="#{acumulacaoProtocoloBean.protocolo.responsaveisContainer.responsaveis}"
									var="responsavel" tableStyle="table-layout: auto" reflow="true">
									<p:column headerText="CPF/CNPJ" width="20%">
										<h:outputText value="#{responsavel.cpfCnpj}" />
									</p:column>
									<p:column headerText="Nome/Razão Social" width="50%">
										<h:outputText value="#{responsavel.nome}" />
									</p:column>
									<p:column headerText="E-mail" width="30%">
										<h:outputText value="#{responsavel.email}" />
									</p:column>
								</p:dataTable>
							</div>
						</div>
					</p:fieldset>
				</p:tab>


			</p:tabView>
			<div class="EmptyBox5" />
			<p:commandButton value="Gerar Protocolo" update="@form"
				action="#{acumulacaoProtocoloBean.gerar()}"
				icon="fa fa-fw fa-check white" style="width: auto;">

			</p:commandButton>
			<p:commandButton type="button" styleClass="RedButton"
				style="width: auto;" onclick="PF('dlgProtocolo').hide()"
				icon="fa fa-fw fa-close white" value="Cancelar">

			</p:commandButton>
		</p:fieldset>
	</h:form>
</h:body>
</html>
