<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">


	<style>
.ui-growl-image-info {
	background-image:
		url("#{resource['primefaces-sentinel:images/warn-blue.svg']}")
		!important; /* test url */
}

.ui-growl-image-info+div.ui-growl-message {
	border-color: #cee4f5 !important;
	color: #63bce2 !important;
}

.ui-growl-item {
	border-color: #c4c9cc !important;
}
</style>
	<h:form id="frmDistribuicaoAcumulacao" prependId="false">
		<p:messages id="mensagens" showDetail="true" showSummary="true" severity="error" />

		<p:fieldset legend="Parâmetros para distribuição das acumulações">


			<div class="ui-g">
				<div class="ui-g-12 ui-lg-6">
					<h:panelGroup>
						<h:outputText value="Auditor Responsável:" styleClass="FontBold" />
						<p:selectOneMenu filter="true" filterMatchMode="contains"
							converter="usuarioConverter"
							value="#{distribuicaoAcumulacaoBean.auditorGeral}">
							<f:selectItem value="#{null}" itemLabel="Selecione um Auditor" />
							<f:selectItems value="#{distribuicaoAcumulacaoBean.listaAuditor}"
								var="auditor" itemLabel="#{auditor.nome}" itemValue="#{auditor}" />
						</p:selectOneMenu>
					</h:panelGroup>
				</div>
			</div>
			<div class="ui-g">
				<div class="ui-g-12 ui-lg-6">
					<h:panelGroup>
						<h:outputText value="Despacho:" styleClass="FontBold" />
						<p:inputTextarea rows="2"
							value="#{distribuicaoAcumulacaoBean.despachoGeral}" />
					</h:panelGroup>
				</div>
			</div>

			<div class="ui-g">
				<div class="ui-g-12">
					<p:commandButton update=":frmTblDistribuicaoAcumulacao @form"
						style="width:auto;" icon="fa fa-fw fa-check white"
						value="Confirmar"
						action="#{distribuicaoAcumulacaoBean.iniciarDistribuicao()}" />
					<p:commandButton type="button" icon="fa fa-fw fa-close white"
						style="width:auto;" styleClass="RedButton" value="Cancelar"
						onclick="PF('dlgIniciarDistribuicaoVar').hide()" />
				</div>
			</div>
			<p:ajaxStatus onstart="PF('statusAjaxDialog').show();"
						onsuccess="PF('statusAjaxDialog').hide();" />
		</p:fieldset>



	</h:form>

	<div class="EmptyBox10" />

	<h:form id="frmTblDistribuicaoAcumulacao" prependId="false">
		<p:fieldset legend="Acumulações para distribuição">
			<p:dataTable id="tblDistribuicaoAcumulacao"
				tableStyle="table-layout: auto" reflow="true"
				paginatorAlwaysVisible="false" paginator="true" editable="true"
				paginatorPosition="bottom"
				paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
				rows="5"
				currentPageReportTemplate="página {currentPage} de {totalPages}"
				value="#{distribuicaoAcumulacaoBean.listaDistribuicaoAcumulacao}"
				var="distribuicaoAcumulacao">

				<p:column headerText="Cód. Acumulação" width="8%"
					styleClass="TexAlCenter">
					<h:outputText value="#{distribuicaoAcumulacao.acumulacao.id}" />
				</p:column>
				<p:column headerText="CPF" styleClass="TexAlCenter" width="15%">
					<h:outputText value="#{distribuicaoAcumulacao.acumulacao.cpf}">
						<f:converter converterId="converter.CpfConverter" />
					</h:outputText>
				</p:column>
				<p:column headerText="Nome">
					<h:outputText value="#{distribuicaoAcumulacao.acumulacao.nome}" />
				</p:column>

				<p:column headerText="Auditor Responsável">

					<p:cellEditor>
						<f:facet name="output">
							<h:outputText
								value="#{distribuicaoAcumulacao.usuarioAuditor.nome}" />
						</f:facet>
						<f:facet name="input">
							<p:selectOneMenu converter="usuarioConverter" filter="true"
								filterMatchMode="contains"
								value="#{distribuicaoAcumulacao.usuarioAuditor}">
								<f:selectItem value="#{null}" itemLabel="Selecione um Auditor" />
								<f:selectItems
									value="#{distribuicaoAcumulacaoBean.listaAuditor}"
									var="auditor" itemLabel="#{auditor.nome}"
									itemValue="#{auditor}" />
							</p:selectOneMenu>
						</f:facet>
					</p:cellEditor>
				</p:column>
				<p:column headerText="Despacho">
					<p:cellEditor>
						<f:facet name="output">
							<h:outputText value="#{distribuicaoAcumulacao.despacho}" />
						</f:facet>
						<f:facet name="input">
							<p:inputText value="#{distribuicaoAcumulacao.despacho}"></p:inputText>
						</f:facet>
					</p:cellEditor>
				</p:column>
				<p:column style="width:48px">
					<p:rowEditor cancelTitle="Cancelar" editTitle="Editar"
						saveTitle="Salvar" />
				</p:column>

			</p:dataTable>
		</p:fieldset>
	</h:form>

</ui:composition>
