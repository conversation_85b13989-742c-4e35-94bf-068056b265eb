<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:p="http://primefaces.org/ui" xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions" template="/resources/template/template.xhtml">

	<ui:define name="content">
		<p:fieldset legend="Auditoria - Trilhas de Acumulação de Cargos">
			<h:form prependId="false">


				<pe:masterDetail id="masterDetail" level="#{trilhaProcessamentoAcumulacaoBean.currentLevel}" showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="listaTrilhas" level="1">
						<f:facet name="label">
							<h:outputText value="Trilhas de Processamento" />
						</f:facet>

						<p:dataTable id="tabelaTrilhasProcessamento" var="trilha" widgetVar="tabelaTrilhasProcessamento"
							value="#{trilhaProcessamentoAcumulacaoBean.listaHistoricoProcessamentoTrilha}" emptyMessage="Nenhuma acumulação encontrada." rows="20"
							paginator="true" paginatorPosition="bottom" currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="20,30,40,50">

							<p:column headerText="Data do Processamento" width="7%" styleClass="TexAlCenter">
								<h:outputText value="#{trilha[4]}">
									<f:converter converterId="dateTimeConverter" />
								</h:outputText>
							</p:column>

							<p:column headerText="Mês de Referência" width="10%" styleClass="TexAlCenter">
								<h:outputText value="#{trilha[2]}" />
							</p:column>

							<p:column headerText="Ano de Referência" width="10%" styleClass="TexAlCenter">
								<h:outputText value="#{trilha[3]}" />
							</p:column>

							<p:column headerText="Quantidade de Servidores" width="10%" styleClass="TexAlCenter">
								<h:outputText value="#{trilha[5]}">
									<f:convertNumber type="number" />
								</h:outputText>
							</p:column>

							<p:column headerText="Quantidade de vínculos" width="10%" styleClass="TexAlCenter">
								<h:outputText value="#{trilha[6]}">
									<f:convertNumber type="number" />
								</h:outputText>
							</p:column>

							<p:column headerText="Ações" width="30%" sortBy="#{acumulacao[2]}">
								<p:commandLink value="a" process="@this" immediate="true">
<!-- 									<pe:selectDetailLevel listener="#{auditoriaAcumulacaoBean.detalharAcumulacao(acumulacao[0], acumulacao)}" /> -->
								</p:commandLink>
							</p:column>

						</p:dataTable>
					</pe:masterDetailLevel>

					<pe:masterDetailLevel id="detailDadosAcumulacao" level="2" contextVar="dados">
						<f:facet name="label">
							<h:outputText value="Dados da Acumulação" />
						</f:facet>
						<p:fieldset legend="Servidor">
							<h:panelGrid columns="2">

							</h:panelGrid>
						</p:fieldset>
						<p:spacer />
						<p:fieldset legend="Relatório de Acumulações">
							<h:panelGrid>

							</h:panelGrid>
						</p:fieldset>
						<p:spacer />
						<p:fieldset>
							<p:panel header="Acumulações">

							</p:panel>
							<h:panelGrid columns="2">
								<p:commandButton value="Voltar" style="margin-top: 10px;" icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
									<pe:selectDetailLevel step="-1" />
								</p:commandButton>
							</h:panelGrid>
						</p:fieldset>
					</pe:masterDetailLevel>
				</pe:masterDetail>
			</h:form>
		</p:fieldset>
	</ui:define>
</ui:composition>