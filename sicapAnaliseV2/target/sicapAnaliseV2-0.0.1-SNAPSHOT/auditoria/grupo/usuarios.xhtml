<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui">
	<h:form id="formUsuario" prependId="false">
		<p:messages showDetail="false" showSummary="true" />
		<p:commandButton rendered="#{usuarioInternoBean.operacao.equals('INCLUIR_USUARIO')}" style="width:auto;"
			styleClass="autoWidth" icon="fa fa-fw fa-plus-circle white"
			value="Incluir Usuário(s) Selecionado(s)"
			update="@form :formMD:tblGrupos :formMD:tblUsuariosGrupo"
			action="#{grupoBean.incluirUsuarioGrupo()}">
			<f:setPropertyActionListener
				value="#{usuarioInternoBean.listaUsuarioSelecionado}"
				target="#{grupoBean.listaUsuarioIncluir}" />
		</p:commandButton>
		
		<div class="EmptyBox10" />
		<p:dataTable id="tblUsuario"
			value="#{usuarioInternoBean.listaUsuario}" widgetVar="tblUsuarioVar"
			var="usuario" rowKey="#{usuario.id}" rowSelectMode="checkbox"
			selection="#{usuarioInternoBean.listaUsuarioSelecionado}" rows="10"
			paginatorAlwaysVisible="false" paginator="true"
			paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
			currentPageReportTemplate="página {currentPage} de {totalPages}"
			paginatorPosition="bottom" sortBy="#{usuario.nome}"
			sortOrder="ascending" emptyMessage="Nenhum servidor encontrado">


			<f:facet name="header">
							Usuários
						</f:facet>

			<p:column selectionMode="multiple"
				style="width:25px;text-align:center;" />

			<p:column headerText="CPF" width="10%" styleClass="TexAlCenter">
				<h:outputText value="#{usuario.cpf}">
					<f:converter converterId="converter.CpfConverter" />
				</h:outputText>
			</p:column>
			<p:column headerText="Nome" sortBy="#{usuario.nome}"
				filterBy="#{usuario.nome.toUpperCase()}" filterMatchMode="contains"
				width="30%">
				<h:outputText value="#{usuario.nome}" />
			</p:column>
			<p:column headerText="Setor" sortBy="#{usuario.setorNome}"
				filterBy="#{usuario.setorNome}" filterMatchMode="contains">
				<h:outputText value="#{usuario.setorNome.toUpperCase()}" />
			</p:column>
			<p:column headerText="Cargo" sortBy="#{usuario.cargoNome}">
				<h:outputText value="#{usuario.cargoNome.toUpperCase()}" />
			</p:column>

		</p:dataTable>
	</h:form>
</ui:composition>