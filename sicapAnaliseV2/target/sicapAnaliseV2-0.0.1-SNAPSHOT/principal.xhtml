<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:f="http://xmlns.jcp.org/jsf/core"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions"
	xmlns:tp="http://xmlns.jcp.org/jsf/composite/componetes"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
		<h:form prependId="false">
			<!-- 			<p:panel header="Situação das Remessas"> -->
			<!-- 				<p:spacer /> -->
			<p:fieldset legend="Dados para a consulta por remessas">

				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Exercício:" styleClass="FontBold" />
							<p:selectOneMenu value="#{principalBean.exercicio}"
								autoWidth="false">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{principalBean.listaExercicio}"
									var="exercicio" itemLabel="#{exercicio}"
									itemValue="#{exercicio}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Ente:" styleClass="FontBold" />
							<p:selectOneMenu value="#{principalBean.ente}" autoWidth="false"
								filter="true" filterMatchMode="contains">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{principalBean.listaEnte}" var="ente"
									itemLabel="#{ente.descricao}" itemValue="#{ente.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Entidade:" styleClass="FontBold" />
							<p:selectOneMenu value="#{principalBean.idEntidadeCjur}"
								autoWidth="false" filter="true" filterMatchMode="contains">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{principalBean.listaEntidade}"
									var="entidade" itemLabel="#{entidade.nome}"
									itemValue="#{entidade.idEntidadeCjur}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Poder:" styleClass="FontBold" />
							<p:selectOneMenu value="#{principalBean.poder}" autoWidth="false">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{principalBean.listaPoder}" var="poder"
									itemLabel="#{poder.descricao}" itemValue="#{poder.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Classificação Administrativa:"
								styleClass="FontBold" />
							<p:selectOneMenu
								value="#{principalBean.classificacaoAdministrativa}"
								autoWidth="false">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems
									value="#{principalBean.listaClassificacaoAdministrativa}"
									var="classificacao" itemLabel="#{classificacao.descricao}"
									itemValue="#{classificacao.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
					<div class="ui-g-12 ui-md-6 ui-lg-4">
						<h:panelGroup>
							<h:outputText value="Esfera:" styleClass="FontBold" />
							<p:selectOneMenu value="#{principalBean.esfera}"
								autoWidth="false">
								<f:selectItem itemLabel="Todos" itemValue="#{0}" />
								<f:selectItems value="#{principalBean.listaEsfera}" var="esfera"
									itemLabel="#{esfera.nome}" itemValue="#{esfera.id}" />
							</p:selectOneMenu>
						</h:panelGroup>
					</div>
				</div>
				<div class="ui-g">
					<div class="ui-g-12">
						<p:commandButton value="Pesquisar" style="width:auto;"
							icon="fa fa-fw fa-search white"
							actionListener="#{principalBean.pesquisar()}"
							update="masterDetail" />
					</div>
				</div>


			</p:fieldset>


			<!-- 				<br /> -->
			<div class="EmptyBox10"></div>
			<p:fieldset legend="Resultados da consulta">

				<pe:masterDetail id="masterDetail"
					level="#{principalBean.currentLevel}"
					showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Início" />
						</f:facet>

						<p:dataTable id="tbremessa" var="remessa" widgetVar="tbremessa"
						reflow="true"
							value="#{principalBean.listaSituacaoRemessas}"
							emptyMessage="Nenhum remessa encontrada." rows="20"
							paginator="true" paginatorAlwaysVisible="false"
							paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							rowsPerPageTemplate="20,30,40,50,100,200"
							currentPageReportTemplate="página {currentPage} de {totalPages}"
							paginatorPosition="bottom">

							<p:column headerText="Nome" width="20%"
								sortBy="#{remessa.entidade}">
								<h:outputText value="#{remessa.entidade}" />
							</p:column>

							<p:column headerText="Ano" 
								styleClass="TexAlCenter" sortBy="#{remessa.ano}">
								<h:outputText value="#{remessa.ano}" />
							</p:column>

							<p:column headerText="Jan" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoJaneiro}"
										styleClass="#{remessa.corJaneiro}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 1)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Fev" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoFevereiro}"
										styleClass="#{remessa.corFevereiro}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 2)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Mar" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoMarco}"
										styleClass="#{remessa.corMarco}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 3)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Abr" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoAbril}"
										styleClass="#{remessa.corAbril}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 4)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Mai" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoMaio}"
										styleClass="#{remessa.corMaio}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 5)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Jun" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoJunho}"
										styleClass="#{remessa.corJunho}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 6)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Jul" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoJulho}"
										styleClass="#{remessa.corJulho}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 7)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Ago" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoAgosto}"
										styleClass="#{remessa.corAgosto}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 8)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Set" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoSetembro}"
										styleClass="#{remessa.corSetembro}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 9)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Out" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoOutubro}"
										styleClass="#{remessa.corOutrubro}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 10)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Nov" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoNovembro}"
										styleClass="#{remessa.corNovembro}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 11)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="Dez"
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoDezembro}"
										styleClass="#{remessa.corDezembro}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 12)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="13º" 
								styleClass="TexAlCenter">
								<p:commandLink process="@this" immediate="true" title="detalhar">
									<h:outputText value="#{remessa.textoDecimo}"
										styleClass="#{remessa.corDecimo}">
									</h:outputText>
									<pe:selectDetailLevel
										listener="#{principalBean.detalharRemessa(remessa.idEntidadeCjur, remessa.ano, 12)}" />
								</p:commandLink>
							</p:column>

							<p:column headerText="-"
								styleClass="TexAlCenter">
								<p:spacer />
								<p:commandLink process="@this" immediate="true" title="detalhar"
									actionListener="#{principalBean.gerarCertidaoEntregaRemessa(remessa)}"
									ajax="false">
									<p:graphicImage name="pdf.png" library="imagens"
										title="Imprimir certidão de entrega das remessas" />
								</p:commandLink>
							</p:column>

							<f:facet name="footer">
								<h:outputLabel
									value="Total de entidades: #{principalBean.listaSituacaoRemessas.size()}" />
							</f:facet>

						</p:dataTable>

					</pe:masterDetailLevel>
					<pe:masterDetailLevel id="detailRemessa" level="2">
						<f:facet name="label">
							<h:outputText value="Detalhar Remessa" />
						</f:facet>
						<p:panel>
							<p:fieldset legend="Entidade">
								<h:panelGrid columns="2">
									<h:outputText value="Nome: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.entidade.nome}"
										styleClass="Fs12" />
									<h:outputText value="Ente: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.entidade.ente.descricao}"
										styleClass="Fs12" />
									<h:outputText value="Poder: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.entidade.poder.descricao}"
										styleClass="Fs12" />
									<h:outputText value="Classificação Administrativa: "
										styleClass="Fs12 FontBold" />
									<h:outputText
										value="#{principalBean.entidade.classificacaoAdministrativa.descricao}"
										styleClass="Fs12" />
									<h:outputText value="Esfera: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.entidade.esfera.nome}"
										styleClass="Fs12" />
								</h:panelGrid>
							</p:fieldset>
							<p:spacer />
							<p:fieldset legend="Remessa">
								<h:panelGrid columns="2">
									<h:outputText value="Competência: " styleClass="Fs12 FontBold" />
									<h:outputText
										value="#{principalBean.remessa.competencia.toString()}"
										styleClass="Fs12" />
									<h:outputText value="Arquivo: " styleClass="Fs12 FontBold" />
									<h:outputText
										value="#{principalBean.remessa.caminhoArquivoCompactado}"
										styleClass="Fs12" />
									<h:outputText value="Hash do Arquivo: "
										styleClass="Fs12 FontBold" />
									<h:outputText
										value="#{principalBean.remessa.hashArquivoCompactado}"
										styleClass="Fs12" />
									<h:outputText value="Situação: " styleClass="Fs12 FontBold" />
									<h:outputText
										value="#{principalBean.remessa.situacao.descricao}"
										styleClass="Fs12" />
									<h:outputText value="Protocolo de Entrega: "
										styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.remessa.protocoloEntrega}"
										styleClass="Fs12" />
									<h:outputText value="Data de Envio: "
										styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.remessa.dataEnvio}"
										styleClass="Fs12">
										<f:converter converterId="dateTimeConverter" />
									</h:outputText>
									<h:outputText value="Prazo de Envio: "
										styleClass="Fs12 FontBold" />
									<h:outputText
										value="#{principalBean.prazoEnvioRemessa.prazoEnvio}"
										styleClass="Fs12">
										<f:converter converterId="dateTimeConverter" />
									</h:outputText>
									<h:outputText value="Data da Assinatura: "
										styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.remessa.dataConfirmacao}"
										styleClass="Fs12">
										<f:converter converterId="dateTimeConverter" />
									</h:outputText>
									<h:outputText value="Tempo de atraso: "
										styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.tempoAtraso}"
										styleClass="Fs12" />
								</h:panelGrid>
							</p:fieldset>
							<p:spacer />
							<p:fieldset legend="Dados da Assinatura">
								<h:panelGrid columns="2">
									<h:outputText value="CPF: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.usuario.login}"
										styleClass="Fs12" />
									<h:outputText value="Nome: " styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.usuario.nome}"
										styleClass="Fs12" />
									<h:outputText value="Tipo de Responabilidade: "
										styleClass="Fs12 FontBold" />
									<h:outputText value="#{principalBean.usuario.tipoResponsavel}"
										styleClass="Fs12" />
									<h:outputText value="Hash da Assinatura: "
										styleClass="Fs12 FontBold" />
									<h:outputText
										value="#{principalBean.remessa.assinaturaRemessa.hashAssinatura}"
										styleClass="Fs12" />
								</h:panelGrid>
							</p:fieldset>
						</p:panel>

						<p:spacer />
						<h:panelGrid columns="2">
							<p:commandButton value="Voltar" style="margin-top: 10px;"
								icon="ui-icon-arrowthick-1-w" process="@this" immediate="true">
								<pe:selectDetailLevel step="-1" />
							</p:commandButton>
						</h:panelGrid>

					</pe:masterDetailLevel>
				</pe:masterDetail>

				<!-- 				<p:spacer /> -->
				<!-- 				<p:fieldset legend="Legenda"> -->

				<div class="EmptyBox10"></div>
				<h:panelGrid columns="8">
					<div class="box corVerde"></div>
					<h:outputText value="Tempestiva" styleClass="FontBold" />
					<div class="box corLaranja"></div>
					<h:outputText value="Intempestiva" styleClass="FontBold" />
					<div class="box corVermelho"></div>
					<h:outputText value="Não enviada" styleClass="FontBold" />
				</h:panelGrid>
			</p:fieldset>
			<!-- 			</p:panel> -->

		</h:form>
	</ui:define>
</ui:composition>