<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="titillium_websemibold" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="544" d="M152 0v287h239v-287h-239zM154 1403h237l-20 -909h-197z" />
<glyph unicode="&#x22;" horiz-adv-x="808" d="M123 1403h217l-12 -475h-197zM471 1403h215l-12 -475h-195z" />
<glyph unicode="#" d="M41 328v186h209v311h-209v187h209v346h194v-346h258v346h195v-346h209v-187h-209v-311h209v-186h-209v-328h-195v328h-258v-328h-194v328h-209zM444 514h258v311h-258v-311z" />
<glyph unicode="$" d="M121 1005.5q0 180.5 119.5 274.5t330.5 94h37l35 275h131l-37 -285l262 -37l-18 -176q-145 16 -266 25l-51 -396q207 -59 286.5 -137t79.5 -242q0 -213 -122 -318.5t-330 -105.5h-17l-29 -237q-131 6 -131 14l29 232q-131 12 -254 37l-41 8l23 176q162 -23 297 -31 l55 424q-213 57 -301 141t-88 264.5zM340 1006q0 -76 42 -115t155 -76l47 367q-244 -4 -244 -176zM586 172q223 8 223 217q0 70 -38 109t-134 67z" />
<glyph unicode="%" d="M41 1083q0 291 236.5 291t236.5 -291q0 -147 -61.5 -221t-175 -74t-175 74t-61.5 221zM209 1083q0 -82 15.5 -118.5t54 -36.5t54 36.5t15.5 118.5t-15.5 118t-54 36t-54 -36t-15.5 -118zM283 -12l454 1427l127 -43l-454 -1425zM633 272q0 291 236.5 291t236.5 -291 q0 -147 -61.5 -221t-175 -74t-175 74t-61.5 221zM799 271q0 -81 15 -118.5t54 -37.5t54.5 36.5t15.5 119.5t-14.5 118t-54.5 35t-55 -36t-15 -117z" />
<glyph unicode="&#x26;" horiz-adv-x="1425" d="M78 408q0 170 71.5 263t231.5 152q-80 92 -104.5 155.5t-24.5 164.5q0 141 105.5 223t292 82t286.5 -83t100 -229.5t-65.5 -230.5t-231.5 -180l250 -248q14 39 28.5 134.5t18.5 158.5l215 -4q-29 -250 -100 -420l244 -223l-127 -146l-232 201q-68 -94 -180.5 -147.5 t-247.5 -53.5q-289 0 -409.5 109t-120.5 322zM303 426q0 -250 281 -250q98 0 180 34t121 93l-387 387q-102 -39 -148.5 -100.5t-46.5 -163.5zM477 1098q0 -100 92 -199l45 -47q109 61 153 114.5t44 137.5q0 156 -167 156t-167 -162z" />
<glyph unicode="'" horiz-adv-x="460" d="M125 1403h217l-14 -475h-197z" />
<glyph unicode="(" horiz-adv-x="591" d="M88 655q0 170 57.5 399.5t114.5 354.5l57 127h211q-70 -176 -132 -458.5t-62 -461t48 -396.5t97 -347l49 -129h-211q-29 47 -73.5 156.5t-75.5 207t-55.5 237.5t-24.5 310z" />
<glyph unicode=")" horiz-adv-x="591" d="M63 -256q23 53 55 142t86 320.5t54 410t-48 408.5t-97 372l-50 139h211q29 -53 74 -172t76 -222.5t55.5 -248.5t24.5 -276.5t-23.5 -269.5t-58.5 -241q-70 -217 -123 -319l-25 -43h-211z" />
<glyph unicode="*" horiz-adv-x="866" d="M94 938l219 162l-213 151l80 111l211 -154l82 252l131 -43l-80 -254h258v-131h-260l80 -250l-127 -39l-84 252l-215 -161z" />
<glyph unicode="+" d="M106 414v200h365v367h201v-367h368v-200h-368v-371h-201v371h-365z" />
<glyph unicode="," horiz-adv-x="503" d="M53 -252l99 506h245l-164 -506h-180z" />
<glyph unicode="-" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="." horiz-adv-x="489" d="M125 0v295h240v-295h-240z" />
<glyph unicode="/" horiz-adv-x="907" d="M66 18l583 1444l197 -71l-586 -1442z" />
<glyph unicode="0" d="M61 666.5q0 377.5 128 542.5t384 165t383 -163.5t127 -541.5t-128 -535t-383 -157t-383 156t-128 533.5zM297 671.5q0 -280.5 66.5 -388t211 -107.5t209 107.5t64.5 388t-63.5 392.5t-211 112t-212 -112t-64.5 -392.5z" />
<glyph unicode="1" d="M180 1059l441 293h208v-1352h-227v1094l-317 -205z" />
<glyph unicode="2" d="M137 0v195l334 344q152 156 213 243.5t61 193t-59 150.5t-194 45q-127 0 -281 -28l-51 -8l-15 180q199 59 412 59q424 0 424 -377q0 -147 -64.5 -255.5t-230.5 -262.5l-289 -280h611v-199h-871z" />
<glyph unicode="3" d="M121 41l14 176q225 -41 397 -41q246 2 246 217q0 98 -63.5 149.5t-167.5 53.5h-273v190h273q76 0 140.5 62.5t64.5 152t-56.5 132.5t-154.5 43q-162 0 -334 -29l-53 -10l-19 172q190 66 417.5 65.5t331 -87.5t103.5 -275q0 -82 -31.5 -147.5t-64.5 -95.5t-82 -62 q109 -51 157 -111.5t48 -196.5q0 -215 -107.5 -318.5t-343.5 -103.5q-92 0 -202.5 15.5t-176.5 32.5z" />
<glyph unicode="4" d="M82 242v174l356 936h252l-377 -912h377v398h230v-398h147v-198h-147v-242h-230v242h-608z" />
<glyph unicode="5" d="M115 47l24 172q242 -41 414 -41q119 0 184.5 65.5t65.5 186.5t-57.5 174t-149.5 53q-166 0 -270 -41l-37 -14l-144 31l37 719h809v-201h-626l-35 -356q139 63 274 63q434 0 434 -405q0 -229 -119.5 -352.5t-338.5 -123.5q-92 0 -209 17.5t-187 34.5z" />
<glyph unicode="6" d="M84 676q0 698 549 698q154 0 332 -37l59 -12l-20 -178q-199 29 -361 29t-241.5 -100.5t-82.5 -280.5l48 16q147 47 247 47q459 0 459 -430q0 -221 -127 -336t-370.5 -115t-367.5 181.5t-124 517.5zM317 598q4 -422 271 -422q121 0 185.5 65.5t64.5 182.5t-63.5 175 t-177.5 58t-241 -45z" />
<glyph unicode="7" d="M150 1147v205h851v-271l-497 -1104l-213 62l481 1046v62h-622z" />
<glyph unicode="8" d="M61 344q0 137 47.5 211t149.5 145q-96 63 -135 132t-39 184q0 172 130 265t355.5 93t359.5 -93t134 -267q0 -123 -38 -185.5t-142 -128.5q104 -68 152 -138t48 -195q0 -205 -141 -297.5t-373 -92.5q-508 0 -508 367zM305 393q0 -211 267.5 -211t267.5 215q0 141 -172 195 h-197q-166 -54 -166 -199zM328 981q0 -123 143 -195h197q151 72 151 195q0 188 -245.5 188t-245.5 -188z" />
<glyph unicode="9" d="M70 922q0 211 130 331.5t353 120.5q256 0 381 -182t125 -538.5t-140.5 -516.5t-410.5 -160q-154 0 -332 37l-59 13l20 178q199 -29 371 -29q311 0 315 393l-49 -16q-156 -53 -250 -53q-221 0 -337.5 105.5t-116.5 316.5zM305 924q0 -224 238 -224q117 0 239 45l43 15 q-4 416 -272 416q-117 0 -182.5 -68t-65.5 -184z" />
<glyph unicode=":" horiz-adv-x="489" d="M125 0v295h240v-295h-240zM125 621v294h240v-294h-240z" />
<glyph unicode=";" horiz-adv-x="544" d="M74 -252l98 506h246l-164 -506h-180zM158 621v294h239v-294h-239z" />
<glyph unicode="&#x3c;" d="M131 422v184l840 393v-227l-602 -254l602 -266v-229z" />
<glyph unicode="=" d="M125 211v201h897v-201h-897zM125 616v201h897v-201h-897z" />
<glyph unicode="&#x3e;" d="M176 23v229l602 266l-602 254v227l840 -393v-184z" />
<glyph unicode="?" horiz-adv-x="903" d="M68 1358q190 68 346 67q215 0 316 -78.5t101 -252.5q0 -137 -33.5 -209t-138 -154t-138.5 -130t-34 -103v-68h-180q-49 63 -49 160q0 51 39 102t139.5 134t133 129t32.5 122q0 150 -217 150q-123 0 -258 -29l-47 -10zM272 0v287h238v-287h-238z" />
<glyph unicode="@" horiz-adv-x="1996" d="M90 535q0 481 247 716.5t695 235.5q891 0 891 -862v-21q0 -338 -94 -474t-285 -136q-92 0 -156.5 29.5t-82.5 58.5l-19 31q-180 -117 -343 -117t-260 113.5t-97 389t90 402.5t311 127q82 0 176 -37l33 -14v29h221v-377q0 -311 21 -379q6 -25 22.5 -43.5t33.5 -22.5t60 -4 t76 29q70 63 70 395v23q0 350 -159 507.5t-513 157.5t-535.5 -190.5t-181.5 -582.5t167 -569t552 -177l293 18l10 -194q-188 -18 -303 -19q-236 0 -403.5 46t-290.5 155q-246 215 -246 756zM811 508q0 -318 166 -318q86 0 233 70q-14 82 -14 277v266q-102 31 -164 31 q-131 0 -176 -75t-45 -251z" />
<glyph unicode="A" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM389 502h449l-172 710h-103z" />
<glyph unicode="B" horiz-adv-x="1253" d="M162 0v1403h532q215 0 321.5 -87t106.5 -278q0 -125 -40 -196.5t-126 -118.5q201 -78 201 -326q0 -397 -446 -397h-549zM389 197h309q113 0 169.5 47t56.5 161.5t-66 160.5t-164 46h-305v-415zM389 805h301q201 0 201 207q0 194 -209 194h-293v-401z" />
<glyph unicode="C" horiz-adv-x="1114" d="M106 706.5q0 389.5 111 554t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-211 -46 -399 -46q-307 0 -417 170t-110 559.5z" />
<glyph unicode="D" horiz-adv-x="1312" d="M162 0v1403h485q324 0 442.5 -164t118.5 -514q0 -178 -23.5 -302t-82.5 -224q-117 -199 -455 -199h-485zM389 201h258q203 0 271 149q33 76 44 162t11 213q0 254 -63.5 366.5t-262.5 112.5h-258v-1003z" />
<glyph unicode="E" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885z" />
<glyph unicode="F" horiz-adv-x="1083" d="M162 0v1403h870v-199h-643v-473h535v-199h-535v-532h-227z" />
<glyph unicode="G" horiz-adv-x="1255" d="M102 705.5q0 375.5 124 547.5t429 172q180 0 406 -41l72 -14l-9 -180q-248 33 -444.5 33t-269 -111t-72.5 -410t68.5 -412.5t277.5 -113.5q150 0 223 19v342h-174v200h400v-712q-55 -14 -224.5 -31t-244.5 -17q-319 0 -440.5 176.5t-121.5 552z" />
<glyph unicode="H" horiz-adv-x="1384" d="M162 0v1403h227v-598h606v598h230v-1403h-230v606h-606v-606h-227z" />
<glyph unicode="I" horiz-adv-x="550" d="M162 0v1403h227v-1403h-227z" />
<glyph unicode="J" horiz-adv-x="606" d="M39 55q109 0 148.5 33t39.5 145v1170h226l2 -1186q0 -219 -92.5 -290.5t-323.5 -71.5v200z" />
<glyph unicode="K" horiz-adv-x="1193" d="M162 0v1403h227v-633l209 16l287 617h260l-346 -705l360 -698h-264l-297 586l-209 -15v-571h-227z" />
<glyph unicode="L" horiz-adv-x="972" d="M162 0v1403h227v-1200h559v-203h-786z" />
<glyph unicode="M" horiz-adv-x="1740" d="M162 0v1403h395l313 -1096l314 1096h397v-1403h-229v1151h-31l-332 -1094h-237l-332 1094h-31v-1151h-227z" />
<glyph unicode="N" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227z" />
<glyph unicode="O" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5z" />
<glyph unicode="P" horiz-adv-x="1210" d="M162 0v1403h514q477 0 477 -471q0 -242 -119.5 -369t-357.5 -127h-287v-436h-227zM389 633h285q246 0 246 299q0 143 -59.5 208.5t-186.5 65.5h-285v-573z" />
<glyph unicode="Q" horiz-adv-x="1343" d="M100 693.5q0 371.5 128 551.5t443.5 180t442.5 -179t127 -552q0 -246 -52 -397.5t-171 -230.5l172 -277l-211 -98l-182 299q-39 -13 -125 -13q-319 0 -445.5 172.5t-126.5 544zM336 694.5q0 -278.5 69.5 -398.5t266 -120t265.5 118t69 398.5t-71 407.5t-263.5 127 t-264 -127t-71.5 -405.5z" />
<glyph unicode="R" horiz-adv-x="1265" d="M162 0v1403h532q475 0 475 -447q0 -299 -229 -405l231 -551h-249l-203 500h-330v-500h-227zM389 696h309q123 0 180.5 70.5t57.5 187.5q0 252 -242 252h-305v-510z" />
<glyph unicode="S" horiz-adv-x="1112" d="M86 1042.5q0 190.5 124 287.5t343 97q158 0 373 -36l71 -13l-18 -184q-287 33 -406 33q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -255q0 -221 -126 -331t-343 -110q-174 0 -385 41l-74 15l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130 t-258 92.5t-299 138.5t-98.5 278.5z" />
<glyph unicode="T" horiz-adv-x="1077" d="M27 1200v203h1024v-203h-396v-1200h-229v1200h-399z" />
<glyph unicode="U" horiz-adv-x="1331" d="M150 453v950h229v-954q1 -273 287 -273h1q285 0 285 273v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362z" />
<glyph unicode="V" horiz-adv-x="1216" d="M41 1403h238l278 -1202h102l279 1202h238l-345 -1403h-446z" />
<glyph unicode="W" horiz-adv-x="1865" d="M49 1403h240l200 -1206h43l267 1202h266l266 -1202h45l201 1206h240l-277 -1403h-366l-242 1124l-240 -1124h-368z" />
<glyph unicode="X" horiz-adv-x="1155" d="M27 0l417 678l-417 725h256l301 -553l305 553h239l-415 -713l415 -690h-254l-301 528l-307 -528h-239z" />
<glyph unicode="Y" horiz-adv-x="1124" d="M8 1403h256l297 -602l299 602h254l-436 -825v-578h-229v578z" />
<glyph unicode="Z" horiz-adv-x="1093" d="M86 0v217l649 946v41h-649v199h922v-219l-652 -946v-39h652v-199h-922z" />
<glyph unicode="[" horiz-adv-x="704" d="M150 -252v1786h477v-199h-252v-1388h252v-199h-477z" />
<glyph unicode="\" horiz-adv-x="964" d="M63 1372l193 86l645 -1431l-194 -82z" />
<glyph unicode="]" horiz-adv-x="704" d="M78 -53h252v1388h-252v199h477v-1786h-477v199z" />
<glyph unicode="^" d="M76 647l389 705h192l396 -705h-234l-254 488l-256 -488h-233z" />
<glyph unicode="_" horiz-adv-x="1280" d="M203 -166h876v-190h-876v190z" />
<glyph unicode="`" horiz-adv-x="520" d="M-20 1339l69 193l483 -207l-53 -145z" />
<glyph unicode="a" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 411 53q1 0 2 1q186 0 268 -81q83 -81 83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM293 301.5 q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5z" />
<glyph unicode="b" horiz-adv-x="1097" d="M135 2v1448h221v-465q141 62 256 62q211 0 304.5 -118t93.5 -417t-111 -417t-389 -118q-96 0 -305 19zM356 184q113 -8 154 -8q164 0 218 77t54 261t-46 259t-150 75q-98 0 -197 -31l-33 -10v-623z" />
<glyph unicode="c" horiz-adv-x="905" d="M90 516q0 287 100.5 409t327.5 122q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-207 -37 -311 -37q-233 0 -328.5 126t-95.5 413z" />
<glyph unicode="d" horiz-adv-x="1105" d="M88 503.5q0 284.5 103.5 414t322.5 129.5q74 0 234 -27v430h223v-1450h-221v53q-150 -76 -275 -76q-201 0 -294 121t-93 405.5zM313 510q0 -188 48.5 -261t153 -73t198.5 37l35 14v607q-123 20 -228 20q-207 0 -207 -344z" />
<glyph unicode="e" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5z" />
<glyph unicode="f" horiz-adv-x="708" d="M63 834v190h119v66q0 219 63.5 301t225.5 82l221 -21l-2 -182q-100 4 -166.5 4t-93.5 -40t-27 -147v-63h271v-190h-271v-834h-221v834h-119z" />
<glyph unicode="g" horiz-adv-x="1075" d="M86 -180q0 76 37 130t119 118q-68 45 -68 149q0 41 55 133l19 31q-147 88 -148 307q0 184 111 270t299 86q90 0 178 -20l31 -6l317 8v-180l-170 10q55 -72 56 -168q0 -201 -101.5 -277.5t-316.5 -76.5q-53 0 -90 8q-29 -70 -29 -107.5t38 -52t183 -16.5q244 -2 334 -65.5 t90 -239.5q0 -336 -485 -336q-233 0 -346 63.5t-113 231.5zM305 -160q0 -72 57.5 -102.5t196.5 -30.5q248 0 248 141q0 80 -44 101.5t-175 23.5l-201 13q-45 -37 -63.5 -69t-18.5 -77zM322 688q0 -94 45 -138t145 -44t144 44t44 138t-45 138t-145 44q-188 0 -188 -182z" />
<glyph unicode="h" horiz-adv-x="1118" d="M135 0v1450h223v-479q152 76 281 76q205 0 279.5 -116t74.5 -382v-549h-223v543q0 166 -35 235.5t-147 69.5q-98 0 -197 -33l-33 -12v-803h-223z" />
<glyph unicode="i" horiz-adv-x="493" d="M135 0v1024h223v-1024h-223zM135 1198v236h223v-236h-223z" />
<glyph unicode="j" horiz-adv-x="495" d="M-84 -299q143 90 182 146.5t39 191.5v985h221v-987q0 -201 -70.5 -297t-283.5 -205zM137 1198v236h221v-236h-221z" />
<glyph unicode="k" horiz-adv-x="1021" d="M135 0v1450h223v-838l127 13l242 399h250l-295 -479l311 -545h-252l-249 432l-134 -14v-418h-223z" />
<glyph unicode="l" horiz-adv-x="518" d="M147 0v1450h224v-1450h-224z" />
<glyph unicode="m" horiz-adv-x="1716" d="M135 0v1024h221v-63q145 86 267 86q178 0 260 -101q187 100 371 100q1 0 1 1q183 0 259 -114t75 -384v-549h-221v543q0 166 -34 235.5t-140 69.5q-92 0 -199 -41l-34 -14q16 -41 16 -263v-530h-221v526q0 182 -33 252t-143 70q-102 0 -191 -41l-31 -12v-795h-223z" />
<glyph unicode="n" horiz-adv-x="1118" d="M135 0v1024h221v-63q150 86 283 86q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-146 69.5q-104 0 -201 -41l-31 -12v-795h-223z" />
<glyph unicode="o" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264z" />
<glyph unicode="p" horiz-adv-x="1099" d="M135 -440v1464h221v-63q141 86 265 86q203 0 297 -124t94 -416t-107.5 -411t-351.5 -119q-84 0 -195 19v-436h-223zM358 186q82 -16 181 -16q139 0 192 80t53 275q0 323 -202 323q-96 0 -193 -43l-31 -14v-605z" />
<glyph unicode="q" horiz-adv-x="1095" d="M88 513q0 298 111.5 416t386.5 118q100 0 307 -19l68 -6v-1462h-222v479q-137 -62 -250 -62q-211 0 -306 119t-95 417zM311 510q0 -182 50.5 -258t154.5 -76q96 0 193 31l30 10v629q-98 8 -153 8q-162 0 -218.5 -81t-56.5 -263z" />
<glyph unicode="r" horiz-adv-x="739" d="M135 0v1024h221v-123q174 113 349 146v-224q-176 -35 -302 -90l-45 -18v-715h-223z" />
<glyph unicode="s" horiz-adv-x="960" d="M82 731q0 158 107.5 235.5t275.5 77.5q131 0 321 -32l64 -13l-4 -186q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -225.5t-103.5 -238t-302.5 -76q-125 0 -315 35l-64 11l8 186q246 -33 354.5 -33t154.5 26.5t46 88t-44 85t-210.5 51.5 t-244.5 87t-78 217z" />
<glyph unicode="t" horiz-adv-x="733" d="M51 834v190h131v297h221v-297h283v-190h-283v-451q0 -125 18.5 -166t94.5 -41l168 6l10 -178q-137 -27 -209 -27q-174 0 -238.5 80t-64.5 301v476h-131z" />
<glyph unicode="u" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t143 -63.5q111 0 203 41l31 12v795h221v-1024h-221v63q-150 -86 -277 -86q-211 0 -282.5 114t-71.5 398z" />
<glyph unicode="v" horiz-adv-x="1005" d="M39 1024h233l197 -834h66l204 834h228l-267 -1024h-397z" />
<glyph unicode="w" horiz-adv-x="1572" d="M55 1024h219l162 -834h41l195 814h229l195 -814h41l159 834h222l-218 -1024h-354l-160 702l-159 -702h-355z" />
<glyph unicode="x" horiz-adv-x="950" d="M37 0l297 514l-297 510h237l201 -354l203 354h237l-305 -504l305 -520h-237l-203 350l-201 -350h-237z" />
<glyph unicode="y" horiz-adv-x="1007" d="M41 1024h219l217 -834h55l218 834h221l-389 -1464h-220l123 440h-176z" />
<glyph unicode="z" horiz-adv-x="929" d="M84 0v199l494 626h-494v199h760v-199l-492 -626h492v-199h-760z" />
<glyph unicode="{" horiz-adv-x="731" d="M39 553v174q117 27 171 77t54 130l-14 260q0 193 91 271.5t312 86.5l2 -188q-109 -8 -148.5 -53t-39.5 -133l14 -256q0 -129 -43 -186.5t-180 -94.5q135 -35 179 -97.5t44 -193.5l-14 -239q0 -88 39 -136.5t145 -54.5v-188q-219 8 -310 89t-91 265l14 248q0 158 -225 219 z" />
<glyph unicode="|" horiz-adv-x="516" d="M147 -440v1890h222v-1890h-222z" />
<glyph unicode="}" horiz-adv-x="731" d="M76 1364l2 188q221 -8 312 -87t91 -271l-14 -260q0 -80 54 -130t171 -77v-174q-225 -61 -225 -219l14 -248q0 -184 -91 -265t-310 -89v188q106 6 145 54.5t39 136.5l-14 239q0 131 44 193.5t179 97.5q-137 37 -180 94.5t-43 186.5l14 256q0 88 -40 133t-148 53z" />
<glyph unicode="~" d="M131 569q127 106 246 107q57 0 210.5 -52.5t194.5 -52.5q72 0 181 64l34 20l19 -178q-37 -37 -107.5 -71.5t-127 -34.5t-215 52t-197.5 52t-93.5 -21.5t-89.5 -43.5l-34 -21z" />
<glyph unicode="&#xa1;" horiz-adv-x="507" d="M135 -379l21 909h196l21 -909h-238zM135 737v287h240v-287h-240z" />
<glyph unicode="&#xa2;" d="M190 510q0 422 369 448v222h186v-230l181 -24l-6 -170q-162 10 -250 10q-139 0 -195.5 -59.5t-56.5 -197.5t56 -194.5t202 -56.5l246 10l6 -170q-98 -20 -183 -24v-234h-184v226q-186 10 -278.5 114.5t-92.5 329.5z" />
<glyph unicode="&#xa3;" d="M158 0v190h161v492h-135v190h135v117q0 223 76 303t248 80q121 0 254 -31l43 -10l-6 -176q-150 18 -243 18.5t-122.5 -41.5t-29.5 -153v-107h327v-190h-327v-492h280l148 33l34 -186l-165 -37h-678z" />
<glyph unicode="&#xa5;" d="M27 1352h254l294 -484l293 484h254l-319 -547h225v-189h-330l-8 -36v-93h338v-188h-338v-299h-227v299h-344v188h344v93l-6 36h-338v189h229z" />
<glyph unicode="&#xa8;" horiz-adv-x="520" d="M-45 1243v234h213v-234h-213zM383 1243v234h213v-234h-213z" />
<glyph unicode="&#xa9;" horiz-adv-x="1318" d="M106 891q0 244 157 406.5t399.5 162.5t396 -166t153.5 -408.5t-153.5 -405.5t-396 -163t-399.5 165t-157 409zM217 888.5q0 -192.5 126 -327.5t315.5 -135t316.5 134t127 327.5t-128 330t-315.5 136.5t-314.5 -136.5t-127 -329zM420 889q0 182 59.5 256t198.5 74 q78 0 162 -27l-13 -148q-74 14 -134 14.5t-79.5 -35.5t-19.5 -129t20.5 -134t73.5 -41l139 12l13 -141q-66 -33 -185.5 -33t-177 75t-57.5 257z" />
<glyph unicode="&#xab;" horiz-adv-x="1175" d="M86 418v166l428 317v-225l-238 -170l238 -193v-227zM625 418v166l428 317v-225l-238 -170l238 -193v-227z" />
<glyph unicode="&#xad;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#xae;" horiz-adv-x="1318" d="M106 891q0 244 157 406.5t399.5 162.5t396 -166t153.5 -408.5t-153.5 -405.5t-395 -163t-399.5 165t-158 409zM217 888.5q0 -192.5 128 -327.5t316.5 -135t314.5 134t126 327.5t-128 330t-315.5 136.5t-314.5 -136.5t-127 -329zM422 571v633h256q233 0 233 -205 q0 -78 -19.5 -121.5t-70.5 -76.5l99 -230h-172l-76 203h-86v-203h-164zM584 897h84q86 0 86 91t-97 91h-73v-182z" />
<glyph unicode="&#xb4;" horiz-adv-x="548" d="M29 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#xb8;" horiz-adv-x="540" d="M72 -453l8 134q70 -4 113 -5q84 0 83 64q0 31 -19 43t-64 12h-64v207h96v-88q129 -2 186.5 -37t57.5 -139.5t-56.5 -157.5t-156.5 -53q-78 0 -158 14z" />
<glyph unicode="&#xbb;" horiz-adv-x="1177" d="M123 86v227l237 193l-237 170v225l428 -317v-166zM662 86v227l237 193l-237 170v225l428 -317v-166z" />
<glyph unicode="&#xbf;" horiz-adv-x="894" d="M66 -70q0 137 33.5 209t138 154t138.5 130t34 103v68h180q49 -63 49 -160q0 -51 -39 -102t-139.5 -134t-133 -129t-32.5 -122q0 -150 217 -150q123 0 258 29l47 10l12 -170q-190 -68 -346 -67q-215 0 -316 78.5t-101 252.5zM387 737v287h238v-287h-238z" />
<glyph unicode="&#xc0;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM309 1724l72 199l483 -211l-57 -156zM389 502h449l-172 710h-103z" />
<glyph unicode="&#xc1;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM352 1712l484 211l71 -199l-497 -168zM389 502h449l-172 710h-103z" />
<glyph unicode="&#xc2;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM240 1589l274 293h193l274 -293h-238l-131 137l-133 -137h-239zM389 502h449l-172 710h-103z" />
<glyph unicode="&#xc3;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM229 1763q12 14 32 36t74 56.5t107.5 34.5t179.5 -51t148 -51q45 0 127 67l29 23l49 -162q-31 -43 -93.5 -84t-111.5 -41t-178 51.5t-156 51.5q-51 0 -131 -68l-26 -22zM389 502h449l-172 710h-103z" />
<glyph unicode="&#xc4;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM291 1604v233h213v-233h-213zM389 502h449l-172 710h-103zM719 1604v233h213v-233h-213z" />
<glyph unicode="&#xc5;" horiz-adv-x="1226" d="M41 0l336 1360q-43 59 -43 137q0 111 80 175.5t199.5 64.5t199.5 -64.5t80 -175.5q0 -78 -43 -137l338 -1360h-230l-75 301h-539l-76 -301h-227zM389 502h449l-172 710h-103zM489 1497q0 -45 33 -69.5t90.5 -24.5t91 24.5t33.5 69.5t-33.5 70.5t-91 25.5t-90.5 -25.5 t-33 -70.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1787" d="M31 0l413 1419h1254v-219h-656v-371h533v-215h-533v-395h656v-219h-881v291h-469l-86 -291h-231zM406 510h411l2 690h-213z" />
<glyph unicode="&#xc7;" horiz-adv-x="1114" d="M106 719q0 377 111 541.5t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-188 -41 -381 -46v-63q129 -2 186.5 -37t57.5 -139.5t-56.5 -157.5t-156.5 -53q-78 0 -158 14l-26 6l8 134q70 -4 112 -5 q84 0 84 64q0 31 -19 43t-65 12h-63v187q-258 18 -353.5 189t-95.5 548z" />
<glyph unicode="&#xc8;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM311 1724l72 199l483 -211l-57 -156z" />
<glyph unicode="&#xc9;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM336 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xca;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM236 1589l274 293h192l275 -293h-238l-131 137l-133 -137h-239z" />
<glyph unicode="&#xcb;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM283 1604v233h213v-233h-213zM711 1604v233h213v-233h-213z" />
<glyph unicode="&#xcc;" horiz-adv-x="550" d="M-47 1724l72 199l483 -211l-57 -156zM162 0v1403h227v-1403h-227z" />
<glyph unicode="&#xcd;" horiz-adv-x="550" d="M20 1712l484 211l71 -199l-497 -168zM162 0v1403h227v-1403h-227z" />
<glyph unicode="&#xce;" horiz-adv-x="550" d="M-100 1589l274 293h193l274 -293h-238l-131 137l-133 -137h-239zM162 0v1403h227v-1403h-227z" />
<glyph unicode="&#xcf;" horiz-adv-x="550" d="M-45 1604v233h213v-233h-213zM162 0v1403h227v-1403h-227zM383 1604v233h213v-233h-213z" />
<glyph unicode="&#xd1;" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227zM322 1763q12 14 31.5 36t73.5 56.5t107.5 34.5t179.5 -51t148 -51q45 0 129 67l27 23l49 -162q-31 -43 -93.5 -84t-111.5 -41t-178 51.5t-156 51.5q-51 0 -131 -68l-26 -22z" />
<glyph unicode="&#xd2;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5zM367 1724l71 199l484 -211l-58 -156z" />
<glyph unicode="&#xd3;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5zM371 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xd4;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM303 1589l275 293h192l274 -293h-237l-131 137l-133 -137h-240zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127 t-264 -127t-71.5 -404.5z" />
<glyph unicode="&#xd5;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM295 1763q12 14 31.5 36t74 56.5t107.5 34.5t179 -51t149 -51q45 0 127 67l28 23l49 -162q-31 -43 -93 -84t-111.5 -41t-178.5 51.5t-155 51.5 q-51 0 -131 -68l-27 -22zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5zM352 1604v233h213v-233h-213zM780 1604 v233h213v-233h-213z" />
<glyph unicode="&#xd8;" horiz-adv-x="1343" d="M100 694q0 371 128 551t444 180q125 0 229 -32l119 256l168 -72l-127 -272q180 -164 180 -611q0 -373 -126 -545t-443 -172q-123 0 -219 25l-117 -252l-164 80l119 254q-190 154 -191 610zM336 698.5q0 -274.5 61 -387.5l416 893q-61 23 -141 23q-193 0 -264.5 -127 t-71.5 -401.5zM543 193q59 -17 129 -17q197 0 265.5 119t68.5 386t-56 388z" />
<glyph unicode="&#xd9;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM377 1724l72 199l483 -211l-58 -156z" />
<glyph unicode="&#xda;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM379 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xdb;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM295 1589l274 293h193l274 -293h-237l-131 137l-133 -137h-240z" />
<glyph unicode="&#xdc;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM346 1604v233h213v-233h-213zM774 1604v233h213v-233h-213z" />
<glyph unicode="&#xdd;" horiz-adv-x="1124" d="M8 1403h256l297 -602l299 602h254l-436 -825v-578h-229v578zM303 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xdf;" horiz-adv-x="1218" d="M135 0v1090q0 209 102.5 296t334 87t338 -73t106.5 -239q0 -109 -42 -163t-128 -93t-108.5 -57.5t-22.5 -47t33.5 -54t167 -89t189.5 -127t56 -182.5q0 -207 -94 -289t-319 -82q-117 0 -248 29l-47 10l8 181q188 -23 284 -23q186 0 187 141q0 61 -36 94t-167 95 q-137 59 -190.5 119.5t-53.5 153.5t37 144.5t122 87t113.5 64.5t28.5 86.5t-45 87t-166.5 29.5t-169 -43t-47.5 -154v-1079h-223z" />
<glyph unicode="&#xe0;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM227 1339l70 193l483 -207 l-53 -145zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM215 1325l483 207l70 -193 l-500 -159zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5z" />
<glyph unicode="&#xe2;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM180 1198l256 299h142 l260 -299h-209l-119 156l-119 -156h-211zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5z" />
<glyph unicode="&#xe3;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM131 1364l33 33 q18 18 71.5 50t97.5 32t143 -41t124 -41q45 0 129 55l27 18l49 -131l-31 -32q-20 -19 -72.5 -51.5t-95.5 -32.5t-142 41t-128 41q-49 0 -129 -52l-27 -18zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM170 1243v234h213v-234 h-213zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5zM598 1243v234h213v-234h-213z" />
<glyph unicode="&#xe5;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM293 301.5 q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5zM305 1311.5q0 97.5 65.5 163t162 65.5t163 -66.5t66.5 -163t-66.5 -161t-163 -64.5t-162 64.5t-65.5 162zM432 1312q0 -44 28.5 -74t73 -30t73 30t28.5 74t-28.5 73.5t-73 29.5t-73 -29.5 t-28.5 -73.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1624" d="M70 308.5q0 158.5 87 226t275 81.5l240 19v65q0 66 -36 100t-101 34q-154 0 -326 -17l-66 -6l-8 197q260 37 432 36.5t252 -106.5q100 106 305 106q418 0 418 -466l-14 -162h-635q2 -123 55 -179.5t199.5 -56t298.5 12.5l59 6l4 -174q-236 -47 -428 -47.5t-295 102.5 l-55 -25q-176 -78 -369 -78q-143 0 -217.5 86.5t-74.5 245zM292.5 302q-0.5 -134 110.5 -134q92 0 184.5 23.5t98.5 27.5q-16 96 -16 244l-236 -17q-141 -10 -141.5 -144zM893 590h430q0 139 -49 198.5t-163 59.5t-166 -61.5t-52 -196.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="905" d="M90 530.5q0 272.5 100.5 394.5t327.5 122q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-205 -37 -309 -37v-63q129 -2 186.5 -37t57.5 -139.5t-56.5 -157.5t-156.5 -53q-78 0 -158 14l-27 6 l9 134q70 -4 112 -5q84 0 84 64q0 31 -19.5 43t-64.5 12h-63v189q-180 23 -255 148.5t-75 398z" />
<glyph unicode="&#xe8;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM236 1339l69 193l483 -207l-53 -145zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM250 1325l483 207l70 -193l-500 -159zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5z" />
<glyph unicode="&#xea;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM203 1198l256 299h141l260 -299h-209l-119 156l-118 -156h-211zM309 590h432q0 152 -48 212t-162.5 60 t-167 -63.5t-54.5 -208.5z" />
<glyph unicode="&#xeb;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM205 1243v234h213v-234h-213zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5zM633 1243v234 h213v-234h-213z" />
<glyph unicode="&#xec;" horiz-adv-x="493" d="M-121 1339l70 193l483 -207l-53 -145zM135 0v1024h223v-1024h-223z" />
<glyph unicode="&#xed;" horiz-adv-x="493" d="M70 1325l483 207l70 -193l-500 -159zM135 0v1024h223v-1024h-223z" />
<glyph unicode="&#xee;" horiz-adv-x="493" d="M-90 1198l256 299h141l260 -299h-209l-118 156l-119 -156h-211zM135 0v1024h223v-1024h-223z" />
<glyph unicode="&#xef;" horiz-adv-x="493" d="M-82 1243v234h213v-234h-213zM135 0v1024h223v-1024h-223zM346 1243v234h213v-234h-213z" />
<glyph unicode="&#xf1;" horiz-adv-x="1118" d="M135 0v1024h221v-63l41 22q41 20 112 42t130 22q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-150.5 69.5t-227.5 -53v-795h-223zM236 1364l30 33q20 18 73.5 50t97.5 32t143.5 -41t124.5 -41q45 0 126 55l29 18l49 -131l-32 -32q-19 -19 -71 -51.5 t-95 -32.5t-142.5 41t-126.5 41q-51 0 -131 -52l-26 -18z" />
<glyph unicode="&#xf2;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM268 1339l70 193l483 -207l-53 -145zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264z" />
<glyph unicode="&#xf3;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM270 1325l484 207l69 -193l-499 -159zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264z" />
<glyph unicode="&#xf4;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM205 1198l256 299h141l260 -299h-209l-118 156l-119 -156h-211zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78 t-50.5 -264z" />
<glyph unicode="&#xf5;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM199 1364l30 33q20 18 73.5 50t97.5 32t143.5 -41t124.5 -41q45 0 127 55l28 18l49 -131l-32 -32q-19 -19 -71 -51.5t-95 -32.5t-142.5 41t-125.5 41 q-51 0 -132 -52l-26 -18zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264z" />
<glyph unicode="&#xf6;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM219 1243v234h213v-234h-213zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264zM647 1243v234h213v-234 h-213z" />
<glyph unicode="&#xf8;" horiz-adv-x="1085" d="M86 518q0 262 105.5 395.5t351.5 133.5q53 0 131 -13l84 207l139 -51l-86 -207q188 -113 188 -465q0 -270 -102 -405.5t-354 -135.5q-78 0 -140 13l-88 -215l-137 51l90 215q-182 117 -182 477zM311 508.5q0 -193.5 49 -264.5l244 608q-26 6 -61 6q-131 0 -181.5 -78 t-50.5 -271.5zM473 172q31 -6 70 -6q137 0 184 82t47 270.5t-53 263.5z" />
<glyph unicode="&#xf9;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t143 -63.5q111 0 203 41l31 12v795h221v-1024h-221v63q-150 -86 -277 -86q-211 0 -282.5 114t-71.5 398zM215 1339l70 193l483 -207l-53 -145z" />
<glyph unicode="&#xfa;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM289 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#xfb;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM188 1198l256 299h142l260 -299h-209l-119 156l-119 -156h-211z" />
<glyph unicode="&#xfc;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM240 1243v234h213v-234h-213zM668 1243v234h213v-234h-213z" />
<glyph unicode="&#xfd;" horiz-adv-x="1007" d="M41 1024h219l217 -834h55l218 834h221l-389 -1464h-220l123 440h-176zM279 1325l483 207l69 -193l-499 -159z" />
<glyph unicode="&#xff;" horiz-adv-x="1007" d="M41 1024h219l217 -834h55l218 834h221l-389 -1464h-220l123 440h-176zM184 1243v234h213v-234h-213zM612 1243v234h213v-234h-213z" />
<glyph unicode="&#x100;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM268 1628v166h680v-166h-680zM389 502h449l-172 710h-103z" />
<glyph unicode="&#x101;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM223 1247v168h578v-168 h-578zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5z" />
<glyph unicode="&#x102;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM252 1872h203q4 -60 46 -96.5t107.5 -36.5t107.5 36.5t46 96.5h203q-12 -133 -104.5 -213t-253.5 -80t-253 80t-102 213zM389 502h449l-172 710h-103z" />
<glyph unicode="&#x103;" horiz-adv-x="1040" d="M70 305.5q0 155.5 81.5 225t256.5 81.5l264 23v65q0 63 -37 98.5t-98 35.5q-152 0 -396 -21l-6 191l70 10q209 31 344 30q172 0 258 -81t86 -261v-434q2 -51 21.5 -72.5t66.5 -27.5l-6 -191q-111 0 -166 16.5t-104 57.5l-50 -18q-49 -18 -134 -37t-159 -19 q-141 0 -216.5 86.5t-75.5 242zM150 1518h229q12 -117 139 -117t137 117h230q-10 -141 -104.5 -227.5t-262.5 -86.5t-262 85t-106 229zM292.5 303q-0.5 -135 110.5 -135q117 0 269 45v250l-238 -17q-141 -8 -141.5 -143z" />
<glyph unicode="&#x104;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-29q-45 -31 -86 -93.5t-41 -94t20.5 -53t53.5 -21.5l92 10l21 -162q-109 -20 -194 -20t-143.5 51t-58.5 139q0 123 140 244h-5l-75 301h-539l-76 -301h-227zM389 502h449l-172 710h-103z" />
<glyph unicode="&#x105;" horiz-adv-x="1036" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-51 0 -74 3q-106 -106 -106 -164q0 -35 20.5 -56.5t52.5 -21.5l93 10l20 -162 q-109 -20 -193.5 -20t-143 51t-58.5 128t42 142.5t83 100.5l41 32q-27 14 -49 33q-164 -76 -330 -76q-305 0 -305 326zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5z" />
<glyph unicode="&#x106;" horiz-adv-x="1114" d="M106 706.5q0 389.5 111 554t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-211 -46 -399 -46q-307 0 -417 170t-110 559.5zM332 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#x107;" horiz-adv-x="905" d="M90 516q0 287 100.5 409t327.5 122q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-207 -37 -311 -37q-233 0 -328.5 126t-95.5 413zM225 1325l484 207l69 -193l-499 -159z" />
<glyph unicode="&#x108;" horiz-adv-x="1114" d="M106 706.5q0 389.5 111 554t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-211 -46 -399 -46q-307 0 -417 170t-110 559.5zM252 1589l274 293h193l274 -293h-237l-131 137l-133 -137h-240z" />
<glyph unicode="&#x109;" horiz-adv-x="905" d="M90 516q0 287 100.5 409t327.5 122q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-207 -37 -311 -37q-233 0 -328.5 126t-95.5 413zM135 1198l256 299h141l261 -299h-209l-119 156l-119 -156h-211 z" />
<glyph unicode="&#x10a;" horiz-adv-x="1114" d="M106 706.5q0 389.5 111 554t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-211 -46 -399 -46q-307 0 -417 170t-110 559.5zM481 1591v234h221v-234h-221z" />
<glyph unicode="&#x10b;" horiz-adv-x="905" d="M90 516q0 287 100.5 409t327.5 122q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-207 -37 -311 -37q-233 0 -328.5 126t-95.5 413zM410 1186v233h221v-233h-221z" />
<glyph unicode="&#x10c;" horiz-adv-x="1114" d="M106 706.5q0 389.5 111 554t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-211 -46 -399 -46q-307 0 -417 170t-110 559.5zM256 1882h240l133 -135l129 135h239l-274 -293h-193z" />
<glyph unicode="&#x10d;" horiz-adv-x="905" d="M90 516q0 287 100.5 409t327.5 122q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-207 -37 -311 -37q-233 0 -328.5 126t-95.5 413zM164 1497h209l121 -154l118 154h209l-260 -299h-141z" />
<glyph unicode="&#x10e;" horiz-adv-x="1312" d="M162 0v1403h485q324 0 442.5 -164t118.5 -514q0 -178 -23.5 -302t-82.5 -224q-117 -199 -455 -199h-485zM244 1882h239l133 -135l129 135h240l-274 -293h-193zM389 201h258q203 0 271 149q33 76 44 162t11 213q0 254 -63.5 366.5t-262.5 112.5h-258v-1003z" />
<glyph unicode="&#x10f;" horiz-adv-x="1280" d="M88 503.5q0 284.5 103.5 414t322.5 129.5q74 0 234 -27v430h223v-1450h-221v53q-150 -76 -275 -76q-201 0 -294 121t-93 405.5zM313 510q0 -188 48.5 -261t153 -73t198.5 37l35 14v607q-123 20 -228 20q-207 0 -207 -344zM1092 938l88 455h217l-109 -455h-196z" />
<glyph unicode="&#x110;" horiz-adv-x="1318" d="M47 598v217h123v604h481q332 0 453 -178q61 -92 85.5 -212t24.5 -297t-23.5 -303t-82.5 -228q-119 -201 -457 -201h-481v598h-123zM395 217h256q205 0 271 150q35 76 46 159.5t11 208.5q0 244 -64.5 355.5t-263.5 111.5h-256v-387h291v-217h-291v-381z" />
<glyph unicode="&#x111;" horiz-adv-x="1105" d="M88 503.5q0 284.5 103.5 414t322.5 129.5q74 0 234 -27v254h-431v192h637v-16h17v-1450h-221v53q-150 -76 -275 -76q-201 0 -294 121t-93 405.5zM313 510q0 -188 48.5 -261t153 -73t198.5 37l35 14v607q-123 20 -228 20q-207 0 -207 -344z" />
<glyph unicode="&#x112;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM256 1628v166h680v-166h-680z" />
<glyph unicode="&#x113;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM240 1247v168h577v-168h-577zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5z" />
<glyph unicode="&#x114;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM262 1872h203q4 -60 46 -96.5t107.5 -36.5t107.5 36.5t46 96.5h203q-12 -133 -104.5 -213t-253 -80t-253 80t-102.5 213z" />
<glyph unicode="&#x115;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM205 1491h172q6 -70 46 -111t106.5 -41t105.5 42t43 110h172q-12 -129 -97 -217t-224.5 -88t-225.5 88t-98 217 zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5z" />
<glyph unicode="&#x116;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM500 1602v233h221v-233h-221z" />
<glyph unicode="&#x117;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5zM412 1186v233h221v-233h-221z" />
<glyph unicode="&#x118;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-60q-45 -31 -86 -93.5t-41 -94t20.5 -53t53.5 -21.5l92 10l21 -162q-109 -20 -194 -20t-143.5 51t-58.5 139q0 123 140 244h-629z" />
<glyph unicode="&#x119;" horiz-adv-x="1042" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t178.5 -59.5q166 0 319 12l58 6l4 -165q-49 -10 -113 -21l27 -2q-131 -116 -131 -186q0 -35 20.5 -56.5t52.5 -21.5l92 10l21 -162q-109 -20 -194 -20t-143 51t-58 150.5t121 216.5 q-59 -6 -109 -7q-227 0 -326.5 125t-99.5 400zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5z" />
<glyph unicode="&#x11a;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM211 1882h240l133 -135l129 135h239l-274 -293h-193z" />
<glyph unicode="&#x11b;" horiz-adv-x="1040" d="M86 502q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400zM190 1497h209l121 -154l119 154h209l-260 -299h-142zM309 590h432q0 152 -48 212t-162.5 60t-167 -63.5 t-54.5 -208.5z" />
<glyph unicode="&#x11c;" horiz-adv-x="1255" d="M102 707.5q0 375.5 124 547.5t429 172q201 0 406 -41l72 -14l-9 -180q-248 33 -444.5 33t-269 -110.5t-72.5 -409.5t68.5 -413t277.5 -114q150 0 223 19v342h-174v200h400v-712q-55 -14 -224.5 -30.5t-244.5 -16.5q-319 0 -440.5 176t-121.5 551.5zM297 1587l274 293h193 l274 -293h-237l-131 137l-133 -137h-240z" />
<glyph unicode="&#x11d;" horiz-adv-x="1075" d="M86 -180q0 76 37 130t119 118q-68 45 -68 149q0 41 55 133l19 31q-147 88 -148 307q0 184 111 270t299 86q90 0 178 -20l31 -6l317 8v-180l-170 10q55 -72 56 -168q0 -201 -101.5 -277.5t-316.5 -76.5q-53 0 -90 8q-29 -70 -29 -107.5t38 -52t183 -16.5q244 -2 334 -65.5 t90 -239.5q0 -336 -485 -336q-233 0 -346 63.5t-113 231.5zM219 1198l256 299h141l261 -299h-209l-119 156l-119 -156h-211zM305 -160q0 -72 57.5 -102.5t196.5 -30.5q248 0 248 141q0 80 -44 101.5t-175 23.5l-201 13q-45 -37 -63.5 -69t-18.5 -77zM322 688q0 -94 45 -138 t145 -44t144 44t44 138t-45 138t-145 44q-188 0 -188 -182z" />
<glyph unicode="&#x11e;" horiz-adv-x="1255" d="M102 705.5q0 375.5 124 547.5t429 172q180 0 406 -41l72 -14l-9 -180q-248 33 -444.5 33t-269 -111t-72.5 -410t68.5 -412.5t277.5 -113.5q150 0 223 19v342h-174v200h400v-712q-55 -14 -224.5 -31t-244.5 -17q-319 0 -440.5 176.5t-121.5 552zM309 1872h203 q4 -60 46 -96.5t107.5 -36.5t107.5 36.5t46 96.5h203q-12 -133 -104.5 -213t-253 -80t-253 80t-102.5 213z" />
<glyph unicode="&#x11f;" horiz-adv-x="1075" d="M86 -180q0 76 37 130t119 118q-68 45 -68 149q0 41 55 133l19 31q-147 88 -148 307q0 184 111 270t299 86q90 0 178 -20l31 -6l317 8v-180l-170 10q55 -72 56 -168q0 -201 -101.5 -277.5t-316.5 -76.5q-53 0 -90 8q-29 -70 -29 -107.5t38 -52t183 -16.5q244 -2 334 -65.5 t90 -239.5q0 -336 -485 -336q-233 0 -346 63.5t-113 231.5zM223 1491h172q6 -70 46 -111t106.5 -41t105.5 42t43 110h172q-12 -129 -97 -217t-224 -88t-225 88t-99 217zM305 -160q0 -72 57.5 -102.5t196.5 -30.5q248 0 248 141q0 80 -44 101.5t-175 23.5l-201 13 q-45 -37 -63.5 -69t-18.5 -77zM322 688q0 -94 45 -138t145 -44t144 44t44 138t-45 138t-145 44q-188 0 -188 -182z" />
<glyph unicode="&#x120;" horiz-adv-x="1255" d="M102 705.5q0 375.5 124 547.5t429 172q180 0 406 -41l72 -14l-9 -180q-248 33 -444.5 33t-269 -111t-72.5 -410t68.5 -412.5t277.5 -113.5q150 0 223 19v342h-174v200h400v-712q-55 -14 -224.5 -31t-244.5 -17q-319 0 -440.5 176.5t-121.5 552zM528 1602v233h222v-233 h-222z" />
<glyph unicode="&#x121;" horiz-adv-x="1075" d="M86 -180q0 76 37 130t119 118q-68 45 -68 149q0 41 55 133l19 31q-147 88 -148 307q0 184 111 270t299 86q90 0 178 -20l31 -6l317 8v-180l-170 10q55 -72 56 -168q0 -201 -101.5 -277.5t-316.5 -76.5q-53 0 -90 8q-29 -70 -29 -107.5t38 -52t183 -16.5q244 -2 334 -65.5 t90 -239.5q0 -336 -485 -336q-233 0 -346 63.5t-113 231.5zM305 -160q0 -72 57.5 -102.5t196.5 -30.5q248 0 248 141q0 80 -44 101.5t-175 23.5l-201 13q-45 -37 -63.5 -69t-18.5 -77zM322 688q0 -94 45 -138t145 -44t144 44t44 138t-45 138t-145 44q-188 0 -188 -182z M434 1186v233h221v-233h-221z" />
<glyph unicode="&#x122;" horiz-adv-x="1255" d="M102 705.5q0 375.5 124 547.5t429 172q180 0 406 -41l72 -14l-9 -180q-248 33 -444.5 33t-269 -111t-72.5 -410t68.5 -412.5t277.5 -113.5q150 0 223 19v342h-174v200h400v-712q-55 -14 -224.5 -31t-244.5 -17q-319 0 -440.5 176.5t-121.5 552zM485 -614l90 456h218 l-109 -456h-199z" />
<glyph unicode="&#x123;" horiz-adv-x="1075" d="M86 -180q0 76 37 130t119 118q-68 45 -68 149q0 41 55 133l19 31q-147 88 -148 307q0 184 111 270t299 86q90 0 178 -20l31 -6l317 8v-180l-170 10q55 -72 56 -168q0 -201 -101.5 -277.5t-316.5 -76.5q-53 0 -90 8q-29 -70 -29 -107.5t38 -52t183 -16.5q244 -2 334 -65.5 t90 -239.5q0 -336 -485 -336q-233 0 -346 63.5t-113 231.5zM305 -160q0 -72 57.5 -102.5t196.5 -30.5q248 0 248 141q0 80 -44 101.5t-175 23.5l-201 13q-45 -37 -63.5 -69t-18.5 -77zM322 688q0 -94 45 -138t145 -44t144 44t44 138t-45 138t-145 44q-188 0 -188 -182z M369 1200l106 455h199l-90 -455h-215z" />
<glyph unicode="&#x124;" horiz-adv-x="1384" d="M162 0v1403h227v-598h606v598h230v-1403h-230v606h-606v-606h-227zM322 1589l274 293h192l275 -293h-238l-131 137l-133 -137h-239z" />
<glyph unicode="&#x125;" horiz-adv-x="1118" d="M135 0v1450h223v-479q152 76 281 76q205 0 279.5 -116t74.5 -382v-549h-223v543q0 166 -35 235.5t-147 69.5q-98 0 -197 -33l-33 -12v-803h-223zM229 1556l256 299h142l260 -299h-209l-119 156l-119 -156h-211z" />
<glyph unicode="&#x126;" horiz-adv-x="1402" d="M35 1014v194h135v195h227v-195h607v195h229v-195h149v-194h-149v-1014h-229v606h-607v-606h-227v1014h-135zM397 805h607v209h-607v-209z" />
<glyph unicode="&#x127;" horiz-adv-x="1118" d="M23 1133v192h112v125h223v-125h301v-192h-301v-162q152 76 281 76q205 0 279.5 -116t74.5 -382v-549h-223v543q0 166 -35 235.5t-147 69.5q-98 0 -197 -33l-33 -12v-803h-223v1133h-112z" />
<glyph unicode="&#x128;" horiz-adv-x="550" d="M-102 1763q12 14 31.5 36t73.5 56.5t107.5 34.5t179.5 -51t148 -51q45 0 127 67l29 23l49 -162q-31 -43 -93.5 -84t-111.5 -41t-178 51.5t-156 51.5q-51 0 -131 -68l-26 -22zM162 0v1403h227v-1403h-227z" />
<glyph unicode="&#x129;" horiz-adv-x="493" d="M-86 1364l33 33q18 18 71.5 50t97.5 32t143 -41t124 -41q45 0 129 55l27 18l49 -131l-31 -32q-20 -19 -72.5 -51.5t-95.5 -32.5t-142.5 41t-127.5 41q-49 0 -129 -52l-27 -18zM135 0v1024h223v-1024h-223z" />
<glyph unicode="&#x12a;" horiz-adv-x="550" d="M-59 1628v166h680v-166h-680zM162 0v1403h227v-1403h-227z" />
<glyph unicode="&#x12b;" horiz-adv-x="493" d="M-41 1247v168h578v-168h-578zM135 0v1024h223v-1024h-223z" />
<glyph unicode="&#x12c;" horiz-adv-x="550" d="M-53 1872h203q4 -60 46 -96.5t107.5 -36.5t107.5 36.5t46 96.5h202q-12 -133 -104 -213t-253 -80t-253 80t-102 213zM162 0v1403h227v-1403h-227z" />
<glyph unicode="&#x12d;" horiz-adv-x="493" d="M-76 1491h172q6 -70 46 -111t106.5 -41t105.5 42t43 110h172q-12 -129 -97 -217t-224 -88t-225 88t-99 217zM135 0v1024h223v-1024h-223z" />
<glyph unicode="&#x12e;" horiz-adv-x="550" d="M35 -244q0 129 145 244h-18v1403h227v-1403q-47 -35 -95 -94.5t-48 -92t20.5 -54t50.5 -21.5l95 10l20 -162q-109 -20 -194.5 -20t-144 51t-58.5 139z" />
<glyph unicode="&#x12f;" horiz-adv-x="493" d="M4 -244q0 125 150 244h-19v1024h223v-1024q-57 -43 -101 -97.5t-44 -88t20.5 -55t53.5 -21.5l94 10l18 -162q-109 -20 -193.5 -20t-143 51t-58.5 139zM135 1198v236h223v-236h-223z" />
<glyph unicode="&#x130;" horiz-adv-x="550" d="M162 0v1403h227v-1403h-227zM164 1602v233h221v-233h-221z" />
<glyph unicode="&#x131;" horiz-adv-x="493" d="M135 0v1024h223v-1024h-223z" />
<glyph unicode="&#x134;" horiz-adv-x="606" d="M-31 1587l275 293h192l275 -293h-238l-131 137l-133 -137h-240zM39 55q109 0 148.5 33t39.5 145v1170h226l2 -1186q0 -219 -92.5 -290.5t-323.5 -71.5v200z" />
<glyph unicode="&#x135;" horiz-adv-x="495" d="M-84 -299q143 90 182 147.5t39 190.5v985h221v-987q0 -201 -70.5 -297t-283.5 -205zM-80 1198l256 299h141l261 -299h-209l-119 156l-119 -156h-211z" />
<glyph unicode="&#x136;" horiz-adv-x="1189" d="M162 0v1419h227v-628l205 14l287 614h264l-346 -708l360 -711h-264l-297 586l-209 -15v-571h-227zM430 -614l88 456h217l-108 -456h-197z" />
<glyph unicode="&#x137;" horiz-adv-x="1021" d="M78 -614l88 456h217l-109 -456h-196zM135 0v1450h223v-838l127 13l242 399h250l-295 -479l311 -545h-252l-249 432l-134 -14v-418h-223z" />
<glyph unicode="&#x139;" horiz-adv-x="974" d="M162 0v1419h227v-1200h559v-219h-786zM246 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#x13a;" horiz-adv-x="518" d="M94 1749l484 207l69 -193l-500 -159zM147 0v1450h224v-1450h-224z" />
<glyph unicode="&#x13b;" horiz-adv-x="972" d="M162 0v1403h227v-1200h559v-203h-786zM360 -614l91 456h217l-109 -456h-199z" />
<glyph unicode="&#x13c;" horiz-adv-x="518" d="M51 -614l90 456h217l-108 -456h-199zM147 0v1450h224v-1450h-224z" />
<glyph unicode="&#x13d;" horiz-adv-x="1015" d="M162 0v1403h227v-1200h559v-203h-786zM709 909v510h215v-510h-215z" />
<glyph unicode="&#x13e;" horiz-adv-x="677" d="M147 0v1450h224v-1450h-224zM489 938l89 455h217l-109 -455h-197z" />
<glyph unicode="&#x141;" horiz-adv-x="987" d="M-31 575l207 144v684h227v-524l242 170l109 -150l-351 -248v-448h560v-203h-787v492l-98 -68z" />
<glyph unicode="&#x142;" horiz-adv-x="716" d="M12 569l228 158v723h221v-567l139 98l109 -152l-248 -174v-655h-221v500l-119 -82z" />
<glyph unicode="&#x143;" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227zM430 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#x144;" horiz-adv-x="1118" d="M135 0v1024h221v-63l41 22q41 20 112 42t130 22q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-150.5 69.5t-227.5 -53v-795h-223zM311 1325l484 207l69 -193l-499 -159z" />
<glyph unicode="&#x145;" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227zM545 -614l90 456h215l-107 -456h-198z" />
<glyph unicode="&#x146;" horiz-adv-x="1118" d="M102 -614l88 456h218l-109 -456h-197zM135 0v1024h221v-63q150 86 283 86q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-146 69.5q-104 0 -201 -41l-31 -12v-795h-223z" />
<glyph unicode="&#x147;" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227zM334 1880h239l134 -135l129 135h239l-274 -293h-193z" />
<glyph unicode="&#x148;" horiz-adv-x="1118" d="M135 0v1024h221v-63q150 86 283 86q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-146 69.5q-104 0 -201 -41l-31 -12v-795h-223zM227 1497h209l121 -154l119 154h209l-260 -299h-142z" />
<glyph unicode="&#x14a;" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1454q0 -219 -92 -291t-324 -72v201q109 0 149 33t40 145v35h-162l-436 1204h-29v-1204h-227z" />
<glyph unicode="&#x14b;" horiz-adv-x="1118" d="M135 0v1022h221v-53q172 78 289 78q195 0 271.5 -126t76.5 -386v-531q0 -205 -66.5 -300t-281.5 -202l-98 185q145 82 185 136t40 173v536q0 156 -35 234t-135 78q-86 0 -205 -41l-41 -12v-791h-221z" />
<glyph unicode="&#x14c;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM326 1628v166h680v-166h-680zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5z" />
<glyph unicode="&#x14d;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM260 1257v168h578v-168h-578zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264z" />
<glyph unicode="&#x14e;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM317 1872h203q4 -60 46 -96.5t107.5 -36.5t107.5 36.5t46 96.5h203q-12 -133 -104 -213t-253 -80t-253 80t-103 213zM336 695.5q0 -277.5 69.5 -398.5 t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM225 1491h172q6 -70 46 -111t106.5 -41t105.5 42t43 110h172q-12 -129 -97 -217t-224 -88t-225 88t-99 217zM311 516q0 -186 47.5 -268t184.5 -82 t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264z" />
<glyph unicode="&#x150;" horiz-adv-x="1343" d="M100 694.5q0 370.5 128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5t-444.5 173.5t-127 544zM315 1597l205 347l182 -80l-219 -336zM336 695.5q0 -277.5 69.5 -398.5t266 -121t265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5z M733 1595l203 347l184 -80l-221 -336z" />
<glyph unicode="&#x151;" horiz-adv-x="1085" d="M86 514q0 266 105.5 399.5t351 133.5t351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5zM145 1282l265 342l174 -115l-277 -340zM311 516q0 -186 47.5 -268t184.5 -82t184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264zM553 1282 l264 344l174 -117l-276 -340z" />
<glyph unicode="&#x152;" horiz-adv-x="1867" d="M104 707q0 395 119 565t418 170q121 0 260 -23h877v-219h-652v-371h529v-217h-529v-393h652v-219h-873q-147 -23 -286.5 -23t-245 44.5t-163.5 140.5t-82 224t-24 321zM340 708.5q0 -290.5 66.5 -399t271.5 -108.5q61 0 223 14v987q-168 14 -229 15q-193 0 -262.5 -109 t-69.5 -399.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1728" d="M86 514q0 266 105.5 398t355.5 132t340 -186q96 186 321.5 186t333 -114.5t107.5 -351.5l-17 -162h-635q2 -123 55.5 -179.5t199.5 -56t300 12.5l58 6l4 -174q-233 -47 -414 -48q-231 0 -321 160q-92 -160 -339 -160t-350.5 135.5t-103.5 401.5zM311 515 q0 -179 48.5 -258t184.5 -79t183 79t47 255t-52 255t-180 79t-179.5 -76t-51.5 -255zM997 590h430q0 141 -48 199.5t-161.5 58.5t-167 -62.5t-53.5 -195.5z" />
<glyph unicode="&#x154;" horiz-adv-x="1265" d="M162 0v1403h532q475 0 475 -447q0 -299 -229 -405l231 -551h-249l-203 500h-330v-500h-227zM365 1712l483 211l72 -199l-498 -168zM389 696h309q123 0 180.5 70.5t57.5 187.5q0 252 -242 252h-305v-510z" />
<glyph unicode="&#x155;" horiz-adv-x="739" d="M115 1325l483 207l70 -193l-500 -159zM135 0v1024h221v-123q174 113 349 146v-224q-176 -35 -302 -90l-45 -18v-715h-223z" />
<glyph unicode="&#x156;" horiz-adv-x="1265" d="M162 0v1403h532q475 0 475 -447q0 -299 -229 -405l231 -551h-249l-203 500h-330v-500h-227zM389 696h309q123 0 180.5 70.5t57.5 187.5q0 252 -242 252h-305v-510zM463 -614l88 456h217l-106 -456h-199z" />
<glyph unicode="&#x157;" horiz-adv-x="739" d="M47 -614l88 456h217l-106 -456h-199zM135 0v1024h221v-123q174 113 349 146v-224q-176 -35 -302 -90l-45 -18v-715h-223z" />
<glyph unicode="&#x158;" horiz-adv-x="1265" d="M162 0v1403h532q475 0 475 -447q0 -299 -229 -405l231 -551h-249l-203 500h-330v-500h-227zM244 1882h239l133 -135l129 135h240l-274 -293h-193zM389 696h309q123 0 180.5 70.5t57.5 187.5q0 252 -242 252h-305v-510z" />
<glyph unicode="&#x159;" horiz-adv-x="739" d="M29 1497h209l120 -154l119 154h209l-260 -299h-141zM135 0v1024h221v-123q174 113 349 146v-224q-176 -35 -302 -90l-45 -18v-715h-223z" />
<glyph unicode="&#x15a;" horiz-adv-x="1112" d="M86 1042.5q0 190.5 124 287.5t343 97q158 0 373 -36l71 -13l-18 -184q-287 33 -406 33q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -255q0 -221 -126 -331t-343 -110q-174 0 -385 41l-74 15l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130 t-258 92.5t-299 138.5t-98.5 278.5zM313 1712l484 211l71 -199l-497 -168z" />
<glyph unicode="&#x15b;" horiz-adv-x="960" d="M82 731q0 158 107.5 235.5t275.5 77.5q131 0 321 -32l64 -13l-4 -186q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -225.5t-103.5 -238t-302.5 -76q-125 0 -315 35l-64 11l8 186q246 -33 354.5 -33t154.5 26.5t46 88t-44 85t-210.5 51.5 t-244.5 87t-78 217zM207 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#x15c;" horiz-adv-x="1112" d="M86 1042.5q0 190.5 124 287.5t343 97q158 0 373 -36l71 -13l-18 -184q-287 33 -406 33q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -255q0 -221 -126 -331t-343 -110q-174 0 -385 41l-74 15l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130 t-258 92.5t-299 138.5t-98.5 278.5zM193 1587l274 293h192l275 -293h-238l-131 137l-133 -137h-239z" />
<glyph unicode="&#x15d;" horiz-adv-x="960" d="M82 731q0 158 107.5 235.5t275.5 77.5q131 0 321 -32l64 -13l-4 -186q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -225.5t-103.5 -238t-302.5 -76q-125 0 -315 35l-64 11l8 186q246 -33 354.5 -33t154.5 26.5t46 88t-44 85t-210.5 51.5 t-244.5 87t-78 217zM154 1198l256 299h141l260 -299h-209l-119 156l-118 -156h-211z" />
<glyph unicode="&#x15e;" horiz-adv-x="1112" d="M86 1042.5q0 190.5 124 287.5t343 97q158 0 373 -36l71 -13l-18 -184q-287 33 -406 33q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -270.5t-107.5 -298t-295.5 -122.5v-68q129 -2 186 -37t57 -139.5t-56 -157.5t-157 -53q-78 0 -157 14l-27 6l8 134 q70 -4 113 -5q84 0 84 64q0 31 -19.5 43t-64.5 12h-64v182q-150 4 -360 43l-68 13l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130t-258 92.5t-299 138.5t-98.5 278.5z" />
<glyph unicode="&#x15f;" horiz-adv-x="960" d="M82 731q0 158 107.5 235.5t275.5 77.5q131 0 321 -32l64 -13l-4 -186q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -221.5q0 -303 -367 -315v-66q129 -2 186.5 -37t57.5 -139.5t-56.5 -157.5t-156.5 -53q-78 0 -158 14l-27 6l9 134q70 -4 112 -5 q84 0 84 64q0 31 -19.5 43t-64.5 12h-63v185q-141 8 -273 32l-49 11l8 186q246 -33 354.5 -33t154.5 26.5t46 88t-44 85t-210.5 51.5t-244.5 87t-78 217z" />
<glyph unicode="&#x160;" horiz-adv-x="1112" d="M86 1042.5q0 190.5 124 287.5t343 97q158 0 373 -36l71 -13l-18 -184q-287 33 -406 33q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -255q0 -221 -126 -331t-343 -110q-174 0 -385 41l-74 15l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130 t-258 92.5t-299 138.5t-98.5 278.5zM221 1882h240l133 -135l129 135h240l-275 -293h-192z" />
<glyph unicode="&#x161;" horiz-adv-x="960" d="M82 731q0 158 107.5 235.5t275.5 77.5q131 0 321 -32l64 -13l-4 -186q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -225.5t-103.5 -238t-302.5 -76q-125 0 -315 35l-64 11l8 186q246 -33 354.5 -33t154.5 26.5t46 88t-44 85t-210.5 51.5 t-244.5 87t-78 217zM172 1497h209l121 -154l119 154h208l-260 -299h-141z" />
<glyph unicode="&#x162;" horiz-adv-x="0" />
<glyph unicode="&#x163;" horiz-adv-x="733" d="M51 834v190h131v297h221v-297h283v-190h-283v-451q0 -125 18.5 -166t94.5 -41l168 6l10 -178q-137 -27 -209 -27q-174 0 -238.5 80t-64.5 301v476h-131zM168 -453l8 134q70 -4 113 -5q84 0 84 64q0 31 -19.5 43t-64.5 12h-64v207h97v-88q129 -2 186 -37t57 -139.5 t-56 -157.5t-157 -53q-78 0 -157 14z" />
<glyph unicode="&#x164;" horiz-adv-x="1077" d="M27 1200v203h1024v-203h-396v-1200h-229v1200h-399zM170 1882h240l133 -135l129 135h239l-274 -293h-193z" />
<glyph unicode="&#x165;" horiz-adv-x="948" d="M51 821v201h131v299h224v-299h282v-201h-282v-426q0 -125 18 -166t94 -41l166 7l12 -191q-137 -27 -209 -27q-174 0 -239.5 81t-65.5 300v463h-131zM764 909v510h215v-510h-215z" />
<glyph unicode="&#x166;" horiz-adv-x="1083" d="M29 1200v203h1024v-203h-396v-446h308v-193h-308v-561h-229v561h-297v193h297v446h-399z" />
<glyph unicode="&#x167;" horiz-adv-x="737" d="M53 821v201h131v299h222v-299h282v-201h-282v-186h229v-168h-229v-72q0 -125 18 -166t94 -41l168 7l10 -191q-137 -27 -209 -27q-174 0 -238.5 80t-64.5 301v109h-84v168h84v186h-131z" />
<glyph unicode="&#x168;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM285 1763q12 14 31.5 36t73.5 56.5t107.5 34.5t179.5 -51t148 -51q45 0 129 67l27 23l49 -162q-31 -43 -93.5 -84t-111.5 -41t-178 51.5t-155 51.5 q-51 0 -132 -68l-26 -22z" />
<glyph unicode="&#x169;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM209 1364l33 33q18 18 71.5 50t97.5 32t143 -41t124 -41q45 0 129 55l27 18l49 -131l-31 -32 q-21 -19 -73 -51.5t-95 -32.5t-142.5 41t-127.5 41q-49 0 -129 -52l-27 -18z" />
<glyph unicode="&#x16a;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM332 1628v166h680v-166h-680z" />
<glyph unicode="&#x16b;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM266 1247v168h578v-168h-578z" />
<glyph unicode="&#x16c;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM315 1872h203q4 -60 46 -96.5t107.5 -36.5t107.5 36.5t46 96.5h203q-12 -133 -104.5 -213t-253 -80t-252.5 80t-103 213z" />
<glyph unicode="&#x16d;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM227 1491h172q7 -70 46.5 -111t106 -41t105.5 42t43 110h172q-12 -129 -97 -217t-224 -88t-225 88t-99 217z " />
<glyph unicode="&#x16e;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM391 1724.5q0 110.5 80 175t200 64.5t199.5 -64.5t79.5 -175t-79.5 -175t-199.5 -64.5t-200 64.5t-80 175zM547 1724q0 -45 32.5 -69.5t90 -24.5 t91.5 24.5t34 69.5t-34 71t-91.5 26t-90 -26t-32.5 -71z" />
<glyph unicode="&#x16f;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM328 1311.5q0 97.5 65.5 163t161.5 65.5t162.5 -66.5t66.5 -163t-66.5 -161t-162.5 -64.5t-161.5 64.5 t-65.5 162zM455 1312q0 -44 28.5 -74t72.5 -30t72.5 30t28.5 74t-28.5 73.5t-72.5 29.5t-72.5 -29.5t-28.5 -73.5z" />
<glyph unicode="&#x170;" horiz-adv-x="1331" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362zM313 1597l205 347l182 -80l-219 -336zM731 1595l203 347l184 -80l-221 -336z" />
<glyph unicode="&#x171;" horiz-adv-x="1112" d="M125 489v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398zM209 1282l264 342l174 -115l-276 -340zM616 1282l265 344l174 -117l-277 -340z" />
<glyph unicode="&#x172;" horiz-adv-x="1333" d="M150 453v950h229v-954q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -381 -326 -455q-125 -113 -125 -182q0 -35 20.5 -56.5t53.5 -21.5l92 10l21 -162q-109 -20 -194 -20t-143.5 51t-58.5 150.5t115 209.5q-244 6 -365.5 120t-121.5 356z" />
<glyph unicode="&#x173;" horiz-adv-x="1112" d="M125 489v533h223v-533q0 -184 31 -247.5t143 -63.5q80 0 197 39l37 12v793h221v-1022q-49 -31 -99.5 -88t-50.5 -94t20.5 -58.5t53.5 -21.5l92 10l21 -162q-109 -20 -195 -20t-144.5 51t-58.5 139q0 125 156 244h-16v53q-160 -76 -277 -76q-211 0 -282.5 114t-71.5 398z " />
<glyph unicode="&#x174;" horiz-adv-x="1865" d="M49 1403h240l200 -1206h43l267 1202h266l266 -1202h45l201 1206h240l-277 -1403h-366l-242 1124l-240 -1124h-368zM563 1587l275 293h192l275 -293h-238l-131 137l-133 -137h-240z" />
<glyph unicode="&#x175;" horiz-adv-x="1572" d="M55 1024h219l162 -834h41l195 814h229l195 -814h41l159 834h222l-218 -1024h-354l-160 702l-159 -702h-355zM461 1198l256 299h141l260 -299h-209l-118 156l-119 -156h-211z" />
<glyph unicode="&#x176;" horiz-adv-x="1124" d="M8 1403h256l297 -602l299 602h254l-436 -825v-578h-229v578zM195 1589l274 293h193l274 -293h-238l-131 137l-133 -137h-239z" />
<glyph unicode="&#x177;" horiz-adv-x="1007" d="M41 1024h219l217 -834h55l218 834h221l-389 -1464h-220l123 440h-176zM180 1198l256 299h142l260 -299h-209l-119 156l-119 -156h-211z" />
<glyph unicode="&#x178;" horiz-adv-x="1124" d="M8 1403h256l297 -602l299 602h254l-436 -825v-578h-229v578zM238 1604v233h213v-233h-213zM666 1604v233h213v-233h-213z" />
<glyph unicode="&#x179;" horiz-adv-x="1093" d="M86 0v217l649 946v41h-649v199h922v-219l-652 -946v-39h652v-199h-922zM276 1712l484 211l71 -199l-497 -168z" />
<glyph unicode="&#x17a;" horiz-adv-x="929" d="M84 0v199l494 626h-494v199h760v-199l-492 -626h492v-199h-760zM195 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#x17b;" horiz-adv-x="1093" d="M86 0v217l649 946v41h-649v199h922v-219l-652 -946v-39h652v-199h-922zM436 1602v233h221v-233h-221z" />
<glyph unicode="&#x17c;" horiz-adv-x="929" d="M84 0v199l494 626h-494v199h760v-199l-492 -626h492v-199h-760zM352 1186v233h221v-233h-221z" />
<glyph unicode="&#x17d;" horiz-adv-x="1093" d="M86 0v217l649 946v41h-649v199h922v-219l-652 -946v-39h652v-199h-922zM186 1882h240l133 -135l129 135h240l-275 -293h-192z" />
<glyph unicode="&#x17e;" horiz-adv-x="929" d="M84 0v199l494 626h-494v199h760v-199l-492 -626h492v-199h-760zM152 1497h208l121 -154l119 154h209l-260 -299h-141z" />
<glyph unicode="&#x192;" d="M86 -268q129 -8 188 -8q125 0 125 178v870h-118v191h118v75q0 209 61.5 304.5t227.5 95.5q53 0 180 -17l39 -6v-182q-92 4 -160.5 4t-96 -42t-27.5 -140v-92h268v-191h-268v-874q0 -209 -76 -291t-248 -82q-74 0 -180 16l-33 6v185z" />
<glyph unicode="&#x1fa;" horiz-adv-x="1226" d="M41 0l334 1358q-43 59 -43 154.5t80 160t199.5 64.5t199.5 -64.5t80 -175.5q0 -78 -41 -135l338 -1362h-230l-75 301h-539l-76 -301h-227zM317 1911l484 211l71 -199l-497 -168zM389 502h449l-172 710h-103zM487 1497q0 -45 33 -69.5t90 -24.5h11q53 2 83.5 26.5 t30.5 68.5t-33.5 69.5t-91 25.5t-90.5 -25.5t-33 -70.5z" />
<glyph unicode="&#x1fb;" horiz-adv-x="1038" d="M70 303q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326zM242 1720l483 207l70 -192 l-500 -160zM283 1311.5q0 97.5 65.5 163t161.5 65.5t162.5 -66.5t66.5 -163t-66.5 -161t-162.5 -64.5t-161.5 64.5t-65.5 162zM293 301.5q0 -141.5 125 -141.5q102 0 217 33l37 12v272l-238 -22q-141 -12 -141 -153.5zM410 1312q0 -44 28.5 -74t72.5 -30t72.5 30t28.5 74 t-28.5 73.5t-72.5 29.5t-72.5 -29.5t-28.5 -73.5z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1789" d="M33 0l413 1419h1254v-219h-656v-371h533v-215h-533v-395h656v-219h-881v291h-469l-86 -291h-231zM408 510h411l2 690h-213zM791 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1624" d="M70 308.5q0 158.5 87 226t275 81.5l240 19v65q0 66 -36 100t-101 34q-154 0 -326 -17l-66 -6l-8 197q260 37 432 36.5t252 -106.5q100 106 305 106q418 0 418 -466l-14 -162h-635q2 -123 55 -179.5t199.5 -56t298.5 12.5l59 6l4 -174q-236 -47 -428 -47.5t-295 102.5 l-55 -25q-176 -78 -369 -78q-143 0 -217.5 86.5t-74.5 245zM292.5 302q-0.5 -134 110.5 -134q92 0 184.5 23.5t98.5 27.5q-16 96 -16 244l-236 -17q-141 -10 -141.5 -144zM555 1325l483 207l70 -193l-500 -159zM893 590h430q0 139 -49 198.5t-163 59.5t-166 -61.5 t-52 -196.5z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1347" d="M102 694q0 371 128 551t444 180q125 0 229 -32l119 256l168 -72l-127 -272q180 -164 180 -611q0 -373 -126 -545t-443 -172q-125 0 -219 25l-117 -252l-164 80l119 254q-190 154 -191 610zM338 698.5q0 -274.5 61 -387.5l416 893q-61 23 -141 23q-193 0 -264.5 -127 t-71.5 -401.5zM383 1712l483 211l72 -199l-498 -168zM545 193q59 -17 129 -17q197 0 265.5 119t68.5 386t-56 388z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1085" d="M86 518q0 262 105.5 395.5t351.5 133.5q53 0 131 -13l84 207l139 -51l-86 -207q188 -113 188 -465q0 -270 -102 -405.5t-354 -135.5q-78 0 -140 13l-88 -215l-137 51l90 215q-182 117 -182 477zM268 1325l484 207l69 -193l-499 -159zM311 508.5q0 -193.5 49 -264.5 l244 608q-26 6 -61 6q-131 0 -181.5 -78t-50.5 -271.5zM473 172q31 -6 70 -6q137 0 184 82t47 270.5t-53 263.5z" />
<glyph unicode="&#x218;" horiz-adv-x="1112" d="M86 1042.5q0 190.5 124 287.5t343 97q158 0 373 -36l71 -13l-18 -184q-287 33 -406 33q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -255q0 -221 -126 -331t-343 -110q-174 0 -385 41l-74 15l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130 t-258 92.5t-299 138.5t-98.5 278.5zM381 -614l90 456h217l-108 -456h-199z" />
<glyph unicode="&#x219;" horiz-adv-x="960" d="M78 -614l90 456h217l-109 -456h-198zM82 731q0 158 107.5 235.5t275.5 77.5q131 0 321 -32l64 -13l-4 -186q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -225.5t-103.5 -238t-302.5 -76q-125 0 -315 35l-64 11l8 186q246 -33 354.5 -33 t154.5 26.5t46 88t-44 85t-210.5 51.5t-244.5 87t-78 217z" />
<glyph unicode="&#x21a;" horiz-adv-x="1077" d="M27 1200v203h1024v-203h-396v-1200h-229v1200h-399zM387 -614l90 456h217l-108 -456h-199z" />
<glyph unicode="&#x21b;" horiz-adv-x="733" d="M51 834v190h131v297h221v-297h283v-190h-283v-451q0 -125 18.5 -166t94.5 -41l168 6l10 -178q-137 -27 -209 -27q-174 0 -238.5 80t-64.5 301v476h-131zM92 -614l90 456h217l-108 -456h-199z" />
<glyph unicode="&#x2c6;" horiz-adv-x="520" d="M-45 1198l256 299h141l260 -299h-209l-118 156l-119 -156h-211z" />
<glyph unicode="&#x2da;" horiz-adv-x="520" d="M39 1311.5q0 97.5 65.5 163t161.5 65.5t163 -66.5t67 -163t-67 -161t-163 -64.5t-161.5 64.5t-65.5 162zM166 1312q0 -44 28.5 -74t72.5 -30t73 30t29 74t-29 73.5t-73 29.5t-72.5 -29.5t-28.5 -73.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="520" d="M-51 1364l31 33q20 18 73 50t97.5 32t143.5 -41t124 -41q45 0 127 55l28 18l50 -131l-33 -32q-19 -19 -71 -51.5t-95 -32.5t-142.5 41t-125.5 41q-51 0 -131 -52l-27 -18z" />
<glyph unicode="&#x2000;" horiz-adv-x="1061" />
<glyph unicode="&#x2001;" horiz-adv-x="2122" />
<glyph unicode="&#x2002;" horiz-adv-x="1061" />
<glyph unicode="&#x2003;" horiz-adv-x="2122" />
<glyph unicode="&#x2004;" horiz-adv-x="707" />
<glyph unicode="&#x2005;" horiz-adv-x="530" />
<glyph unicode="&#x2006;" horiz-adv-x="353" />
<glyph unicode="&#x2007;" horiz-adv-x="353" />
<glyph unicode="&#x2008;" horiz-adv-x="265" />
<glyph unicode="&#x2009;" horiz-adv-x="424" />
<glyph unicode="&#x200a;" horiz-adv-x="117" />
<glyph unicode="&#x2010;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#x2011;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#x2012;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#x2013;" horiz-adv-x="1273" d="M125 451v190h1024v-190h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2297" d="M125 451v190h2048v-190h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="495" d="M86 934l156 481h170l-93 -481h-233z" />
<glyph unicode="&#x2019;" horiz-adv-x="487" d="M96 936l92 481h234l-156 -481h-170z" />
<glyph unicode="&#x201a;" horiz-adv-x="489" d="M117 -117l45 232h110l-73 -232h-82z" />
<glyph unicode="&#x201c;" horiz-adv-x="845" d="M86 934l156 481h170l-93 -481h-233zM436 934l156 481h170l-92 -481h-234z" />
<glyph unicode="&#x201d;" horiz-adv-x="843" d="M96 938l92 481h234l-156 -481h-170zM451 938l92 481h233l-155 -481h-170z" />
<glyph unicode="&#x201e;" horiz-adv-x="806" d="M12 -246l156 482h170l-92 -482h-234zM369 -246l155 482h170l-92 -482h-233z" />
<glyph unicode="&#x2022;" horiz-adv-x="942" d="M215 211v577h512v-577h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1531" d="M125 0v295h240v-295h-240zM647 0v295h240v-295h-240zM1167 0v295h240v-295h-240z" />
<glyph unicode="&#x202f;" horiz-adv-x="424" />
<glyph unicode="&#x2039;" horiz-adv-x="636" d="M86 418v166l428 317v-225l-238 -170l238 -193v-227z" />
<glyph unicode="&#x203a;" horiz-adv-x="636" d="M121 100v228l237 192l-237 170v225l428 -317v-164z" />
<glyph unicode="&#x205f;" horiz-adv-x="530" />
<glyph unicode="&#x20ac;" d="M57 414v172h117q-2 29 -2 90v72h-115v172h127q29 240 141.5 347t354.5 107q158 0 387 -49l-8 -178q-166 31 -328 31t-229.5 -58.5t-87.5 -199.5h528v-172h-543v-162h543v-172h-528q23 -131 90 -185.5t212.5 -54.5t342.5 29l6 -180q-203 -45 -385 -46q-238 0 -350.5 105.5 t-143.5 331.5h-129z" />
<glyph unicode="&#x2122;" horiz-adv-x="1368" d="M174 1169v138h410v-138h-107v-489h-151v489h-152zM641 678v629h182l107 -369l114 369h181v-629h-142v401l-98 -364h-102l-101 364v-401h-141z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1024" d="M0 0v1024h1024v-1024h-1024z" />
<hkern u1="&#x22;" u2="&#x129;" k="-12" />
<hkern u1="&#x22;" u2="&#xef;" k="-27" />
<hkern u1="&#x22;" u2="&#xee;" k="-18" />
<hkern u1="&#x22;" u2="&#xec;" k="-47" />
<hkern u1="&#x22;" u2="&#xc6;" k="92" />
<hkern u1="&#x22;" u2="&#x40;" k="23" />
<hkern u1="&#x22;" u2="&#x2f;" k="129" />
<hkern u1="&#x22;" u2="&#x26;" k="49" />
<hkern u1="&#x26;" u2="&#x201d;" k="82" />
<hkern u1="&#x26;" u2="&#x2019;" k="82" />
<hkern u1="&#x26;" u2="&#x21b;" k="6" />
<hkern u1="&#x26;" u2="&#x21a;" k="76" />
<hkern u1="&#x26;" u2="&#x1fe;" k="6" />
<hkern u1="&#x26;" u2="&#x178;" k="102" />
<hkern u1="&#x26;" u2="&#x177;" k="10" />
<hkern u1="&#x26;" u2="&#x176;" k="102" />
<hkern u1="&#x26;" u2="&#x175;" k="8" />
<hkern u1="&#x26;" u2="&#x174;" k="33" />
<hkern u1="&#x26;" u2="&#x172;" k="6" />
<hkern u1="&#x26;" u2="&#x170;" k="6" />
<hkern u1="&#x26;" u2="&#x16e;" k="6" />
<hkern u1="&#x26;" u2="&#x16c;" k="6" />
<hkern u1="&#x26;" u2="&#x16a;" k="6" />
<hkern u1="&#x26;" u2="&#x168;" k="6" />
<hkern u1="&#x26;" u2="&#x167;" k="6" />
<hkern u1="&#x26;" u2="&#x166;" k="76" />
<hkern u1="&#x26;" u2="&#x165;" k="6" />
<hkern u1="&#x26;" u2="&#x164;" k="76" />
<hkern u1="&#x26;" u2="&#x152;" k="6" />
<hkern u1="&#x26;" u2="&#x150;" k="6" />
<hkern u1="&#x26;" u2="&#x14e;" k="6" />
<hkern u1="&#x26;" u2="&#x14c;" k="6" />
<hkern u1="&#x26;" u2="&#x122;" k="6" />
<hkern u1="&#x26;" u2="&#x120;" k="6" />
<hkern u1="&#x26;" u2="&#x11e;" k="6" />
<hkern u1="&#x26;" u2="&#x11c;" k="6" />
<hkern u1="&#x26;" u2="&#x10c;" k="6" />
<hkern u1="&#x26;" u2="&#x10a;" k="6" />
<hkern u1="&#x26;" u2="&#x108;" k="6" />
<hkern u1="&#x26;" u2="&#x106;" k="6" />
<hkern u1="&#x26;" u2="&#xff;" k="10" />
<hkern u1="&#x26;" u2="&#xfd;" k="10" />
<hkern u1="&#x26;" u2="&#xdd;" k="102" />
<hkern u1="&#x26;" u2="&#xdc;" k="6" />
<hkern u1="&#x26;" u2="&#xdb;" k="6" />
<hkern u1="&#x26;" u2="&#xda;" k="6" />
<hkern u1="&#x26;" u2="&#xd9;" k="6" />
<hkern u1="&#x26;" u2="&#xd8;" k="6" />
<hkern u1="&#x26;" u2="&#xd6;" k="6" />
<hkern u1="&#x26;" u2="&#xd5;" k="6" />
<hkern u1="&#x26;" u2="&#xd4;" k="6" />
<hkern u1="&#x26;" u2="&#xd3;" k="6" />
<hkern u1="&#x26;" u2="&#xd2;" k="6" />
<hkern u1="&#x26;" u2="&#xc7;" k="6" />
<hkern u1="&#x26;" u2="y" k="10" />
<hkern u1="&#x26;" u2="w" k="8" />
<hkern u1="&#x26;" u2="v" k="10" />
<hkern u1="&#x26;" u2="t" k="6" />
<hkern u1="&#x26;" u2="Y" k="102" />
<hkern u1="&#x26;" u2="W" k="33" />
<hkern u1="&#x26;" u2="V" k="55" />
<hkern u1="&#x26;" u2="U" k="6" />
<hkern u1="&#x26;" u2="T" k="76" />
<hkern u1="&#x26;" u2="Q" k="6" />
<hkern u1="&#x26;" u2="O" k="6" />
<hkern u1="&#x26;" u2="G" k="6" />
<hkern u1="&#x26;" u2="C" k="6" />
<hkern u1="&#x26;" u2="&#x27;" k="88" />
<hkern u1="&#x26;" u2="&#x22;" k="88" />
<hkern u1="&#x27;" u2="&#x129;" k="-12" />
<hkern u1="&#x27;" u2="&#xef;" k="-27" />
<hkern u1="&#x27;" u2="&#xee;" k="-18" />
<hkern u1="&#x27;" u2="&#xec;" k="-47" />
<hkern u1="&#x27;" u2="&#xc6;" k="92" />
<hkern u1="&#x27;" u2="&#x40;" k="23" />
<hkern u1="&#x27;" u2="&#x2f;" k="129" />
<hkern u1="&#x27;" u2="&#x26;" k="49" />
<hkern u1="&#x28;" u2="&#x219;" k="14" />
<hkern u1="&#x28;" u2="&#x1ff;" k="39" />
<hkern u1="&#x28;" u2="&#x1fe;" k="29" />
<hkern u1="&#x28;" u2="&#x1fd;" k="18" />
<hkern u1="&#x28;" u2="&#x1fb;" k="18" />
<hkern u1="&#x28;" u2="&#x177;" k="14" />
<hkern u1="&#x28;" u2="&#x175;" k="20" />
<hkern u1="&#x28;" u2="&#x173;" k="29" />
<hkern u1="&#x28;" u2="&#x171;" k="29" />
<hkern u1="&#x28;" u2="&#x16f;" k="29" />
<hkern u1="&#x28;" u2="&#x16d;" k="29" />
<hkern u1="&#x28;" u2="&#x16b;" k="29" />
<hkern u1="&#x28;" u2="&#x169;" k="29" />
<hkern u1="&#x28;" u2="&#x161;" k="14" />
<hkern u1="&#x28;" u2="&#x15f;" k="14" />
<hkern u1="&#x28;" u2="&#x15d;" k="14" />
<hkern u1="&#x28;" u2="&#x15b;" k="14" />
<hkern u1="&#x28;" u2="&#x159;" k="16" />
<hkern u1="&#x28;" u2="&#x157;" k="16" />
<hkern u1="&#x28;" u2="&#x155;" k="16" />
<hkern u1="&#x28;" u2="&#x153;" k="39" />
<hkern u1="&#x28;" u2="&#x152;" k="29" />
<hkern u1="&#x28;" u2="&#x151;" k="39" />
<hkern u1="&#x28;" u2="&#x150;" k="29" />
<hkern u1="&#x28;" u2="&#x14f;" k="39" />
<hkern u1="&#x28;" u2="&#x14e;" k="29" />
<hkern u1="&#x28;" u2="&#x14d;" k="39" />
<hkern u1="&#x28;" u2="&#x14c;" k="29" />
<hkern u1="&#x28;" u2="&#x14b;" k="16" />
<hkern u1="&#x28;" u2="&#x148;" k="16" />
<hkern u1="&#x28;" u2="&#x146;" k="16" />
<hkern u1="&#x28;" u2="&#x144;" k="16" />
<hkern u1="&#x28;" u2="&#x135;" k="-33" />
<hkern u1="&#x28;" u2="&#x12d;" k="-45" />
<hkern u1="&#x28;" u2="&#x129;" k="-14" />
<hkern u1="&#x28;" u2="&#x122;" k="29" />
<hkern u1="&#x28;" u2="&#x120;" k="29" />
<hkern u1="&#x28;" u2="&#x11e;" k="29" />
<hkern u1="&#x28;" u2="&#x11c;" k="29" />
<hkern u1="&#x28;" u2="&#x11b;" k="39" />
<hkern u1="&#x28;" u2="&#x119;" k="39" />
<hkern u1="&#x28;" u2="&#x117;" k="39" />
<hkern u1="&#x28;" u2="&#x115;" k="39" />
<hkern u1="&#x28;" u2="&#x113;" k="39" />
<hkern u1="&#x28;" u2="&#x111;" k="37" />
<hkern u1="&#x28;" u2="&#x10f;" k="37" />
<hkern u1="&#x28;" u2="&#x10d;" k="39" />
<hkern u1="&#x28;" u2="&#x10c;" k="27" />
<hkern u1="&#x28;" u2="&#x10b;" k="39" />
<hkern u1="&#x28;" u2="&#x10a;" k="27" />
<hkern u1="&#x28;" u2="&#x109;" k="39" />
<hkern u1="&#x28;" u2="&#x108;" k="27" />
<hkern u1="&#x28;" u2="&#x107;" k="39" />
<hkern u1="&#x28;" u2="&#x106;" k="27" />
<hkern u1="&#x28;" u2="&#x105;" k="18" />
<hkern u1="&#x28;" u2="&#x103;" k="18" />
<hkern u1="&#x28;" u2="&#x101;" k="18" />
<hkern u1="&#x28;" u2="&#xff;" k="14" />
<hkern u1="&#x28;" u2="&#xfd;" k="14" />
<hkern u1="&#x28;" u2="&#xfc;" k="29" />
<hkern u1="&#x28;" u2="&#xfb;" k="29" />
<hkern u1="&#x28;" u2="&#xfa;" k="29" />
<hkern u1="&#x28;" u2="&#xf9;" k="29" />
<hkern u1="&#x28;" u2="&#xf8;" k="39" />
<hkern u1="&#x28;" u2="&#xf6;" k="39" />
<hkern u1="&#x28;" u2="&#xf5;" k="39" />
<hkern u1="&#x28;" u2="&#xf4;" k="39" />
<hkern u1="&#x28;" u2="&#xf3;" k="39" />
<hkern u1="&#x28;" u2="&#xf2;" k="39" />
<hkern u1="&#x28;" u2="&#xf1;" k="16" />
<hkern u1="&#x28;" u2="&#xef;" k="-57" />
<hkern u1="&#x28;" u2="&#xec;" k="-43" />
<hkern u1="&#x28;" u2="&#xeb;" k="39" />
<hkern u1="&#x28;" u2="&#xea;" k="39" />
<hkern u1="&#x28;" u2="&#xe9;" k="39" />
<hkern u1="&#x28;" u2="&#xe8;" k="39" />
<hkern u1="&#x28;" u2="&#xe7;" k="39" />
<hkern u1="&#x28;" u2="&#xe6;" k="18" />
<hkern u1="&#x28;" u2="&#xe5;" k="18" />
<hkern u1="&#x28;" u2="&#xe4;" k="18" />
<hkern u1="&#x28;" u2="&#xe3;" k="18" />
<hkern u1="&#x28;" u2="&#xe2;" k="18" />
<hkern u1="&#x28;" u2="&#xe1;" k="18" />
<hkern u1="&#x28;" u2="&#xe0;" k="18" />
<hkern u1="&#x28;" u2="&#xd8;" k="29" />
<hkern u1="&#x28;" u2="&#xd6;" k="29" />
<hkern u1="&#x28;" u2="&#xd5;" k="29" />
<hkern u1="&#x28;" u2="&#xd4;" k="29" />
<hkern u1="&#x28;" u2="&#xd3;" k="29" />
<hkern u1="&#x28;" u2="&#xd2;" k="29" />
<hkern u1="&#x28;" u2="&#xc7;" k="27" />
<hkern u1="&#x28;" u2="&#x7b;" k="25" />
<hkern u1="&#x28;" u2="y" k="14" />
<hkern u1="&#x28;" u2="w" k="20" />
<hkern u1="&#x28;" u2="v" k="14" />
<hkern u1="&#x28;" u2="u" k="29" />
<hkern u1="&#x28;" u2="s" k="14" />
<hkern u1="&#x28;" u2="r" k="16" />
<hkern u1="&#x28;" u2="q" k="37" />
<hkern u1="&#x28;" u2="p" k="16" />
<hkern u1="&#x28;" u2="o" k="39" />
<hkern u1="&#x28;" u2="n" k="16" />
<hkern u1="&#x28;" u2="m" k="16" />
<hkern u1="&#x28;" u2="j" k="-33" />
<hkern u1="&#x28;" u2="f" k="14" />
<hkern u1="&#x28;" u2="e" k="39" />
<hkern u1="&#x28;" u2="d" k="37" />
<hkern u1="&#x28;" u2="c" k="39" />
<hkern u1="&#x28;" u2="a" k="18" />
<hkern u1="&#x28;" u2="Q" k="29" />
<hkern u1="&#x28;" u2="O" k="29" />
<hkern u1="&#x28;" u2="G" k="29" />
<hkern u1="&#x28;" u2="C" k="27" />
<hkern u1="&#x29;" u2="&#x7d;" k="8" />
<hkern u1="&#x29;" u2="]" k="8" />
<hkern u1="&#x2a;" u2="&#x21a;" k="-14" />
<hkern u1="&#x2a;" u2="&#x219;" k="23" />
<hkern u1="&#x2a;" u2="&#x1ff;" k="33" />
<hkern u1="&#x2a;" u2="&#x1fc;" k="68" />
<hkern u1="&#x2a;" u2="&#x1fa;" k="68" />
<hkern u1="&#x2a;" u2="&#x17d;" k="10" />
<hkern u1="&#x2a;" u2="&#x17b;" k="10" />
<hkern u1="&#x2a;" u2="&#x179;" k="10" />
<hkern u1="&#x2a;" u2="&#x167;" k="-29" />
<hkern u1="&#x2a;" u2="&#x166;" k="-14" />
<hkern u1="&#x2a;" u2="&#x165;" k="-27" />
<hkern u1="&#x2a;" u2="&#x164;" k="-14" />
<hkern u1="&#x2a;" u2="&#x161;" k="23" />
<hkern u1="&#x2a;" u2="&#x15f;" k="23" />
<hkern u1="&#x2a;" u2="&#x15d;" k="23" />
<hkern u1="&#x2a;" u2="&#x15b;" k="23" />
<hkern u1="&#x2a;" u2="&#x153;" k="33" />
<hkern u1="&#x2a;" u2="&#x151;" k="33" />
<hkern u1="&#x2a;" u2="&#x14f;" k="33" />
<hkern u1="&#x2a;" u2="&#x14d;" k="33" />
<hkern u1="&#x2a;" u2="&#x135;" k="-70" />
<hkern u1="&#x2a;" u2="&#x134;" k="31" />
<hkern u1="&#x2a;" u2="&#x129;" k="-37" />
<hkern u1="&#x2a;" u2="&#x123;" k="29" />
<hkern u1="&#x2a;" u2="&#x121;" k="29" />
<hkern u1="&#x2a;" u2="&#x11f;" k="29" />
<hkern u1="&#x2a;" u2="&#x11d;" k="29" />
<hkern u1="&#x2a;" u2="&#x11b;" k="33" />
<hkern u1="&#x2a;" u2="&#x119;" k="33" />
<hkern u1="&#x2a;" u2="&#x117;" k="33" />
<hkern u1="&#x2a;" u2="&#x115;" k="33" />
<hkern u1="&#x2a;" u2="&#x113;" k="33" />
<hkern u1="&#x2a;" u2="&#x111;" k="39" />
<hkern u1="&#x2a;" u2="&#x10f;" k="39" />
<hkern u1="&#x2a;" u2="&#x10d;" k="33" />
<hkern u1="&#x2a;" u2="&#x10b;" k="33" />
<hkern u1="&#x2a;" u2="&#x109;" k="33" />
<hkern u1="&#x2a;" u2="&#x107;" k="33" />
<hkern u1="&#x2a;" u2="&#x104;" k="68" />
<hkern u1="&#x2a;" u2="&#x102;" k="68" />
<hkern u1="&#x2a;" u2="&#x100;" k="68" />
<hkern u1="&#x2a;" u2="&#xf8;" k="33" />
<hkern u1="&#x2a;" u2="&#xf6;" k="33" />
<hkern u1="&#x2a;" u2="&#xf5;" k="33" />
<hkern u1="&#x2a;" u2="&#xf4;" k="33" />
<hkern u1="&#x2a;" u2="&#xf3;" k="33" />
<hkern u1="&#x2a;" u2="&#xf2;" k="33" />
<hkern u1="&#x2a;" u2="&#xef;" k="-61" />
<hkern u1="&#x2a;" u2="&#xee;" k="-80" />
<hkern u1="&#x2a;" u2="&#xec;" k="-37" />
<hkern u1="&#x2a;" u2="&#xeb;" k="33" />
<hkern u1="&#x2a;" u2="&#xea;" k="33" />
<hkern u1="&#x2a;" u2="&#xe9;" k="33" />
<hkern u1="&#x2a;" u2="&#xe8;" k="33" />
<hkern u1="&#x2a;" u2="&#xe7;" k="33" />
<hkern u1="&#x2a;" u2="&#xc6;" k="84" />
<hkern u1="&#x2a;" u2="&#xc5;" k="68" />
<hkern u1="&#x2a;" u2="&#xc4;" k="68" />
<hkern u1="&#x2a;" u2="&#xc3;" k="68" />
<hkern u1="&#x2a;" u2="&#xc2;" k="68" />
<hkern u1="&#x2a;" u2="&#xc1;" k="68" />
<hkern u1="&#x2a;" u2="&#xc0;" k="68" />
<hkern u1="&#x2a;" u2="s" k="23" />
<hkern u1="&#x2a;" u2="q" k="39" />
<hkern u1="&#x2a;" u2="o" k="33" />
<hkern u1="&#x2a;" u2="g" k="29" />
<hkern u1="&#x2a;" u2="e" k="33" />
<hkern u1="&#x2a;" u2="d" k="39" />
<hkern u1="&#x2a;" u2="c" k="33" />
<hkern u1="&#x2a;" u2="Z" k="10" />
<hkern u1="&#x2a;" u2="T" k="-14" />
<hkern u1="&#x2a;" u2="J" k="31" />
<hkern u1="&#x2a;" u2="A" k="68" />
<hkern u1="&#x2c;" u2="v" k="57" />
<hkern u1="&#x2c;" u2="f" k="20" />
<hkern u1="&#x2c;" u2="V" k="92" />
<hkern u1="&#x2d;" u2="&#xc6;" k="27" />
<hkern u1="&#x2d;" u2="x" k="53" />
<hkern u1="&#x2d;" u2="v" k="23" />
<hkern u1="&#x2d;" u2="f" k="23" />
<hkern u1="&#x2d;" u2="X" k="72" />
<hkern u1="&#x2d;" u2="V" k="55" />
<hkern u1="&#x2e;" u2="v" k="57" />
<hkern u1="&#x2e;" u2="f" k="20" />
<hkern u1="&#x2e;" u2="V" k="92" />
<hkern u1="&#x2f;" u2="&#x219;" k="57" />
<hkern u1="&#x2f;" u2="&#x1ff;" k="72" />
<hkern u1="&#x2f;" u2="&#x1fe;" k="23" />
<hkern u1="&#x2f;" u2="&#x1fd;" k="51" />
<hkern u1="&#x2f;" u2="&#x1fc;" k="82" />
<hkern u1="&#x2f;" u2="&#x1fb;" k="51" />
<hkern u1="&#x2f;" u2="&#x1fa;" k="82" />
<hkern u1="&#x2f;" u2="&#x17e;" k="27" />
<hkern u1="&#x2f;" u2="&#x17c;" k="27" />
<hkern u1="&#x2f;" u2="&#x17a;" k="27" />
<hkern u1="&#x2f;" u2="&#x177;" k="20" />
<hkern u1="&#x2f;" u2="&#x175;" k="18" />
<hkern u1="&#x2f;" u2="&#x173;" k="39" />
<hkern u1="&#x2f;" u2="&#x171;" k="39" />
<hkern u1="&#x2f;" u2="&#x16f;" k="39" />
<hkern u1="&#x2f;" u2="&#x16d;" k="39" />
<hkern u1="&#x2f;" u2="&#x16b;" k="39" />
<hkern u1="&#x2f;" u2="&#x169;" k="39" />
<hkern u1="&#x2f;" u2="&#x161;" k="57" />
<hkern u1="&#x2f;" u2="&#x15f;" k="57" />
<hkern u1="&#x2f;" u2="&#x15d;" k="57" />
<hkern u1="&#x2f;" u2="&#x15b;" k="57" />
<hkern u1="&#x2f;" u2="&#x159;" k="43" />
<hkern u1="&#x2f;" u2="&#x157;" k="43" />
<hkern u1="&#x2f;" u2="&#x155;" k="43" />
<hkern u1="&#x2f;" u2="&#x153;" k="72" />
<hkern u1="&#x2f;" u2="&#x152;" k="23" />
<hkern u1="&#x2f;" u2="&#x151;" k="72" />
<hkern u1="&#x2f;" u2="&#x150;" k="23" />
<hkern u1="&#x2f;" u2="&#x14f;" k="72" />
<hkern u1="&#x2f;" u2="&#x14e;" k="23" />
<hkern u1="&#x2f;" u2="&#x14d;" k="72" />
<hkern u1="&#x2f;" u2="&#x14c;" k="23" />
<hkern u1="&#x2f;" u2="&#x14b;" k="43" />
<hkern u1="&#x2f;" u2="&#x148;" k="43" />
<hkern u1="&#x2f;" u2="&#x146;" k="43" />
<hkern u1="&#x2f;" u2="&#x144;" k="43" />
<hkern u1="&#x2f;" u2="&#x134;" k="33" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-41" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-29" />
<hkern u1="&#x2f;" u2="&#x129;" k="-66" />
<hkern u1="&#x2f;" u2="&#x123;" k="68" />
<hkern u1="&#x2f;" u2="&#x122;" k="23" />
<hkern u1="&#x2f;" u2="&#x121;" k="68" />
<hkern u1="&#x2f;" u2="&#x120;" k="23" />
<hkern u1="&#x2f;" u2="&#x11f;" k="68" />
<hkern u1="&#x2f;" u2="&#x11e;" k="23" />
<hkern u1="&#x2f;" u2="&#x11d;" k="68" />
<hkern u1="&#x2f;" u2="&#x11c;" k="23" />
<hkern u1="&#x2f;" u2="&#x11b;" k="72" />
<hkern u1="&#x2f;" u2="&#x119;" k="72" />
<hkern u1="&#x2f;" u2="&#x117;" k="72" />
<hkern u1="&#x2f;" u2="&#x115;" k="72" />
<hkern u1="&#x2f;" u2="&#x113;" k="72" />
<hkern u1="&#x2f;" u2="&#x111;" k="72" />
<hkern u1="&#x2f;" u2="&#x10f;" k="72" />
<hkern u1="&#x2f;" u2="&#x10d;" k="72" />
<hkern u1="&#x2f;" u2="&#x10c;" k="18" />
<hkern u1="&#x2f;" u2="&#x10b;" k="72" />
<hkern u1="&#x2f;" u2="&#x10a;" k="18" />
<hkern u1="&#x2f;" u2="&#x109;" k="72" />
<hkern u1="&#x2f;" u2="&#x108;" k="18" />
<hkern u1="&#x2f;" u2="&#x107;" k="72" />
<hkern u1="&#x2f;" u2="&#x106;" k="18" />
<hkern u1="&#x2f;" u2="&#x105;" k="51" />
<hkern u1="&#x2f;" u2="&#x104;" k="82" />
<hkern u1="&#x2f;" u2="&#x103;" k="51" />
<hkern u1="&#x2f;" u2="&#x102;" k="82" />
<hkern u1="&#x2f;" u2="&#x101;" k="51" />
<hkern u1="&#x2f;" u2="&#x100;" k="82" />
<hkern u1="&#x2f;" u2="&#xff;" k="20" />
<hkern u1="&#x2f;" u2="&#xfd;" k="20" />
<hkern u1="&#x2f;" u2="&#xfc;" k="39" />
<hkern u1="&#x2f;" u2="&#xfb;" k="39" />
<hkern u1="&#x2f;" u2="&#xfa;" k="39" />
<hkern u1="&#x2f;" u2="&#xf9;" k="39" />
<hkern u1="&#x2f;" u2="&#xf8;" k="72" />
<hkern u1="&#x2f;" u2="&#xf6;" k="72" />
<hkern u1="&#x2f;" u2="&#xf5;" k="72" />
<hkern u1="&#x2f;" u2="&#xf4;" k="72" />
<hkern u1="&#x2f;" u2="&#xf3;" k="72" />
<hkern u1="&#x2f;" u2="&#xf2;" k="72" />
<hkern u1="&#x2f;" u2="&#xf1;" k="43" />
<hkern u1="&#x2f;" u2="&#xef;" k="-74" />
<hkern u1="&#x2f;" u2="&#xec;" k="-86" />
<hkern u1="&#x2f;" u2="&#xeb;" k="72" />
<hkern u1="&#x2f;" u2="&#xea;" k="72" />
<hkern u1="&#x2f;" u2="&#xe9;" k="72" />
<hkern u1="&#x2f;" u2="&#xe8;" k="72" />
<hkern u1="&#x2f;" u2="&#xe7;" k="72" />
<hkern u1="&#x2f;" u2="&#xe6;" k="51" />
<hkern u1="&#x2f;" u2="&#xe5;" k="51" />
<hkern u1="&#x2f;" u2="&#xe4;" k="51" />
<hkern u1="&#x2f;" u2="&#xe3;" k="51" />
<hkern u1="&#x2f;" u2="&#xe2;" k="51" />
<hkern u1="&#x2f;" u2="&#xe1;" k="51" />
<hkern u1="&#x2f;" u2="&#xe0;" k="51" />
<hkern u1="&#x2f;" u2="&#xd8;" k="23" />
<hkern u1="&#x2f;" u2="&#xd6;" k="23" />
<hkern u1="&#x2f;" u2="&#xd5;" k="23" />
<hkern u1="&#x2f;" u2="&#xd4;" k="23" />
<hkern u1="&#x2f;" u2="&#xd3;" k="23" />
<hkern u1="&#x2f;" u2="&#xd2;" k="23" />
<hkern u1="&#x2f;" u2="&#xc7;" k="18" />
<hkern u1="&#x2f;" u2="&#xc6;" k="96" />
<hkern u1="&#x2f;" u2="&#xc5;" k="82" />
<hkern u1="&#x2f;" u2="&#xc4;" k="82" />
<hkern u1="&#x2f;" u2="&#xc3;" k="82" />
<hkern u1="&#x2f;" u2="&#xc2;" k="82" />
<hkern u1="&#x2f;" u2="&#xc1;" k="82" />
<hkern u1="&#x2f;" u2="&#xc0;" k="82" />
<hkern u1="&#x2f;" u2="z" k="27" />
<hkern u1="&#x2f;" u2="y" k="20" />
<hkern u1="&#x2f;" u2="w" k="18" />
<hkern u1="&#x2f;" u2="v" k="20" />
<hkern u1="&#x2f;" u2="u" k="39" />
<hkern u1="&#x2f;" u2="s" k="57" />
<hkern u1="&#x2f;" u2="r" k="43" />
<hkern u1="&#x2f;" u2="q" k="72" />
<hkern u1="&#x2f;" u2="p" k="43" />
<hkern u1="&#x2f;" u2="o" k="72" />
<hkern u1="&#x2f;" u2="n" k="43" />
<hkern u1="&#x2f;" u2="m" k="43" />
<hkern u1="&#x2f;" u2="g" k="68" />
<hkern u1="&#x2f;" u2="e" k="72" />
<hkern u1="&#x2f;" u2="d" k="72" />
<hkern u1="&#x2f;" u2="c" k="72" />
<hkern u1="&#x2f;" u2="a" k="51" />
<hkern u1="&#x2f;" u2="Q" k="23" />
<hkern u1="&#x2f;" u2="O" k="23" />
<hkern u1="&#x2f;" u2="J" k="33" />
<hkern u1="&#x2f;" u2="G" k="23" />
<hkern u1="&#x2f;" u2="C" k="18" />
<hkern u1="&#x2f;" u2="A" k="82" />
<hkern u1="&#x2f;" u2="&#x2f;" k="598" />
<hkern u1="&#x3a;" u2="V" k="25" />
<hkern u1="&#x3b;" u2="V" k="25" />
<hkern u1="&#x40;" u2="&#x21a;" k="14" />
<hkern u1="&#x40;" u2="&#x178;" k="55" />
<hkern u1="&#x40;" u2="&#x176;" k="55" />
<hkern u1="&#x40;" u2="&#x166;" k="14" />
<hkern u1="&#x40;" u2="&#x164;" k="14" />
<hkern u1="&#x40;" u2="&#x134;" k="6" />
<hkern u1="&#x40;" u2="&#xdd;" k="55" />
<hkern u1="&#x40;" u2="&#xc6;" k="6" />
<hkern u1="&#x40;" u2="Y" k="55" />
<hkern u1="&#x40;" u2="V" k="6" />
<hkern u1="&#x40;" u2="T" k="14" />
<hkern u1="&#x40;" u2="J" k="6" />
<hkern u1="A" u2="&#x2122;" k="72" />
<hkern u1="A" u2="&#xae;" k="43" />
<hkern u1="A" u2="&#x7d;" k="18" />
<hkern u1="A" u2="v" k="33" />
<hkern u1="A" u2="f" k="16" />
<hkern u1="A" u2="]" k="20" />
<hkern u1="A" u2="\" k="90" />
<hkern u1="A" u2="V" k="57" />
<hkern u1="A" u2="&#x3f;" k="39" />
<hkern u1="A" u2="&#x2a;" k="63" />
<hkern u1="B" u2="&#x21a;" k="14" />
<hkern u1="B" u2="&#x1fc;" k="18" />
<hkern u1="B" u2="&#x1fa;" k="18" />
<hkern u1="B" u2="&#x178;" k="47" />
<hkern u1="B" u2="&#x176;" k="47" />
<hkern u1="B" u2="&#x166;" k="14" />
<hkern u1="B" u2="&#x164;" k="14" />
<hkern u1="B" u2="&#x134;" k="14" />
<hkern u1="B" u2="&#x123;" k="16" />
<hkern u1="B" u2="&#x121;" k="16" />
<hkern u1="B" u2="&#x11f;" k="16" />
<hkern u1="B" u2="&#x11d;" k="16" />
<hkern u1="B" u2="&#x104;" k="18" />
<hkern u1="B" u2="&#x102;" k="18" />
<hkern u1="B" u2="&#x100;" k="18" />
<hkern u1="B" u2="&#xdd;" k="47" />
<hkern u1="B" u2="&#xc6;" k="25" />
<hkern u1="B" u2="&#xc5;" k="18" />
<hkern u1="B" u2="&#xc4;" k="18" />
<hkern u1="B" u2="&#xc3;" k="18" />
<hkern u1="B" u2="&#xc2;" k="18" />
<hkern u1="B" u2="&#xc1;" k="18" />
<hkern u1="B" u2="&#xc0;" k="18" />
<hkern u1="B" u2="&#x7d;" k="18" />
<hkern u1="B" u2="g" k="16" />
<hkern u1="B" u2="]" k="39" />
<hkern u1="B" u2="\" k="25" />
<hkern u1="B" u2="Y" k="47" />
<hkern u1="B" u2="X" k="25" />
<hkern u1="B" u2="V" k="20" />
<hkern u1="B" u2="T" k="14" />
<hkern u1="B" u2="J" k="14" />
<hkern u1="B" u2="A" k="18" />
<hkern u1="B" u2="&#x3f;" k="6" />
<hkern u1="C" u2="&#x135;" k="-33" />
<hkern u1="C" u2="&#x12d;" k="-25" />
<hkern u1="C" u2="&#x12b;" k="-8" />
<hkern u1="C" u2="&#x129;" k="-53" />
<hkern u1="C" u2="&#xef;" k="-53" />
<hkern u1="C" u2="&#xee;" k="-43" />
<hkern u1="C" u2="&#xec;" k="-80" />
<hkern u1="C" u2="&#xae;" k="10" />
<hkern u1="C" u2="v" k="20" />
<hkern u1="C" u2="f" k="10" />
<hkern u1="D" u2="&#xc6;" k="31" />
<hkern u1="D" u2="&#x7d;" k="37" />
<hkern u1="D" u2="]" k="47" />
<hkern u1="D" u2="\" k="27" />
<hkern u1="D" u2="X" k="39" />
<hkern u1="D" u2="V" k="23" />
<hkern u1="D" u2="&#x3f;" k="8" />
<hkern u1="D" u2="&#x2f;" k="23" />
<hkern u1="D" u2="&#x29;" k="25" />
<hkern u1="E" u2="&#x135;" k="-33" />
<hkern u1="E" u2="&#x12d;" k="-16" />
<hkern u1="E" u2="&#x12b;" k="-6" />
<hkern u1="E" u2="&#x129;" k="-47" />
<hkern u1="E" u2="&#xef;" k="-49" />
<hkern u1="E" u2="&#xee;" k="-43" />
<hkern u1="E" u2="&#xec;" k="-80" />
<hkern u1="E" u2="v" k="10" />
<hkern u1="F" u2="&#x2026;" k="121" />
<hkern u1="F" u2="&#x201e;" k="121" />
<hkern u1="F" u2="&#x201a;" k="121" />
<hkern u1="F" u2="&#x2014;" k="14" />
<hkern u1="F" u2="&#x2013;" k="14" />
<hkern u1="F" u2="&#x21b;" k="6" />
<hkern u1="F" u2="&#x219;" k="35" />
<hkern u1="F" u2="&#x218;" k="8" />
<hkern u1="F" u2="&#x1ff;" k="35" />
<hkern u1="F" u2="&#x1fe;" k="8" />
<hkern u1="F" u2="&#x1fd;" k="49" />
<hkern u1="F" u2="&#x1fc;" k="53" />
<hkern u1="F" u2="&#x1fb;" k="49" />
<hkern u1="F" u2="&#x1fa;" k="53" />
<hkern u1="F" u2="&#x17e;" k="29" />
<hkern u1="F" u2="&#x17c;" k="29" />
<hkern u1="F" u2="&#x17a;" k="29" />
<hkern u1="F" u2="&#x177;" k="18" />
<hkern u1="F" u2="&#x175;" k="18" />
<hkern u1="F" u2="&#x173;" k="31" />
<hkern u1="F" u2="&#x171;" k="31" />
<hkern u1="F" u2="&#x16f;" k="31" />
<hkern u1="F" u2="&#x16d;" k="31" />
<hkern u1="F" u2="&#x16b;" k="31" />
<hkern u1="F" u2="&#x169;" k="31" />
<hkern u1="F" u2="&#x167;" k="6" />
<hkern u1="F" u2="&#x165;" k="6" />
<hkern u1="F" u2="&#x161;" k="35" />
<hkern u1="F" u2="&#x160;" k="8" />
<hkern u1="F" u2="&#x15f;" k="35" />
<hkern u1="F" u2="&#x15e;" k="8" />
<hkern u1="F" u2="&#x15d;" k="35" />
<hkern u1="F" u2="&#x15c;" k="8" />
<hkern u1="F" u2="&#x15b;" k="35" />
<hkern u1="F" u2="&#x15a;" k="8" />
<hkern u1="F" u2="&#x159;" k="39" />
<hkern u1="F" u2="&#x157;" k="39" />
<hkern u1="F" u2="&#x155;" k="39" />
<hkern u1="F" u2="&#x153;" k="35" />
<hkern u1="F" u2="&#x152;" k="8" />
<hkern u1="F" u2="&#x151;" k="35" />
<hkern u1="F" u2="&#x150;" k="8" />
<hkern u1="F" u2="&#x14f;" k="35" />
<hkern u1="F" u2="&#x14e;" k="8" />
<hkern u1="F" u2="&#x14d;" k="35" />
<hkern u1="F" u2="&#x14c;" k="8" />
<hkern u1="F" u2="&#x14b;" k="39" />
<hkern u1="F" u2="&#x148;" k="39" />
<hkern u1="F" u2="&#x146;" k="39" />
<hkern u1="F" u2="&#x144;" k="39" />
<hkern u1="F" u2="&#x135;" k="-59" />
<hkern u1="F" u2="&#x134;" k="29" />
<hkern u1="F" u2="&#x131;" k="39" />
<hkern u1="F" u2="&#x12d;" k="-53" />
<hkern u1="F" u2="&#x12b;" k="-39" />
<hkern u1="F" u2="&#x129;" k="-84" />
<hkern u1="F" u2="&#x123;" k="43" />
<hkern u1="F" u2="&#x122;" k="8" />
<hkern u1="F" u2="&#x121;" k="43" />
<hkern u1="F" u2="&#x120;" k="8" />
<hkern u1="F" u2="&#x11f;" k="43" />
<hkern u1="F" u2="&#x11e;" k="8" />
<hkern u1="F" u2="&#x11d;" k="43" />
<hkern u1="F" u2="&#x11c;" k="8" />
<hkern u1="F" u2="&#x11b;" k="35" />
<hkern u1="F" u2="&#x119;" k="35" />
<hkern u1="F" u2="&#x117;" k="35" />
<hkern u1="F" u2="&#x115;" k="35" />
<hkern u1="F" u2="&#x113;" k="35" />
<hkern u1="F" u2="&#x111;" k="39" />
<hkern u1="F" u2="&#x10f;" k="39" />
<hkern u1="F" u2="&#x10d;" k="35" />
<hkern u1="F" u2="&#x10c;" k="8" />
<hkern u1="F" u2="&#x10b;" k="35" />
<hkern u1="F" u2="&#x10a;" k="8" />
<hkern u1="F" u2="&#x109;" k="35" />
<hkern u1="F" u2="&#x108;" k="8" />
<hkern u1="F" u2="&#x107;" k="35" />
<hkern u1="F" u2="&#x106;" k="8" />
<hkern u1="F" u2="&#x105;" k="49" />
<hkern u1="F" u2="&#x104;" k="53" />
<hkern u1="F" u2="&#x103;" k="49" />
<hkern u1="F" u2="&#x102;" k="53" />
<hkern u1="F" u2="&#x101;" k="49" />
<hkern u1="F" u2="&#x100;" k="53" />
<hkern u1="F" u2="&#xff;" k="18" />
<hkern u1="F" u2="&#xfd;" k="18" />
<hkern u1="F" u2="&#xfc;" k="31" />
<hkern u1="F" u2="&#xfb;" k="31" />
<hkern u1="F" u2="&#xfa;" k="31" />
<hkern u1="F" u2="&#xf9;" k="31" />
<hkern u1="F" u2="&#xf8;" k="35" />
<hkern u1="F" u2="&#xf6;" k="35" />
<hkern u1="F" u2="&#xf5;" k="35" />
<hkern u1="F" u2="&#xf4;" k="35" />
<hkern u1="F" u2="&#xf3;" k="35" />
<hkern u1="F" u2="&#xf2;" k="35" />
<hkern u1="F" u2="&#xf1;" k="39" />
<hkern u1="F" u2="&#xef;" k="-82" />
<hkern u1="F" u2="&#xee;" k="-70" />
<hkern u1="F" u2="&#xec;" k="-117" />
<hkern u1="F" u2="&#xeb;" k="35" />
<hkern u1="F" u2="&#xea;" k="35" />
<hkern u1="F" u2="&#xe9;" k="35" />
<hkern u1="F" u2="&#xe8;" k="35" />
<hkern u1="F" u2="&#xe7;" k="35" />
<hkern u1="F" u2="&#xe6;" k="49" />
<hkern u1="F" u2="&#xe5;" k="49" />
<hkern u1="F" u2="&#xe4;" k="49" />
<hkern u1="F" u2="&#xe3;" k="49" />
<hkern u1="F" u2="&#xe2;" k="49" />
<hkern u1="F" u2="&#xe1;" k="49" />
<hkern u1="F" u2="&#xe0;" k="49" />
<hkern u1="F" u2="&#xd8;" k="8" />
<hkern u1="F" u2="&#xd6;" k="8" />
<hkern u1="F" u2="&#xd5;" k="8" />
<hkern u1="F" u2="&#xd4;" k="8" />
<hkern u1="F" u2="&#xd3;" k="8" />
<hkern u1="F" u2="&#xd2;" k="8" />
<hkern u1="F" u2="&#xc7;" k="8" />
<hkern u1="F" u2="&#xc6;" k="76" />
<hkern u1="F" u2="&#xc5;" k="53" />
<hkern u1="F" u2="&#xc4;" k="53" />
<hkern u1="F" u2="&#xc3;" k="53" />
<hkern u1="F" u2="&#xc2;" k="53" />
<hkern u1="F" u2="&#xc1;" k="53" />
<hkern u1="F" u2="&#xc0;" k="53" />
<hkern u1="F" u2="z" k="29" />
<hkern u1="F" u2="y" k="18" />
<hkern u1="F" u2="x" k="29" />
<hkern u1="F" u2="w" k="18" />
<hkern u1="F" u2="v" k="10" />
<hkern u1="F" u2="u" k="31" />
<hkern u1="F" u2="t" k="6" />
<hkern u1="F" u2="s" k="35" />
<hkern u1="F" u2="r" k="39" />
<hkern u1="F" u2="q" k="39" />
<hkern u1="F" u2="p" k="39" />
<hkern u1="F" u2="o" k="35" />
<hkern u1="F" u2="n" k="39" />
<hkern u1="F" u2="m" k="39" />
<hkern u1="F" u2="g" k="43" />
<hkern u1="F" u2="f" k="12" />
<hkern u1="F" u2="e" k="35" />
<hkern u1="F" u2="d" k="39" />
<hkern u1="F" u2="c" k="35" />
<hkern u1="F" u2="a" k="49" />
<hkern u1="F" u2="S" k="8" />
<hkern u1="F" u2="Q" k="8" />
<hkern u1="F" u2="O" k="8" />
<hkern u1="F" u2="J" k="29" />
<hkern u1="F" u2="G" k="8" />
<hkern u1="F" u2="C" k="8" />
<hkern u1="F" u2="A" k="53" />
<hkern u1="F" u2="&#x2f;" k="72" />
<hkern u1="F" u2="&#x2e;" k="121" />
<hkern u1="F" u2="&#x2d;" k="14" />
<hkern u1="F" u2="&#x2c;" k="121" />
<hkern u1="G" u2="&#xef;" k="-23" />
<hkern u1="G" u2="&#xee;" k="-14" />
<hkern u1="G" u2="&#xec;" k="-43" />
<hkern u1="G" u2="v" k="12" />
<hkern u1="G" u2="f" k="12" />
<hkern u1="G" u2="\" k="14" />
<hkern u1="G" u2="V" k="18" />
<hkern u1="H" u2="&#xec;" k="-12" />
<hkern u1="H" u2="f" k="8" />
<hkern u1="I" u2="&#xec;" k="-12" />
<hkern u1="I" u2="f" k="8" />
<hkern u1="J" u2="&#xec;" k="-16" />
<hkern u1="J" u2="f" k="8" />
<hkern u1="K" u2="&#x12d;" k="-55" />
<hkern u1="K" u2="&#x12b;" k="-31" />
<hkern u1="K" u2="&#x129;" k="-68" />
<hkern u1="K" u2="&#xef;" k="-84" />
<hkern u1="K" u2="&#xee;" k="-8" />
<hkern u1="K" u2="&#xec;" k="-98" />
<hkern u1="K" u2="&#xae;" k="10" />
<hkern u1="K" u2="v" k="41" />
<hkern u1="K" u2="f" k="16" />
<hkern u1="L" u2="&#x2122;" k="168" />
<hkern u1="L" u2="&#xae;" k="145" />
<hkern u1="L" u2="&#x7d;" k="10" />
<hkern u1="L" u2="v" k="78" />
<hkern u1="L" u2="f" k="12" />
<hkern u1="L" u2="]" k="12" />
<hkern u1="L" u2="\" k="145" />
<hkern u1="L" u2="V" k="117" />
<hkern u1="L" u2="&#x3f;" k="12" />
<hkern u1="L" u2="&#x2a;" k="166" />
<hkern u1="M" u2="&#xec;" k="-12" />
<hkern u1="M" u2="f" k="8" />
<hkern u1="N" u2="&#xec;" k="-12" />
<hkern u1="N" u2="f" k="8" />
<hkern u1="O" u2="&#xc6;" k="29" />
<hkern u1="O" u2="&#x7d;" k="35" />
<hkern u1="O" u2="]" k="49" />
<hkern u1="O" u2="\" k="29" />
<hkern u1="O" u2="X" k="37" />
<hkern u1="O" u2="V" k="23" />
<hkern u1="O" u2="&#x3f;" k="6" />
<hkern u1="O" u2="&#x2f;" k="20" />
<hkern u1="O" u2="&#x29;" k="20" />
<hkern u1="P" u2="&#x2039;" k="8" />
<hkern u1="P" u2="&#x2026;" k="143" />
<hkern u1="P" u2="&#x201e;" k="143" />
<hkern u1="P" u2="&#x201a;" k="143" />
<hkern u1="P" u2="&#x2014;" k="8" />
<hkern u1="P" u2="&#x2013;" k="8" />
<hkern u1="P" u2="&#x1fd;" k="10" />
<hkern u1="P" u2="&#x1fc;" k="49" />
<hkern u1="P" u2="&#x1fb;" k="10" />
<hkern u1="P" u2="&#x1fa;" k="49" />
<hkern u1="P" u2="&#x17d;" k="6" />
<hkern u1="P" u2="&#x17b;" k="6" />
<hkern u1="P" u2="&#x179;" k="6" />
<hkern u1="P" u2="&#x178;" k="41" />
<hkern u1="P" u2="&#x176;" k="41" />
<hkern u1="P" u2="&#x135;" k="-16" />
<hkern u1="P" u2="&#x134;" k="33" />
<hkern u1="P" u2="&#x105;" k="10" />
<hkern u1="P" u2="&#x104;" k="49" />
<hkern u1="P" u2="&#x103;" k="10" />
<hkern u1="P" u2="&#x102;" k="49" />
<hkern u1="P" u2="&#x101;" k="10" />
<hkern u1="P" u2="&#x100;" k="49" />
<hkern u1="P" u2="&#xef;" k="-12" />
<hkern u1="P" u2="&#xee;" k="-25" />
<hkern u1="P" u2="&#xec;" k="-16" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xe5;" k="10" />
<hkern u1="P" u2="&#xe4;" k="10" />
<hkern u1="P" u2="&#xe3;" k="10" />
<hkern u1="P" u2="&#xe2;" k="10" />
<hkern u1="P" u2="&#xe1;" k="10" />
<hkern u1="P" u2="&#xe0;" k="10" />
<hkern u1="P" u2="&#xdd;" k="41" />
<hkern u1="P" u2="&#xc6;" k="61" />
<hkern u1="P" u2="&#xc5;" k="49" />
<hkern u1="P" u2="&#xc4;" k="49" />
<hkern u1="P" u2="&#xc3;" k="49" />
<hkern u1="P" u2="&#xc2;" k="49" />
<hkern u1="P" u2="&#xc1;" k="49" />
<hkern u1="P" u2="&#xc0;" k="49" />
<hkern u1="P" u2="&#xab;" k="8" />
<hkern u1="P" u2="&#x7d;" k="31" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="]" k="35" />
<hkern u1="P" u2="\" k="18" />
<hkern u1="P" u2="Z" k="6" />
<hkern u1="P" u2="Y" k="41" />
<hkern u1="P" u2="X" k="35" />
<hkern u1="P" u2="V" k="14" />
<hkern u1="P" u2="J" k="33" />
<hkern u1="P" u2="A" k="49" />
<hkern u1="P" u2="&#x2f;" k="76" />
<hkern u1="P" u2="&#x2e;" k="143" />
<hkern u1="P" u2="&#x2d;" k="8" />
<hkern u1="P" u2="&#x2c;" k="143" />
<hkern u1="P" u2="&#x29;" k="14" />
<hkern u1="Q" u2="&#xc6;" k="29" />
<hkern u1="Q" u2="&#x7d;" k="35" />
<hkern u1="Q" u2="]" k="49" />
<hkern u1="Q" u2="\" k="29" />
<hkern u1="Q" u2="X" k="37" />
<hkern u1="Q" u2="V" k="23" />
<hkern u1="Q" u2="&#x3f;" k="6" />
<hkern u1="Q" u2="&#x2f;" k="20" />
<hkern u1="Q" u2="&#x29;" k="20" />
<hkern u1="R" u2="&#xc6;" k="18" />
<hkern u1="R" u2="&#x7d;" k="14" />
<hkern u1="R" u2="]" k="16" />
<hkern u1="R" u2="\" k="23" />
<hkern u1="R" u2="X" k="8" />
<hkern u1="R" u2="V" k="20" />
<hkern u1="S" u2="&#x129;" k="-14" />
<hkern u1="S" u2="&#xef;" k="-31" />
<hkern u1="S" u2="&#xee;" k="-12" />
<hkern u1="S" u2="&#xec;" k="-49" />
<hkern u1="S" u2="&#xc6;" k="23" />
<hkern u1="S" u2="x" k="18" />
<hkern u1="S" u2="v" k="16" />
<hkern u1="S" u2="f" k="16" />
<hkern u1="S" u2="X" k="10" />
<hkern u1="S" u2="V" k="18" />
<hkern u1="T" u2="&#x16d;" k="135" />
<hkern u1="T" u2="&#x169;" k="135" />
<hkern u1="T" u2="&#x15d;" k="139" />
<hkern u1="T" u2="&#x159;" k="86" />
<hkern u1="T" u2="&#x155;" k="104" />
<hkern u1="T" u2="&#x151;" k="121" />
<hkern u1="T" u2="&#x135;" k="-74" />
<hkern u1="T" u2="&#x131;" k="137" />
<hkern u1="T" u2="&#x12d;" k="-76" />
<hkern u1="T" u2="&#x12b;" k="-61" />
<hkern u1="T" u2="&#x129;" k="-106" />
<hkern u1="T" u2="&#x11f;" k="170" />
<hkern u1="T" u2="&#x109;" k="121" />
<hkern u1="T" u2="&#xef;" k="-104" />
<hkern u1="T" u2="&#xee;" k="-84" />
<hkern u1="T" u2="&#xec;" k="-141" />
<hkern u1="T" u2="&#xe4;" k="135" />
<hkern u1="T" u2="&#xe3;" k="111" />
<hkern u1="T" u2="&#xc6;" k="104" />
<hkern u1="T" u2="&#xae;" k="8" />
<hkern u1="T" u2="x" k="121" />
<hkern u1="T" u2="v" k="117" />
<hkern u1="T" u2="f" k="31" />
<hkern u1="T" u2="&#x40;" k="41" />
<hkern u1="T" u2="&#x2f;" k="102" />
<hkern u1="T" u2="&#x26;" k="31" />
<hkern u1="U" u2="&#xec;" k="-20" />
<hkern u1="U" u2="&#xc6;" k="16" />
<hkern u1="U" u2="f" k="8" />
<hkern u1="U" u2="&#x2f;" k="25" />
<hkern u1="V" u2="&#x203a;" k="31" />
<hkern u1="V" u2="&#x2039;" k="53" />
<hkern u1="V" u2="&#x2026;" k="92" />
<hkern u1="V" u2="&#x201e;" k="92" />
<hkern u1="V" u2="&#x201a;" k="92" />
<hkern u1="V" u2="&#x2014;" k="55" />
<hkern u1="V" u2="&#x2013;" k="55" />
<hkern u1="V" u2="&#x219;" k="47" />
<hkern u1="V" u2="&#x218;" k="16" />
<hkern u1="V" u2="&#x1ff;" k="61" />
<hkern u1="V" u2="&#x1fe;" k="23" />
<hkern u1="V" u2="&#x1fd;" k="51" />
<hkern u1="V" u2="&#x1fc;" k="57" />
<hkern u1="V" u2="&#x1fb;" k="51" />
<hkern u1="V" u2="&#x1fa;" k="57" />
<hkern u1="V" u2="&#x17e;" k="27" />
<hkern u1="V" u2="&#x17c;" k="27" />
<hkern u1="V" u2="&#x17a;" k="27" />
<hkern u1="V" u2="&#x177;" k="14" />
<hkern u1="V" u2="&#x175;" k="18" />
<hkern u1="V" u2="&#x173;" k="41" />
<hkern u1="V" u2="&#x171;" k="41" />
<hkern u1="V" u2="&#x16f;" k="41" />
<hkern u1="V" u2="&#x16d;" k="41" />
<hkern u1="V" u2="&#x16b;" k="41" />
<hkern u1="V" u2="&#x169;" k="41" />
<hkern u1="V" u2="&#x161;" k="47" />
<hkern u1="V" u2="&#x160;" k="16" />
<hkern u1="V" u2="&#x15f;" k="47" />
<hkern u1="V" u2="&#x15e;" k="16" />
<hkern u1="V" u2="&#x15d;" k="47" />
<hkern u1="V" u2="&#x15c;" k="16" />
<hkern u1="V" u2="&#x15b;" k="47" />
<hkern u1="V" u2="&#x15a;" k="16" />
<hkern u1="V" u2="&#x159;" k="43" />
<hkern u1="V" u2="&#x157;" k="47" />
<hkern u1="V" u2="&#x155;" k="45" />
<hkern u1="V" u2="&#x153;" k="61" />
<hkern u1="V" u2="&#x152;" k="23" />
<hkern u1="V" u2="&#x151;" k="61" />
<hkern u1="V" u2="&#x150;" k="23" />
<hkern u1="V" u2="&#x14f;" k="61" />
<hkern u1="V" u2="&#x14e;" k="23" />
<hkern u1="V" u2="&#x14d;" k="61" />
<hkern u1="V" u2="&#x14c;" k="23" />
<hkern u1="V" u2="&#x14b;" k="47" />
<hkern u1="V" u2="&#x148;" k="47" />
<hkern u1="V" u2="&#x146;" k="47" />
<hkern u1="V" u2="&#x144;" k="47" />
<hkern u1="V" u2="&#x135;" k="-41" />
<hkern u1="V" u2="&#x134;" k="37" />
<hkern u1="V" u2="&#x131;" k="47" />
<hkern u1="V" u2="&#x12d;" k="-63" />
<hkern u1="V" u2="&#x12b;" k="-45" />
<hkern u1="V" u2="&#x129;" k="-84" />
<hkern u1="V" u2="&#x123;" k="68" />
<hkern u1="V" u2="&#x122;" k="23" />
<hkern u1="V" u2="&#x121;" k="68" />
<hkern u1="V" u2="&#x120;" k="23" />
<hkern u1="V" u2="&#x11f;" k="68" />
<hkern u1="V" u2="&#x11e;" k="23" />
<hkern u1="V" u2="&#x11d;" k="68" />
<hkern u1="V" u2="&#x11c;" k="23" />
<hkern u1="V" u2="&#x11b;" k="61" />
<hkern u1="V" u2="&#x119;" k="61" />
<hkern u1="V" u2="&#x117;" k="61" />
<hkern u1="V" u2="&#x115;" k="61" />
<hkern u1="V" u2="&#x113;" k="61" />
<hkern u1="V" u2="&#x111;" k="61" />
<hkern u1="V" u2="&#x10f;" k="61" />
<hkern u1="V" u2="&#x10d;" k="61" />
<hkern u1="V" u2="&#x10c;" k="20" />
<hkern u1="V" u2="&#x10b;" k="61" />
<hkern u1="V" u2="&#x10a;" k="20" />
<hkern u1="V" u2="&#x109;" k="61" />
<hkern u1="V" u2="&#x108;" k="20" />
<hkern u1="V" u2="&#x107;" k="61" />
<hkern u1="V" u2="&#x106;" k="20" />
<hkern u1="V" u2="&#x105;" k="51" />
<hkern u1="V" u2="&#x104;" k="57" />
<hkern u1="V" u2="&#x103;" k="51" />
<hkern u1="V" u2="&#x102;" k="57" />
<hkern u1="V" u2="&#x101;" k="51" />
<hkern u1="V" u2="&#x100;" k="57" />
<hkern u1="V" u2="&#xff;" k="14" />
<hkern u1="V" u2="&#xfd;" k="14" />
<hkern u1="V" u2="&#xfc;" k="41" />
<hkern u1="V" u2="&#xfb;" k="41" />
<hkern u1="V" u2="&#xfa;" k="41" />
<hkern u1="V" u2="&#xf9;" k="41" />
<hkern u1="V" u2="&#xf8;" k="61" />
<hkern u1="V" u2="&#xf6;" k="61" />
<hkern u1="V" u2="&#xf5;" k="61" />
<hkern u1="V" u2="&#xf4;" k="61" />
<hkern u1="V" u2="&#xf3;" k="61" />
<hkern u1="V" u2="&#xf2;" k="61" />
<hkern u1="V" u2="&#xf1;" k="47" />
<hkern u1="V" u2="&#xef;" k="-92" />
<hkern u1="V" u2="&#xee;" k="-45" />
<hkern u1="V" u2="&#xec;" k="-115" />
<hkern u1="V" u2="&#xeb;" k="61" />
<hkern u1="V" u2="&#xea;" k="61" />
<hkern u1="V" u2="&#xe9;" k="61" />
<hkern u1="V" u2="&#xe8;" k="61" />
<hkern u1="V" u2="&#xe7;" k="61" />
<hkern u1="V" u2="&#xe6;" k="51" />
<hkern u1="V" u2="&#xe5;" k="51" />
<hkern u1="V" u2="&#xe4;" k="51" />
<hkern u1="V" u2="&#xe3;" k="51" />
<hkern u1="V" u2="&#xe2;" k="51" />
<hkern u1="V" u2="&#xe1;" k="51" />
<hkern u1="V" u2="&#xe0;" k="51" />
<hkern u1="V" u2="&#xd8;" k="23" />
<hkern u1="V" u2="&#xd6;" k="23" />
<hkern u1="V" u2="&#xd5;" k="23" />
<hkern u1="V" u2="&#xd4;" k="23" />
<hkern u1="V" u2="&#xd3;" k="23" />
<hkern u1="V" u2="&#xd2;" k="23" />
<hkern u1="V" u2="&#xc7;" k="20" />
<hkern u1="V" u2="&#xc6;" k="66" />
<hkern u1="V" u2="&#xc5;" k="57" />
<hkern u1="V" u2="&#xc4;" k="57" />
<hkern u1="V" u2="&#xc3;" k="57" />
<hkern u1="V" u2="&#xc2;" k="57" />
<hkern u1="V" u2="&#xc1;" k="57" />
<hkern u1="V" u2="&#xc0;" k="57" />
<hkern u1="V" u2="&#xbb;" k="31" />
<hkern u1="V" u2="&#xae;" k="6" />
<hkern u1="V" u2="&#xab;" k="53" />
<hkern u1="V" u2="z" k="27" />
<hkern u1="V" u2="y" k="14" />
<hkern u1="V" u2="x" k="14" />
<hkern u1="V" u2="w" k="18" />
<hkern u1="V" u2="v" k="14" />
<hkern u1="V" u2="u" k="41" />
<hkern u1="V" u2="s" k="47" />
<hkern u1="V" u2="r" k="47" />
<hkern u1="V" u2="q" k="61" />
<hkern u1="V" u2="p" k="47" />
<hkern u1="V" u2="o" k="61" />
<hkern u1="V" u2="n" k="47" />
<hkern u1="V" u2="m" k="47" />
<hkern u1="V" u2="g" k="68" />
<hkern u1="V" u2="f" k="10" />
<hkern u1="V" u2="e" k="61" />
<hkern u1="V" u2="d" k="61" />
<hkern u1="V" u2="c" k="61" />
<hkern u1="V" u2="a" k="51" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="Q" k="23" />
<hkern u1="V" u2="O" k="23" />
<hkern u1="V" u2="J" k="37" />
<hkern u1="V" u2="G" k="23" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="57" />
<hkern u1="V" u2="&#x40;" k="27" />
<hkern u1="V" u2="&#x3b;" k="25" />
<hkern u1="V" u2="&#x3a;" k="25" />
<hkern u1="V" u2="&#x2f;" k="84" />
<hkern u1="V" u2="&#x2e;" k="92" />
<hkern u1="V" u2="&#x2d;" k="55" />
<hkern u1="V" u2="&#x2c;" k="92" />
<hkern u1="V" u2="&#x26;" k="33" />
<hkern u1="W" u2="&#x135;" k="-41" />
<hkern u1="W" u2="&#x131;" k="29" />
<hkern u1="W" u2="&#x12d;" k="-53" />
<hkern u1="W" u2="&#x12b;" k="-37" />
<hkern u1="W" u2="&#x129;" k="-76" />
<hkern u1="W" u2="&#xef;" k="-80" />
<hkern u1="W" u2="&#xee;" k="-45" />
<hkern u1="W" u2="&#xec;" k="-104" />
<hkern u1="W" u2="&#xc6;" k="51" />
<hkern u1="W" u2="&#x2f;" k="57" />
<hkern u1="W" u2="&#x26;" k="6" />
<hkern u1="X" u2="&#x2039;" k="41" />
<hkern u1="X" u2="&#x2014;" k="72" />
<hkern u1="X" u2="&#x2013;" k="72" />
<hkern u1="X" u2="&#x21b;" k="20" />
<hkern u1="X" u2="&#x1ff;" k="49" />
<hkern u1="X" u2="&#x1fe;" k="37" />
<hkern u1="X" u2="&#x1fd;" k="8" />
<hkern u1="X" u2="&#x1fb;" k="8" />
<hkern u1="X" u2="&#x177;" k="51" />
<hkern u1="X" u2="&#x175;" k="51" />
<hkern u1="X" u2="&#x173;" k="37" />
<hkern u1="X" u2="&#x171;" k="37" />
<hkern u1="X" u2="&#x16f;" k="37" />
<hkern u1="X" u2="&#x16d;" k="37" />
<hkern u1="X" u2="&#x16b;" k="37" />
<hkern u1="X" u2="&#x169;" k="37" />
<hkern u1="X" u2="&#x167;" k="20" />
<hkern u1="X" u2="&#x165;" k="20" />
<hkern u1="X" u2="&#x159;" k="8" />
<hkern u1="X" u2="&#x157;" k="8" />
<hkern u1="X" u2="&#x155;" k="8" />
<hkern u1="X" u2="&#x153;" k="49" />
<hkern u1="X" u2="&#x152;" k="37" />
<hkern u1="X" u2="&#x151;" k="49" />
<hkern u1="X" u2="&#x150;" k="37" />
<hkern u1="X" u2="&#x14f;" k="49" />
<hkern u1="X" u2="&#x14e;" k="37" />
<hkern u1="X" u2="&#x14d;" k="49" />
<hkern u1="X" u2="&#x14c;" k="37" />
<hkern u1="X" u2="&#x14b;" k="8" />
<hkern u1="X" u2="&#x148;" k="8" />
<hkern u1="X" u2="&#x146;" k="8" />
<hkern u1="X" u2="&#x144;" k="8" />
<hkern u1="X" u2="&#x135;" k="-10" />
<hkern u1="X" u2="&#x12d;" k="-76" />
<hkern u1="X" u2="&#x12b;" k="-53" />
<hkern u1="X" u2="&#x129;" k="-82" />
<hkern u1="X" u2="&#x123;" k="37" />
<hkern u1="X" u2="&#x122;" k="37" />
<hkern u1="X" u2="&#x121;" k="37" />
<hkern u1="X" u2="&#x120;" k="37" />
<hkern u1="X" u2="&#x11f;" k="37" />
<hkern u1="X" u2="&#x11e;" k="37" />
<hkern u1="X" u2="&#x11d;" k="37" />
<hkern u1="X" u2="&#x11c;" k="37" />
<hkern u1="X" u2="&#x11b;" k="49" />
<hkern u1="X" u2="&#x119;" k="49" />
<hkern u1="X" u2="&#x117;" k="49" />
<hkern u1="X" u2="&#x115;" k="49" />
<hkern u1="X" u2="&#x113;" k="49" />
<hkern u1="X" u2="&#x111;" k="39" />
<hkern u1="X" u2="&#x10f;" k="39" />
<hkern u1="X" u2="&#x10d;" k="49" />
<hkern u1="X" u2="&#x10c;" k="35" />
<hkern u1="X" u2="&#x10b;" k="49" />
<hkern u1="X" u2="&#x10a;" k="35" />
<hkern u1="X" u2="&#x109;" k="49" />
<hkern u1="X" u2="&#x108;" k="35" />
<hkern u1="X" u2="&#x107;" k="49" />
<hkern u1="X" u2="&#x106;" k="35" />
<hkern u1="X" u2="&#x105;" k="8" />
<hkern u1="X" u2="&#x103;" k="8" />
<hkern u1="X" u2="&#x101;" k="8" />
<hkern u1="X" u2="&#xff;" k="51" />
<hkern u1="X" u2="&#xfd;" k="51" />
<hkern u1="X" u2="&#xfc;" k="37" />
<hkern u1="X" u2="&#xfb;" k="37" />
<hkern u1="X" u2="&#xfa;" k="37" />
<hkern u1="X" u2="&#xf9;" k="37" />
<hkern u1="X" u2="&#xf8;" k="49" />
<hkern u1="X" u2="&#xf6;" k="49" />
<hkern u1="X" u2="&#xf5;" k="49" />
<hkern u1="X" u2="&#xf4;" k="49" />
<hkern u1="X" u2="&#xf3;" k="49" />
<hkern u1="X" u2="&#xf2;" k="49" />
<hkern u1="X" u2="&#xf1;" k="8" />
<hkern u1="X" u2="&#xef;" k="-104" />
<hkern u1="X" u2="&#xee;" k="-20" />
<hkern u1="X" u2="&#xec;" k="-115" />
<hkern u1="X" u2="&#xeb;" k="49" />
<hkern u1="X" u2="&#xea;" k="49" />
<hkern u1="X" u2="&#xe9;" k="49" />
<hkern u1="X" u2="&#xe8;" k="49" />
<hkern u1="X" u2="&#xe7;" k="49" />
<hkern u1="X" u2="&#xe6;" k="8" />
<hkern u1="X" u2="&#xe5;" k="8" />
<hkern u1="X" u2="&#xe4;" k="8" />
<hkern u1="X" u2="&#xe3;" k="8" />
<hkern u1="X" u2="&#xe2;" k="8" />
<hkern u1="X" u2="&#xe1;" k="8" />
<hkern u1="X" u2="&#xe0;" k="8" />
<hkern u1="X" u2="&#xd8;" k="37" />
<hkern u1="X" u2="&#xd6;" k="37" />
<hkern u1="X" u2="&#xd5;" k="37" />
<hkern u1="X" u2="&#xd4;" k="37" />
<hkern u1="X" u2="&#xd3;" k="37" />
<hkern u1="X" u2="&#xd2;" k="37" />
<hkern u1="X" u2="&#xc7;" k="35" />
<hkern u1="X" u2="&#xae;" k="8" />
<hkern u1="X" u2="&#xab;" k="41" />
<hkern u1="X" u2="y" k="51" />
<hkern u1="X" u2="w" k="51" />
<hkern u1="X" u2="v" k="49" />
<hkern u1="X" u2="u" k="37" />
<hkern u1="X" u2="t" k="20" />
<hkern u1="X" u2="r" k="8" />
<hkern u1="X" u2="q" k="39" />
<hkern u1="X" u2="p" k="8" />
<hkern u1="X" u2="o" k="49" />
<hkern u1="X" u2="n" k="8" />
<hkern u1="X" u2="m" k="8" />
<hkern u1="X" u2="g" k="37" />
<hkern u1="X" u2="f" k="14" />
<hkern u1="X" u2="e" k="49" />
<hkern u1="X" u2="d" k="39" />
<hkern u1="X" u2="c" k="49" />
<hkern u1="X" u2="a" k="8" />
<hkern u1="X" u2="Q" k="37" />
<hkern u1="X" u2="O" k="37" />
<hkern u1="X" u2="G" k="37" />
<hkern u1="X" u2="C" k="35" />
<hkern u1="X" u2="&#x2d;" k="72" />
<hkern u1="Y" u2="&#x159;" k="66" />
<hkern u1="Y" u2="&#x155;" k="78" />
<hkern u1="Y" u2="&#x151;" k="111" />
<hkern u1="Y" u2="&#x142;" k="12" />
<hkern u1="Y" u2="&#x135;" k="-23" />
<hkern u1="Y" u2="&#x131;" k="123" />
<hkern u1="Y" u2="&#x12d;" k="-92" />
<hkern u1="Y" u2="&#x12b;" k="-70" />
<hkern u1="Y" u2="&#x129;" k="-96" />
<hkern u1="Y" u2="&#x103;" k="121" />
<hkern u1="Y" u2="&#xff;" k="57" />
<hkern u1="Y" u2="&#xef;" k="-121" />
<hkern u1="Y" u2="&#xee;" k="-33" />
<hkern u1="Y" u2="&#xec;" k="-129" />
<hkern u1="Y" u2="&#xeb;" k="131" />
<hkern u1="Y" u2="&#xe4;" k="100" />
<hkern u1="Y" u2="&#xe3;" k="86" />
<hkern u1="Y" u2="&#xdf;" k="20" />
<hkern u1="Y" u2="&#xc6;" k="115" />
<hkern u1="Y" u2="&#xae;" k="41" />
<hkern u1="Y" u2="x" k="76" />
<hkern u1="Y" u2="v" k="74" />
<hkern u1="Y" u2="f" k="43" />
<hkern u1="Y" u2="&#x40;" k="74" />
<hkern u1="Y" u2="&#x2f;" k="133" />
<hkern u1="Y" u2="&#x2a;" k="-8" />
<hkern u1="Y" u2="&#x26;" k="66" />
<hkern u1="Z" u2="&#x135;" k="-37" />
<hkern u1="Z" u2="&#x12d;" k="-10" />
<hkern u1="Z" u2="&#x12b;" k="-8" />
<hkern u1="Z" u2="&#x129;" k="-47" />
<hkern u1="Z" u2="&#xef;" k="-45" />
<hkern u1="Z" u2="&#xee;" k="-47" />
<hkern u1="Z" u2="&#xec;" k="-82" />
<hkern u1="Z" u2="&#xae;" k="6" />
<hkern u1="Z" u2="v" k="18" />
<hkern u1="Z" u2="f" k="10" />
<hkern u1="[" u2="&#x21b;" k="35" />
<hkern u1="[" u2="&#x219;" k="23" />
<hkern u1="[" u2="&#x218;" k="10" />
<hkern u1="[" u2="&#x1ff;" k="66" />
<hkern u1="[" u2="&#x1fe;" k="49" />
<hkern u1="[" u2="&#x1fd;" k="47" />
<hkern u1="[" u2="&#x1fc;" k="20" />
<hkern u1="[" u2="&#x1fb;" k="47" />
<hkern u1="[" u2="&#x1fa;" k="20" />
<hkern u1="[" u2="&#x17e;" k="18" />
<hkern u1="[" u2="&#x17c;" k="18" />
<hkern u1="[" u2="&#x17a;" k="18" />
<hkern u1="[" u2="&#x177;" k="49" />
<hkern u1="[" u2="&#x175;" k="53" />
<hkern u1="[" u2="&#x173;" k="53" />
<hkern u1="[" u2="&#x171;" k="53" />
<hkern u1="[" u2="&#x16f;" k="53" />
<hkern u1="[" u2="&#x16d;" k="53" />
<hkern u1="[" u2="&#x16b;" k="53" />
<hkern u1="[" u2="&#x169;" k="53" />
<hkern u1="[" u2="&#x167;" k="35" />
<hkern u1="[" u2="&#x165;" k="35" />
<hkern u1="[" u2="&#x161;" k="23" />
<hkern u1="[" u2="&#x160;" k="10" />
<hkern u1="[" u2="&#x15f;" k="23" />
<hkern u1="[" u2="&#x15e;" k="10" />
<hkern u1="[" u2="&#x15d;" k="23" />
<hkern u1="[" u2="&#x15c;" k="10" />
<hkern u1="[" u2="&#x15b;" k="23" />
<hkern u1="[" u2="&#x15a;" k="10" />
<hkern u1="[" u2="&#x159;" k="23" />
<hkern u1="[" u2="&#x157;" k="23" />
<hkern u1="[" u2="&#x155;" k="23" />
<hkern u1="[" u2="&#x153;" k="66" />
<hkern u1="[" u2="&#x152;" k="49" />
<hkern u1="[" u2="&#x151;" k="66" />
<hkern u1="[" u2="&#x150;" k="49" />
<hkern u1="[" u2="&#x14f;" k="66" />
<hkern u1="[" u2="&#x14e;" k="49" />
<hkern u1="[" u2="&#x14d;" k="66" />
<hkern u1="[" u2="&#x14c;" k="49" />
<hkern u1="[" u2="&#x14b;" k="23" />
<hkern u1="[" u2="&#x148;" k="23" />
<hkern u1="[" u2="&#x146;" k="23" />
<hkern u1="[" u2="&#x144;" k="23" />
<hkern u1="[" u2="&#x135;" k="-14" />
<hkern u1="[" u2="&#x12d;" k="-51" />
<hkern u1="[" u2="&#x129;" k="-43" />
<hkern u1="[" u2="&#x122;" k="49" />
<hkern u1="[" u2="&#x120;" k="49" />
<hkern u1="[" u2="&#x11e;" k="49" />
<hkern u1="[" u2="&#x11c;" k="49" />
<hkern u1="[" u2="&#x11b;" k="66" />
<hkern u1="[" u2="&#x119;" k="66" />
<hkern u1="[" u2="&#x117;" k="66" />
<hkern u1="[" u2="&#x115;" k="66" />
<hkern u1="[" u2="&#x113;" k="66" />
<hkern u1="[" u2="&#x111;" k="63" />
<hkern u1="[" u2="&#x10f;" k="63" />
<hkern u1="[" u2="&#x10d;" k="66" />
<hkern u1="[" u2="&#x10c;" k="41" />
<hkern u1="[" u2="&#x10b;" k="66" />
<hkern u1="[" u2="&#x10a;" k="41" />
<hkern u1="[" u2="&#x109;" k="66" />
<hkern u1="[" u2="&#x108;" k="41" />
<hkern u1="[" u2="&#x107;" k="66" />
<hkern u1="[" u2="&#x106;" k="41" />
<hkern u1="[" u2="&#x105;" k="47" />
<hkern u1="[" u2="&#x104;" k="20" />
<hkern u1="[" u2="&#x103;" k="47" />
<hkern u1="[" u2="&#x102;" k="20" />
<hkern u1="[" u2="&#x101;" k="47" />
<hkern u1="[" u2="&#x100;" k="20" />
<hkern u1="[" u2="&#xff;" k="49" />
<hkern u1="[" u2="&#xfd;" k="49" />
<hkern u1="[" u2="&#xfc;" k="53" />
<hkern u1="[" u2="&#xfb;" k="53" />
<hkern u1="[" u2="&#xfa;" k="53" />
<hkern u1="[" u2="&#xf9;" k="53" />
<hkern u1="[" u2="&#xf8;" k="66" />
<hkern u1="[" u2="&#xf6;" k="66" />
<hkern u1="[" u2="&#xf5;" k="66" />
<hkern u1="[" u2="&#xf4;" k="66" />
<hkern u1="[" u2="&#xf3;" k="66" />
<hkern u1="[" u2="&#xf2;" k="66" />
<hkern u1="[" u2="&#xf1;" k="23" />
<hkern u1="[" u2="&#xef;" k="-57" />
<hkern u1="[" u2="&#xec;" k="-90" />
<hkern u1="[" u2="&#xeb;" k="66" />
<hkern u1="[" u2="&#xea;" k="66" />
<hkern u1="[" u2="&#xe9;" k="66" />
<hkern u1="[" u2="&#xe8;" k="66" />
<hkern u1="[" u2="&#xe7;" k="66" />
<hkern u1="[" u2="&#xe6;" k="47" />
<hkern u1="[" u2="&#xe5;" k="47" />
<hkern u1="[" u2="&#xe4;" k="47" />
<hkern u1="[" u2="&#xe3;" k="47" />
<hkern u1="[" u2="&#xe2;" k="47" />
<hkern u1="[" u2="&#xe1;" k="47" />
<hkern u1="[" u2="&#xe0;" k="47" />
<hkern u1="[" u2="&#xd8;" k="49" />
<hkern u1="[" u2="&#xd6;" k="49" />
<hkern u1="[" u2="&#xd5;" k="49" />
<hkern u1="[" u2="&#xd4;" k="49" />
<hkern u1="[" u2="&#xd3;" k="49" />
<hkern u1="[" u2="&#xd2;" k="49" />
<hkern u1="[" u2="&#xc7;" k="41" />
<hkern u1="[" u2="&#xc6;" k="20" />
<hkern u1="[" u2="&#xc5;" k="20" />
<hkern u1="[" u2="&#xc4;" k="20" />
<hkern u1="[" u2="&#xc3;" k="20" />
<hkern u1="[" u2="&#xc2;" k="20" />
<hkern u1="[" u2="&#xc1;" k="20" />
<hkern u1="[" u2="&#xc0;" k="20" />
<hkern u1="[" u2="&#x7b;" k="39" />
<hkern u1="[" u2="z" k="18" />
<hkern u1="[" u2="y" k="49" />
<hkern u1="[" u2="x" k="16" />
<hkern u1="[" u2="w" k="53" />
<hkern u1="[" u2="v" k="51" />
<hkern u1="[" u2="u" k="53" />
<hkern u1="[" u2="t" k="35" />
<hkern u1="[" u2="s" k="23" />
<hkern u1="[" u2="r" k="23" />
<hkern u1="[" u2="q" k="63" />
<hkern u1="[" u2="p" k="23" />
<hkern u1="[" u2="o" k="66" />
<hkern u1="[" u2="n" k="23" />
<hkern u1="[" u2="m" k="23" />
<hkern u1="[" u2="j" k="-14" />
<hkern u1="[" u2="f" k="25" />
<hkern u1="[" u2="e" k="66" />
<hkern u1="[" u2="d" k="63" />
<hkern u1="[" u2="c" k="66" />
<hkern u1="[" u2="a" k="47" />
<hkern u1="[" u2="S" k="10" />
<hkern u1="[" u2="Q" k="49" />
<hkern u1="[" u2="O" k="49" />
<hkern u1="[" u2="G" k="49" />
<hkern u1="[" u2="C" k="41" />
<hkern u1="[" u2="A" k="20" />
<hkern u1="[" u2="&#x28;" k="8" />
<hkern u1="\" u2="&#x201d;" k="139" />
<hkern u1="\" u2="&#x2019;" k="139" />
<hkern u1="\" u2="&#x21b;" k="29" />
<hkern u1="\" u2="&#x21a;" k="115" />
<hkern u1="\" u2="&#x218;" k="14" />
<hkern u1="\" u2="&#x1ff;" k="14" />
<hkern u1="\" u2="&#x1fe;" k="31" />
<hkern u1="\" u2="&#x178;" k="143" />
<hkern u1="\" u2="&#x177;" k="47" />
<hkern u1="\" u2="&#x176;" k="143" />
<hkern u1="\" u2="&#x175;" k="37" />
<hkern u1="\" u2="&#x174;" k="66" />
<hkern u1="\" u2="&#x172;" k="33" />
<hkern u1="\" u2="&#x170;" k="33" />
<hkern u1="\" u2="&#x16e;" k="33" />
<hkern u1="\" u2="&#x16c;" k="33" />
<hkern u1="\" u2="&#x16a;" k="33" />
<hkern u1="\" u2="&#x168;" k="33" />
<hkern u1="\" u2="&#x167;" k="29" />
<hkern u1="\" u2="&#x166;" k="115" />
<hkern u1="\" u2="&#x165;" k="29" />
<hkern u1="\" u2="&#x164;" k="115" />
<hkern u1="\" u2="&#x160;" k="14" />
<hkern u1="\" u2="&#x15e;" k="14" />
<hkern u1="\" u2="&#x15c;" k="14" />
<hkern u1="\" u2="&#x15a;" k="14" />
<hkern u1="\" u2="&#x153;" k="14" />
<hkern u1="\" u2="&#x152;" k="31" />
<hkern u1="\" u2="&#x151;" k="14" />
<hkern u1="\" u2="&#x150;" k="31" />
<hkern u1="\" u2="&#x14f;" k="14" />
<hkern u1="\" u2="&#x14e;" k="31" />
<hkern u1="\" u2="&#x14d;" k="14" />
<hkern u1="\" u2="&#x14c;" k="31" />
<hkern u1="\" u2="&#x122;" k="31" />
<hkern u1="\" u2="&#x120;" k="31" />
<hkern u1="\" u2="&#x11e;" k="31" />
<hkern u1="\" u2="&#x11c;" k="31" />
<hkern u1="\" u2="&#x11b;" k="14" />
<hkern u1="\" u2="&#x119;" k="14" />
<hkern u1="\" u2="&#x117;" k="14" />
<hkern u1="\" u2="&#x115;" k="14" />
<hkern u1="\" u2="&#x113;" k="14" />
<hkern u1="\" u2="&#x10d;" k="14" />
<hkern u1="\" u2="&#x10c;" k="29" />
<hkern u1="\" u2="&#x10b;" k="14" />
<hkern u1="\" u2="&#x10a;" k="29" />
<hkern u1="\" u2="&#x109;" k="14" />
<hkern u1="\" u2="&#x108;" k="29" />
<hkern u1="\" u2="&#x107;" k="14" />
<hkern u1="\" u2="&#x106;" k="29" />
<hkern u1="\" u2="&#xff;" k="47" />
<hkern u1="\" u2="&#xfd;" k="47" />
<hkern u1="\" u2="&#xf8;" k="14" />
<hkern u1="\" u2="&#xf6;" k="14" />
<hkern u1="\" u2="&#xf5;" k="14" />
<hkern u1="\" u2="&#xf4;" k="14" />
<hkern u1="\" u2="&#xf3;" k="14" />
<hkern u1="\" u2="&#xf2;" k="14" />
<hkern u1="\" u2="&#xeb;" k="14" />
<hkern u1="\" u2="&#xea;" k="14" />
<hkern u1="\" u2="&#xe9;" k="14" />
<hkern u1="\" u2="&#xe8;" k="14" />
<hkern u1="\" u2="&#xe7;" k="14" />
<hkern u1="\" u2="&#xdd;" k="143" />
<hkern u1="\" u2="&#xdc;" k="33" />
<hkern u1="\" u2="&#xdb;" k="33" />
<hkern u1="\" u2="&#xda;" k="33" />
<hkern u1="\" u2="&#xd9;" k="33" />
<hkern u1="\" u2="&#xd8;" k="31" />
<hkern u1="\" u2="&#xd6;" k="31" />
<hkern u1="\" u2="&#xd5;" k="31" />
<hkern u1="\" u2="&#xd4;" k="31" />
<hkern u1="\" u2="&#xd3;" k="31" />
<hkern u1="\" u2="&#xd2;" k="31" />
<hkern u1="\" u2="&#xc7;" k="29" />
<hkern u1="\" u2="y" k="47" />
<hkern u1="\" u2="w" k="37" />
<hkern u1="\" u2="v" k="45" />
<hkern u1="\" u2="t" k="29" />
<hkern u1="\" u2="o" k="14" />
<hkern u1="\" u2="f" k="18" />
<hkern u1="\" u2="e" k="14" />
<hkern u1="\" u2="c" k="14" />
<hkern u1="\" u2="Y" k="143" />
<hkern u1="\" u2="W" k="66" />
<hkern u1="\" u2="V" k="92" />
<hkern u1="\" u2="U" k="33" />
<hkern u1="\" u2="T" k="115" />
<hkern u1="\" u2="S" k="14" />
<hkern u1="\" u2="Q" k="31" />
<hkern u1="\" u2="O" k="31" />
<hkern u1="\" u2="G" k="31" />
<hkern u1="\" u2="C" k="29" />
<hkern u1="\" u2="&#x27;" k="145" />
<hkern u1="\" u2="&#x22;" k="145" />
<hkern u1="a" u2="&#x2122;" k="29" />
<hkern u1="a" u2="&#x7d;" k="8" />
<hkern u1="a" u2="v" k="10" />
<hkern u1="a" u2="]" k="10" />
<hkern u1="a" u2="\" k="82" />
<hkern u1="a" u2="V" k="55" />
<hkern u1="a" u2="&#x3f;" k="29" />
<hkern u1="a" u2="&#x2a;" k="12" />
<hkern u1="b" u2="&#x2122;" k="33" />
<hkern u1="b" u2="&#xc6;" k="14" />
<hkern u1="b" u2="&#x7d;" k="51" />
<hkern u1="b" u2="x" k="20" />
<hkern u1="b" u2="v" k="14" />
<hkern u1="b" u2="]" k="63" />
<hkern u1="b" u2="\" k="78" />
<hkern u1="b" u2="X" k="43" />
<hkern u1="b" u2="V" k="59" />
<hkern u1="b" u2="&#x3f;" k="41" />
<hkern u1="b" u2="&#x2a;" k="16" />
<hkern u1="b" u2="&#x29;" k="37" />
<hkern u1="c" u2="&#x7d;" k="14" />
<hkern u1="c" u2="]" k="16" />
<hkern u1="c" u2="\" k="37" />
<hkern u1="c" u2="V" k="25" />
<hkern u1="c" u2="&#x3f;" k="8" />
<hkern u1="d" u2="&#xef;" k="-12" />
<hkern u1="d" u2="&#xec;" k="-27" />
<hkern u1="e" u2="&#x2122;" k="27" />
<hkern u1="e" u2="&#xc6;" k="10" />
<hkern u1="e" u2="&#x7d;" k="37" />
<hkern u1="e" u2="v" k="14" />
<hkern u1="e" u2="]" k="23" />
<hkern u1="e" u2="\" k="74" />
<hkern u1="e" u2="X" k="8" />
<hkern u1="e" u2="V" k="57" />
<hkern u1="e" u2="&#x3f;" k="31" />
<hkern u1="e" u2="&#x29;" k="18" />
<hkern u1="f" u2="&#x203a;" k="18" />
<hkern u1="f" u2="&#x2039;" k="57" />
<hkern u1="f" u2="&#x2026;" k="76" />
<hkern u1="f" u2="&#x201e;" k="76" />
<hkern u1="f" u2="&#x201a;" k="76" />
<hkern u1="f" u2="&#x2014;" k="74" />
<hkern u1="f" u2="&#x2013;" k="74" />
<hkern u1="f" u2="&#x21a;" k="27" />
<hkern u1="f" u2="&#x1ff;" k="12" />
<hkern u1="f" u2="&#x1fc;" k="47" />
<hkern u1="f" u2="&#x1fa;" k="47" />
<hkern u1="f" u2="&#x17d;" k="10" />
<hkern u1="f" u2="&#x17b;" k="10" />
<hkern u1="f" u2="&#x179;" k="10" />
<hkern u1="f" u2="&#x178;" k="-6" />
<hkern u1="f" u2="&#x176;" k="-6" />
<hkern u1="f" u2="&#x166;" k="27" />
<hkern u1="f" u2="&#x164;" k="27" />
<hkern u1="f" u2="&#x153;" k="12" />
<hkern u1="f" u2="&#x151;" k="12" />
<hkern u1="f" u2="&#x14f;" k="12" />
<hkern u1="f" u2="&#x14d;" k="12" />
<hkern u1="f" u2="&#x135;" k="-55" />
<hkern u1="f" u2="&#x134;" k="31" />
<hkern u1="f" u2="&#x12d;" k="-96" />
<hkern u1="f" u2="&#x12b;" k="-37" />
<hkern u1="f" u2="&#x129;" k="-86" />
<hkern u1="f" u2="&#x11b;" k="12" />
<hkern u1="f" u2="&#x119;" k="12" />
<hkern u1="f" u2="&#x117;" k="12" />
<hkern u1="f" u2="&#x115;" k="12" />
<hkern u1="f" u2="&#x113;" k="12" />
<hkern u1="f" u2="&#x111;" k="8" />
<hkern u1="f" u2="&#x10f;" k="8" />
<hkern u1="f" u2="&#x10d;" k="12" />
<hkern u1="f" u2="&#x10b;" k="12" />
<hkern u1="f" u2="&#x109;" k="12" />
<hkern u1="f" u2="&#x107;" k="12" />
<hkern u1="f" u2="&#x104;" k="47" />
<hkern u1="f" u2="&#x102;" k="47" />
<hkern u1="f" u2="&#x100;" k="47" />
<hkern u1="f" u2="&#xf8;" k="12" />
<hkern u1="f" u2="&#xf6;" k="12" />
<hkern u1="f" u2="&#xf5;" k="12" />
<hkern u1="f" u2="&#xf4;" k="12" />
<hkern u1="f" u2="&#xf3;" k="12" />
<hkern u1="f" u2="&#xf2;" k="12" />
<hkern u1="f" u2="&#xef;" k="-90" />
<hkern u1="f" u2="&#xee;" k="-68" />
<hkern u1="f" u2="&#xec;" k="-147" />
<hkern u1="f" u2="&#xeb;" k="12" />
<hkern u1="f" u2="&#xea;" k="12" />
<hkern u1="f" u2="&#xe9;" k="12" />
<hkern u1="f" u2="&#xe8;" k="12" />
<hkern u1="f" u2="&#xe7;" k="12" />
<hkern u1="f" u2="&#xdd;" k="-6" />
<hkern u1="f" u2="&#xc6;" k="57" />
<hkern u1="f" u2="&#xc5;" k="47" />
<hkern u1="f" u2="&#xc4;" k="47" />
<hkern u1="f" u2="&#xc3;" k="47" />
<hkern u1="f" u2="&#xc2;" k="47" />
<hkern u1="f" u2="&#xc1;" k="47" />
<hkern u1="f" u2="&#xc0;" k="47" />
<hkern u1="f" u2="&#xbb;" k="18" />
<hkern u1="f" u2="&#xab;" k="57" />
<hkern u1="f" u2="q" k="8" />
<hkern u1="f" u2="o" k="12" />
<hkern u1="f" u2="e" k="12" />
<hkern u1="f" u2="d" k="8" />
<hkern u1="f" u2="c" k="12" />
<hkern u1="f" u2="Z" k="10" />
<hkern u1="f" u2="Y" k="-6" />
<hkern u1="f" u2="X" k="12" />
<hkern u1="f" u2="T" k="27" />
<hkern u1="f" u2="J" k="31" />
<hkern u1="f" u2="A" k="47" />
<hkern u1="f" u2="&#x2f;" k="53" />
<hkern u1="f" u2="&#x2e;" k="76" />
<hkern u1="f" u2="&#x2d;" k="74" />
<hkern u1="f" u2="&#x2c;" k="76" />
<hkern u1="f" u2="&#x26;" k="8" />
<hkern u1="g" u2="&#x135;" k="-45" />
<hkern u1="g" u2="j" k="-45" />
<hkern u1="g" u2="\" k="23" />
<hkern u1="g" u2="V" k="8" />
<hkern u1="h" u2="&#x2122;" k="31" />
<hkern u1="h" u2="&#x7d;" k="20" />
<hkern u1="h" u2="v" k="10" />
<hkern u1="h" u2="]" k="23" />
<hkern u1="h" u2="\" k="78" />
<hkern u1="h" u2="V" k="57" />
<hkern u1="h" u2="&#x3f;" k="35" />
<hkern u1="h" u2="&#x2a;" k="12" />
<hkern u1="h" u2="&#x29;" k="16" />
<hkern u1="i" u2="&#xef;" k="-12" />
<hkern u1="i" u2="&#xec;" k="-27" />
<hkern u1="j" u2="&#xef;" k="-12" />
<hkern u1="j" u2="&#xec;" k="-27" />
<hkern u1="k" u2="&#x7d;" k="12" />
<hkern u1="k" u2="]" k="14" />
<hkern u1="k" u2="\" k="27" />
<hkern u1="k" u2="V" k="16" />
<hkern u1="k" u2="&#x3f;" k="6" />
<hkern u1="l" u2="&#xec;" k="-18" />
<hkern u1="m" u2="&#x2122;" k="31" />
<hkern u1="m" u2="&#x7d;" k="20" />
<hkern u1="m" u2="v" k="10" />
<hkern u1="m" u2="]" k="23" />
<hkern u1="m" u2="\" k="78" />
<hkern u1="m" u2="V" k="57" />
<hkern u1="m" u2="&#x3f;" k="35" />
<hkern u1="m" u2="&#x2a;" k="12" />
<hkern u1="m" u2="&#x29;" k="16" />
<hkern u1="n" u2="&#x2122;" k="31" />
<hkern u1="n" u2="&#x7d;" k="20" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="]" k="23" />
<hkern u1="n" u2="\" k="78" />
<hkern u1="n" u2="V" k="57" />
<hkern u1="n" u2="&#x3f;" k="35" />
<hkern u1="n" u2="&#x2a;" k="12" />
<hkern u1="n" u2="&#x29;" k="16" />
<hkern u1="o" u2="&#x2122;" k="31" />
<hkern u1="o" u2="&#xc6;" k="14" />
<hkern u1="o" u2="&#x7d;" k="51" />
<hkern u1="o" u2="x" k="23" />
<hkern u1="o" u2="v" k="16" />
<hkern u1="o" u2="]" k="66" />
<hkern u1="o" u2="\" k="80" />
<hkern u1="o" u2="X" k="49" />
<hkern u1="o" u2="V" k="61" />
<hkern u1="o" u2="&#x3f;" k="39" />
<hkern u1="o" u2="&#x2a;" k="12" />
<hkern u1="o" u2="&#x29;" k="39" />
<hkern u1="p" u2="&#x2122;" k="33" />
<hkern u1="p" u2="&#xc6;" k="14" />
<hkern u1="p" u2="&#x7d;" k="51" />
<hkern u1="p" u2="x" k="20" />
<hkern u1="p" u2="v" k="14" />
<hkern u1="p" u2="]" k="63" />
<hkern u1="p" u2="\" k="78" />
<hkern u1="p" u2="X" k="43" />
<hkern u1="p" u2="V" k="59" />
<hkern u1="p" u2="&#x3f;" k="41" />
<hkern u1="p" u2="&#x2a;" k="16" />
<hkern u1="p" u2="&#x29;" k="37" />
<hkern u1="q" u2="&#x2122;" k="20" />
<hkern u1="q" u2="&#x7d;" k="20" />
<hkern u1="q" u2="]" k="23" />
<hkern u1="q" u2="\" k="53" />
<hkern u1="q" u2="X" k="8" />
<hkern u1="q" u2="V" k="47" />
<hkern u1="q" u2="&#x3f;" k="10" />
<hkern u1="q" u2="&#x29;" k="16" />
<hkern u1="r" u2="&#xc6;" k="74" />
<hkern u1="r" u2="&#x7d;" k="35" />
<hkern u1="r" u2="]" k="47" />
<hkern u1="r" u2="\" k="16" />
<hkern u1="r" u2="X" k="57" />
<hkern u1="r" u2="&#x2f;" k="68" />
<hkern u1="r" u2="&#x29;" k="14" />
<hkern u1="r" u2="&#x26;" k="8" />
<hkern u1="s" u2="&#x2122;" k="23" />
<hkern u1="s" u2="&#xc6;" k="10" />
<hkern u1="s" u2="&#x7d;" k="39" />
<hkern u1="s" u2="v" k="12" />
<hkern u1="s" u2="]" k="49" />
<hkern u1="s" u2="\" k="53" />
<hkern u1="s" u2="X" k="12" />
<hkern u1="s" u2="V" k="41" />
<hkern u1="s" u2="&#x3f;" k="10" />
<hkern u1="s" u2="&#x29;" k="20" />
<hkern u1="t" u2="&#x7d;" k="8" />
<hkern u1="t" u2="]" k="10" />
<hkern u1="t" u2="\" k="27" />
<hkern u1="t" u2="V" k="8" />
<hkern u1="u" u2="&#x2122;" k="20" />
<hkern u1="u" u2="&#x7d;" k="20" />
<hkern u1="u" u2="]" k="23" />
<hkern u1="u" u2="\" k="53" />
<hkern u1="u" u2="X" k="8" />
<hkern u1="u" u2="V" k="47" />
<hkern u1="u" u2="&#x3f;" k="10" />
<hkern u1="u" u2="&#x29;" k="16" />
<hkern u1="v" u2="&#x2039;" k="23" />
<hkern u1="v" u2="&#x2026;" k="57" />
<hkern u1="v" u2="&#x201e;" k="57" />
<hkern u1="v" u2="&#x201a;" k="57" />
<hkern u1="v" u2="&#x2014;" k="23" />
<hkern u1="v" u2="&#x2013;" k="23" />
<hkern u1="v" u2="&#x21a;" k="119" />
<hkern u1="v" u2="&#x219;" k="12" />
<hkern u1="v" u2="&#x1ff;" k="16" />
<hkern u1="v" u2="&#x1fd;" k="14" />
<hkern u1="v" u2="&#x1fc;" k="33" />
<hkern u1="v" u2="&#x1fb;" k="14" />
<hkern u1="v" u2="&#x1fa;" k="33" />
<hkern u1="v" u2="&#x17d;" k="23" />
<hkern u1="v" u2="&#x17b;" k="23" />
<hkern u1="v" u2="&#x179;" k="23" />
<hkern u1="v" u2="&#x178;" k="74" />
<hkern u1="v" u2="&#x176;" k="74" />
<hkern u1="v" u2="&#x166;" k="119" />
<hkern u1="v" u2="&#x164;" k="119" />
<hkern u1="v" u2="&#x161;" k="12" />
<hkern u1="v" u2="&#x15f;" k="12" />
<hkern u1="v" u2="&#x15d;" k="12" />
<hkern u1="v" u2="&#x15b;" k="12" />
<hkern u1="v" u2="&#x153;" k="16" />
<hkern u1="v" u2="&#x151;" k="16" />
<hkern u1="v" u2="&#x14f;" k="16" />
<hkern u1="v" u2="&#x14d;" k="16" />
<hkern u1="v" u2="&#x134;" k="37" />
<hkern u1="v" u2="&#x123;" k="16" />
<hkern u1="v" u2="&#x121;" k="16" />
<hkern u1="v" u2="&#x11f;" k="16" />
<hkern u1="v" u2="&#x11d;" k="16" />
<hkern u1="v" u2="&#x11b;" k="16" />
<hkern u1="v" u2="&#x119;" k="16" />
<hkern u1="v" u2="&#x117;" k="16" />
<hkern u1="v" u2="&#x115;" k="16" />
<hkern u1="v" u2="&#x113;" k="16" />
<hkern u1="v" u2="&#x111;" k="14" />
<hkern u1="v" u2="&#x10f;" k="14" />
<hkern u1="v" u2="&#x10d;" k="16" />
<hkern u1="v" u2="&#x10b;" k="16" />
<hkern u1="v" u2="&#x109;" k="16" />
<hkern u1="v" u2="&#x107;" k="16" />
<hkern u1="v" u2="&#x105;" k="14" />
<hkern u1="v" u2="&#x104;" k="33" />
<hkern u1="v" u2="&#x103;" k="14" />
<hkern u1="v" u2="&#x102;" k="33" />
<hkern u1="v" u2="&#x101;" k="14" />
<hkern u1="v" u2="&#x100;" k="33" />
<hkern u1="v" u2="&#xf8;" k="16" />
<hkern u1="v" u2="&#xf6;" k="16" />
<hkern u1="v" u2="&#xf5;" k="16" />
<hkern u1="v" u2="&#xf4;" k="16" />
<hkern u1="v" u2="&#xf3;" k="16" />
<hkern u1="v" u2="&#xf2;" k="16" />
<hkern u1="v" u2="&#xeb;" k="16" />
<hkern u1="v" u2="&#xea;" k="16" />
<hkern u1="v" u2="&#xe9;" k="16" />
<hkern u1="v" u2="&#xe8;" k="16" />
<hkern u1="v" u2="&#xe7;" k="16" />
<hkern u1="v" u2="&#xe6;" k="14" />
<hkern u1="v" u2="&#xe5;" k="14" />
<hkern u1="v" u2="&#xe4;" k="14" />
<hkern u1="v" u2="&#xe3;" k="14" />
<hkern u1="v" u2="&#xe2;" k="14" />
<hkern u1="v" u2="&#xe1;" k="14" />
<hkern u1="v" u2="&#xe0;" k="14" />
<hkern u1="v" u2="&#xdd;" k="74" />
<hkern u1="v" u2="&#xc6;" k="39" />
<hkern u1="v" u2="&#xc5;" k="33" />
<hkern u1="v" u2="&#xc4;" k="33" />
<hkern u1="v" u2="&#xc3;" k="33" />
<hkern u1="v" u2="&#xc2;" k="33" />
<hkern u1="v" u2="&#xc1;" k="33" />
<hkern u1="v" u2="&#xc0;" k="33" />
<hkern u1="v" u2="&#xab;" k="23" />
<hkern u1="v" u2="&#x7d;" k="39" />
<hkern u1="v" u2="s" k="12" />
<hkern u1="v" u2="q" k="14" />
<hkern u1="v" u2="o" k="16" />
<hkern u1="v" u2="g" k="16" />
<hkern u1="v" u2="e" k="16" />
<hkern u1="v" u2="d" k="14" />
<hkern u1="v" u2="c" k="16" />
<hkern u1="v" u2="a" k="14" />
<hkern u1="v" u2="]" k="51" />
<hkern u1="v" u2="\" k="27" />
<hkern u1="v" u2="Z" k="23" />
<hkern u1="v" u2="Y" k="74" />
<hkern u1="v" u2="X" k="49" />
<hkern u1="v" u2="V" k="14" />
<hkern u1="v" u2="T" k="119" />
<hkern u1="v" u2="J" k="37" />
<hkern u1="v" u2="A" k="33" />
<hkern u1="v" u2="&#x3f;" k="8" />
<hkern u1="v" u2="&#x2f;" k="39" />
<hkern u1="v" u2="&#x2e;" k="57" />
<hkern u1="v" u2="&#x2d;" k="23" />
<hkern u1="v" u2="&#x2c;" k="57" />
<hkern u1="v" u2="&#x29;" k="16" />
<hkern u1="w" u2="&#xc6;" k="33" />
<hkern u1="w" u2="&#x7d;" k="43" />
<hkern u1="w" u2="]" k="53" />
<hkern u1="w" u2="\" k="27" />
<hkern u1="w" u2="X" k="49" />
<hkern u1="w" u2="V" k="18" />
<hkern u1="w" u2="&#x3f;" k="8" />
<hkern u1="w" u2="&#x2f;" k="31" />
<hkern u1="w" u2="&#x29;" k="20" />
<hkern u1="x" u2="&#x2039;" k="47" />
<hkern u1="x" u2="&#x2014;" k="55" />
<hkern u1="x" u2="&#x2013;" k="55" />
<hkern u1="x" u2="&#x21a;" k="119" />
<hkern u1="x" u2="&#x1ff;" k="23" />
<hkern u1="x" u2="&#x178;" k="72" />
<hkern u1="x" u2="&#x176;" k="72" />
<hkern u1="x" u2="&#x166;" k="119" />
<hkern u1="x" u2="&#x164;" k="119" />
<hkern u1="x" u2="&#x153;" k="23" />
<hkern u1="x" u2="&#x151;" k="23" />
<hkern u1="x" u2="&#x14f;" k="23" />
<hkern u1="x" u2="&#x14d;" k="23" />
<hkern u1="x" u2="&#x123;" k="18" />
<hkern u1="x" u2="&#x121;" k="18" />
<hkern u1="x" u2="&#x11f;" k="18" />
<hkern u1="x" u2="&#x11d;" k="18" />
<hkern u1="x" u2="&#x11b;" k="23" />
<hkern u1="x" u2="&#x119;" k="23" />
<hkern u1="x" u2="&#x117;" k="23" />
<hkern u1="x" u2="&#x115;" k="23" />
<hkern u1="x" u2="&#x113;" k="23" />
<hkern u1="x" u2="&#x111;" k="23" />
<hkern u1="x" u2="&#x10f;" k="23" />
<hkern u1="x" u2="&#x10d;" k="23" />
<hkern u1="x" u2="&#x10b;" k="23" />
<hkern u1="x" u2="&#x109;" k="23" />
<hkern u1="x" u2="&#x107;" k="23" />
<hkern u1="x" u2="&#xf8;" k="23" />
<hkern u1="x" u2="&#xf6;" k="23" />
<hkern u1="x" u2="&#xf5;" k="23" />
<hkern u1="x" u2="&#xf4;" k="23" />
<hkern u1="x" u2="&#xf3;" k="23" />
<hkern u1="x" u2="&#xf2;" k="23" />
<hkern u1="x" u2="&#xeb;" k="23" />
<hkern u1="x" u2="&#xea;" k="23" />
<hkern u1="x" u2="&#xe9;" k="23" />
<hkern u1="x" u2="&#xe8;" k="23" />
<hkern u1="x" u2="&#xe7;" k="23" />
<hkern u1="x" u2="&#xdd;" k="72" />
<hkern u1="x" u2="&#xab;" k="47" />
<hkern u1="x" u2="&#x7d;" k="14" />
<hkern u1="x" u2="q" k="23" />
<hkern u1="x" u2="o" k="23" />
<hkern u1="x" u2="g" k="18" />
<hkern u1="x" u2="e" k="23" />
<hkern u1="x" u2="d" k="23" />
<hkern u1="x" u2="c" k="23" />
<hkern u1="x" u2="]" k="16" />
<hkern u1="x" u2="\" k="23" />
<hkern u1="x" u2="Y" k="72" />
<hkern u1="x" u2="V" k="12" />
<hkern u1="x" u2="T" k="119" />
<hkern u1="x" u2="&#x2d;" k="55" />
<hkern u1="y" u2="&#xc6;" k="39" />
<hkern u1="y" u2="&#x7d;" k="35" />
<hkern u1="y" u2="]" k="47" />
<hkern u1="y" u2="\" k="27" />
<hkern u1="y" u2="X" k="49" />
<hkern u1="y" u2="V" k="14" />
<hkern u1="y" u2="&#x3f;" k="8" />
<hkern u1="y" u2="&#x2f;" k="41" />
<hkern u1="z" u2="&#x7d;" k="16" />
<hkern u1="z" u2="]" k="18" />
<hkern u1="z" u2="\" k="39" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="z" u2="&#x3f;" k="8" />
<hkern u1="&#x7b;" u2="&#x21b;" k="16" />
<hkern u1="&#x7b;" u2="&#x219;" k="20" />
<hkern u1="&#x7b;" u2="&#x218;" k="10" />
<hkern u1="&#x7b;" u2="&#x1ff;" k="51" />
<hkern u1="&#x7b;" u2="&#x1fe;" k="39" />
<hkern u1="&#x7b;" u2="&#x1fd;" k="39" />
<hkern u1="&#x7b;" u2="&#x1fc;" k="18" />
<hkern u1="&#x7b;" u2="&#x1fb;" k="39" />
<hkern u1="&#x7b;" u2="&#x1fa;" k="18" />
<hkern u1="&#x7b;" u2="&#x17e;" k="16" />
<hkern u1="&#x7b;" u2="&#x17c;" k="16" />
<hkern u1="&#x7b;" u2="&#x17a;" k="16" />
<hkern u1="&#x7b;" u2="&#x177;" k="37" />
<hkern u1="&#x7b;" u2="&#x175;" k="43" />
<hkern u1="&#x7b;" u2="&#x173;" k="45" />
<hkern u1="&#x7b;" u2="&#x171;" k="45" />
<hkern u1="&#x7b;" u2="&#x16f;" k="45" />
<hkern u1="&#x7b;" u2="&#x16d;" k="45" />
<hkern u1="&#x7b;" u2="&#x16b;" k="45" />
<hkern u1="&#x7b;" u2="&#x169;" k="45" />
<hkern u1="&#x7b;" u2="&#x167;" k="16" />
<hkern u1="&#x7b;" u2="&#x165;" k="16" />
<hkern u1="&#x7b;" u2="&#x161;" k="20" />
<hkern u1="&#x7b;" u2="&#x160;" k="10" />
<hkern u1="&#x7b;" u2="&#x15f;" k="20" />
<hkern u1="&#x7b;" u2="&#x15e;" k="10" />
<hkern u1="&#x7b;" u2="&#x15d;" k="20" />
<hkern u1="&#x7b;" u2="&#x15c;" k="10" />
<hkern u1="&#x7b;" u2="&#x15b;" k="20" />
<hkern u1="&#x7b;" u2="&#x15a;" k="10" />
<hkern u1="&#x7b;" u2="&#x159;" k="20" />
<hkern u1="&#x7b;" u2="&#x157;" k="20" />
<hkern u1="&#x7b;" u2="&#x155;" k="20" />
<hkern u1="&#x7b;" u2="&#x153;" k="51" />
<hkern u1="&#x7b;" u2="&#x152;" k="39" />
<hkern u1="&#x7b;" u2="&#x151;" k="51" />
<hkern u1="&#x7b;" u2="&#x150;" k="39" />
<hkern u1="&#x7b;" u2="&#x14f;" k="51" />
<hkern u1="&#x7b;" u2="&#x14e;" k="39" />
<hkern u1="&#x7b;" u2="&#x14d;" k="51" />
<hkern u1="&#x7b;" u2="&#x14c;" k="39" />
<hkern u1="&#x7b;" u2="&#x14b;" k="20" />
<hkern u1="&#x7b;" u2="&#x148;" k="20" />
<hkern u1="&#x7b;" u2="&#x146;" k="20" />
<hkern u1="&#x7b;" u2="&#x144;" k="20" />
<hkern u1="&#x7b;" u2="&#x135;" k="-33" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-49" />
<hkern u1="&#x7b;" u2="&#x129;" k="-41" />
<hkern u1="&#x7b;" u2="&#x122;" k="39" />
<hkern u1="&#x7b;" u2="&#x120;" k="39" />
<hkern u1="&#x7b;" u2="&#x11e;" k="39" />
<hkern u1="&#x7b;" u2="&#x11c;" k="39" />
<hkern u1="&#x7b;" u2="&#x11b;" k="51" />
<hkern u1="&#x7b;" u2="&#x119;" k="51" />
<hkern u1="&#x7b;" u2="&#x117;" k="51" />
<hkern u1="&#x7b;" u2="&#x115;" k="51" />
<hkern u1="&#x7b;" u2="&#x113;" k="51" />
<hkern u1="&#x7b;" u2="&#x111;" k="51" />
<hkern u1="&#x7b;" u2="&#x10f;" k="51" />
<hkern u1="&#x7b;" u2="&#x10d;" k="51" />
<hkern u1="&#x7b;" u2="&#x10c;" k="35" />
<hkern u1="&#x7b;" u2="&#x10b;" k="51" />
<hkern u1="&#x7b;" u2="&#x10a;" k="35" />
<hkern u1="&#x7b;" u2="&#x109;" k="51" />
<hkern u1="&#x7b;" u2="&#x108;" k="35" />
<hkern u1="&#x7b;" u2="&#x107;" k="51" />
<hkern u1="&#x7b;" u2="&#x106;" k="35" />
<hkern u1="&#x7b;" u2="&#x105;" k="39" />
<hkern u1="&#x7b;" u2="&#x104;" k="18" />
<hkern u1="&#x7b;" u2="&#x103;" k="39" />
<hkern u1="&#x7b;" u2="&#x102;" k="18" />
<hkern u1="&#x7b;" u2="&#x101;" k="39" />
<hkern u1="&#x7b;" u2="&#x100;" k="18" />
<hkern u1="&#x7b;" u2="&#xff;" k="37" />
<hkern u1="&#x7b;" u2="&#xfd;" k="37" />
<hkern u1="&#x7b;" u2="&#xfc;" k="45" />
<hkern u1="&#x7b;" u2="&#xfb;" k="45" />
<hkern u1="&#x7b;" u2="&#xfa;" k="45" />
<hkern u1="&#x7b;" u2="&#xf9;" k="45" />
<hkern u1="&#x7b;" u2="&#xf8;" k="51" />
<hkern u1="&#x7b;" u2="&#xf6;" k="51" />
<hkern u1="&#x7b;" u2="&#xf5;" k="51" />
<hkern u1="&#x7b;" u2="&#xf4;" k="51" />
<hkern u1="&#x7b;" u2="&#xf3;" k="51" />
<hkern u1="&#x7b;" u2="&#xf2;" k="51" />
<hkern u1="&#x7b;" u2="&#xf1;" k="20" />
<hkern u1="&#x7b;" u2="&#xef;" k="-55" />
<hkern u1="&#x7b;" u2="&#xec;" k="-86" />
<hkern u1="&#x7b;" u2="&#xeb;" k="51" />
<hkern u1="&#x7b;" u2="&#xea;" k="51" />
<hkern u1="&#x7b;" u2="&#xe9;" k="51" />
<hkern u1="&#x7b;" u2="&#xe8;" k="51" />
<hkern u1="&#x7b;" u2="&#xe7;" k="51" />
<hkern u1="&#x7b;" u2="&#xe6;" k="39" />
<hkern u1="&#x7b;" u2="&#xe5;" k="39" />
<hkern u1="&#x7b;" u2="&#xe4;" k="39" />
<hkern u1="&#x7b;" u2="&#xe3;" k="39" />
<hkern u1="&#x7b;" u2="&#xe2;" k="39" />
<hkern u1="&#x7b;" u2="&#xe1;" k="39" />
<hkern u1="&#x7b;" u2="&#xe0;" k="39" />
<hkern u1="&#x7b;" u2="&#xd8;" k="39" />
<hkern u1="&#x7b;" u2="&#xd6;" k="39" />
<hkern u1="&#x7b;" u2="&#xd5;" k="39" />
<hkern u1="&#x7b;" u2="&#xd4;" k="39" />
<hkern u1="&#x7b;" u2="&#xd3;" k="39" />
<hkern u1="&#x7b;" u2="&#xd2;" k="39" />
<hkern u1="&#x7b;" u2="&#xc7;" k="35" />
<hkern u1="&#x7b;" u2="&#xc6;" k="18" />
<hkern u1="&#x7b;" u2="&#xc5;" k="18" />
<hkern u1="&#x7b;" u2="&#xc4;" k="18" />
<hkern u1="&#x7b;" u2="&#xc3;" k="18" />
<hkern u1="&#x7b;" u2="&#xc2;" k="18" />
<hkern u1="&#x7b;" u2="&#xc1;" k="18" />
<hkern u1="&#x7b;" u2="&#xc0;" k="18" />
<hkern u1="&#x7b;" u2="&#x7b;" k="31" />
<hkern u1="&#x7b;" u2="z" k="16" />
<hkern u1="&#x7b;" u2="y" k="37" />
<hkern u1="&#x7b;" u2="x" k="14" />
<hkern u1="&#x7b;" u2="w" k="43" />
<hkern u1="&#x7b;" u2="v" k="37" />
<hkern u1="&#x7b;" u2="u" k="45" />
<hkern u1="&#x7b;" u2="t" k="16" />
<hkern u1="&#x7b;" u2="s" k="20" />
<hkern u1="&#x7b;" u2="r" k="20" />
<hkern u1="&#x7b;" u2="q" k="51" />
<hkern u1="&#x7b;" u2="p" k="20" />
<hkern u1="&#x7b;" u2="o" k="51" />
<hkern u1="&#x7b;" u2="n" k="20" />
<hkern u1="&#x7b;" u2="m" k="20" />
<hkern u1="&#x7b;" u2="j" k="-33" />
<hkern u1="&#x7b;" u2="f" k="10" />
<hkern u1="&#x7b;" u2="e" k="51" />
<hkern u1="&#x7b;" u2="d" k="51" />
<hkern u1="&#x7b;" u2="c" k="51" />
<hkern u1="&#x7b;" u2="a" k="39" />
<hkern u1="&#x7b;" u2="S" k="10" />
<hkern u1="&#x7b;" u2="Q" k="39" />
<hkern u1="&#x7b;" u2="O" k="39" />
<hkern u1="&#x7b;" u2="G" k="39" />
<hkern u1="&#x7b;" u2="C" k="35" />
<hkern u1="&#x7b;" u2="A" k="18" />
<hkern u1="&#x7b;" u2="&#x28;" k="8" />
<hkern u1="&#x7c;" u2="&#xec;" k="-23" />
<hkern u1="&#x7d;" u2="&#x7d;" k="31" />
<hkern u1="&#x7d;" u2="]" k="39" />
<hkern u1="&#x7d;" u2="&#x29;" k="25" />
<hkern u1="&#xa1;" u2="&#x21a;" k="78" />
<hkern u1="&#xa1;" u2="&#x178;" k="59" />
<hkern u1="&#xa1;" u2="&#x176;" k="59" />
<hkern u1="&#xa1;" u2="&#x166;" k="78" />
<hkern u1="&#xa1;" u2="&#x164;" k="78" />
<hkern u1="&#xa1;" u2="&#xdd;" k="59" />
<hkern u1="&#xa1;" u2="Y" k="59" />
<hkern u1="&#xa1;" u2="V" k="6" />
<hkern u1="&#xa1;" u2="T" k="78" />
<hkern u1="&#xab;" u2="V" k="31" />
<hkern u1="&#xae;" u2="&#x21a;" k="8" />
<hkern u1="&#xae;" u2="&#x1fc;" k="47" />
<hkern u1="&#xae;" u2="&#x1fa;" k="47" />
<hkern u1="&#xae;" u2="&#x17d;" k="12" />
<hkern u1="&#xae;" u2="&#x17b;" k="12" />
<hkern u1="&#xae;" u2="&#x179;" k="12" />
<hkern u1="&#xae;" u2="&#x178;" k="43" />
<hkern u1="&#xae;" u2="&#x176;" k="43" />
<hkern u1="&#xae;" u2="&#x166;" k="8" />
<hkern u1="&#xae;" u2="&#x164;" k="8" />
<hkern u1="&#xae;" u2="&#x134;" k="37" />
<hkern u1="&#xae;" u2="&#x104;" k="47" />
<hkern u1="&#xae;" u2="&#x102;" k="47" />
<hkern u1="&#xae;" u2="&#x100;" k="47" />
<hkern u1="&#xae;" u2="&#xdd;" k="43" />
<hkern u1="&#xae;" u2="&#xc6;" k="59" />
<hkern u1="&#xae;" u2="&#xc5;" k="47" />
<hkern u1="&#xae;" u2="&#xc4;" k="47" />
<hkern u1="&#xae;" u2="&#xc3;" k="47" />
<hkern u1="&#xae;" u2="&#xc2;" k="47" />
<hkern u1="&#xae;" u2="&#xc1;" k="47" />
<hkern u1="&#xae;" u2="&#xc0;" k="47" />
<hkern u1="&#xae;" u2="Z" k="12" />
<hkern u1="&#xae;" u2="Y" k="43" />
<hkern u1="&#xae;" u2="X" k="8" />
<hkern u1="&#xae;" u2="V" k="6" />
<hkern u1="&#xae;" u2="T" k="8" />
<hkern u1="&#xae;" u2="J" k="37" />
<hkern u1="&#xae;" u2="A" k="47" />
<hkern u1="&#xbb;" u2="&#x141;" k="-10" />
<hkern u1="&#xbb;" u2="&#xc6;" k="6" />
<hkern u1="&#xbb;" u2="x" k="47" />
<hkern u1="&#xbb;" u2="v" k="6" />
<hkern u1="&#xbb;" u2="f" k="14" />
<hkern u1="&#xbb;" u2="X" k="45" />
<hkern u1="&#xbb;" u2="V" k="51" />
<hkern u1="&#xbf;" u2="&#x21b;" k="49" />
<hkern u1="&#xbf;" u2="&#x21a;" k="139" />
<hkern u1="&#xbf;" u2="&#x219;" k="51" />
<hkern u1="&#xbf;" u2="&#x218;" k="39" />
<hkern u1="&#xbf;" u2="&#x1ff;" k="55" />
<hkern u1="&#xbf;" u2="&#x1fe;" k="45" />
<hkern u1="&#xbf;" u2="&#x1fd;" k="53" />
<hkern u1="&#xbf;" u2="&#x1fc;" k="53" />
<hkern u1="&#xbf;" u2="&#x1fb;" k="53" />
<hkern u1="&#xbf;" u2="&#x1fa;" k="53" />
<hkern u1="&#xbf;" u2="&#x17e;" k="47" />
<hkern u1="&#xbf;" u2="&#x17d;" k="51" />
<hkern u1="&#xbf;" u2="&#x17c;" k="47" />
<hkern u1="&#xbf;" u2="&#x17b;" k="51" />
<hkern u1="&#xbf;" u2="&#x17a;" k="47" />
<hkern u1="&#xbf;" u2="&#x179;" k="51" />
<hkern u1="&#xbf;" u2="&#x178;" k="123" />
<hkern u1="&#xbf;" u2="&#x177;" k="55" />
<hkern u1="&#xbf;" u2="&#x176;" k="123" />
<hkern u1="&#xbf;" u2="&#x175;" k="53" />
<hkern u1="&#xbf;" u2="&#x174;" k="63" />
<hkern u1="&#xbf;" u2="&#x173;" k="51" />
<hkern u1="&#xbf;" u2="&#x172;" k="47" />
<hkern u1="&#xbf;" u2="&#x171;" k="51" />
<hkern u1="&#xbf;" u2="&#x170;" k="47" />
<hkern u1="&#xbf;" u2="&#x16f;" k="51" />
<hkern u1="&#xbf;" u2="&#x16e;" k="47" />
<hkern u1="&#xbf;" u2="&#x16d;" k="51" />
<hkern u1="&#xbf;" u2="&#x16c;" k="47" />
<hkern u1="&#xbf;" u2="&#x16b;" k="51" />
<hkern u1="&#xbf;" u2="&#x16a;" k="47" />
<hkern u1="&#xbf;" u2="&#x169;" k="51" />
<hkern u1="&#xbf;" u2="&#x168;" k="47" />
<hkern u1="&#xbf;" u2="&#x167;" k="49" />
<hkern u1="&#xbf;" u2="&#x166;" k="139" />
<hkern u1="&#xbf;" u2="&#x165;" k="49" />
<hkern u1="&#xbf;" u2="&#x164;" k="139" />
<hkern u1="&#xbf;" u2="&#x161;" k="51" />
<hkern u1="&#xbf;" u2="&#x160;" k="39" />
<hkern u1="&#xbf;" u2="&#x15f;" k="51" />
<hkern u1="&#xbf;" u2="&#x15e;" k="39" />
<hkern u1="&#xbf;" u2="&#x15d;" k="51" />
<hkern u1="&#xbf;" u2="&#x15c;" k="39" />
<hkern u1="&#xbf;" u2="&#x15b;" k="51" />
<hkern u1="&#xbf;" u2="&#x15a;" k="39" />
<hkern u1="&#xbf;" u2="&#x159;" k="49" />
<hkern u1="&#xbf;" u2="&#x158;" k="41" />
<hkern u1="&#xbf;" u2="&#x157;" k="49" />
<hkern u1="&#xbf;" u2="&#x156;" k="41" />
<hkern u1="&#xbf;" u2="&#x155;" k="49" />
<hkern u1="&#xbf;" u2="&#x154;" k="41" />
<hkern u1="&#xbf;" u2="&#x153;" k="55" />
<hkern u1="&#xbf;" u2="&#x152;" k="45" />
<hkern u1="&#xbf;" u2="&#x151;" k="55" />
<hkern u1="&#xbf;" u2="&#x150;" k="45" />
<hkern u1="&#xbf;" u2="&#x14f;" k="55" />
<hkern u1="&#xbf;" u2="&#x14e;" k="45" />
<hkern u1="&#xbf;" u2="&#x14d;" k="55" />
<hkern u1="&#xbf;" u2="&#x14c;" k="45" />
<hkern u1="&#xbf;" u2="&#x14b;" k="49" />
<hkern u1="&#xbf;" u2="&#x14a;" k="41" />
<hkern u1="&#xbf;" u2="&#x148;" k="49" />
<hkern u1="&#xbf;" u2="&#x147;" k="41" />
<hkern u1="&#xbf;" u2="&#x146;" k="49" />
<hkern u1="&#xbf;" u2="&#x145;" k="41" />
<hkern u1="&#xbf;" u2="&#x144;" k="49" />
<hkern u1="&#xbf;" u2="&#x143;" k="41" />
<hkern u1="&#xbf;" u2="&#x142;" k="49" />
<hkern u1="&#xbf;" u2="&#x141;" k="41" />
<hkern u1="&#xbf;" u2="&#x13e;" k="49" />
<hkern u1="&#xbf;" u2="&#x13d;" k="41" />
<hkern u1="&#xbf;" u2="&#x13c;" k="49" />
<hkern u1="&#xbf;" u2="&#x13b;" k="41" />
<hkern u1="&#xbf;" u2="&#x13a;" k="49" />
<hkern u1="&#xbf;" u2="&#x139;" k="41" />
<hkern u1="&#xbf;" u2="&#x137;" k="49" />
<hkern u1="&#xbf;" u2="&#x136;" k="41" />
<hkern u1="&#xbf;" u2="&#x135;" k="49" />
<hkern u1="&#xbf;" u2="&#x134;" k="12" />
<hkern u1="&#xbf;" u2="&#x131;" k="49" />
<hkern u1="&#xbf;" u2="&#x130;" k="41" />
<hkern u1="&#xbf;" u2="&#x12f;" k="27" />
<hkern u1="&#xbf;" u2="&#x12e;" k="39" />
<hkern u1="&#xbf;" u2="&#x12d;" k="49" />
<hkern u1="&#xbf;" u2="&#x12c;" k="41" />
<hkern u1="&#xbf;" u2="&#x12b;" k="49" />
<hkern u1="&#xbf;" u2="&#x12a;" k="41" />
<hkern u1="&#xbf;" u2="&#x129;" k="49" />
<hkern u1="&#xbf;" u2="&#x128;" k="41" />
<hkern u1="&#xbf;" u2="&#x127;" k="49" />
<hkern u1="&#xbf;" u2="&#x126;" k="41" />
<hkern u1="&#xbf;" u2="&#x125;" k="49" />
<hkern u1="&#xbf;" u2="&#x124;" k="41" />
<hkern u1="&#xbf;" u2="&#x122;" k="45" />
<hkern u1="&#xbf;" u2="&#x120;" k="45" />
<hkern u1="&#xbf;" u2="&#x11e;" k="45" />
<hkern u1="&#xbf;" u2="&#x11c;" k="45" />
<hkern u1="&#xbf;" u2="&#x11b;" k="55" />
<hkern u1="&#xbf;" u2="&#x11a;" k="41" />
<hkern u1="&#xbf;" u2="&#x119;" k="55" />
<hkern u1="&#xbf;" u2="&#x118;" k="41" />
<hkern u1="&#xbf;" u2="&#x117;" k="55" />
<hkern u1="&#xbf;" u2="&#x116;" k="41" />
<hkern u1="&#xbf;" u2="&#x115;" k="55" />
<hkern u1="&#xbf;" u2="&#x114;" k="41" />
<hkern u1="&#xbf;" u2="&#x113;" k="55" />
<hkern u1="&#xbf;" u2="&#x112;" k="41" />
<hkern u1="&#xbf;" u2="&#x111;" k="55" />
<hkern u1="&#xbf;" u2="&#x110;" k="41" />
<hkern u1="&#xbf;" u2="&#x10f;" k="55" />
<hkern u1="&#xbf;" u2="&#x10e;" k="41" />
<hkern u1="&#xbf;" u2="&#x10d;" k="55" />
<hkern u1="&#xbf;" u2="&#x10c;" k="43" />
<hkern u1="&#xbf;" u2="&#x10b;" k="55" />
<hkern u1="&#xbf;" u2="&#x10a;" k="43" />
<hkern u1="&#xbf;" u2="&#x109;" k="55" />
<hkern u1="&#xbf;" u2="&#x108;" k="43" />
<hkern u1="&#xbf;" u2="&#x107;" k="55" />
<hkern u1="&#xbf;" u2="&#x106;" k="43" />
<hkern u1="&#xbf;" u2="&#x105;" k="53" />
<hkern u1="&#xbf;" u2="&#x104;" k="53" />
<hkern u1="&#xbf;" u2="&#x103;" k="53" />
<hkern u1="&#xbf;" u2="&#x102;" k="53" />
<hkern u1="&#xbf;" u2="&#x101;" k="53" />
<hkern u1="&#xbf;" u2="&#x100;" k="53" />
<hkern u1="&#xbf;" u2="&#xff;" k="55" />
<hkern u1="&#xbf;" u2="&#xfd;" k="55" />
<hkern u1="&#xbf;" u2="&#xfc;" k="51" />
<hkern u1="&#xbf;" u2="&#xfb;" k="51" />
<hkern u1="&#xbf;" u2="&#xfa;" k="51" />
<hkern u1="&#xbf;" u2="&#xf9;" k="51" />
<hkern u1="&#xbf;" u2="&#xf8;" k="55" />
<hkern u1="&#xbf;" u2="&#xf6;" k="55" />
<hkern u1="&#xbf;" u2="&#xf5;" k="55" />
<hkern u1="&#xbf;" u2="&#xf4;" k="55" />
<hkern u1="&#xbf;" u2="&#xf3;" k="55" />
<hkern u1="&#xbf;" u2="&#xf2;" k="55" />
<hkern u1="&#xbf;" u2="&#xf1;" k="49" />
<hkern u1="&#xbf;" u2="&#xef;" k="49" />
<hkern u1="&#xbf;" u2="&#xee;" k="49" />
<hkern u1="&#xbf;" u2="&#xed;" k="49" />
<hkern u1="&#xbf;" u2="&#xec;" k="49" />
<hkern u1="&#xbf;" u2="&#xeb;" k="55" />
<hkern u1="&#xbf;" u2="&#xea;" k="55" />
<hkern u1="&#xbf;" u2="&#xe9;" k="55" />
<hkern u1="&#xbf;" u2="&#xe8;" k="55" />
<hkern u1="&#xbf;" u2="&#xe7;" k="55" />
<hkern u1="&#xbf;" u2="&#xe6;" k="53" />
<hkern u1="&#xbf;" u2="&#xe5;" k="53" />
<hkern u1="&#xbf;" u2="&#xe4;" k="53" />
<hkern u1="&#xbf;" u2="&#xe3;" k="53" />
<hkern u1="&#xbf;" u2="&#xe2;" k="53" />
<hkern u1="&#xbf;" u2="&#xe1;" k="53" />
<hkern u1="&#xbf;" u2="&#xe0;" k="53" />
<hkern u1="&#xbf;" u2="&#xdf;" k="49" />
<hkern u1="&#xbf;" u2="&#xdd;" k="123" />
<hkern u1="&#xbf;" u2="&#xdc;" k="47" />
<hkern u1="&#xbf;" u2="&#xdb;" k="47" />
<hkern u1="&#xbf;" u2="&#xda;" k="47" />
<hkern u1="&#xbf;" u2="&#xd9;" k="47" />
<hkern u1="&#xbf;" u2="&#xd8;" k="45" />
<hkern u1="&#xbf;" u2="&#xd6;" k="45" />
<hkern u1="&#xbf;" u2="&#xd5;" k="45" />
<hkern u1="&#xbf;" u2="&#xd4;" k="45" />
<hkern u1="&#xbf;" u2="&#xd3;" k="45" />
<hkern u1="&#xbf;" u2="&#xd2;" k="45" />
<hkern u1="&#xbf;" u2="&#xd1;" k="41" />
<hkern u1="&#xbf;" u2="&#xcf;" k="41" />
<hkern u1="&#xbf;" u2="&#xce;" k="41" />
<hkern u1="&#xbf;" u2="&#xcd;" k="41" />
<hkern u1="&#xbf;" u2="&#xcc;" k="41" />
<hkern u1="&#xbf;" u2="&#xcb;" k="41" />
<hkern u1="&#xbf;" u2="&#xca;" k="41" />
<hkern u1="&#xbf;" u2="&#xc9;" k="41" />
<hkern u1="&#xbf;" u2="&#xc8;" k="41" />
<hkern u1="&#xbf;" u2="&#xc7;" k="43" />
<hkern u1="&#xbf;" u2="&#xc6;" k="55" />
<hkern u1="&#xbf;" u2="&#xc5;" k="53" />
<hkern u1="&#xbf;" u2="&#xc4;" k="53" />
<hkern u1="&#xbf;" u2="&#xc3;" k="53" />
<hkern u1="&#xbf;" u2="&#xc2;" k="53" />
<hkern u1="&#xbf;" u2="&#xc1;" k="53" />
<hkern u1="&#xbf;" u2="&#xc0;" k="53" />
<hkern u1="&#xbf;" u2="z" k="47" />
<hkern u1="&#xbf;" u2="y" k="55" />
<hkern u1="&#xbf;" u2="x" k="43" />
<hkern u1="&#xbf;" u2="w" k="53" />
<hkern u1="&#xbf;" u2="v" k="57" />
<hkern u1="&#xbf;" u2="u" k="51" />
<hkern u1="&#xbf;" u2="t" k="49" />
<hkern u1="&#xbf;" u2="s" k="51" />
<hkern u1="&#xbf;" u2="r" k="49" />
<hkern u1="&#xbf;" u2="q" k="55" />
<hkern u1="&#xbf;" u2="p" k="49" />
<hkern u1="&#xbf;" u2="o" k="55" />
<hkern u1="&#xbf;" u2="n" k="49" />
<hkern u1="&#xbf;" u2="m" k="49" />
<hkern u1="&#xbf;" u2="l" k="49" />
<hkern u1="&#xbf;" u2="k" k="49" />
<hkern u1="&#xbf;" u2="j" k="49" />
<hkern u1="&#xbf;" u2="i" k="49" />
<hkern u1="&#xbf;" u2="h" k="49" />
<hkern u1="&#xbf;" u2="f" k="47" />
<hkern u1="&#xbf;" u2="e" k="55" />
<hkern u1="&#xbf;" u2="d" k="55" />
<hkern u1="&#xbf;" u2="c" k="55" />
<hkern u1="&#xbf;" u2="b" k="49" />
<hkern u1="&#xbf;" u2="a" k="53" />
<hkern u1="&#xbf;" u2="Z" k="51" />
<hkern u1="&#xbf;" u2="Y" k="123" />
<hkern u1="&#xbf;" u2="X" k="53" />
<hkern u1="&#xbf;" u2="W" k="63" />
<hkern u1="&#xbf;" u2="V" k="78" />
<hkern u1="&#xbf;" u2="U" k="47" />
<hkern u1="&#xbf;" u2="T" k="139" />
<hkern u1="&#xbf;" u2="S" k="39" />
<hkern u1="&#xbf;" u2="R" k="41" />
<hkern u1="&#xbf;" u2="Q" k="45" />
<hkern u1="&#xbf;" u2="P" k="41" />
<hkern u1="&#xbf;" u2="O" k="45" />
<hkern u1="&#xbf;" u2="N" k="41" />
<hkern u1="&#xbf;" u2="M" k="41" />
<hkern u1="&#xbf;" u2="L" k="41" />
<hkern u1="&#xbf;" u2="K" k="41" />
<hkern u1="&#xbf;" u2="J" k="12" />
<hkern u1="&#xbf;" u2="I" k="41" />
<hkern u1="&#xbf;" u2="H" k="41" />
<hkern u1="&#xbf;" u2="G" k="45" />
<hkern u1="&#xbf;" u2="F" k="41" />
<hkern u1="&#xbf;" u2="E" k="41" />
<hkern u1="&#xbf;" u2="D" k="41" />
<hkern u1="&#xbf;" u2="C" k="43" />
<hkern u1="&#xbf;" u2="B" k="41" />
<hkern u1="&#xbf;" u2="A" k="53" />
<hkern u1="&#xc0;" u2="&#x2122;" k="72" />
<hkern u1="&#xc0;" u2="&#xae;" k="43" />
<hkern u1="&#xc0;" u2="&#x7d;" k="18" />
<hkern u1="&#xc0;" u2="v" k="33" />
<hkern u1="&#xc0;" u2="f" k="16" />
<hkern u1="&#xc0;" u2="]" k="20" />
<hkern u1="&#xc0;" u2="\" k="90" />
<hkern u1="&#xc0;" u2="V" k="57" />
<hkern u1="&#xc0;" u2="&#x3f;" k="39" />
<hkern u1="&#xc0;" u2="&#x2a;" k="63" />
<hkern u1="&#xc1;" u2="&#x2122;" k="72" />
<hkern u1="&#xc1;" u2="&#xae;" k="43" />
<hkern u1="&#xc1;" u2="&#x7d;" k="18" />
<hkern u1="&#xc1;" u2="v" k="33" />
<hkern u1="&#xc1;" u2="f" k="16" />
<hkern u1="&#xc1;" u2="]" k="20" />
<hkern u1="&#xc1;" u2="\" k="90" />
<hkern u1="&#xc1;" u2="V" k="57" />
<hkern u1="&#xc1;" u2="&#x3f;" k="39" />
<hkern u1="&#xc1;" u2="&#x2a;" k="63" />
<hkern u1="&#xc2;" u2="&#x2122;" k="72" />
<hkern u1="&#xc2;" u2="&#xae;" k="43" />
<hkern u1="&#xc2;" u2="&#x7d;" k="18" />
<hkern u1="&#xc2;" u2="v" k="33" />
<hkern u1="&#xc2;" u2="f" k="16" />
<hkern u1="&#xc2;" u2="]" k="20" />
<hkern u1="&#xc2;" u2="\" k="90" />
<hkern u1="&#xc2;" u2="V" k="57" />
<hkern u1="&#xc2;" u2="&#x3f;" k="39" />
<hkern u1="&#xc2;" u2="&#x2a;" k="63" />
<hkern u1="&#xc3;" u2="&#x2122;" k="72" />
<hkern u1="&#xc3;" u2="&#xae;" k="43" />
<hkern u1="&#xc3;" u2="&#x7d;" k="18" />
<hkern u1="&#xc3;" u2="v" k="33" />
<hkern u1="&#xc3;" u2="f" k="16" />
<hkern u1="&#xc3;" u2="]" k="20" />
<hkern u1="&#xc3;" u2="\" k="90" />
<hkern u1="&#xc3;" u2="V" k="57" />
<hkern u1="&#xc3;" u2="&#x3f;" k="39" />
<hkern u1="&#xc3;" u2="&#x2a;" k="63" />
<hkern u1="&#xc4;" u2="&#x2122;" k="72" />
<hkern u1="&#xc4;" u2="&#xae;" k="43" />
<hkern u1="&#xc4;" u2="&#x7d;" k="18" />
<hkern u1="&#xc4;" u2="v" k="33" />
<hkern u1="&#xc4;" u2="f" k="16" />
<hkern u1="&#xc4;" u2="]" k="20" />
<hkern u1="&#xc4;" u2="\" k="90" />
<hkern u1="&#xc4;" u2="V" k="57" />
<hkern u1="&#xc4;" u2="&#x3f;" k="39" />
<hkern u1="&#xc4;" u2="&#x2a;" k="63" />
<hkern u1="&#xc5;" u2="&#x2122;" k="72" />
<hkern u1="&#xc5;" u2="&#xae;" k="43" />
<hkern u1="&#xc5;" u2="&#x7d;" k="18" />
<hkern u1="&#xc5;" u2="v" k="33" />
<hkern u1="&#xc5;" u2="f" k="16" />
<hkern u1="&#xc5;" u2="]" k="20" />
<hkern u1="&#xc5;" u2="\" k="90" />
<hkern u1="&#xc5;" u2="V" k="57" />
<hkern u1="&#xc5;" u2="&#x3f;" k="39" />
<hkern u1="&#xc5;" u2="&#x2a;" k="63" />
<hkern u1="&#xc6;" u2="&#x135;" k="-33" />
<hkern u1="&#xc6;" u2="&#x12d;" k="-16" />
<hkern u1="&#xc6;" u2="&#x12b;" k="-6" />
<hkern u1="&#xc6;" u2="&#x129;" k="-47" />
<hkern u1="&#xc6;" u2="&#xef;" k="-49" />
<hkern u1="&#xc6;" u2="&#xee;" k="-43" />
<hkern u1="&#xc6;" u2="&#xec;" k="-80" />
<hkern u1="&#xc6;" u2="v" k="10" />
<hkern u1="&#xc7;" u2="&#x135;" k="-33" />
<hkern u1="&#xc7;" u2="&#x12d;" k="-25" />
<hkern u1="&#xc7;" u2="&#x12b;" k="-8" />
<hkern u1="&#xc7;" u2="&#x129;" k="-53" />
<hkern u1="&#xc7;" u2="&#xef;" k="-53" />
<hkern u1="&#xc7;" u2="&#xee;" k="-43" />
<hkern u1="&#xc7;" u2="&#xec;" k="-80" />
<hkern u1="&#xc7;" u2="&#xae;" k="10" />
<hkern u1="&#xc7;" u2="v" k="20" />
<hkern u1="&#xc7;" u2="f" k="10" />
<hkern u1="&#xc8;" u2="&#x135;" k="-33" />
<hkern u1="&#xc8;" u2="&#x12d;" k="-16" />
<hkern u1="&#xc8;" u2="&#x12b;" k="-6" />
<hkern u1="&#xc8;" u2="&#x129;" k="-47" />
<hkern u1="&#xc8;" u2="&#xef;" k="-49" />
<hkern u1="&#xc8;" u2="&#xee;" k="-43" />
<hkern u1="&#xc8;" u2="&#xec;" k="-80" />
<hkern u1="&#xc8;" u2="v" k="10" />
<hkern u1="&#xc9;" u2="&#x135;" k="-33" />
<hkern u1="&#xc9;" u2="&#x12d;" k="-16" />
<hkern u1="&#xc9;" u2="&#x12b;" k="-6" />
<hkern u1="&#xc9;" u2="&#x129;" k="-47" />
<hkern u1="&#xc9;" u2="&#xef;" k="-49" />
<hkern u1="&#xc9;" u2="&#xee;" k="-43" />
<hkern u1="&#xc9;" u2="&#xec;" k="-80" />
<hkern u1="&#xc9;" u2="v" k="10" />
<hkern u1="&#xca;" u2="&#x135;" k="-33" />
<hkern u1="&#xca;" u2="&#x12d;" k="-16" />
<hkern u1="&#xca;" u2="&#x12b;" k="-6" />
<hkern u1="&#xca;" u2="&#x129;" k="-47" />
<hkern u1="&#xca;" u2="&#xef;" k="-49" />
<hkern u1="&#xca;" u2="&#xee;" k="-43" />
<hkern u1="&#xca;" u2="&#xec;" k="-80" />
<hkern u1="&#xca;" u2="v" k="10" />
<hkern u1="&#xcb;" u2="&#x135;" k="-33" />
<hkern u1="&#xcb;" u2="&#x12d;" k="-16" />
<hkern u1="&#xcb;" u2="&#x12b;" k="-6" />
<hkern u1="&#xcb;" u2="&#x129;" k="-47" />
<hkern u1="&#xcb;" u2="&#xef;" k="-49" />
<hkern u1="&#xcb;" u2="&#xee;" k="-43" />
<hkern u1="&#xcb;" u2="&#xec;" k="-80" />
<hkern u1="&#xcb;" u2="v" k="10" />
<hkern u1="&#xcc;" u2="&#xec;" k="-12" />
<hkern u1="&#xcc;" u2="f" k="8" />
<hkern u1="&#xcd;" u2="&#xec;" k="-12" />
<hkern u1="&#xcd;" u2="f" k="8" />
<hkern u1="&#xce;" u2="&#xec;" k="-12" />
<hkern u1="&#xce;" u2="f" k="8" />
<hkern u1="&#xcf;" u2="&#xec;" k="-12" />
<hkern u1="&#xcf;" u2="f" k="8" />
<hkern u1="&#xd1;" u2="&#xec;" k="-12" />
<hkern u1="&#xd1;" u2="f" k="8" />
<hkern u1="&#xd2;" u2="&#xc6;" k="29" />
<hkern u1="&#xd2;" u2="&#x7d;" k="35" />
<hkern u1="&#xd2;" u2="]" k="49" />
<hkern u1="&#xd2;" u2="\" k="29" />
<hkern u1="&#xd2;" u2="X" k="37" />
<hkern u1="&#xd2;" u2="V" k="23" />
<hkern u1="&#xd2;" u2="&#x3f;" k="6" />
<hkern u1="&#xd2;" u2="&#x2f;" k="20" />
<hkern u1="&#xd2;" u2="&#x29;" k="20" />
<hkern u1="&#xd3;" u2="&#xc6;" k="29" />
<hkern u1="&#xd3;" u2="&#x7d;" k="35" />
<hkern u1="&#xd3;" u2="]" k="49" />
<hkern u1="&#xd3;" u2="\" k="29" />
<hkern u1="&#xd3;" u2="X" k="37" />
<hkern u1="&#xd3;" u2="V" k="23" />
<hkern u1="&#xd3;" u2="&#x3f;" k="6" />
<hkern u1="&#xd3;" u2="&#x2f;" k="20" />
<hkern u1="&#xd3;" u2="&#x29;" k="20" />
<hkern u1="&#xd4;" u2="&#xc6;" k="29" />
<hkern u1="&#xd4;" u2="&#x7d;" k="35" />
<hkern u1="&#xd4;" u2="]" k="49" />
<hkern u1="&#xd4;" u2="\" k="29" />
<hkern u1="&#xd4;" u2="X" k="37" />
<hkern u1="&#xd4;" u2="V" k="23" />
<hkern u1="&#xd4;" u2="&#x3f;" k="6" />
<hkern u1="&#xd4;" u2="&#x2f;" k="20" />
<hkern u1="&#xd4;" u2="&#x29;" k="20" />
<hkern u1="&#xd5;" u2="&#xc6;" k="29" />
<hkern u1="&#xd5;" u2="&#x7d;" k="35" />
<hkern u1="&#xd5;" u2="]" k="49" />
<hkern u1="&#xd5;" u2="\" k="29" />
<hkern u1="&#xd5;" u2="X" k="37" />
<hkern u1="&#xd5;" u2="V" k="23" />
<hkern u1="&#xd5;" u2="&#x3f;" k="6" />
<hkern u1="&#xd5;" u2="&#x2f;" k="20" />
<hkern u1="&#xd5;" u2="&#x29;" k="20" />
<hkern u1="&#xd6;" u2="&#xc6;" k="29" />
<hkern u1="&#xd6;" u2="&#x7d;" k="35" />
<hkern u1="&#xd6;" u2="]" k="49" />
<hkern u1="&#xd6;" u2="\" k="29" />
<hkern u1="&#xd6;" u2="X" k="37" />
<hkern u1="&#xd6;" u2="V" k="23" />
<hkern u1="&#xd6;" u2="&#x3f;" k="6" />
<hkern u1="&#xd6;" u2="&#x2f;" k="20" />
<hkern u1="&#xd6;" u2="&#x29;" k="20" />
<hkern u1="&#xd8;" u2="&#xc6;" k="29" />
<hkern u1="&#xd8;" u2="&#x7d;" k="35" />
<hkern u1="&#xd8;" u2="]" k="49" />
<hkern u1="&#xd8;" u2="\" k="29" />
<hkern u1="&#xd8;" u2="X" k="37" />
<hkern u1="&#xd8;" u2="V" k="23" />
<hkern u1="&#xd8;" u2="&#x3f;" k="6" />
<hkern u1="&#xd8;" u2="&#x2f;" k="20" />
<hkern u1="&#xd8;" u2="&#x29;" k="20" />
<hkern u1="&#xd9;" u2="&#xec;" k="-20" />
<hkern u1="&#xd9;" u2="&#xc6;" k="16" />
<hkern u1="&#xd9;" u2="f" k="8" />
<hkern u1="&#xd9;" u2="&#x2f;" k="25" />
<hkern u1="&#xda;" u2="&#xec;" k="-20" />
<hkern u1="&#xda;" u2="&#xc6;" k="16" />
<hkern u1="&#xda;" u2="f" k="8" />
<hkern u1="&#xda;" u2="&#x2f;" k="25" />
<hkern u1="&#xdb;" u2="&#xec;" k="-20" />
<hkern u1="&#xdb;" u2="&#xc6;" k="16" />
<hkern u1="&#xdb;" u2="f" k="8" />
<hkern u1="&#xdb;" u2="&#x2f;" k="25" />
<hkern u1="&#xdc;" u2="&#xec;" k="-20" />
<hkern u1="&#xdc;" u2="&#xc6;" k="16" />
<hkern u1="&#xdc;" u2="f" k="8" />
<hkern u1="&#xdc;" u2="&#x2f;" k="25" />
<hkern u1="&#xdd;" u2="&#x159;" k="66" />
<hkern u1="&#xdd;" u2="&#x155;" k="78" />
<hkern u1="&#xdd;" u2="&#x151;" k="111" />
<hkern u1="&#xdd;" u2="&#x142;" k="12" />
<hkern u1="&#xdd;" u2="&#x135;" k="-23" />
<hkern u1="&#xdd;" u2="&#x131;" k="123" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-92" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-70" />
<hkern u1="&#xdd;" u2="&#x129;" k="-96" />
<hkern u1="&#xdd;" u2="&#x103;" k="121" />
<hkern u1="&#xdd;" u2="&#xff;" k="57" />
<hkern u1="&#xdd;" u2="&#xef;" k="-121" />
<hkern u1="&#xdd;" u2="&#xee;" k="-33" />
<hkern u1="&#xdd;" u2="&#xec;" k="-129" />
<hkern u1="&#xdd;" u2="&#xeb;" k="131" />
<hkern u1="&#xdd;" u2="&#xe4;" k="100" />
<hkern u1="&#xdd;" u2="&#xe3;" k="86" />
<hkern u1="&#xdd;" u2="&#xdf;" k="20" />
<hkern u1="&#xdd;" u2="&#xc6;" k="115" />
<hkern u1="&#xdd;" u2="&#xae;" k="41" />
<hkern u1="&#xdd;" u2="x" k="76" />
<hkern u1="&#xdd;" u2="v" k="74" />
<hkern u1="&#xdd;" u2="f" k="43" />
<hkern u1="&#xdd;" u2="&#x40;" k="74" />
<hkern u1="&#xdd;" u2="&#x2f;" k="133" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-8" />
<hkern u1="&#xdd;" u2="&#x26;" k="66" />
<hkern u1="&#xdf;" u2="&#x2122;" k="20" />
<hkern u1="&#xdf;" u2="&#x201d;" k="27" />
<hkern u1="&#xdf;" u2="&#x201c;" k="31" />
<hkern u1="&#xdf;" u2="&#x2019;" k="27" />
<hkern u1="&#xdf;" u2="&#x2018;" k="31" />
<hkern u1="&#xdf;" u2="&#x21b;" k="14" />
<hkern u1="&#xdf;" u2="&#x21a;" k="47" />
<hkern u1="&#xdf;" u2="&#x178;" k="78" />
<hkern u1="&#xdf;" u2="&#x177;" k="31" />
<hkern u1="&#xdf;" u2="&#x176;" k="78" />
<hkern u1="&#xdf;" u2="&#x175;" k="16" />
<hkern u1="&#xdf;" u2="&#x174;" k="33" />
<hkern u1="&#xdf;" u2="&#x172;" k="8" />
<hkern u1="&#xdf;" u2="&#x170;" k="8" />
<hkern u1="&#xdf;" u2="&#x16e;" k="8" />
<hkern u1="&#xdf;" u2="&#x16c;" k="8" />
<hkern u1="&#xdf;" u2="&#x16a;" k="8" />
<hkern u1="&#xdf;" u2="&#x168;" k="8" />
<hkern u1="&#xdf;" u2="&#x167;" k="14" />
<hkern u1="&#xdf;" u2="&#x166;" k="47" />
<hkern u1="&#xdf;" u2="&#x165;" k="14" />
<hkern u1="&#xdf;" u2="&#x164;" k="47" />
<hkern u1="&#xdf;" u2="&#x134;" k="18" />
<hkern u1="&#xdf;" u2="&#x123;" k="10" />
<hkern u1="&#xdf;" u2="&#x121;" k="10" />
<hkern u1="&#xdf;" u2="&#x11f;" k="10" />
<hkern u1="&#xdf;" u2="&#x11d;" k="10" />
<hkern u1="&#xdf;" u2="&#xff;" k="31" />
<hkern u1="&#xdf;" u2="&#xfd;" k="31" />
<hkern u1="&#xdf;" u2="&#xdd;" k="78" />
<hkern u1="&#xdf;" u2="&#xdc;" k="8" />
<hkern u1="&#xdf;" u2="&#xdb;" k="8" />
<hkern u1="&#xdf;" u2="&#xda;" k="8" />
<hkern u1="&#xdf;" u2="&#xd9;" k="8" />
<hkern u1="&#xdf;" u2="&#xae;" k="25" />
<hkern u1="&#xdf;" u2="&#x7d;" k="14" />
<hkern u1="&#xdf;" u2="y" k="31" />
<hkern u1="&#xdf;" u2="x" k="12" />
<hkern u1="&#xdf;" u2="w" k="16" />
<hkern u1="&#xdf;" u2="v" k="27" />
<hkern u1="&#xdf;" u2="t" k="14" />
<hkern u1="&#xdf;" u2="g" k="10" />
<hkern u1="&#xdf;" u2="f" k="12" />
<hkern u1="&#xdf;" u2="]" k="31" />
<hkern u1="&#xdf;" u2="\" k="41" />
<hkern u1="&#xdf;" u2="Y" k="78" />
<hkern u1="&#xdf;" u2="X" k="10" />
<hkern u1="&#xdf;" u2="W" k="33" />
<hkern u1="&#xdf;" u2="V" k="49" />
<hkern u1="&#xdf;" u2="U" k="8" />
<hkern u1="&#xdf;" u2="T" k="47" />
<hkern u1="&#xdf;" u2="J" k="18" />
<hkern u1="&#xdf;" u2="&#x3f;" k="8" />
<hkern u1="&#xdf;" u2="&#x2a;" k="27" />
<hkern u1="&#xdf;" u2="&#x27;" k="25" />
<hkern u1="&#xdf;" u2="&#x22;" k="25" />
<hkern u1="&#xe0;" u2="&#x2122;" k="29" />
<hkern u1="&#xe0;" u2="&#x7d;" k="8" />
<hkern u1="&#xe0;" u2="v" k="10" />
<hkern u1="&#xe0;" u2="]" k="10" />
<hkern u1="&#xe0;" u2="\" k="82" />
<hkern u1="&#xe0;" u2="V" k="55" />
<hkern u1="&#xe0;" u2="&#x3f;" k="29" />
<hkern u1="&#xe0;" u2="&#x2a;" k="12" />
<hkern u1="&#xe1;" u2="&#x2122;" k="29" />
<hkern u1="&#xe1;" u2="&#x7d;" k="8" />
<hkern u1="&#xe1;" u2="v" k="10" />
<hkern u1="&#xe1;" u2="]" k="10" />
<hkern u1="&#xe1;" u2="\" k="82" />
<hkern u1="&#xe1;" u2="V" k="55" />
<hkern u1="&#xe1;" u2="&#x3f;" k="29" />
<hkern u1="&#xe1;" u2="&#x2a;" k="12" />
<hkern u1="&#xe2;" u2="&#x2122;" k="29" />
<hkern u1="&#xe2;" u2="&#x7d;" k="8" />
<hkern u1="&#xe2;" u2="v" k="10" />
<hkern u1="&#xe2;" u2="]" k="10" />
<hkern u1="&#xe2;" u2="\" k="82" />
<hkern u1="&#xe2;" u2="V" k="55" />
<hkern u1="&#xe2;" u2="&#x3f;" k="29" />
<hkern u1="&#xe2;" u2="&#x2a;" k="12" />
<hkern u1="&#xe3;" u2="&#x2122;" k="29" />
<hkern u1="&#xe3;" u2="&#x7d;" k="8" />
<hkern u1="&#xe3;" u2="v" k="10" />
<hkern u1="&#xe3;" u2="]" k="10" />
<hkern u1="&#xe3;" u2="\" k="82" />
<hkern u1="&#xe3;" u2="V" k="55" />
<hkern u1="&#xe3;" u2="&#x3f;" k="29" />
<hkern u1="&#xe3;" u2="&#x2a;" k="12" />
<hkern u1="&#xe4;" u2="&#x2122;" k="29" />
<hkern u1="&#xe4;" u2="&#x7d;" k="8" />
<hkern u1="&#xe4;" u2="v" k="10" />
<hkern u1="&#xe4;" u2="]" k="10" />
<hkern u1="&#xe4;" u2="\" k="82" />
<hkern u1="&#xe4;" u2="V" k="55" />
<hkern u1="&#xe4;" u2="&#x3f;" k="29" />
<hkern u1="&#xe4;" u2="&#x2a;" k="12" />
<hkern u1="&#xe5;" u2="&#x2122;" k="29" />
<hkern u1="&#xe5;" u2="&#x7d;" k="8" />
<hkern u1="&#xe5;" u2="v" k="10" />
<hkern u1="&#xe5;" u2="]" k="10" />
<hkern u1="&#xe5;" u2="\" k="82" />
<hkern u1="&#xe5;" u2="V" k="55" />
<hkern u1="&#xe5;" u2="&#x3f;" k="29" />
<hkern u1="&#xe5;" u2="&#x2a;" k="12" />
<hkern u1="&#xe6;" u2="&#x2122;" k="27" />
<hkern u1="&#xe6;" u2="&#xc6;" k="10" />
<hkern u1="&#xe6;" u2="&#x7d;" k="37" />
<hkern u1="&#xe6;" u2="v" k="14" />
<hkern u1="&#xe6;" u2="]" k="23" />
<hkern u1="&#xe6;" u2="\" k="74" />
<hkern u1="&#xe6;" u2="X" k="8" />
<hkern u1="&#xe6;" u2="V" k="57" />
<hkern u1="&#xe6;" u2="&#x3f;" k="31" />
<hkern u1="&#xe6;" u2="&#x29;" k="18" />
<hkern u1="&#xe7;" u2="&#x7d;" k="14" />
<hkern u1="&#xe7;" u2="]" k="16" />
<hkern u1="&#xe7;" u2="\" k="37" />
<hkern u1="&#xe7;" u2="V" k="25" />
<hkern u1="&#xe7;" u2="&#x3f;" k="8" />
<hkern u1="&#xe8;" u2="&#x2122;" k="27" />
<hkern u1="&#xe8;" u2="&#xc6;" k="10" />
<hkern u1="&#xe8;" u2="&#x7d;" k="37" />
<hkern u1="&#xe8;" u2="v" k="14" />
<hkern u1="&#xe8;" u2="]" k="23" />
<hkern u1="&#xe8;" u2="\" k="74" />
<hkern u1="&#xe8;" u2="X" k="8" />
<hkern u1="&#xe8;" u2="V" k="57" />
<hkern u1="&#xe8;" u2="&#x3f;" k="31" />
<hkern u1="&#xe8;" u2="&#x29;" k="18" />
<hkern u1="&#xe9;" u2="&#x2122;" k="27" />
<hkern u1="&#xe9;" u2="&#xc6;" k="10" />
<hkern u1="&#xe9;" u2="&#x7d;" k="37" />
<hkern u1="&#xe9;" u2="v" k="14" />
<hkern u1="&#xe9;" u2="]" k="23" />
<hkern u1="&#xe9;" u2="\" k="74" />
<hkern u1="&#xe9;" u2="X" k="8" />
<hkern u1="&#xe9;" u2="V" k="57" />
<hkern u1="&#xe9;" u2="&#x3f;" k="31" />
<hkern u1="&#xe9;" u2="&#x29;" k="18" />
<hkern u1="&#xea;" u2="&#x2122;" k="27" />
<hkern u1="&#xea;" u2="&#xc6;" k="10" />
<hkern u1="&#xea;" u2="&#x7d;" k="37" />
<hkern u1="&#xea;" u2="v" k="14" />
<hkern u1="&#xea;" u2="]" k="23" />
<hkern u1="&#xea;" u2="\" k="74" />
<hkern u1="&#xea;" u2="X" k="8" />
<hkern u1="&#xea;" u2="V" k="57" />
<hkern u1="&#xea;" u2="&#x3f;" k="31" />
<hkern u1="&#xea;" u2="&#x29;" k="18" />
<hkern u1="&#xeb;" u2="&#x2122;" k="27" />
<hkern u1="&#xeb;" u2="&#xc6;" k="10" />
<hkern u1="&#xeb;" u2="&#x7d;" k="37" />
<hkern u1="&#xeb;" u2="v" k="14" />
<hkern u1="&#xeb;" u2="]" k="23" />
<hkern u1="&#xeb;" u2="\" k="74" />
<hkern u1="&#xeb;" u2="X" k="8" />
<hkern u1="&#xeb;" u2="V" k="57" />
<hkern u1="&#xeb;" u2="&#x3f;" k="31" />
<hkern u1="&#xeb;" u2="&#x29;" k="18" />
<hkern u1="&#xec;" u2="&#xef;" k="-12" />
<hkern u1="&#xec;" u2="&#xec;" k="-27" />
<hkern u1="&#xed;" u2="&#x2122;" k="-55" />
<hkern u1="&#xed;" u2="&#x201d;" k="-12" />
<hkern u1="&#xed;" u2="&#x2019;" k="-12" />
<hkern u1="&#xed;" u2="&#x165;" k="-20" />
<hkern u1="&#xed;" u2="&#x159;" k="-86" />
<hkern u1="&#xed;" u2="&#x142;" k="-27" />
<hkern u1="&#xed;" u2="&#x13e;" k="-27" />
<hkern u1="&#xed;" u2="&#x13c;" k="-27" />
<hkern u1="&#xed;" u2="&#x13a;" k="-27" />
<hkern u1="&#xed;" u2="&#x137;" k="-37" />
<hkern u1="&#xed;" u2="&#x135;" k="-37" />
<hkern u1="&#xed;" u2="&#x131;" k="-37" />
<hkern u1="&#xed;" u2="&#x12f;" k="-37" />
<hkern u1="&#xed;" u2="&#x12d;" k="-37" />
<hkern u1="&#xed;" u2="&#x12b;" k="-37" />
<hkern u1="&#xed;" u2="&#x129;" k="-37" />
<hkern u1="&#xed;" u2="&#x127;" k="-37" />
<hkern u1="&#xed;" u2="&#x125;" k="-37" />
<hkern u1="&#xed;" u2="&#xef;" k="-12" />
<hkern u1="&#xed;" u2="&#xee;" k="-37" />
<hkern u1="&#xed;" u2="&#xed;" k="-37" />
<hkern u1="&#xed;" u2="&#xec;" k="-27" />
<hkern u1="&#xed;" u2="&#xdf;" k="-37" />
<hkern u1="&#xed;" u2="&#x7d;" k="-94" />
<hkern u1="&#xed;" u2="&#x7c;" k="-33" />
<hkern u1="&#xed;" u2="l" k="-27" />
<hkern u1="&#xed;" u2="k" k="-37" />
<hkern u1="&#xed;" u2="j" k="-37" />
<hkern u1="&#xed;" u2="i" k="-37" />
<hkern u1="&#xed;" u2="h" k="-37" />
<hkern u1="&#xed;" u2="b" k="-37" />
<hkern u1="&#xed;" u2="]" k="-98" />
<hkern u1="&#xed;" u2="\" k="-96" />
<hkern u1="&#xed;" u2="&#x3f;" k="-102" />
<hkern u1="&#xed;" u2="&#x2a;" k="-74" />
<hkern u1="&#xed;" u2="&#x29;" k="-53" />
<hkern u1="&#xed;" u2="&#x27;" k="-51" />
<hkern u1="&#xed;" u2="&#x22;" k="-51" />
<hkern u1="&#xed;" u2="&#x21;" k="-31" />
<hkern u1="&#xee;" u2="&#x2122;" k="-10" />
<hkern u1="&#xee;" u2="&#xef;" k="-12" />
<hkern u1="&#xee;" u2="&#xec;" k="-27" />
<hkern u1="&#xee;" u2="&#x3f;" k="-39" />
<hkern u1="&#xee;" u2="&#x2a;" k="-55" />
<hkern u1="&#xef;" u2="&#x2122;" k="-14" />
<hkern u1="&#xef;" u2="&#xef;" k="-12" />
<hkern u1="&#xef;" u2="&#xec;" k="-27" />
<hkern u1="&#xef;" u2="&#x7d;" k="-41" />
<hkern u1="&#xef;" u2="]" k="-43" />
<hkern u1="&#xef;" u2="\" k="-53" />
<hkern u1="&#xef;" u2="&#x3f;" k="-51" />
<hkern u1="&#xef;" u2="&#x2a;" k="-68" />
<hkern u1="&#xef;" u2="&#x29;" k="-43" />
<hkern u1="&#xef;" u2="&#x27;" k="-8" />
<hkern u1="&#xef;" u2="&#x22;" k="-8" />
<hkern u1="&#xf1;" u2="&#x2122;" k="31" />
<hkern u1="&#xf1;" u2="&#x7d;" k="20" />
<hkern u1="&#xf1;" u2="v" k="10" />
<hkern u1="&#xf1;" u2="]" k="23" />
<hkern u1="&#xf1;" u2="\" k="78" />
<hkern u1="&#xf1;" u2="V" k="57" />
<hkern u1="&#xf1;" u2="&#x3f;" k="35" />
<hkern u1="&#xf1;" u2="&#x2a;" k="12" />
<hkern u1="&#xf1;" u2="&#x29;" k="16" />
<hkern u1="&#xf2;" u2="&#x2122;" k="31" />
<hkern u1="&#xf2;" u2="&#xc6;" k="14" />
<hkern u1="&#xf2;" u2="&#x7d;" k="51" />
<hkern u1="&#xf2;" u2="x" k="23" />
<hkern u1="&#xf2;" u2="v" k="16" />
<hkern u1="&#xf2;" u2="]" k="66" />
<hkern u1="&#xf2;" u2="\" k="80" />
<hkern u1="&#xf2;" u2="X" k="49" />
<hkern u1="&#xf2;" u2="V" k="61" />
<hkern u1="&#xf2;" u2="&#x3f;" k="39" />
<hkern u1="&#xf2;" u2="&#x2a;" k="12" />
<hkern u1="&#xf2;" u2="&#x29;" k="39" />
<hkern u1="&#xf3;" u2="&#x2122;" k="31" />
<hkern u1="&#xf3;" u2="&#xc6;" k="14" />
<hkern u1="&#xf3;" u2="&#x7d;" k="51" />
<hkern u1="&#xf3;" u2="x" k="23" />
<hkern u1="&#xf3;" u2="v" k="16" />
<hkern u1="&#xf3;" u2="]" k="66" />
<hkern u1="&#xf3;" u2="\" k="80" />
<hkern u1="&#xf3;" u2="X" k="49" />
<hkern u1="&#xf3;" u2="V" k="61" />
<hkern u1="&#xf3;" u2="&#x3f;" k="39" />
<hkern u1="&#xf3;" u2="&#x2a;" k="12" />
<hkern u1="&#xf3;" u2="&#x29;" k="39" />
<hkern u1="&#xf4;" u2="&#x2122;" k="31" />
<hkern u1="&#xf4;" u2="&#xc6;" k="14" />
<hkern u1="&#xf4;" u2="&#x7d;" k="51" />
<hkern u1="&#xf4;" u2="x" k="23" />
<hkern u1="&#xf4;" u2="v" k="16" />
<hkern u1="&#xf4;" u2="]" k="66" />
<hkern u1="&#xf4;" u2="\" k="80" />
<hkern u1="&#xf4;" u2="X" k="49" />
<hkern u1="&#xf4;" u2="V" k="61" />
<hkern u1="&#xf4;" u2="&#x3f;" k="39" />
<hkern u1="&#xf4;" u2="&#x2a;" k="12" />
<hkern u1="&#xf4;" u2="&#x29;" k="39" />
<hkern u1="&#xf5;" u2="&#x2122;" k="31" />
<hkern u1="&#xf5;" u2="&#xc6;" k="14" />
<hkern u1="&#xf5;" u2="&#x7d;" k="51" />
<hkern u1="&#xf5;" u2="x" k="23" />
<hkern u1="&#xf5;" u2="v" k="16" />
<hkern u1="&#xf5;" u2="]" k="66" />
<hkern u1="&#xf5;" u2="\" k="80" />
<hkern u1="&#xf5;" u2="X" k="49" />
<hkern u1="&#xf5;" u2="V" k="61" />
<hkern u1="&#xf5;" u2="&#x3f;" k="39" />
<hkern u1="&#xf5;" u2="&#x2a;" k="12" />
<hkern u1="&#xf5;" u2="&#x29;" k="39" />
<hkern u1="&#xf6;" u2="&#x2122;" k="31" />
<hkern u1="&#xf6;" u2="&#xc6;" k="14" />
<hkern u1="&#xf6;" u2="&#x7d;" k="51" />
<hkern u1="&#xf6;" u2="x" k="23" />
<hkern u1="&#xf6;" u2="v" k="16" />
<hkern u1="&#xf6;" u2="]" k="66" />
<hkern u1="&#xf6;" u2="\" k="80" />
<hkern u1="&#xf6;" u2="X" k="49" />
<hkern u1="&#xf6;" u2="V" k="61" />
<hkern u1="&#xf6;" u2="&#x3f;" k="39" />
<hkern u1="&#xf6;" u2="&#x2a;" k="12" />
<hkern u1="&#xf6;" u2="&#x29;" k="39" />
<hkern u1="&#xf8;" u2="&#x2122;" k="31" />
<hkern u1="&#xf8;" u2="&#xc6;" k="14" />
<hkern u1="&#xf8;" u2="&#x7d;" k="51" />
<hkern u1="&#xf8;" u2="x" k="23" />
<hkern u1="&#xf8;" u2="v" k="16" />
<hkern u1="&#xf8;" u2="]" k="66" />
<hkern u1="&#xf8;" u2="\" k="80" />
<hkern u1="&#xf8;" u2="X" k="49" />
<hkern u1="&#xf8;" u2="V" k="61" />
<hkern u1="&#xf8;" u2="&#x3f;" k="39" />
<hkern u1="&#xf8;" u2="&#x2a;" k="12" />
<hkern u1="&#xf8;" u2="&#x29;" k="39" />
<hkern u1="&#xf9;" u2="&#x2122;" k="20" />
<hkern u1="&#xf9;" u2="&#x7d;" k="20" />
<hkern u1="&#xf9;" u2="]" k="23" />
<hkern u1="&#xf9;" u2="\" k="53" />
<hkern u1="&#xf9;" u2="X" k="8" />
<hkern u1="&#xf9;" u2="V" k="47" />
<hkern u1="&#xf9;" u2="&#x3f;" k="10" />
<hkern u1="&#xf9;" u2="&#x29;" k="16" />
<hkern u1="&#xfa;" u2="&#x2122;" k="20" />
<hkern u1="&#xfa;" u2="&#x7d;" k="20" />
<hkern u1="&#xfa;" u2="]" k="23" />
<hkern u1="&#xfa;" u2="\" k="53" />
<hkern u1="&#xfa;" u2="X" k="8" />
<hkern u1="&#xfa;" u2="V" k="47" />
<hkern u1="&#xfa;" u2="&#x3f;" k="10" />
<hkern u1="&#xfa;" u2="&#x29;" k="16" />
<hkern u1="&#xfb;" u2="&#x2122;" k="20" />
<hkern u1="&#xfb;" u2="&#x7d;" k="20" />
<hkern u1="&#xfb;" u2="]" k="23" />
<hkern u1="&#xfb;" u2="\" k="53" />
<hkern u1="&#xfb;" u2="X" k="8" />
<hkern u1="&#xfb;" u2="V" k="47" />
<hkern u1="&#xfb;" u2="&#x3f;" k="10" />
<hkern u1="&#xfb;" u2="&#x29;" k="16" />
<hkern u1="&#xfc;" u2="&#x2122;" k="20" />
<hkern u1="&#xfc;" u2="&#x7d;" k="20" />
<hkern u1="&#xfc;" u2="]" k="23" />
<hkern u1="&#xfc;" u2="\" k="53" />
<hkern u1="&#xfc;" u2="X" k="8" />
<hkern u1="&#xfc;" u2="V" k="47" />
<hkern u1="&#xfc;" u2="&#x3f;" k="10" />
<hkern u1="&#xfc;" u2="&#x29;" k="16" />
<hkern u1="&#xfd;" u2="&#xc6;" k="39" />
<hkern u1="&#xfd;" u2="&#x7d;" k="35" />
<hkern u1="&#xfd;" u2="]" k="47" />
<hkern u1="&#xfd;" u2="\" k="27" />
<hkern u1="&#xfd;" u2="X" k="49" />
<hkern u1="&#xfd;" u2="V" k="14" />
<hkern u1="&#xfd;" u2="&#x3f;" k="8" />
<hkern u1="&#xfd;" u2="&#x2f;" k="41" />
<hkern u1="&#xff;" u2="&#xc6;" k="39" />
<hkern u1="&#xff;" u2="&#x7d;" k="35" />
<hkern u1="&#xff;" u2="]" k="47" />
<hkern u1="&#xff;" u2="\" k="27" />
<hkern u1="&#xff;" u2="X" k="49" />
<hkern u1="&#xff;" u2="V" k="14" />
<hkern u1="&#xff;" u2="&#x3f;" k="8" />
<hkern u1="&#xff;" u2="&#x2f;" k="41" />
<hkern u1="&#x100;" u2="&#x2122;" k="72" />
<hkern u1="&#x100;" u2="&#xae;" k="43" />
<hkern u1="&#x100;" u2="&#x7d;" k="18" />
<hkern u1="&#x100;" u2="v" k="33" />
<hkern u1="&#x100;" u2="f" k="16" />
<hkern u1="&#x100;" u2="]" k="20" />
<hkern u1="&#x100;" u2="\" k="90" />
<hkern u1="&#x100;" u2="V" k="57" />
<hkern u1="&#x100;" u2="&#x3f;" k="39" />
<hkern u1="&#x100;" u2="&#x2a;" k="63" />
<hkern u1="&#x101;" u2="&#x2122;" k="29" />
<hkern u1="&#x101;" u2="&#x7d;" k="8" />
<hkern u1="&#x101;" u2="v" k="10" />
<hkern u1="&#x101;" u2="]" k="10" />
<hkern u1="&#x101;" u2="\" k="82" />
<hkern u1="&#x101;" u2="V" k="55" />
<hkern u1="&#x101;" u2="&#x3f;" k="29" />
<hkern u1="&#x101;" u2="&#x2a;" k="12" />
<hkern u1="&#x102;" u2="&#x2122;" k="72" />
<hkern u1="&#x102;" u2="&#xae;" k="43" />
<hkern u1="&#x102;" u2="&#x7d;" k="18" />
<hkern u1="&#x102;" u2="v" k="33" />
<hkern u1="&#x102;" u2="f" k="16" />
<hkern u1="&#x102;" u2="]" k="20" />
<hkern u1="&#x102;" u2="\" k="90" />
<hkern u1="&#x102;" u2="V" k="57" />
<hkern u1="&#x102;" u2="&#x3f;" k="39" />
<hkern u1="&#x102;" u2="&#x2a;" k="63" />
<hkern u1="&#x103;" u2="&#x2122;" k="29" />
<hkern u1="&#x103;" u2="&#x7d;" k="8" />
<hkern u1="&#x103;" u2="v" k="10" />
<hkern u1="&#x103;" u2="]" k="10" />
<hkern u1="&#x103;" u2="\" k="82" />
<hkern u1="&#x103;" u2="V" k="55" />
<hkern u1="&#x103;" u2="&#x3f;" k="29" />
<hkern u1="&#x103;" u2="&#x2a;" k="12" />
<hkern u1="&#x104;" u2="&#x2122;" k="72" />
<hkern u1="&#x104;" u2="&#x201e;" k="-23" />
<hkern u1="&#x104;" u2="&#xae;" k="43" />
<hkern u1="&#x104;" u2="&#x7d;" k="18" />
<hkern u1="&#x104;" u2="v" k="33" />
<hkern u1="&#x104;" u2="j" k="-109" />
<hkern u1="&#x104;" u2="f" k="16" />
<hkern u1="&#x104;" u2="]" k="20" />
<hkern u1="&#x104;" u2="\" k="90" />
<hkern u1="&#x104;" u2="V" k="57" />
<hkern u1="&#x104;" u2="&#x3f;" k="39" />
<hkern u1="&#x104;" u2="&#x2a;" k="63" />
<hkern u1="&#x105;" u2="&#x2122;" k="29" />
<hkern u1="&#x105;" u2="&#x7d;" k="8" />
<hkern u1="&#x105;" u2="v" k="10" />
<hkern u1="&#x105;" u2="j" k="-63" />
<hkern u1="&#x105;" u2="]" k="10" />
<hkern u1="&#x105;" u2="\" k="82" />
<hkern u1="&#x105;" u2="V" k="55" />
<hkern u1="&#x105;" u2="&#x3f;" k="29" />
<hkern u1="&#x105;" u2="&#x2a;" k="12" />
<hkern u1="&#x106;" u2="&#x135;" k="-33" />
<hkern u1="&#x106;" u2="&#x12d;" k="-25" />
<hkern u1="&#x106;" u2="&#x12b;" k="-8" />
<hkern u1="&#x106;" u2="&#x129;" k="-53" />
<hkern u1="&#x106;" u2="&#xef;" k="-53" />
<hkern u1="&#x106;" u2="&#xee;" k="-43" />
<hkern u1="&#x106;" u2="&#xec;" k="-80" />
<hkern u1="&#x106;" u2="&#xae;" k="10" />
<hkern u1="&#x106;" u2="v" k="20" />
<hkern u1="&#x106;" u2="f" k="10" />
<hkern u1="&#x107;" u2="&#x7d;" k="14" />
<hkern u1="&#x107;" u2="]" k="16" />
<hkern u1="&#x107;" u2="\" k="37" />
<hkern u1="&#x107;" u2="V" k="25" />
<hkern u1="&#x107;" u2="&#x3f;" k="8" />
<hkern u1="&#x108;" u2="&#x135;" k="-33" />
<hkern u1="&#x108;" u2="&#x12d;" k="-25" />
<hkern u1="&#x108;" u2="&#x12b;" k="-8" />
<hkern u1="&#x108;" u2="&#x129;" k="-53" />
<hkern u1="&#x108;" u2="&#xef;" k="-53" />
<hkern u1="&#x108;" u2="&#xee;" k="-43" />
<hkern u1="&#x108;" u2="&#xec;" k="-80" />
<hkern u1="&#x108;" u2="&#xae;" k="10" />
<hkern u1="&#x108;" u2="v" k="20" />
<hkern u1="&#x108;" u2="f" k="10" />
<hkern u1="&#x109;" u2="&#x7d;" k="14" />
<hkern u1="&#x109;" u2="]" k="16" />
<hkern u1="&#x109;" u2="\" k="37" />
<hkern u1="&#x109;" u2="V" k="25" />
<hkern u1="&#x109;" u2="&#x3f;" k="8" />
<hkern u1="&#x10a;" u2="&#x135;" k="-33" />
<hkern u1="&#x10a;" u2="&#x12d;" k="-25" />
<hkern u1="&#x10a;" u2="&#x12b;" k="-8" />
<hkern u1="&#x10a;" u2="&#x129;" k="-53" />
<hkern u1="&#x10a;" u2="&#xef;" k="-53" />
<hkern u1="&#x10a;" u2="&#xee;" k="-43" />
<hkern u1="&#x10a;" u2="&#xec;" k="-80" />
<hkern u1="&#x10a;" u2="&#xae;" k="10" />
<hkern u1="&#x10a;" u2="v" k="20" />
<hkern u1="&#x10a;" u2="f" k="10" />
<hkern u1="&#x10b;" u2="&#x7d;" k="14" />
<hkern u1="&#x10b;" u2="]" k="16" />
<hkern u1="&#x10b;" u2="\" k="37" />
<hkern u1="&#x10b;" u2="V" k="25" />
<hkern u1="&#x10b;" u2="&#x3f;" k="8" />
<hkern u1="&#x10c;" u2="&#x135;" k="-33" />
<hkern u1="&#x10c;" u2="&#x12d;" k="-25" />
<hkern u1="&#x10c;" u2="&#x12b;" k="-8" />
<hkern u1="&#x10c;" u2="&#x129;" k="-53" />
<hkern u1="&#x10c;" u2="&#xef;" k="-53" />
<hkern u1="&#x10c;" u2="&#xee;" k="-43" />
<hkern u1="&#x10c;" u2="&#xec;" k="-80" />
<hkern u1="&#x10c;" u2="&#xae;" k="10" />
<hkern u1="&#x10c;" u2="v" k="20" />
<hkern u1="&#x10c;" u2="f" k="10" />
<hkern u1="&#x10d;" u2="&#x7d;" k="14" />
<hkern u1="&#x10d;" u2="]" k="16" />
<hkern u1="&#x10d;" u2="\" k="37" />
<hkern u1="&#x10d;" u2="V" k="25" />
<hkern u1="&#x10d;" u2="&#x3f;" k="8" />
<hkern u1="&#x10e;" u2="&#xc6;" k="31" />
<hkern u1="&#x10e;" u2="&#x7d;" k="37" />
<hkern u1="&#x10e;" u2="]" k="47" />
<hkern u1="&#x10e;" u2="\" k="27" />
<hkern u1="&#x10e;" u2="X" k="39" />
<hkern u1="&#x10e;" u2="V" k="23" />
<hkern u1="&#x10e;" u2="&#x3f;" k="8" />
<hkern u1="&#x10e;" u2="&#x2f;" k="23" />
<hkern u1="&#x10e;" u2="&#x29;" k="25" />
<hkern u1="&#x10f;" u2="&#x2122;" k="-31" />
<hkern u1="&#x10f;" u2="&#x17e;" k="-37" />
<hkern u1="&#x10f;" u2="&#x161;" k="-27" />
<hkern u1="&#x10f;" u2="&#x10d;" k="-25" />
<hkern u1="&#x10f;" u2="&#xe1;" k="-14" />
<hkern u1="&#x10f;" u2="&#xdf;" k="-45" />
<hkern u1="&#x10f;" u2="&#x7d;" k="-72" />
<hkern u1="&#x10f;" u2="&#x7c;" k="-20" />
<hkern u1="&#x10f;" u2="x" k="-41" />
<hkern u1="&#x10f;" u2="v" k="-37" />
<hkern u1="&#x10f;" u2="f" k="-12" />
<hkern u1="&#x10f;" u2="]" k="-74" />
<hkern u1="&#x10f;" u2="\" k="-100" />
<hkern u1="&#x10f;" u2="&#x3f;" k="-94" />
<hkern u1="&#x10f;" u2="&#x2f;" k="53" />
<hkern u1="&#x10f;" u2="&#x2a;" k="-90" />
<hkern u1="&#x10f;" u2="&#x29;" k="-55" />
<hkern u1="&#x10f;" u2="&#x21;" k="-20" />
<hkern u1="&#x110;" u2="&#xc6;" k="31" />
<hkern u1="&#x110;" u2="&#x7d;" k="37" />
<hkern u1="&#x110;" u2="]" k="47" />
<hkern u1="&#x110;" u2="\" k="27" />
<hkern u1="&#x110;" u2="X" k="39" />
<hkern u1="&#x110;" u2="V" k="23" />
<hkern u1="&#x110;" u2="&#x3f;" k="8" />
<hkern u1="&#x110;" u2="&#x2f;" k="23" />
<hkern u1="&#x110;" u2="&#x29;" k="25" />
<hkern u1="&#x111;" u2="&#xef;" k="-12" />
<hkern u1="&#x111;" u2="&#xec;" k="-27" />
<hkern u1="&#x112;" u2="&#x135;" k="-33" />
<hkern u1="&#x112;" u2="&#x12d;" k="-16" />
<hkern u1="&#x112;" u2="&#x12b;" k="-6" />
<hkern u1="&#x112;" u2="&#x129;" k="-47" />
<hkern u1="&#x112;" u2="&#xef;" k="-49" />
<hkern u1="&#x112;" u2="&#xee;" k="-43" />
<hkern u1="&#x112;" u2="&#xec;" k="-80" />
<hkern u1="&#x112;" u2="v" k="10" />
<hkern u1="&#x113;" u2="&#x2122;" k="27" />
<hkern u1="&#x113;" u2="&#xc6;" k="10" />
<hkern u1="&#x113;" u2="&#x7d;" k="37" />
<hkern u1="&#x113;" u2="v" k="14" />
<hkern u1="&#x113;" u2="]" k="23" />
<hkern u1="&#x113;" u2="\" k="74" />
<hkern u1="&#x113;" u2="X" k="8" />
<hkern u1="&#x113;" u2="V" k="57" />
<hkern u1="&#x113;" u2="&#x3f;" k="31" />
<hkern u1="&#x113;" u2="&#x29;" k="18" />
<hkern u1="&#x114;" u2="&#x135;" k="-33" />
<hkern u1="&#x114;" u2="&#x12d;" k="-16" />
<hkern u1="&#x114;" u2="&#x12b;" k="-6" />
<hkern u1="&#x114;" u2="&#x129;" k="-47" />
<hkern u1="&#x114;" u2="&#xef;" k="-49" />
<hkern u1="&#x114;" u2="&#xee;" k="-43" />
<hkern u1="&#x114;" u2="&#xec;" k="-80" />
<hkern u1="&#x114;" u2="v" k="10" />
<hkern u1="&#x115;" u2="&#x2122;" k="27" />
<hkern u1="&#x115;" u2="&#xc6;" k="10" />
<hkern u1="&#x115;" u2="&#x7d;" k="37" />
<hkern u1="&#x115;" u2="v" k="14" />
<hkern u1="&#x115;" u2="]" k="23" />
<hkern u1="&#x115;" u2="\" k="74" />
<hkern u1="&#x115;" u2="X" k="8" />
<hkern u1="&#x115;" u2="V" k="57" />
<hkern u1="&#x115;" u2="&#x3f;" k="31" />
<hkern u1="&#x115;" u2="&#x29;" k="18" />
<hkern u1="&#x116;" u2="&#x135;" k="-33" />
<hkern u1="&#x116;" u2="&#x12d;" k="-16" />
<hkern u1="&#x116;" u2="&#x12b;" k="-6" />
<hkern u1="&#x116;" u2="&#x129;" k="-47" />
<hkern u1="&#x116;" u2="&#xef;" k="-49" />
<hkern u1="&#x116;" u2="&#xee;" k="-43" />
<hkern u1="&#x116;" u2="&#xec;" k="-80" />
<hkern u1="&#x116;" u2="v" k="10" />
<hkern u1="&#x117;" u2="&#x2122;" k="27" />
<hkern u1="&#x117;" u2="&#xc6;" k="10" />
<hkern u1="&#x117;" u2="&#x7d;" k="37" />
<hkern u1="&#x117;" u2="v" k="14" />
<hkern u1="&#x117;" u2="]" k="23" />
<hkern u1="&#x117;" u2="\" k="74" />
<hkern u1="&#x117;" u2="X" k="8" />
<hkern u1="&#x117;" u2="V" k="57" />
<hkern u1="&#x117;" u2="&#x3f;" k="31" />
<hkern u1="&#x117;" u2="&#x29;" k="18" />
<hkern u1="&#x118;" u2="&#x135;" k="-33" />
<hkern u1="&#x118;" u2="&#x12d;" k="-16" />
<hkern u1="&#x118;" u2="&#x12b;" k="-6" />
<hkern u1="&#x118;" u2="&#x129;" k="-47" />
<hkern u1="&#x118;" u2="&#xef;" k="-49" />
<hkern u1="&#x118;" u2="&#xee;" k="-43" />
<hkern u1="&#x118;" u2="&#xec;" k="-80" />
<hkern u1="&#x118;" u2="v" k="10" />
<hkern u1="&#x118;" u2="j" k="-33" />
<hkern u1="&#x119;" u2="&#x2122;" k="27" />
<hkern u1="&#x119;" u2="&#xc6;" k="10" />
<hkern u1="&#x119;" u2="&#x7d;" k="37" />
<hkern u1="&#x119;" u2="v" k="14" />
<hkern u1="&#x119;" u2="]" k="23" />
<hkern u1="&#x119;" u2="\" k="74" />
<hkern u1="&#x119;" u2="X" k="8" />
<hkern u1="&#x119;" u2="V" k="57" />
<hkern u1="&#x119;" u2="&#x3f;" k="31" />
<hkern u1="&#x119;" u2="&#x29;" k="18" />
<hkern u1="&#x11a;" u2="&#x135;" k="-33" />
<hkern u1="&#x11a;" u2="&#x12d;" k="-16" />
<hkern u1="&#x11a;" u2="&#x12b;" k="-6" />
<hkern u1="&#x11a;" u2="&#x129;" k="-47" />
<hkern u1="&#x11a;" u2="&#xef;" k="-49" />
<hkern u1="&#x11a;" u2="&#xee;" k="-43" />
<hkern u1="&#x11a;" u2="&#xec;" k="-80" />
<hkern u1="&#x11a;" u2="v" k="10" />
<hkern u1="&#x11b;" u2="&#x2122;" k="27" />
<hkern u1="&#x11b;" u2="&#xc6;" k="10" />
<hkern u1="&#x11b;" u2="&#x7d;" k="37" />
<hkern u1="&#x11b;" u2="v" k="14" />
<hkern u1="&#x11b;" u2="]" k="23" />
<hkern u1="&#x11b;" u2="\" k="74" />
<hkern u1="&#x11b;" u2="X" k="8" />
<hkern u1="&#x11b;" u2="V" k="57" />
<hkern u1="&#x11b;" u2="&#x3f;" k="31" />
<hkern u1="&#x11b;" u2="&#x29;" k="18" />
<hkern u1="&#x11c;" u2="&#xef;" k="-23" />
<hkern u1="&#x11c;" u2="&#xee;" k="-14" />
<hkern u1="&#x11c;" u2="&#xec;" k="-43" />
<hkern u1="&#x11c;" u2="v" k="12" />
<hkern u1="&#x11c;" u2="f" k="12" />
<hkern u1="&#x11c;" u2="\" k="14" />
<hkern u1="&#x11c;" u2="V" k="18" />
<hkern u1="&#x11d;" u2="&#x135;" k="-45" />
<hkern u1="&#x11d;" u2="j" k="-45" />
<hkern u1="&#x11d;" u2="\" k="23" />
<hkern u1="&#x11d;" u2="V" k="8" />
<hkern u1="&#x11e;" u2="&#xef;" k="-23" />
<hkern u1="&#x11e;" u2="&#xee;" k="-14" />
<hkern u1="&#x11e;" u2="&#xec;" k="-43" />
<hkern u1="&#x11e;" u2="v" k="12" />
<hkern u1="&#x11e;" u2="f" k="12" />
<hkern u1="&#x11e;" u2="\" k="14" />
<hkern u1="&#x11e;" u2="V" k="18" />
<hkern u1="&#x11f;" u2="&#x135;" k="-45" />
<hkern u1="&#x11f;" u2="j" k="-45" />
<hkern u1="&#x11f;" u2="\" k="23" />
<hkern u1="&#x11f;" u2="V" k="8" />
<hkern u1="&#x120;" u2="&#xef;" k="-23" />
<hkern u1="&#x120;" u2="&#xee;" k="-14" />
<hkern u1="&#x120;" u2="&#xec;" k="-43" />
<hkern u1="&#x120;" u2="v" k="12" />
<hkern u1="&#x120;" u2="f" k="12" />
<hkern u1="&#x120;" u2="\" k="14" />
<hkern u1="&#x120;" u2="V" k="18" />
<hkern u1="&#x121;" u2="&#x135;" k="-45" />
<hkern u1="&#x121;" u2="j" k="-45" />
<hkern u1="&#x121;" u2="\" k="23" />
<hkern u1="&#x121;" u2="V" k="8" />
<hkern u1="&#x122;" u2="&#xef;" k="-23" />
<hkern u1="&#x122;" u2="&#xee;" k="-14" />
<hkern u1="&#x122;" u2="&#xec;" k="-43" />
<hkern u1="&#x122;" u2="v" k="12" />
<hkern u1="&#x122;" u2="f" k="12" />
<hkern u1="&#x122;" u2="\" k="14" />
<hkern u1="&#x122;" u2="V" k="18" />
<hkern u1="&#x123;" u2="&#x135;" k="-45" />
<hkern u1="&#x123;" u2="j" k="-45" />
<hkern u1="&#x123;" u2="\" k="23" />
<hkern u1="&#x123;" u2="V" k="8" />
<hkern u1="&#x124;" u2="&#xec;" k="-12" />
<hkern u1="&#x124;" u2="f" k="8" />
<hkern u1="&#x125;" u2="&#x2122;" k="31" />
<hkern u1="&#x125;" u2="&#x7d;" k="20" />
<hkern u1="&#x125;" u2="v" k="10" />
<hkern u1="&#x125;" u2="]" k="23" />
<hkern u1="&#x125;" u2="\" k="78" />
<hkern u1="&#x125;" u2="V" k="57" />
<hkern u1="&#x125;" u2="&#x3f;" k="35" />
<hkern u1="&#x125;" u2="&#x2a;" k="12" />
<hkern u1="&#x125;" u2="&#x29;" k="16" />
<hkern u1="&#x126;" u2="&#xec;" k="-12" />
<hkern u1="&#x126;" u2="f" k="8" />
<hkern u1="&#x126;" u2="&#x2a;" k="-33" />
<hkern u1="&#x127;" u2="&#x2122;" k="31" />
<hkern u1="&#x127;" u2="&#x7d;" k="20" />
<hkern u1="&#x127;" u2="v" k="10" />
<hkern u1="&#x127;" u2="]" k="23" />
<hkern u1="&#x127;" u2="\" k="78" />
<hkern u1="&#x127;" u2="V" k="57" />
<hkern u1="&#x127;" u2="&#x3f;" k="35" />
<hkern u1="&#x127;" u2="&#x2a;" k="12" />
<hkern u1="&#x127;" u2="&#x29;" k="16" />
<hkern u1="&#x128;" u2="&#xec;" k="-12" />
<hkern u1="&#x128;" u2="f" k="8" />
<hkern u1="&#x129;" u2="&#x2122;" k="-20" />
<hkern u1="&#x129;" u2="&#xef;" k="-12" />
<hkern u1="&#x129;" u2="&#xec;" k="-27" />
<hkern u1="&#x129;" u2="&#x7d;" k="-55" />
<hkern u1="&#x129;" u2="]" k="-55" />
<hkern u1="&#x129;" u2="\" k="-72" />
<hkern u1="&#x129;" u2="&#x3f;" k="-80" />
<hkern u1="&#x129;" u2="&#x2a;" k="-53" />
<hkern u1="&#x129;" u2="&#x29;" k="-20" />
<hkern u1="&#x129;" u2="&#x27;" k="-16" />
<hkern u1="&#x129;" u2="&#x22;" k="-16" />
<hkern u1="&#x12a;" u2="&#xec;" k="-12" />
<hkern u1="&#x12a;" u2="f" k="8" />
<hkern u1="&#x12b;" u2="&#xef;" k="-12" />
<hkern u1="&#x12b;" u2="&#xec;" k="-27" />
<hkern u1="&#x12b;" u2="\" k="-27" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-29" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-33" />
<hkern u1="&#x12c;" u2="&#xec;" k="-12" />
<hkern u1="&#x12c;" u2="f" k="8" />
<hkern u1="&#x12d;" u2="&#xef;" k="-12" />
<hkern u1="&#x12d;" u2="&#xec;" k="-27" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-51" />
<hkern u1="&#x12d;" u2="]" k="-51" />
<hkern u1="&#x12d;" u2="\" k="-29" />
<hkern u1="&#x12d;" u2="&#x3f;" k="-29" />
<hkern u1="&#x12d;" u2="&#x2a;" k="-20" />
<hkern u1="&#x12d;" u2="&#x29;" k="-45" />
<hkern u1="&#x12e;" u2="&#xec;" k="-12" />
<hkern u1="&#x12e;" u2="j" k="-10" />
<hkern u1="&#x12e;" u2="f" k="8" />
<hkern u1="&#x12f;" u2="&#xef;" k="-12" />
<hkern u1="&#x12f;" u2="&#xec;" k="-27" />
<hkern u1="&#x12f;" u2="j" k="-16" />
<hkern u1="&#x130;" u2="&#xec;" k="-12" />
<hkern u1="&#x130;" u2="f" k="8" />
<hkern u1="&#x131;" u2="&#xef;" k="-12" />
<hkern u1="&#x131;" u2="&#xec;" k="-27" />
<hkern u1="&#x134;" u2="&#xec;" k="-16" />
<hkern u1="&#x134;" u2="f" k="8" />
<hkern u1="&#x135;" u2="&#x2122;" k="-20" />
<hkern u1="&#x135;" u2="&#xef;" k="-12" />
<hkern u1="&#x135;" u2="&#xec;" k="-27" />
<hkern u1="&#x135;" u2="&#x3f;" k="-49" />
<hkern u1="&#x135;" u2="&#x2a;" k="-55" />
<hkern u1="&#x135;" u2="&#x27;" k="-12" />
<hkern u1="&#x135;" u2="&#x22;" k="-12" />
<hkern u1="&#x136;" u2="&#x12d;" k="-55" />
<hkern u1="&#x136;" u2="&#x12b;" k="-31" />
<hkern u1="&#x136;" u2="&#x129;" k="-68" />
<hkern u1="&#x136;" u2="&#xef;" k="-84" />
<hkern u1="&#x136;" u2="&#xee;" k="-8" />
<hkern u1="&#x136;" u2="&#xec;" k="-98" />
<hkern u1="&#x136;" u2="&#xae;" k="10" />
<hkern u1="&#x136;" u2="v" k="41" />
<hkern u1="&#x136;" u2="f" k="16" />
<hkern u1="&#x137;" u2="&#x7d;" k="12" />
<hkern u1="&#x137;" u2="]" k="14" />
<hkern u1="&#x137;" u2="\" k="27" />
<hkern u1="&#x137;" u2="V" k="16" />
<hkern u1="&#x137;" u2="&#x3f;" k="6" />
<hkern u1="&#x139;" u2="&#x2122;" k="168" />
<hkern u1="&#x139;" u2="&#xae;" k="145" />
<hkern u1="&#x139;" u2="&#x7d;" k="10" />
<hkern u1="&#x139;" u2="v" k="78" />
<hkern u1="&#x139;" u2="f" k="12" />
<hkern u1="&#x139;" u2="]" k="12" />
<hkern u1="&#x139;" u2="\" k="145" />
<hkern u1="&#x139;" u2="V" k="117" />
<hkern u1="&#x139;" u2="&#x3f;" k="12" />
<hkern u1="&#x139;" u2="&#x2a;" k="166" />
<hkern u1="&#x13a;" u2="&#xec;" k="-18" />
<hkern u1="&#x13b;" u2="&#x2122;" k="168" />
<hkern u1="&#x13b;" u2="&#xae;" k="145" />
<hkern u1="&#x13b;" u2="&#x7d;" k="10" />
<hkern u1="&#x13b;" u2="v" k="78" />
<hkern u1="&#x13b;" u2="f" k="12" />
<hkern u1="&#x13b;" u2="]" k="12" />
<hkern u1="&#x13b;" u2="\" k="145" />
<hkern u1="&#x13b;" u2="V" k="117" />
<hkern u1="&#x13b;" u2="&#x3f;" k="12" />
<hkern u1="&#x13b;" u2="&#x2a;" k="166" />
<hkern u1="&#x13c;" u2="&#xec;" k="-18" />
<hkern u1="&#x13d;" u2="&#x2122;" k="141" />
<hkern u1="&#x13d;" u2="&#x201d;" k="127" />
<hkern u1="&#x13d;" u2="&#x201c;" k="121" />
<hkern u1="&#x13d;" u2="&#x2019;" k="127" />
<hkern u1="&#x13d;" u2="&#x2018;" k="121" />
<hkern u1="&#x13d;" u2="&#x21a;" k="59" />
<hkern u1="&#x13d;" u2="&#x178;" k="41" />
<hkern u1="&#x13d;" u2="&#x176;" k="41" />
<hkern u1="&#x13d;" u2="&#x174;" k="66" />
<hkern u1="&#x13d;" u2="&#x166;" k="59" />
<hkern u1="&#x13d;" u2="&#x164;" k="59" />
<hkern u1="&#x13d;" u2="&#xdd;" k="41" />
<hkern u1="&#x13d;" u2="&#xae;" k="133" />
<hkern u1="&#x13d;" u2="&#x7d;" k="10" />
<hkern u1="&#x13d;" u2="v" k="78" />
<hkern u1="&#x13d;" u2="f" k="12" />
<hkern u1="&#x13d;" u2="]" k="12" />
<hkern u1="&#x13d;" u2="\" k="98" />
<hkern u1="&#x13d;" u2="Y" k="41" />
<hkern u1="&#x13d;" u2="W" k="66" />
<hkern u1="&#x13d;" u2="V" k="66" />
<hkern u1="&#x13d;" u2="T" k="59" />
<hkern u1="&#x13d;" u2="&#x3f;" k="12" />
<hkern u1="&#x13d;" u2="&#x2a;" k="70" />
<hkern u1="&#x13d;" u2="&#x27;" k="152" />
<hkern u1="&#x13d;" u2="&#x22;" k="152" />
<hkern u1="&#x13e;" u2="&#x2122;" k="-31" />
<hkern u1="&#x13e;" u2="&#x17e;" k="-37" />
<hkern u1="&#x13e;" u2="&#x161;" k="-27" />
<hkern u1="&#x13e;" u2="&#x10d;" k="-25" />
<hkern u1="&#x13e;" u2="&#xe1;" k="-14" />
<hkern u1="&#x13e;" u2="&#xdf;" k="-45" />
<hkern u1="&#x13e;" u2="&#x7d;" k="-72" />
<hkern u1="&#x13e;" u2="&#x7c;" k="-20" />
<hkern u1="&#x13e;" u2="x" k="-41" />
<hkern u1="&#x13e;" u2="v" k="-37" />
<hkern u1="&#x13e;" u2="f" k="-12" />
<hkern u1="&#x13e;" u2="]" k="-74" />
<hkern u1="&#x13e;" u2="\" k="-100" />
<hkern u1="&#x13e;" u2="&#x3f;" k="-94" />
<hkern u1="&#x13e;" u2="&#x2f;" k="53" />
<hkern u1="&#x13e;" u2="&#x2a;" k="-90" />
<hkern u1="&#x13e;" u2="&#x29;" k="-55" />
<hkern u1="&#x13e;" u2="&#x21;" k="-20" />
<hkern u1="&#x141;" u2="&#x2122;" k="168" />
<hkern u1="&#x141;" u2="&#xae;" k="145" />
<hkern u1="&#x141;" u2="&#x7d;" k="10" />
<hkern u1="&#x141;" u2="v" k="78" />
<hkern u1="&#x141;" u2="f" k="12" />
<hkern u1="&#x141;" u2="]" k="12" />
<hkern u1="&#x141;" u2="\" k="145" />
<hkern u1="&#x141;" u2="V" k="117" />
<hkern u1="&#x141;" u2="&#x3f;" k="12" />
<hkern u1="&#x141;" u2="&#x2a;" k="166" />
<hkern u1="&#x142;" u2="&#xec;" k="-18" />
<hkern u1="&#x143;" u2="&#xec;" k="-12" />
<hkern u1="&#x143;" u2="f" k="8" />
<hkern u1="&#x144;" u2="&#x2122;" k="31" />
<hkern u1="&#x144;" u2="&#x7d;" k="20" />
<hkern u1="&#x144;" u2="v" k="10" />
<hkern u1="&#x144;" u2="]" k="23" />
<hkern u1="&#x144;" u2="\" k="78" />
<hkern u1="&#x144;" u2="V" k="57" />
<hkern u1="&#x144;" u2="&#x3f;" k="35" />
<hkern u1="&#x144;" u2="&#x2a;" k="12" />
<hkern u1="&#x144;" u2="&#x29;" k="16" />
<hkern u1="&#x145;" u2="&#xec;" k="-12" />
<hkern u1="&#x145;" u2="f" k="8" />
<hkern u1="&#x146;" u2="&#x2122;" k="31" />
<hkern u1="&#x146;" u2="&#x7d;" k="20" />
<hkern u1="&#x146;" u2="v" k="10" />
<hkern u1="&#x146;" u2="]" k="23" />
<hkern u1="&#x146;" u2="\" k="78" />
<hkern u1="&#x146;" u2="V" k="57" />
<hkern u1="&#x146;" u2="&#x3f;" k="35" />
<hkern u1="&#x146;" u2="&#x2a;" k="12" />
<hkern u1="&#x146;" u2="&#x29;" k="16" />
<hkern u1="&#x147;" u2="&#xec;" k="-12" />
<hkern u1="&#x147;" u2="f" k="8" />
<hkern u1="&#x148;" u2="&#x2122;" k="31" />
<hkern u1="&#x148;" u2="&#x7d;" k="20" />
<hkern u1="&#x148;" u2="v" k="10" />
<hkern u1="&#x148;" u2="]" k="23" />
<hkern u1="&#x148;" u2="\" k="78" />
<hkern u1="&#x148;" u2="V" k="57" />
<hkern u1="&#x148;" u2="&#x3f;" k="35" />
<hkern u1="&#x148;" u2="&#x2a;" k="12" />
<hkern u1="&#x148;" u2="&#x29;" k="16" />
<hkern u1="&#x14a;" u2="&#xec;" k="-12" />
<hkern u1="&#x14a;" u2="f" k="8" />
<hkern u1="&#x14b;" u2="&#x2122;" k="31" />
<hkern u1="&#x14b;" u2="&#x7d;" k="20" />
<hkern u1="&#x14b;" u2="v" k="10" />
<hkern u1="&#x14b;" u2="]" k="23" />
<hkern u1="&#x14b;" u2="\" k="78" />
<hkern u1="&#x14b;" u2="V" k="57" />
<hkern u1="&#x14b;" u2="&#x3f;" k="35" />
<hkern u1="&#x14b;" u2="&#x2a;" k="12" />
<hkern u1="&#x14b;" u2="&#x29;" k="16" />
<hkern u1="&#x14c;" u2="&#xc6;" k="29" />
<hkern u1="&#x14c;" u2="&#x7d;" k="35" />
<hkern u1="&#x14c;" u2="]" k="49" />
<hkern u1="&#x14c;" u2="\" k="29" />
<hkern u1="&#x14c;" u2="X" k="37" />
<hkern u1="&#x14c;" u2="V" k="23" />
<hkern u1="&#x14c;" u2="&#x3f;" k="6" />
<hkern u1="&#x14c;" u2="&#x2f;" k="20" />
<hkern u1="&#x14c;" u2="&#x29;" k="20" />
<hkern u1="&#x14d;" u2="&#x2122;" k="31" />
<hkern u1="&#x14d;" u2="&#xc6;" k="14" />
<hkern u1="&#x14d;" u2="&#x7d;" k="51" />
<hkern u1="&#x14d;" u2="x" k="23" />
<hkern u1="&#x14d;" u2="v" k="16" />
<hkern u1="&#x14d;" u2="]" k="66" />
<hkern u1="&#x14d;" u2="\" k="80" />
<hkern u1="&#x14d;" u2="X" k="49" />
<hkern u1="&#x14d;" u2="V" k="61" />
<hkern u1="&#x14d;" u2="&#x3f;" k="39" />
<hkern u1="&#x14d;" u2="&#x2a;" k="12" />
<hkern u1="&#x14d;" u2="&#x29;" k="39" />
<hkern u1="&#x14e;" u2="&#xc6;" k="29" />
<hkern u1="&#x14e;" u2="&#x7d;" k="35" />
<hkern u1="&#x14e;" u2="]" k="49" />
<hkern u1="&#x14e;" u2="\" k="29" />
<hkern u1="&#x14e;" u2="X" k="37" />
<hkern u1="&#x14e;" u2="V" k="23" />
<hkern u1="&#x14e;" u2="&#x3f;" k="6" />
<hkern u1="&#x14e;" u2="&#x2f;" k="20" />
<hkern u1="&#x14e;" u2="&#x29;" k="20" />
<hkern u1="&#x14f;" u2="&#x2122;" k="31" />
<hkern u1="&#x14f;" u2="&#xc6;" k="14" />
<hkern u1="&#x14f;" u2="&#x7d;" k="51" />
<hkern u1="&#x14f;" u2="x" k="23" />
<hkern u1="&#x14f;" u2="v" k="16" />
<hkern u1="&#x14f;" u2="]" k="66" />
<hkern u1="&#x14f;" u2="\" k="80" />
<hkern u1="&#x14f;" u2="X" k="49" />
<hkern u1="&#x14f;" u2="V" k="61" />
<hkern u1="&#x14f;" u2="&#x3f;" k="39" />
<hkern u1="&#x14f;" u2="&#x2a;" k="12" />
<hkern u1="&#x14f;" u2="&#x29;" k="39" />
<hkern u1="&#x150;" u2="&#xc6;" k="29" />
<hkern u1="&#x150;" u2="&#x7d;" k="35" />
<hkern u1="&#x150;" u2="]" k="49" />
<hkern u1="&#x150;" u2="\" k="29" />
<hkern u1="&#x150;" u2="X" k="37" />
<hkern u1="&#x150;" u2="V" k="23" />
<hkern u1="&#x150;" u2="&#x3f;" k="6" />
<hkern u1="&#x150;" u2="&#x2f;" k="20" />
<hkern u1="&#x150;" u2="&#x29;" k="20" />
<hkern u1="&#x151;" u2="&#x2122;" k="31" />
<hkern u1="&#x151;" u2="&#xc6;" k="14" />
<hkern u1="&#x151;" u2="&#x7d;" k="51" />
<hkern u1="&#x151;" u2="x" k="23" />
<hkern u1="&#x151;" u2="v" k="16" />
<hkern u1="&#x151;" u2="]" k="66" />
<hkern u1="&#x151;" u2="\" k="80" />
<hkern u1="&#x151;" u2="X" k="49" />
<hkern u1="&#x151;" u2="V" k="61" />
<hkern u1="&#x151;" u2="&#x3f;" k="39" />
<hkern u1="&#x151;" u2="&#x2a;" k="12" />
<hkern u1="&#x151;" u2="&#x29;" k="39" />
<hkern u1="&#x152;" u2="&#x135;" k="-33" />
<hkern u1="&#x152;" u2="&#x12d;" k="-16" />
<hkern u1="&#x152;" u2="&#x12b;" k="-6" />
<hkern u1="&#x152;" u2="&#x129;" k="-47" />
<hkern u1="&#x152;" u2="&#xef;" k="-49" />
<hkern u1="&#x152;" u2="&#xee;" k="-43" />
<hkern u1="&#x152;" u2="&#xec;" k="-80" />
<hkern u1="&#x152;" u2="v" k="10" />
<hkern u1="&#x153;" u2="&#x2122;" k="27" />
<hkern u1="&#x153;" u2="&#xc6;" k="10" />
<hkern u1="&#x153;" u2="&#x7d;" k="37" />
<hkern u1="&#x153;" u2="v" k="14" />
<hkern u1="&#x153;" u2="]" k="23" />
<hkern u1="&#x153;" u2="\" k="74" />
<hkern u1="&#x153;" u2="X" k="8" />
<hkern u1="&#x153;" u2="V" k="57" />
<hkern u1="&#x153;" u2="&#x3f;" k="31" />
<hkern u1="&#x153;" u2="&#x29;" k="18" />
<hkern u1="&#x154;" u2="&#xc6;" k="18" />
<hkern u1="&#x154;" u2="&#x7d;" k="14" />
<hkern u1="&#x154;" u2="]" k="16" />
<hkern u1="&#x154;" u2="\" k="23" />
<hkern u1="&#x154;" u2="X" k="8" />
<hkern u1="&#x154;" u2="V" k="20" />
<hkern u1="&#x155;" u2="&#xc6;" k="74" />
<hkern u1="&#x155;" u2="&#x7d;" k="35" />
<hkern u1="&#x155;" u2="]" k="47" />
<hkern u1="&#x155;" u2="\" k="16" />
<hkern u1="&#x155;" u2="X" k="57" />
<hkern u1="&#x155;" u2="&#x2f;" k="68" />
<hkern u1="&#x155;" u2="&#x29;" k="14" />
<hkern u1="&#x155;" u2="&#x26;" k="8" />
<hkern u1="&#x156;" u2="&#xc6;" k="18" />
<hkern u1="&#x156;" u2="&#x7d;" k="14" />
<hkern u1="&#x156;" u2="]" k="16" />
<hkern u1="&#x156;" u2="\" k="23" />
<hkern u1="&#x156;" u2="X" k="8" />
<hkern u1="&#x156;" u2="V" k="20" />
<hkern u1="&#x157;" u2="&#xc6;" k="74" />
<hkern u1="&#x157;" u2="&#x7d;" k="35" />
<hkern u1="&#x157;" u2="]" k="47" />
<hkern u1="&#x157;" u2="\" k="16" />
<hkern u1="&#x157;" u2="X" k="57" />
<hkern u1="&#x157;" u2="&#x2f;" k="68" />
<hkern u1="&#x157;" u2="&#x29;" k="14" />
<hkern u1="&#x157;" u2="&#x26;" k="8" />
<hkern u1="&#x158;" u2="&#xc6;" k="18" />
<hkern u1="&#x158;" u2="&#x7d;" k="14" />
<hkern u1="&#x158;" u2="]" k="16" />
<hkern u1="&#x158;" u2="\" k="23" />
<hkern u1="&#x158;" u2="X" k="8" />
<hkern u1="&#x158;" u2="V" k="20" />
<hkern u1="&#x159;" u2="&#xc6;" k="74" />
<hkern u1="&#x159;" u2="&#x7d;" k="35" />
<hkern u1="&#x159;" u2="]" k="47" />
<hkern u1="&#x159;" u2="\" k="16" />
<hkern u1="&#x159;" u2="X" k="57" />
<hkern u1="&#x159;" u2="&#x2f;" k="68" />
<hkern u1="&#x159;" u2="&#x29;" k="14" />
<hkern u1="&#x159;" u2="&#x26;" k="8" />
<hkern u1="&#x15a;" u2="&#x129;" k="-14" />
<hkern u1="&#x15a;" u2="&#xef;" k="-31" />
<hkern u1="&#x15a;" u2="&#xee;" k="-12" />
<hkern u1="&#x15a;" u2="&#xec;" k="-49" />
<hkern u1="&#x15a;" u2="&#xc6;" k="23" />
<hkern u1="&#x15a;" u2="x" k="18" />
<hkern u1="&#x15a;" u2="v" k="16" />
<hkern u1="&#x15a;" u2="f" k="16" />
<hkern u1="&#x15a;" u2="X" k="10" />
<hkern u1="&#x15a;" u2="V" k="18" />
<hkern u1="&#x15b;" u2="&#x2122;" k="23" />
<hkern u1="&#x15b;" u2="&#xc6;" k="10" />
<hkern u1="&#x15b;" u2="&#x7d;" k="39" />
<hkern u1="&#x15b;" u2="v" k="12" />
<hkern u1="&#x15b;" u2="]" k="49" />
<hkern u1="&#x15b;" u2="\" k="53" />
<hkern u1="&#x15b;" u2="X" k="12" />
<hkern u1="&#x15b;" u2="V" k="41" />
<hkern u1="&#x15b;" u2="&#x3f;" k="10" />
<hkern u1="&#x15b;" u2="&#x29;" k="20" />
<hkern u1="&#x15c;" u2="&#x129;" k="-14" />
<hkern u1="&#x15c;" u2="&#xef;" k="-31" />
<hkern u1="&#x15c;" u2="&#xee;" k="-12" />
<hkern u1="&#x15c;" u2="&#xec;" k="-49" />
<hkern u1="&#x15c;" u2="&#xc6;" k="23" />
<hkern u1="&#x15c;" u2="x" k="18" />
<hkern u1="&#x15c;" u2="v" k="16" />
<hkern u1="&#x15c;" u2="f" k="16" />
<hkern u1="&#x15c;" u2="X" k="10" />
<hkern u1="&#x15c;" u2="V" k="18" />
<hkern u1="&#x15d;" u2="&#x2122;" k="23" />
<hkern u1="&#x15d;" u2="&#xc6;" k="10" />
<hkern u1="&#x15d;" u2="&#x7d;" k="39" />
<hkern u1="&#x15d;" u2="v" k="12" />
<hkern u1="&#x15d;" u2="]" k="49" />
<hkern u1="&#x15d;" u2="\" k="53" />
<hkern u1="&#x15d;" u2="X" k="12" />
<hkern u1="&#x15d;" u2="V" k="41" />
<hkern u1="&#x15d;" u2="&#x3f;" k="10" />
<hkern u1="&#x15d;" u2="&#x29;" k="20" />
<hkern u1="&#x15e;" u2="&#x129;" k="-14" />
<hkern u1="&#x15e;" u2="&#xef;" k="-31" />
<hkern u1="&#x15e;" u2="&#xee;" k="-12" />
<hkern u1="&#x15e;" u2="&#xec;" k="-49" />
<hkern u1="&#x15e;" u2="&#xc6;" k="23" />
<hkern u1="&#x15e;" u2="x" k="18" />
<hkern u1="&#x15e;" u2="v" k="16" />
<hkern u1="&#x15e;" u2="f" k="16" />
<hkern u1="&#x15e;" u2="X" k="10" />
<hkern u1="&#x15e;" u2="V" k="18" />
<hkern u1="&#x15f;" u2="&#x2122;" k="23" />
<hkern u1="&#x15f;" u2="&#xc6;" k="10" />
<hkern u1="&#x15f;" u2="&#x7d;" k="39" />
<hkern u1="&#x15f;" u2="v" k="12" />
<hkern u1="&#x15f;" u2="]" k="49" />
<hkern u1="&#x15f;" u2="\" k="53" />
<hkern u1="&#x15f;" u2="X" k="12" />
<hkern u1="&#x15f;" u2="V" k="41" />
<hkern u1="&#x15f;" u2="&#x3f;" k="10" />
<hkern u1="&#x15f;" u2="&#x29;" k="20" />
<hkern u1="&#x160;" u2="&#x129;" k="-14" />
<hkern u1="&#x160;" u2="&#xef;" k="-31" />
<hkern u1="&#x160;" u2="&#xee;" k="-12" />
<hkern u1="&#x160;" u2="&#xec;" k="-49" />
<hkern u1="&#x160;" u2="&#xc6;" k="23" />
<hkern u1="&#x160;" u2="x" k="18" />
<hkern u1="&#x160;" u2="v" k="16" />
<hkern u1="&#x160;" u2="f" k="16" />
<hkern u1="&#x160;" u2="X" k="10" />
<hkern u1="&#x160;" u2="V" k="18" />
<hkern u1="&#x161;" u2="&#x2122;" k="23" />
<hkern u1="&#x161;" u2="&#xc6;" k="10" />
<hkern u1="&#x161;" u2="&#x7d;" k="39" />
<hkern u1="&#x161;" u2="v" k="12" />
<hkern u1="&#x161;" u2="]" k="49" />
<hkern u1="&#x161;" u2="\" k="53" />
<hkern u1="&#x161;" u2="X" k="12" />
<hkern u1="&#x161;" u2="V" k="41" />
<hkern u1="&#x161;" u2="&#x3f;" k="10" />
<hkern u1="&#x161;" u2="&#x29;" k="20" />
<hkern u1="&#x164;" u2="&#x16d;" k="135" />
<hkern u1="&#x164;" u2="&#x169;" k="135" />
<hkern u1="&#x164;" u2="&#x15d;" k="139" />
<hkern u1="&#x164;" u2="&#x159;" k="86" />
<hkern u1="&#x164;" u2="&#x155;" k="104" />
<hkern u1="&#x164;" u2="&#x151;" k="121" />
<hkern u1="&#x164;" u2="&#x135;" k="-74" />
<hkern u1="&#x164;" u2="&#x131;" k="137" />
<hkern u1="&#x164;" u2="&#x12d;" k="-76" />
<hkern u1="&#x164;" u2="&#x12b;" k="-61" />
<hkern u1="&#x164;" u2="&#x129;" k="-106" />
<hkern u1="&#x164;" u2="&#x11f;" k="170" />
<hkern u1="&#x164;" u2="&#x109;" k="121" />
<hkern u1="&#x164;" u2="&#xef;" k="-104" />
<hkern u1="&#x164;" u2="&#xee;" k="-84" />
<hkern u1="&#x164;" u2="&#xec;" k="-141" />
<hkern u1="&#x164;" u2="&#xe4;" k="135" />
<hkern u1="&#x164;" u2="&#xe3;" k="111" />
<hkern u1="&#x164;" u2="&#xc6;" k="104" />
<hkern u1="&#x164;" u2="&#xae;" k="8" />
<hkern u1="&#x164;" u2="x" k="121" />
<hkern u1="&#x164;" u2="v" k="117" />
<hkern u1="&#x164;" u2="f" k="31" />
<hkern u1="&#x164;" u2="&#x40;" k="41" />
<hkern u1="&#x164;" u2="&#x2f;" k="102" />
<hkern u1="&#x164;" u2="&#x26;" k="31" />
<hkern u1="&#x165;" u2="&#x203a;" k="23" />
<hkern u1="&#x165;" u2="&#x2039;" k="104" />
<hkern u1="&#x165;" u2="&#x2026;" k="78" />
<hkern u1="&#x165;" u2="&#x201e;" k="78" />
<hkern u1="&#x165;" u2="&#x201c;" k="-12" />
<hkern u1="&#x165;" u2="&#x201a;" k="78" />
<hkern u1="&#x165;" u2="&#x2018;" k="-12" />
<hkern u1="&#x165;" u2="&#x2014;" k="127" />
<hkern u1="&#x165;" u2="&#x2013;" k="127" />
<hkern u1="&#x165;" u2="&#x21b;" k="-29" />
<hkern u1="&#x165;" u2="&#x1ff;" k="25" />
<hkern u1="&#x165;" u2="&#x177;" k="-39" />
<hkern u1="&#x165;" u2="&#x175;" k="-27" />
<hkern u1="&#x165;" u2="&#x167;" k="-29" />
<hkern u1="&#x165;" u2="&#x165;" k="-29" />
<hkern u1="&#x165;" u2="&#x153;" k="25" />
<hkern u1="&#x165;" u2="&#x151;" k="25" />
<hkern u1="&#x165;" u2="&#x14f;" k="25" />
<hkern u1="&#x165;" u2="&#x14d;" k="25" />
<hkern u1="&#x165;" u2="&#x142;" k="-12" />
<hkern u1="&#x165;" u2="&#x13e;" k="-12" />
<hkern u1="&#x165;" u2="&#x13c;" k="-12" />
<hkern u1="&#x165;" u2="&#x13a;" k="-12" />
<hkern u1="&#x165;" u2="&#x137;" k="-20" />
<hkern u1="&#x165;" u2="&#x135;" k="-20" />
<hkern u1="&#x165;" u2="&#x131;" k="-20" />
<hkern u1="&#x165;" u2="&#x12f;" k="-20" />
<hkern u1="&#x165;" u2="&#x12d;" k="-20" />
<hkern u1="&#x165;" u2="&#x12b;" k="-20" />
<hkern u1="&#x165;" u2="&#x129;" k="-20" />
<hkern u1="&#x165;" u2="&#x127;" k="-20" />
<hkern u1="&#x165;" u2="&#x125;" k="-20" />
<hkern u1="&#x165;" u2="&#x123;" k="14" />
<hkern u1="&#x165;" u2="&#x121;" k="14" />
<hkern u1="&#x165;" u2="&#x11f;" k="14" />
<hkern u1="&#x165;" u2="&#x11d;" k="14" />
<hkern u1="&#x165;" u2="&#x11b;" k="25" />
<hkern u1="&#x165;" u2="&#x119;" k="25" />
<hkern u1="&#x165;" u2="&#x117;" k="25" />
<hkern u1="&#x165;" u2="&#x115;" k="25" />
<hkern u1="&#x165;" u2="&#x113;" k="25" />
<hkern u1="&#x165;" u2="&#x111;" k="25" />
<hkern u1="&#x165;" u2="&#x10f;" k="25" />
<hkern u1="&#x165;" u2="&#x10d;" k="25" />
<hkern u1="&#x165;" u2="&#x10b;" k="25" />
<hkern u1="&#x165;" u2="&#x109;" k="25" />
<hkern u1="&#x165;" u2="&#x107;" k="25" />
<hkern u1="&#x165;" u2="&#xff;" k="-39" />
<hkern u1="&#x165;" u2="&#xfd;" k="-39" />
<hkern u1="&#x165;" u2="&#xf8;" k="25" />
<hkern u1="&#x165;" u2="&#xf6;" k="25" />
<hkern u1="&#x165;" u2="&#xf5;" k="25" />
<hkern u1="&#x165;" u2="&#xf4;" k="25" />
<hkern u1="&#x165;" u2="&#xf3;" k="25" />
<hkern u1="&#x165;" u2="&#xf2;" k="25" />
<hkern u1="&#x165;" u2="&#xef;" k="-20" />
<hkern u1="&#x165;" u2="&#xee;" k="-20" />
<hkern u1="&#x165;" u2="&#xed;" k="-20" />
<hkern u1="&#x165;" u2="&#xec;" k="-20" />
<hkern u1="&#x165;" u2="&#xeb;" k="25" />
<hkern u1="&#x165;" u2="&#xea;" k="25" />
<hkern u1="&#x165;" u2="&#xe9;" k="25" />
<hkern u1="&#x165;" u2="&#xe8;" k="25" />
<hkern u1="&#x165;" u2="&#xe7;" k="25" />
<hkern u1="&#x165;" u2="&#xe4;" k="-16" />
<hkern u1="&#x165;" u2="&#xdf;" k="-20" />
<hkern u1="&#x165;" u2="&#xbb;" k="23" />
<hkern u1="&#x165;" u2="&#xab;" k="104" />
<hkern u1="&#x165;" u2="&#x7d;" k="-12" />
<hkern u1="&#x165;" u2="y" k="-39" />
<hkern u1="&#x165;" u2="x" k="-41" />
<hkern u1="&#x165;" u2="w" k="-27" />
<hkern u1="&#x165;" u2="v" k="-41" />
<hkern u1="&#x165;" u2="t" k="-29" />
<hkern u1="&#x165;" u2="q" k="25" />
<hkern u1="&#x165;" u2="o" k="25" />
<hkern u1="&#x165;" u2="l" k="-12" />
<hkern u1="&#x165;" u2="k" k="-20" />
<hkern u1="&#x165;" u2="j" k="-20" />
<hkern u1="&#x165;" u2="i" k="-20" />
<hkern u1="&#x165;" u2="h" k="-20" />
<hkern u1="&#x165;" u2="g" k="14" />
<hkern u1="&#x165;" u2="f" k="-14" />
<hkern u1="&#x165;" u2="e" k="25" />
<hkern u1="&#x165;" u2="d" k="25" />
<hkern u1="&#x165;" u2="c" k="25" />
<hkern u1="&#x165;" u2="b" k="-20" />
<hkern u1="&#x165;" u2="]" k="-14" />
<hkern u1="&#x165;" u2="\" k="-20" />
<hkern u1="&#x165;" u2="&#x3f;" k="-20" />
<hkern u1="&#x165;" u2="&#x2f;" k="55" />
<hkern u1="&#x165;" u2="&#x2e;" k="78" />
<hkern u1="&#x165;" u2="&#x2d;" k="127" />
<hkern u1="&#x165;" u2="&#x2c;" k="78" />
<hkern u1="&#x165;" u2="&#x2a;" k="-43" />
<hkern u1="&#x166;" u2="&#x16d;" k="135" />
<hkern u1="&#x166;" u2="&#x169;" k="135" />
<hkern u1="&#x166;" u2="&#x15d;" k="139" />
<hkern u1="&#x166;" u2="&#x159;" k="86" />
<hkern u1="&#x166;" u2="&#x155;" k="104" />
<hkern u1="&#x166;" u2="&#x151;" k="121" />
<hkern u1="&#x166;" u2="&#x135;" k="-74" />
<hkern u1="&#x166;" u2="&#x131;" k="137" />
<hkern u1="&#x166;" u2="&#x12d;" k="-76" />
<hkern u1="&#x166;" u2="&#x12b;" k="-61" />
<hkern u1="&#x166;" u2="&#x129;" k="-106" />
<hkern u1="&#x166;" u2="&#x11f;" k="170" />
<hkern u1="&#x166;" u2="&#x109;" k="121" />
<hkern u1="&#x166;" u2="&#xef;" k="-104" />
<hkern u1="&#x166;" u2="&#xee;" k="-84" />
<hkern u1="&#x166;" u2="&#xec;" k="-141" />
<hkern u1="&#x166;" u2="&#xe4;" k="135" />
<hkern u1="&#x166;" u2="&#xe3;" k="111" />
<hkern u1="&#x166;" u2="&#xc6;" k="104" />
<hkern u1="&#x166;" u2="&#xae;" k="8" />
<hkern u1="&#x166;" u2="x" k="121" />
<hkern u1="&#x166;" u2="v" k="117" />
<hkern u1="&#x166;" u2="f" k="31" />
<hkern u1="&#x166;" u2="&#x40;" k="41" />
<hkern u1="&#x166;" u2="&#x2f;" k="102" />
<hkern u1="&#x166;" u2="&#x26;" k="31" />
<hkern u1="&#x167;" u2="&#x7d;" k="8" />
<hkern u1="&#x167;" u2="]" k="10" />
<hkern u1="&#x167;" u2="\" k="27" />
<hkern u1="&#x167;" u2="V" k="8" />
<hkern u1="&#x168;" u2="&#xec;" k="-20" />
<hkern u1="&#x168;" u2="&#xc6;" k="16" />
<hkern u1="&#x168;" u2="f" k="8" />
<hkern u1="&#x168;" u2="&#x2f;" k="25" />
<hkern u1="&#x169;" u2="&#x2122;" k="20" />
<hkern u1="&#x169;" u2="&#x7d;" k="20" />
<hkern u1="&#x169;" u2="]" k="23" />
<hkern u1="&#x169;" u2="\" k="53" />
<hkern u1="&#x169;" u2="X" k="8" />
<hkern u1="&#x169;" u2="V" k="47" />
<hkern u1="&#x169;" u2="&#x3f;" k="10" />
<hkern u1="&#x169;" u2="&#x29;" k="16" />
<hkern u1="&#x16a;" u2="&#xec;" k="-20" />
<hkern u1="&#x16a;" u2="&#xc6;" k="16" />
<hkern u1="&#x16a;" u2="f" k="8" />
<hkern u1="&#x16a;" u2="&#x2f;" k="25" />
<hkern u1="&#x16b;" u2="&#x2122;" k="20" />
<hkern u1="&#x16b;" u2="&#x7d;" k="20" />
<hkern u1="&#x16b;" u2="]" k="23" />
<hkern u1="&#x16b;" u2="\" k="53" />
<hkern u1="&#x16b;" u2="X" k="8" />
<hkern u1="&#x16b;" u2="V" k="47" />
<hkern u1="&#x16b;" u2="&#x3f;" k="10" />
<hkern u1="&#x16b;" u2="&#x29;" k="16" />
<hkern u1="&#x16c;" u2="&#xec;" k="-20" />
<hkern u1="&#x16c;" u2="&#xc6;" k="16" />
<hkern u1="&#x16c;" u2="f" k="8" />
<hkern u1="&#x16c;" u2="&#x2f;" k="25" />
<hkern u1="&#x16d;" u2="&#x2122;" k="20" />
<hkern u1="&#x16d;" u2="&#x7d;" k="20" />
<hkern u1="&#x16d;" u2="]" k="23" />
<hkern u1="&#x16d;" u2="\" k="53" />
<hkern u1="&#x16d;" u2="X" k="8" />
<hkern u1="&#x16d;" u2="V" k="47" />
<hkern u1="&#x16d;" u2="&#x3f;" k="10" />
<hkern u1="&#x16d;" u2="&#x29;" k="16" />
<hkern u1="&#x16e;" u2="&#xec;" k="-20" />
<hkern u1="&#x16e;" u2="&#xc6;" k="16" />
<hkern u1="&#x16e;" u2="f" k="8" />
<hkern u1="&#x16e;" u2="&#x2f;" k="25" />
<hkern u1="&#x16f;" u2="&#x2122;" k="20" />
<hkern u1="&#x16f;" u2="&#x7d;" k="20" />
<hkern u1="&#x16f;" u2="]" k="23" />
<hkern u1="&#x16f;" u2="\" k="53" />
<hkern u1="&#x16f;" u2="X" k="8" />
<hkern u1="&#x16f;" u2="V" k="47" />
<hkern u1="&#x16f;" u2="&#x3f;" k="10" />
<hkern u1="&#x16f;" u2="&#x29;" k="16" />
<hkern u1="&#x170;" u2="&#xec;" k="-20" />
<hkern u1="&#x170;" u2="&#xc6;" k="16" />
<hkern u1="&#x170;" u2="f" k="8" />
<hkern u1="&#x170;" u2="&#x2f;" k="25" />
<hkern u1="&#x171;" u2="&#x2122;" k="20" />
<hkern u1="&#x171;" u2="&#x7d;" k="20" />
<hkern u1="&#x171;" u2="]" k="23" />
<hkern u1="&#x171;" u2="\" k="53" />
<hkern u1="&#x171;" u2="X" k="8" />
<hkern u1="&#x171;" u2="V" k="47" />
<hkern u1="&#x171;" u2="&#x3f;" k="10" />
<hkern u1="&#x171;" u2="&#x29;" k="16" />
<hkern u1="&#x172;" u2="&#xec;" k="-20" />
<hkern u1="&#x172;" u2="&#xc6;" k="16" />
<hkern u1="&#x172;" u2="f" k="8" />
<hkern u1="&#x172;" u2="&#x2f;" k="25" />
<hkern u1="&#x173;" u2="&#x2122;" k="20" />
<hkern u1="&#x173;" u2="&#x7d;" k="20" />
<hkern u1="&#x173;" u2="j" k="-16" />
<hkern u1="&#x173;" u2="]" k="23" />
<hkern u1="&#x173;" u2="\" k="53" />
<hkern u1="&#x173;" u2="X" k="8" />
<hkern u1="&#x173;" u2="V" k="47" />
<hkern u1="&#x173;" u2="&#x3f;" k="10" />
<hkern u1="&#x173;" u2="&#x29;" k="16" />
<hkern u1="&#x174;" u2="&#x135;" k="-41" />
<hkern u1="&#x174;" u2="&#x131;" k="29" />
<hkern u1="&#x174;" u2="&#x12d;" k="-53" />
<hkern u1="&#x174;" u2="&#x12b;" k="-37" />
<hkern u1="&#x174;" u2="&#x129;" k="-76" />
<hkern u1="&#x174;" u2="&#xef;" k="-80" />
<hkern u1="&#x174;" u2="&#xee;" k="-45" />
<hkern u1="&#x174;" u2="&#xec;" k="-104" />
<hkern u1="&#x174;" u2="&#xc6;" k="51" />
<hkern u1="&#x174;" u2="&#x2f;" k="57" />
<hkern u1="&#x174;" u2="&#x26;" k="6" />
<hkern u1="&#x175;" u2="&#xc6;" k="33" />
<hkern u1="&#x175;" u2="&#x7d;" k="43" />
<hkern u1="&#x175;" u2="]" k="53" />
<hkern u1="&#x175;" u2="\" k="27" />
<hkern u1="&#x175;" u2="X" k="49" />
<hkern u1="&#x175;" u2="V" k="18" />
<hkern u1="&#x175;" u2="&#x3f;" k="8" />
<hkern u1="&#x175;" u2="&#x2f;" k="31" />
<hkern u1="&#x175;" u2="&#x29;" k="20" />
<hkern u1="&#x176;" u2="&#x159;" k="66" />
<hkern u1="&#x176;" u2="&#x155;" k="78" />
<hkern u1="&#x176;" u2="&#x151;" k="111" />
<hkern u1="&#x176;" u2="&#x142;" k="12" />
<hkern u1="&#x176;" u2="&#x135;" k="-23" />
<hkern u1="&#x176;" u2="&#x131;" k="123" />
<hkern u1="&#x176;" u2="&#x12d;" k="-92" />
<hkern u1="&#x176;" u2="&#x12b;" k="-70" />
<hkern u1="&#x176;" u2="&#x129;" k="-96" />
<hkern u1="&#x176;" u2="&#x103;" k="121" />
<hkern u1="&#x176;" u2="&#xff;" k="57" />
<hkern u1="&#x176;" u2="&#xef;" k="-121" />
<hkern u1="&#x176;" u2="&#xee;" k="-33" />
<hkern u1="&#x176;" u2="&#xec;" k="-129" />
<hkern u1="&#x176;" u2="&#xeb;" k="131" />
<hkern u1="&#x176;" u2="&#xe4;" k="100" />
<hkern u1="&#x176;" u2="&#xe3;" k="86" />
<hkern u1="&#x176;" u2="&#xdf;" k="20" />
<hkern u1="&#x176;" u2="&#xc6;" k="115" />
<hkern u1="&#x176;" u2="&#xae;" k="41" />
<hkern u1="&#x176;" u2="x" k="76" />
<hkern u1="&#x176;" u2="v" k="74" />
<hkern u1="&#x176;" u2="f" k="43" />
<hkern u1="&#x176;" u2="&#x40;" k="74" />
<hkern u1="&#x176;" u2="&#x2f;" k="133" />
<hkern u1="&#x176;" u2="&#x2a;" k="-8" />
<hkern u1="&#x176;" u2="&#x26;" k="66" />
<hkern u1="&#x177;" u2="&#xc6;" k="39" />
<hkern u1="&#x177;" u2="&#x7d;" k="35" />
<hkern u1="&#x177;" u2="]" k="47" />
<hkern u1="&#x177;" u2="\" k="27" />
<hkern u1="&#x177;" u2="X" k="49" />
<hkern u1="&#x177;" u2="V" k="14" />
<hkern u1="&#x177;" u2="&#x3f;" k="8" />
<hkern u1="&#x177;" u2="&#x2f;" k="41" />
<hkern u1="&#x178;" u2="&#x159;" k="66" />
<hkern u1="&#x178;" u2="&#x155;" k="78" />
<hkern u1="&#x178;" u2="&#x151;" k="111" />
<hkern u1="&#x178;" u2="&#x142;" k="12" />
<hkern u1="&#x178;" u2="&#x135;" k="-23" />
<hkern u1="&#x178;" u2="&#x131;" k="123" />
<hkern u1="&#x178;" u2="&#x12d;" k="-92" />
<hkern u1="&#x178;" u2="&#x12b;" k="-70" />
<hkern u1="&#x178;" u2="&#x129;" k="-96" />
<hkern u1="&#x178;" u2="&#x103;" k="121" />
<hkern u1="&#x178;" u2="&#xff;" k="57" />
<hkern u1="&#x178;" u2="&#xef;" k="-121" />
<hkern u1="&#x178;" u2="&#xee;" k="-33" />
<hkern u1="&#x178;" u2="&#xec;" k="-129" />
<hkern u1="&#x178;" u2="&#xeb;" k="131" />
<hkern u1="&#x178;" u2="&#xe4;" k="100" />
<hkern u1="&#x178;" u2="&#xe3;" k="86" />
<hkern u1="&#x178;" u2="&#xdf;" k="20" />
<hkern u1="&#x178;" u2="&#xc6;" k="115" />
<hkern u1="&#x178;" u2="&#xae;" k="41" />
<hkern u1="&#x178;" u2="x" k="76" />
<hkern u1="&#x178;" u2="v" k="74" />
<hkern u1="&#x178;" u2="f" k="43" />
<hkern u1="&#x178;" u2="&#x40;" k="74" />
<hkern u1="&#x178;" u2="&#x2f;" k="133" />
<hkern u1="&#x178;" u2="&#x2a;" k="-8" />
<hkern u1="&#x178;" u2="&#x26;" k="66" />
<hkern u1="&#x179;" u2="&#x135;" k="-37" />
<hkern u1="&#x179;" u2="&#x12d;" k="-10" />
<hkern u1="&#x179;" u2="&#x12b;" k="-8" />
<hkern u1="&#x179;" u2="&#x129;" k="-47" />
<hkern u1="&#x179;" u2="&#xef;" k="-45" />
<hkern u1="&#x179;" u2="&#xee;" k="-47" />
<hkern u1="&#x179;" u2="&#xec;" k="-82" />
<hkern u1="&#x179;" u2="&#xae;" k="6" />
<hkern u1="&#x179;" u2="v" k="18" />
<hkern u1="&#x179;" u2="f" k="10" />
<hkern u1="&#x17a;" u2="&#x7d;" k="16" />
<hkern u1="&#x17a;" u2="]" k="18" />
<hkern u1="&#x17a;" u2="\" k="39" />
<hkern u1="&#x17a;" u2="V" k="27" />
<hkern u1="&#x17a;" u2="&#x3f;" k="8" />
<hkern u1="&#x17b;" u2="&#x135;" k="-37" />
<hkern u1="&#x17b;" u2="&#x12d;" k="-10" />
<hkern u1="&#x17b;" u2="&#x12b;" k="-8" />
<hkern u1="&#x17b;" u2="&#x129;" k="-47" />
<hkern u1="&#x17b;" u2="&#xef;" k="-45" />
<hkern u1="&#x17b;" u2="&#xee;" k="-47" />
<hkern u1="&#x17b;" u2="&#xec;" k="-82" />
<hkern u1="&#x17b;" u2="&#xae;" k="6" />
<hkern u1="&#x17b;" u2="v" k="18" />
<hkern u1="&#x17b;" u2="f" k="10" />
<hkern u1="&#x17c;" u2="&#x7d;" k="16" />
<hkern u1="&#x17c;" u2="]" k="18" />
<hkern u1="&#x17c;" u2="\" k="39" />
<hkern u1="&#x17c;" u2="V" k="27" />
<hkern u1="&#x17c;" u2="&#x3f;" k="8" />
<hkern u1="&#x17d;" u2="&#x135;" k="-37" />
<hkern u1="&#x17d;" u2="&#x12d;" k="-10" />
<hkern u1="&#x17d;" u2="&#x12b;" k="-8" />
<hkern u1="&#x17d;" u2="&#x129;" k="-47" />
<hkern u1="&#x17d;" u2="&#xef;" k="-45" />
<hkern u1="&#x17d;" u2="&#xee;" k="-47" />
<hkern u1="&#x17d;" u2="&#xec;" k="-82" />
<hkern u1="&#x17d;" u2="&#xae;" k="6" />
<hkern u1="&#x17d;" u2="v" k="18" />
<hkern u1="&#x17d;" u2="f" k="10" />
<hkern u1="&#x17e;" u2="&#x7d;" k="16" />
<hkern u1="&#x17e;" u2="]" k="18" />
<hkern u1="&#x17e;" u2="\" k="39" />
<hkern u1="&#x17e;" u2="V" k="27" />
<hkern u1="&#x17e;" u2="&#x3f;" k="8" />
<hkern u1="&#x1fa;" u2="&#x2122;" k="72" />
<hkern u1="&#x1fa;" u2="&#xae;" k="43" />
<hkern u1="&#x1fa;" u2="&#x7d;" k="18" />
<hkern u1="&#x1fa;" u2="v" k="33" />
<hkern u1="&#x1fa;" u2="f" k="16" />
<hkern u1="&#x1fa;" u2="]" k="20" />
<hkern u1="&#x1fa;" u2="\" k="90" />
<hkern u1="&#x1fa;" u2="V" k="57" />
<hkern u1="&#x1fa;" u2="&#x3f;" k="39" />
<hkern u1="&#x1fa;" u2="&#x2a;" k="63" />
<hkern u1="&#x1fb;" u2="&#x2122;" k="29" />
<hkern u1="&#x1fb;" u2="&#x7d;" k="8" />
<hkern u1="&#x1fb;" u2="v" k="10" />
<hkern u1="&#x1fb;" u2="]" k="10" />
<hkern u1="&#x1fb;" u2="\" k="82" />
<hkern u1="&#x1fb;" u2="V" k="55" />
<hkern u1="&#x1fb;" u2="&#x3f;" k="29" />
<hkern u1="&#x1fb;" u2="&#x2a;" k="12" />
<hkern u1="&#x1fc;" u2="&#x135;" k="-33" />
<hkern u1="&#x1fc;" u2="&#x12d;" k="-16" />
<hkern u1="&#x1fc;" u2="&#x12b;" k="-6" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-47" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-49" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-43" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-80" />
<hkern u1="&#x1fc;" u2="v" k="10" />
<hkern u1="&#x1fd;" u2="&#x2122;" k="27" />
<hkern u1="&#x1fd;" u2="&#xc6;" k="10" />
<hkern u1="&#x1fd;" u2="&#x7d;" k="37" />
<hkern u1="&#x1fd;" u2="v" k="14" />
<hkern u1="&#x1fd;" u2="]" k="23" />
<hkern u1="&#x1fd;" u2="\" k="74" />
<hkern u1="&#x1fd;" u2="X" k="8" />
<hkern u1="&#x1fd;" u2="V" k="57" />
<hkern u1="&#x1fd;" u2="&#x3f;" k="31" />
<hkern u1="&#x1fd;" u2="&#x29;" k="18" />
<hkern u1="&#x1fe;" u2="&#xc6;" k="29" />
<hkern u1="&#x1fe;" u2="&#x7d;" k="35" />
<hkern u1="&#x1fe;" u2="]" k="49" />
<hkern u1="&#x1fe;" u2="\" k="29" />
<hkern u1="&#x1fe;" u2="X" k="37" />
<hkern u1="&#x1fe;" u2="V" k="23" />
<hkern u1="&#x1fe;" u2="&#x3f;" k="6" />
<hkern u1="&#x1fe;" u2="&#x2f;" k="20" />
<hkern u1="&#x1fe;" u2="&#x29;" k="20" />
<hkern u1="&#x1ff;" u2="&#x2122;" k="31" />
<hkern u1="&#x1ff;" u2="&#xc6;" k="14" />
<hkern u1="&#x1ff;" u2="&#x7d;" k="51" />
<hkern u1="&#x1ff;" u2="x" k="23" />
<hkern u1="&#x1ff;" u2="v" k="16" />
<hkern u1="&#x1ff;" u2="]" k="66" />
<hkern u1="&#x1ff;" u2="\" k="80" />
<hkern u1="&#x1ff;" u2="X" k="49" />
<hkern u1="&#x1ff;" u2="V" k="61" />
<hkern u1="&#x1ff;" u2="&#x3f;" k="39" />
<hkern u1="&#x1ff;" u2="&#x2a;" k="12" />
<hkern u1="&#x1ff;" u2="&#x29;" k="39" />
<hkern u1="&#x218;" u2="&#x129;" k="-14" />
<hkern u1="&#x218;" u2="&#xef;" k="-31" />
<hkern u1="&#x218;" u2="&#xee;" k="-12" />
<hkern u1="&#x218;" u2="&#xec;" k="-49" />
<hkern u1="&#x218;" u2="&#xc6;" k="23" />
<hkern u1="&#x218;" u2="x" k="18" />
<hkern u1="&#x218;" u2="v" k="16" />
<hkern u1="&#x218;" u2="f" k="16" />
<hkern u1="&#x218;" u2="X" k="10" />
<hkern u1="&#x218;" u2="V" k="18" />
<hkern u1="&#x219;" u2="&#x2122;" k="23" />
<hkern u1="&#x219;" u2="&#xc6;" k="10" />
<hkern u1="&#x219;" u2="&#x7d;" k="39" />
<hkern u1="&#x219;" u2="v" k="12" />
<hkern u1="&#x219;" u2="]" k="49" />
<hkern u1="&#x219;" u2="\" k="53" />
<hkern u1="&#x219;" u2="X" k="12" />
<hkern u1="&#x219;" u2="V" k="41" />
<hkern u1="&#x219;" u2="&#x3f;" k="10" />
<hkern u1="&#x219;" u2="&#x29;" k="20" />
<hkern u1="&#x21a;" u2="&#x16d;" k="135" />
<hkern u1="&#x21a;" u2="&#x169;" k="135" />
<hkern u1="&#x21a;" u2="&#x15d;" k="139" />
<hkern u1="&#x21a;" u2="&#x159;" k="86" />
<hkern u1="&#x21a;" u2="&#x155;" k="104" />
<hkern u1="&#x21a;" u2="&#x151;" k="121" />
<hkern u1="&#x21a;" u2="&#x135;" k="-74" />
<hkern u1="&#x21a;" u2="&#x131;" k="137" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-76" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-61" />
<hkern u1="&#x21a;" u2="&#x129;" k="-106" />
<hkern u1="&#x21a;" u2="&#x11f;" k="170" />
<hkern u1="&#x21a;" u2="&#x109;" k="121" />
<hkern u1="&#x21a;" u2="&#xef;" k="-104" />
<hkern u1="&#x21a;" u2="&#xee;" k="-84" />
<hkern u1="&#x21a;" u2="&#xec;" k="-141" />
<hkern u1="&#x21a;" u2="&#xe4;" k="135" />
<hkern u1="&#x21a;" u2="&#xe3;" k="111" />
<hkern u1="&#x21a;" u2="&#xc6;" k="104" />
<hkern u1="&#x21a;" u2="&#xae;" k="8" />
<hkern u1="&#x21a;" u2="x" k="121" />
<hkern u1="&#x21a;" u2="v" k="117" />
<hkern u1="&#x21a;" u2="f" k="31" />
<hkern u1="&#x21a;" u2="&#x40;" k="41" />
<hkern u1="&#x21a;" u2="&#x2f;" k="102" />
<hkern u1="&#x21a;" u2="&#x26;" k="31" />
<hkern u1="&#x21b;" u2="&#x7d;" k="8" />
<hkern u1="&#x21b;" u2="]" k="10" />
<hkern u1="&#x21b;" u2="\" k="27" />
<hkern u1="&#x21b;" u2="V" k="8" />
<hkern u1="&#x2013;" u2="&#xc6;" k="27" />
<hkern u1="&#x2013;" u2="x" k="53" />
<hkern u1="&#x2013;" u2="v" k="23" />
<hkern u1="&#x2013;" u2="f" k="23" />
<hkern u1="&#x2013;" u2="X" k="72" />
<hkern u1="&#x2013;" u2="V" k="55" />
<hkern u1="&#x2014;" u2="&#xc6;" k="27" />
<hkern u1="&#x2014;" u2="x" k="53" />
<hkern u1="&#x2014;" u2="v" k="23" />
<hkern u1="&#x2014;" u2="f" k="23" />
<hkern u1="&#x2014;" u2="X" k="72" />
<hkern u1="&#x2014;" u2="V" k="55" />
<hkern u1="&#x2018;" u2="&#x135;" k="-12" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-25" />
<hkern u1="&#x2018;" u2="&#x129;" k="-43" />
<hkern u1="&#x2018;" u2="&#xef;" k="-55" />
<hkern u1="&#x2018;" u2="&#xee;" k="-23" />
<hkern u1="&#x2018;" u2="&#xec;" k="-74" />
<hkern u1="&#x2018;" u2="&#xc6;" k="100" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-39" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-18" />
<hkern u1="&#x2019;" u2="&#x129;" k="-53" />
<hkern u1="&#x2019;" u2="&#xef;" k="-70" />
<hkern u1="&#x2019;" u2="&#xee;" k="-14" />
<hkern u1="&#x2019;" u2="&#xec;" k="-80" />
<hkern u1="&#x2019;" u2="&#xc6;" k="109" />
<hkern u1="&#x2019;" u2="&#x40;" k="49" />
<hkern u1="&#x2019;" u2="&#x2f;" k="147" />
<hkern u1="&#x2019;" u2="&#x26;" k="55" />
<hkern u1="&#x201a;" u2="v" k="57" />
<hkern u1="&#x201a;" u2="f" k="20" />
<hkern u1="&#x201a;" u2="V" k="92" />
<hkern u1="&#x201c;" u2="&#x135;" k="-12" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-25" />
<hkern u1="&#x201c;" u2="&#x129;" k="-43" />
<hkern u1="&#x201c;" u2="&#xef;" k="-55" />
<hkern u1="&#x201c;" u2="&#xee;" k="-23" />
<hkern u1="&#x201c;" u2="&#xec;" k="-74" />
<hkern u1="&#x201c;" u2="&#xc6;" k="100" />
<hkern u1="&#x201d;" u2="&#x135;" k="-10" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-39" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-18" />
<hkern u1="&#x201d;" u2="&#x129;" k="-53" />
<hkern u1="&#x201d;" u2="&#xef;" k="-70" />
<hkern u1="&#x201d;" u2="&#xee;" k="-14" />
<hkern u1="&#x201d;" u2="&#xec;" k="-80" />
<hkern u1="&#x201d;" u2="&#xc6;" k="109" />
<hkern u1="&#x201d;" u2="&#x40;" k="49" />
<hkern u1="&#x201d;" u2="&#x2f;" k="147" />
<hkern u1="&#x201d;" u2="&#x26;" k="55" />
<hkern u1="&#x201e;" u2="v" k="57" />
<hkern u1="&#x201e;" u2="f" k="20" />
<hkern u1="&#x201e;" u2="V" k="92" />
<hkern u1="&#x2039;" u2="V" k="31" />
<hkern u1="&#x203a;" u2="&#x141;" k="-10" />
<hkern u1="&#x203a;" u2="&#xc6;" k="6" />
<hkern u1="&#x203a;" u2="x" k="47" />
<hkern u1="&#x203a;" u2="v" k="6" />
<hkern u1="&#x203a;" u2="f" k="14" />
<hkern u1="&#x203a;" u2="X" k="45" />
<hkern u1="&#x203a;" u2="V" k="51" />
<hkern u1="&#x2122;" u2="&#x1fc;" k="51" />
<hkern u1="&#x2122;" u2="&#x1fa;" k="51" />
<hkern u1="&#x2122;" u2="&#x135;" k="-45" />
<hkern u1="&#x2122;" u2="&#x134;" k="33" />
<hkern u1="&#x2122;" u2="&#x12d;" k="-25" />
<hkern u1="&#x2122;" u2="&#x129;" k="-31" />
<hkern u1="&#x2122;" u2="&#x104;" k="51" />
<hkern u1="&#x2122;" u2="&#x102;" k="51" />
<hkern u1="&#x2122;" u2="&#x100;" k="51" />
<hkern u1="&#x2122;" u2="&#xef;" k="-53" />
<hkern u1="&#x2122;" u2="&#xee;" k="-53" />
<hkern u1="&#x2122;" u2="&#xec;" k="-68" />
<hkern u1="&#x2122;" u2="&#xc6;" k="63" />
<hkern u1="&#x2122;" u2="&#xc5;" k="51" />
<hkern u1="&#x2122;" u2="&#xc4;" k="51" />
<hkern u1="&#x2122;" u2="&#xc3;" k="51" />
<hkern u1="&#x2122;" u2="&#xc2;" k="51" />
<hkern u1="&#x2122;" u2="&#xc1;" k="51" />
<hkern u1="&#x2122;" u2="&#xc0;" k="51" />
<hkern u1="&#x2122;" u2="J" k="33" />
<hkern u1="&#x2122;" u2="A" k="51" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="6" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="d,q,dcaron,dcroat" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="29" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="w,wcircumflex" 	k="23" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="D,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="27" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="55" />
<hkern g1="D,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="25" />
<hkern g1="D,Dcaron,Dcroat" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="6" />
<hkern g1="D,Dcaron,Dcroat" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="d,q,dcaron,dcroat" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="6" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="12" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="w,wcircumflex" 	k="14" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="J,Jcircumflex" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="J,Jcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="K,Kcommaaccent" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="18" />
<hkern g1="K,Kcommaaccent" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="23" />
<hkern g1="K,Kcommaaccent" 	g2="d,q,dcaron,dcroat" 	k="29" />
<hkern g1="K,Kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="31" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="K,Kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="33" />
<hkern g1="K,Kcommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="20" />
<hkern g1="K,Kcommaaccent" 	g2="w,wcircumflex" 	k="45" />
<hkern g1="K,Kcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="43" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="K,Kcommaaccent" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="31" />
<hkern g1="K,Kcommaaccent" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="8" />
<hkern g1="K,Kcommaaccent" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="10" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="14" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="25" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="172" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="18" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="W,Wcircumflex" 	k="90" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="178" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="8" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="hyphen,endash,emdash" 	k="121" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteleft,quotedblleft" 	k="168" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteright,quotedblright" 	k="166" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quotedbl,quotesingle" 	k="168" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="29" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="w,wcircumflex" 	k="53" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="80" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="guillemotright,guilsinglright" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="27" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="57" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="J,Jcircumflex" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="8" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="45" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="d,q,dcaron,dcroat" 	k="16" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="16" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="14" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="8" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="31" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="w,wcircumflex" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="18" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="12" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="25" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="d,q,dcaron,dcroat" 	k="158" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="176" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="123" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="162" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="35" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex" 	k="119" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex" 	k="119" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="119" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="139" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="84" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="J,Jcircumflex" 	k="29" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="121" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="162" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="137" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="106" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="b" 	k="6" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="104" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="158" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="158" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="d,q,dcaron,dcroat" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="J,Jcircumflex" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="z,zacute,zdotaccent,zcaron" 	k="8" />
<hkern g1="W,Wcircumflex" 	g2="d,q,dcaron,dcroat" 	k="39" />
<hkern g1="W,Wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="45" />
<hkern g1="W,Wcircumflex" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="W,Wcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="41" />
<hkern g1="W,Wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="W,Wcircumflex" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="23" />
<hkern g1="W,Wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="41" />
<hkern g1="W,Wcircumflex" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="W,Wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="W,Wcircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="29" />
<hkern g1="W,Wcircumflex" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="W,Wcircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="29" />
<hkern g1="W,Wcircumflex" 	g2="z,zacute,zdotaccent,zcaron" 	k="10" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="53" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="59" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="37" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="d,q,dcaron,dcroat" 	k="141" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="150" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="hyphen,endash,emdash" 	k="137" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="143" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="t,tcaron,tbar,uni021B" 	k="35" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="w,wcircumflex" 	k="86" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="y,yacute,ydieresis,ycircumflex" 	k="78" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="127" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="113" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="104" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="J,Jcircumflex" 	k="55" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="147" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="131" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="123" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="86" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="colon,semicolon" 	k="82" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="143" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="100" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="31" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="w,wcircumflex" 	k="23" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="150" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="133" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="w,wcircumflex" 	k="6" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="b,p" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="12" />
<hkern g1="b,p" 	g2="T,Tcaron,Tbar,uni021A" 	k="160" />
<hkern g1="b,p" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="b,p" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="b,p" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="137" />
<hkern g1="b,p" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="b,p" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="b,p" 	g2="quotedbl,quotesingle" 	k="29" />
<hkern g1="b,p" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="b,p" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="12" />
<hkern g1="b,p" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="b,p" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="18" />
<hkern g1="b,p" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="176" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex" 	k="8" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="100" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="104" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="82" />
<hkern g1="d,dcroat" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="166" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="166" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="123" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="66" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="113" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W,Wcircumflex" 	k="6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="88" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="119" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="123" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="76" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="86" />
<hkern g1="guillemotright,guilsinglright" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="w,wcircumflex" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="45" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="123" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="137" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="125" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="135" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="w,wcircumflex" 	k="6" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="47" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="49" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="k,kcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="129" />
<hkern g1="k,kcommaaccent" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="k,kcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="78" />
<hkern g1="k,kcommaaccent" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="k,kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash" 	k="59" />
<hkern g1="k,kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="23" />
<hkern g1="k,kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="l,lacute,lcommaaccent,lslash" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="dcaron,lcaron" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="dcaron,lcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-45" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-39" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex" 	k="-20" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="-35" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="68" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="80" />
<hkern g1="dcaron,lcaron" 	g2="b" 	k="-98" />
<hkern g1="dcaron,lcaron" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="6" />
<hkern g1="dcaron,lcaron" 	g2="h,k,germandbls,hcircumflex,hbar,kcommaaccent" 	k="-98" />
<hkern g1="dcaron,lcaron" 	g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	k="-98" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,lcommaaccent,lcaron,lslash" 	k="-86" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="166" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="139" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="y,yacute,ydieresis,ycircumflex" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="J,Jcircumflex" 	k="16" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="166" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="143" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T,Tcaron,Tbar,uni021A" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W,Wcircumflex" 	k="66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="147" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="260" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="266" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="276" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t,tcaron,tbar,uni021B" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w,wcircumflex" 	k="45" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis,ycircumflex" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q,dcaron,dcroat" 	k="47" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="J,Jcircumflex" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="285" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="d,q,dcaron,dcroat" 	k="59" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="160" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="88" />
<hkern g1="quoteright,quotedblright" 	g2="J,Jcircumflex" 	k="33" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="301" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="12" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="55" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q,dcaron,dcroat" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="137" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="27" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="92" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="74" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="276" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotright,guilsinglright" 	k="33" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="111" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="49" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="d,q,dcaron,dcroat" 	k="10" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="6" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="hyphen,endash,emdash" 	k="72" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="8" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="55" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="J,Jcircumflex" 	k="43" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="23" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="160" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="W,Wcircumflex" 	k="25" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="109" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="121" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="63" />
<hkern g1="t,tbar,uni021B" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="139" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex" 	k="29" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="123" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="J,Jcircumflex" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="w,wcircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="119" />
<hkern g1="w,wcircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="86" />
<hkern g1="w,wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="10" />
<hkern g1="w,wcircumflex" 	g2="hyphen,endash,emdash" 	k="6" />
<hkern g1="w,wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="6" />
<hkern g1="w,wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="29" />
<hkern g1="w,wcircumflex" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="w,wcircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="27" />
<hkern g1="w,wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="45" />
<hkern g1="w,wcircumflex" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="8" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="119" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="76" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="d,q,dcaron,dcroat" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="33" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="J,Jcircumflex" 	k="37" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="23" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,aringacute,aeacute" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="14" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="160" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="8" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex" 	k="12" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="104" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="45" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="J,Jcircumflex" 	k="6" />
</font>
</defs></svg> 