/*
* SENTINEL LAYOUT & THEME
* Designed by <PERSON><PERSON> ALTUN - <EMAIL>
* Supported Browsers > Google Chrome 3+ , Mozilla Firefox 3+ , Safari 3+ , Opera 3+ , <PERSON><PERSON><PERSON>rowser , Inernet Explorer 9+
* Copyright 2014 - PrimeFaces Theme Market - PrimeTek */

/* FONT-FACE*/
.FontTitilliumRegular{font-family: 'titillium_webregular';}
.FontTitilliumBoldItalic{font-family: 'titillium_webbold_italic';}
.FontTitilliumSemiBoldItalic{font-family: 'titillium_websemibold_italic';}


/*BODY*/
body{background-color: #F6F8F8; margin:0px;}
.GRAYback{background-color:#F7F7F7;}

/* LOGIN PAGE */
.login-back{background-image:url("/sentinel/javax.faces.resource/images/login-back.svg.xhtml?ln=sentinel-layout"); background-position:top left; background-repeat:no-repeat; background-size: 50%;}
.error-back{background-image:url("/sentinel/javax.faces.resource/images/error-back.svg.xhtml?ln=sentinel-layout"); background-position:right bottom; background-repeat:no-repeat; background-size: 900px; background-attachment: fixed;}

    #login-logo{width:auto; margin-bottom:-2px; padding:0px; margin:18px; line-height: 30px; display:inline-block;}
        #login-logo img{height:30px;}
                    
    #login-box{min-height: 292px; margin-bottom: 20px; overflow: hidden;/* -webkit-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.2); -moz-box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.2); box-shadow: 0px 1px 2px 0px rgba(0,0,0,0.2); */ border: solid 1px #C0C5C7;}
        .TabBtn{background-color:#F4F7F9; padding: 20px 0px; cursor:pointer; border-bottom: solid 1px #E6E8E8; color:#96A5AE; font-size: 22px;}
        .TabBtn:hover{color:#00AEEF;}
        .TabBtnActiveLeft{ background-color: #ffffff; cursor:auto; border-bottom:solid 1px #E6E8E8; color:#00AEEF; border-right:solid 1px #E6E8E8;}
        .TabBtnActiveRight{ background-color: #ffffff; cursor:auto; border-bottom:solid 1px #E6E8E8; color:#00AEEF; border-left:solid 1px #E6E8E8;}
        
        #login-box{font-size: 14px;}
            #login-box input[type="text"],#login-box input[type="password"]{border: solid 1px #C0C5C7 !important; padding: 10px !important; box-shadow: none; color:#00AEEF !important; text-align: center;}
            #login-box input[type="text"]:hover, #login-box input[type="text"]:focus,
            #login-box input[type="password"]:hover, #login-box input[type="password"]:focus{border:solid 1px #00AEEF !important;}
            #login-box .ui-button{border:none !important; box-shadow: none; width:85%;}
                #login-box .ui-button .ui-button-text{padding:16px !important; font-size: 18px !important;}


	/* HEADER BAR */
	#layout-header{width:100%; display:block; position:fixed; z-index:1000; box-sizing:content-box;
					border-bottom:solid 1px #0b4170; border-top:solid 1px #0b4170;
					background: #1578c9;
					background: -moz-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -webkit-gradient(left top, left bottom, color-stop(0%, #1578c9), color-stop(100%, #0b66b1));
					background: -webkit-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -o-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: -ms-linear-gradient(top, #1578c9 0%, #0b66b1 100%);
					background: linear-gradient(to bottom, #1578c9 0%, #0b66b1 100%);
					-webkit-box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.4);
					-moz-box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.4);
					box-shadow: 0px 0px 2px 0px rgba(0,0,0,0.4);}
		#layout-logo{width:auto; margin-bottom:-2px; padding:0px; margin:18px; float:left; line-height: 16px;}
                    #layout-logo img{height:16px;}
                    
		.layout-header-widgets{margin:0px; padding:0px; position:absolute; right:10px; top:6px;}
			.layout-header-widgets li{background-color:#0f446c; width:34px; height:34px; margin-left:10px; /*for background transition overflow problem of safari*/-webkit-backface-visibility: hidden;}
			.layout-header-widgets li:hover{background-color:#165686;}
				.layout-header-widgets li i{margin-top:7px; display:block;cursor:pointer}
				.alertBubble{background-color:#e90c45; color:#ffffff; overflow:hidden; padding:3px 4px; display:inline-block; position:absolute; margin-top:-5px;}
				
				.layout-header-widgets-submenu{width:200px; padding:14px; margin:0px; position:relative; margin-top:8px; margin-left:-180px; overflow:auto; display:none; max-height: 300px;
											   background-image:url("/sentinel/javax.faces.resource/images/popupBlueArrow.png.xhtml?ln=sentinel-layout"); background-repeat:no-repeat; background-position:top right;}
					.layout-header-widgets-submenu li{padding:0px; margin:0px; margin-left:0px !important; border-bottom:solid 1px #083359; width:100% !important; height:auto !important; 
													display:block; background-color:#0b4170;}
						.layout-header-widgets-submenu li i{float:left; margin:0px 10px !important;}
						.layout-header-widgets-submenu li a{padding:10px; display:block; text-align:left; overflow:hidden;}
	
	/* MENU BAR */
	#layout-menubar{float:left; padding-top:55px; background-color: #3D3D3D; position:relative; z-index: 998;}
	.bigmenu{width:260px; border-right: solid 1px #272727;}
		.layout-menubarinner-box{width:230px; display:table; padding:10px 15px; overflow: hidden;}
			#layout-menubar-resize{display:inline-block; padding:5px; background-color:#2B2B2B; color:#FFD100; float:right;}
			#layout-menubar-resize2{display:none; padding:5px; background-color:#2B2B2B; color:#FFD100; float:right;}
			.layout-menubar-search{width:200px; padding:0px 0px 3px 0px; border:0px; border:none; background-color:transparent; outline:none; border-bottom:solid 1px #5A5A5A;
								   color:#5A5A5A !important; font-size:14px;}
			.layout-menubar-search:focus{color:#939393 !important;}
		.layout-menubar-container{width:260px; padding:0px; margin-bottom:60px;}
			#layout-menubar li {width:100%; padding:0px; color:#D9D9D9; font-size:14px; cursor:pointer;}
			#layout-menubar li:hover{background-color:#303030;}
			.layout-menubar-active{background-color:#303030; text-shadow: 0 1px 0 #000000;}
				.layout-menubar-active i{}
				#layout-menubar li a{color:#D9D9D9; padding:15px; display:block; border-bottom:solid 1px #4D4D4D; -webkit-transition: all 0.5s ease;
                                                          -moz-transition: all 0.5s ease; -ms-transition: all 0.5s ease; -o-transition: all 0.5s ease; transition: all 0.5s ease;}
                                #layout-menubar li a:hover{color:#FFD100; padding-left:20px;}
			.layout-menubar-submenu-container{padding:0px; overflow:hidden;display: none;}
			.layout-menubar-submenu-open{background-color:#353333;}
			
			#layout-menubar.layout-menubar-open-fullscr{height:100%; overflow-y:scroll; max-height:100%;}
				#layout-menubar.layout-menubar-open-fullscr li{width:100%;}
			
	/* SEARCH AREA */

	.slimsearch:hover{position:relative; background-color: #303030; width:280px !important;}
            .slimsearch:hover input{display:inline-block !important;}
	.slimsearch i{margin-left: 6px; margin-right:13px;}
	.slimsearch:hover i{color:#FFD100;}
	.slimsearch:hover input{border-bottom:0px !important; margin-bottom: -1px;}
			
	/* MENUBAR SLIM */
	.slimmenu{width:50px; z-index: 999 !important;}
		.slimmenu .layout-menubarinner-box{padding:10px 9px;}
			.slimmenu #layout-menubar-resize{float:left;}
                        .slimmenu .layout-menubar-container{width:48px;}
                        .slimmenu .layout-menubarinner-box{width:48px;}
                            .slimmenu .layout-menubarinner-box input{display:none;}
			.slimmenu .layout-menubar-container>li{height:50px; overflow:hidden;}
			#layout-menubar.slimmenu .layout-menubar-container>li:hover{position:relative; width:330px; min-height:50px !important; height:auto;}
                            .slimmenu li a{width:300px; border-bottom: 1px solid #4D4D4D;}
                            .slimmenu li a:hover{color:#FFD100; padding-left:25px;}
                            .slimmenu li:hover a{display:block; width:auto;}
                                .slimmenu .layout-menubar-container li a .i{ margin-right:20px;}
	
	/* PORTLETS COVER */
	#layout-portlets-cover{background-color: #ffffff; overflow-y:hidden; padding-top: 55px; color:#72828B; overflow-x:auto;}

    #sm-mobiletopmenu {
        display: none;
    }

/* OTHERS -----------------------------------------------------------------*/
a{text-decoration:none; outline:none;}
img{outline:none;}
li{list-style:none;}
.showCode{width:98%; max-width:98%; padding:10px; border:0px; overflow-x:hidden; background-color:transparent; min-height:100px;}
.Top20Percent{top:20%;}

/* button colors */
.RedButton .ui-button-text{box-shadow: inset 0 0 0 1px #F23030; border: solid 1px #871717; text-shadow: 0 -1px 0 #670606;
background: #c91515;
background: -moz-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #c91515), color-stop(100%, #b10b0b));
background: -webkit-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: -o-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: -ms-linear-gradient(top, #c91515 0%, #b10b0b 100%);
background: linear-gradient(to bottom, #c91515 0%, #b10b0b 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c91515', endColorstr='#b10b0b', GradientType=0 );}
.RedButton:hover .ui-button-text, .RedButton:focus .ui-button-text{color:#ffffff !important;
background: #b10b0b;
background: -moz-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #b10b0b), color-stop(100%, #c91515));
background: -webkit-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: -o-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: -ms-linear-gradient(top, #b10b0b 0%, #c91515 100%);
background: linear-gradient(to bottom, #b10b0b 0%, #c91515 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b10b0b', endColorstr='#c91515', GradientType=0 );}

.GreenButton .ui-button-text{box-shadow: inset 0 0 0 1px #54DB26; border: solid 1px #2B8717; text-shadow: 0 -1px 0 #18840B;
background: #2dc915;
background: -moz-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #2dc915), color-stop(100%, #0bb113));
background: -webkit-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: -o-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: -ms-linear-gradient(top, #2dc915 0%, #0bb113 100%);
background: linear-gradient(to bottom, #2dc915 0%, #0bb113 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2dc915', endColorstr='#0bb113', GradientType=0 );}
.GreenButton:hover .ui-button-text, .GreenButton:focus .ui-button-text{color:#ffffff !important;
background: #0bb113;
background: -moz-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #0bb113), color-stop(100%, #2dc915));
background: -webkit-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: -o-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: -ms-linear-gradient(top, #0bb113 0%, #2dc915 100%);
background: linear-gradient(to bottom, #0bb113 0%, #2dc915 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#0bb113', endColorstr='#2dc915', GradientType=0 );}

.OrangeButton .ui-button-text{box-shadow: inset 0 0 0 1px #DBB626; border: solid 1px #876C17; text-shadow: 0 -1px 0 #7B6308;
background: #c99f15;
background: -moz-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #c99f15), color-stop(100%, #b1900b));
background: -webkit-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: -o-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: -ms-linear-gradient(top, #c99f15 0%, #b1900b 100%);
background: linear-gradient(to bottom, #c99f15 0%, #b1900b 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#c99f15', endColorstr='#b1900b', GradientType=0 );}
.OrangeButton:hover .ui-button-text, .OrangeButton:focus .ui-button-text{color:#ffffff !important;
background: #b1900b;
background: -moz-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: -webkit-gradient(left top, left bottom, color-stop(0%, #b1900b), color-stop(100%, #c99f15));
background: -webkit-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: -o-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: -ms-linear-gradient(top, #b1900b 0%, #c99f15 100%);
background: linear-gradient(to bottom, #b1900b 0%, #c99f15 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#b1900b', endColorstr='#c99f15', GradientType=0 );}


/* shadow for elements*/
.shadows{-webkit-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5); -moz-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5); box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5);}

/* font colors */
.gray{color:#5A5A5A;}
.softgray{color:#909090;}
.yellow{color:#FFD100;}
.white{ color:#ffffff;}
.red{color:#e90c45;}
.orange{color:#F15A29;}
.softblue{color:#C1E1F4;}
.hardblue{color:#27AAE1;}
.leaden{color:#72828B;}

/* bordered boxes */
.GrayBorderedBox{ border:solid 1px #C0C5C7;}
.BorBotGray{ border-bottom:solid 1px #C0C5C7;}
.BorBotLeaden{border-bottom: solid 1px #E4E9EC;}


/* background colors */
.white-back{ background-color:#ffffff;}
.red-back{ background-color:#EF3C56;}
.gray-back{background-color:#F7F7F7;}
.leaden-back{background-color: #FBFCFD;}
.orange-back{ background-color:#F15A29;}

/* background colors */
.softBrownBack{background-color:#DBD0BD;}
.softGreenBack{background-color:#8BD8B1;}
.softOrangeBack{background-color:#F9BB70;}
.softYellowBack{background-color:#FFE771;}
.softRedBack{background-color:#EF7F88;}
.softBlueBack{background-color:#6BCCED;}
.softGrayBack{background-color:#F2F2F2;}

/* margin for layout menu bar links*/
.marginLevel-1{margin-left:20px;}
.marginLevel-2{margin-left:40px;}
.marginLevel-3{margin-left:60px;}
.marginLevel-4{margin-left:80px;}

/* fonts */
.fontRegular{font-family: 'titillium_webregular' !important;}
.fontItalic{font-style:italic;}

.fontSemibold{font-family: 'titillium_websemibold';}
.fontSemiboldItalic{font-family: 'titillium_websemibold_italic';}

.fontBold{font-family: 'titillium_webbold';}
.fontBoldItalic{font-family: 'titillium_webbold_italic';}


/* Font Icons Page CSS ======================================================================================================== */
.row {
  margin-left: -20px;
  *zoom: 1;
}
.row:before,
.row:after {
  display: table;
  content: "";
  line-height: 0;
}
.row:after {
  clear: both;
}
[class*="span"] {
  float: left;
  min-height: 1px;
  margin-left: 20px;
}
.fi-container,
.navbar-static-top .fi-container,
.navbar-fixed-top .fi-container,
.navbar-fixed-bottom .fi-container {
  width: auto;
}
.span12 {
  width: 940px;
}
.span11 {
  width: 860px;
}
.span10 {
  width: 780px;
}
.span9 {
  width: 700px;
}
.span8 {
  width: 620px;
}
.span7 {
  width: 540px;
}
.span6 {
  width: 460px;
}
.span5 {
  width: 380px;
}
.span4 {
  width: 300px;
}
.span3 {
  width: 23%;
}
.span2 {
  width: 140px;
}
.span1 {
  width: 60px;
}
[class*="span"].pull-right,
.row-fluid [class*="span"].pull-right {
  float: right;
}
.fi-container {
  *zoom: 1;
}
.fi-container:before,
.fi-container:after {
  display: table;
  content: "";
  line-height: 0;
}
.fi-container:after {
  clear: both;
}
p {
  margin: 0 0 10px;
}
.lead {
  margin-bottom: 20px;
  font-size: 21px;
  font-weight: 200;
  line-height: 30px;
}
small {
  font-size: 85%;
}
.the-icons {
  font-size: 20px;
  line-height: 40px;
}
.switch {
  position: absolute;
  right: 0;
  bottom: 10px;
  color: #666;
}
.switch input {
  margin-right: 0.3em;
}
.codesOn .i-name {
  display: none;
}
.codesOn .i-code {
  display: inline;
}
.i-code {
  display: none;
}

#buttonArea {
    display: block;
}

#sm-topmenu>li:focus,
#sm-topmenu a:focus{background-color: #165686 !important;}

#buttonArea > a:focus
{-webkit-box-shadow: inset 0px 0px 0px 1px rgba(100,100,100,1);
-moz-box-shadow: inset 0px 0px 0px 1px rgba(100,100,100,1);
box-shadow: inset 0px 0px 0px 1px rgba(100,100,100,1);
}

#layout-menubar .layout-menubar-container li a:focus {color: #FFD100; padding-left: 20px;}


/* MEDIA QUERIES **************************************************************************************************************** */
@media (min-width: 1201px) {
    #layout-menubar {
        width:260px; 
        border-right: solid 1px #272727;
    }
    
    #layout-menubar.slimmenu {
        width:50px;
    }
}

@media (min-width: 641px) {
    #sm-topmenu li,
    #sm-topmenu li.active-topmenuitem{
        height: 34px;
    }
        
    #layout-menubar.layout-menubar-open-fullscr{height:auto; overflow-y:visible; max-height:auto;}
        #layout-menubar.layout-menubar-open-fullscr li{width:100%;}
        
    body.OvHidden {
        overflow: visible;
    }
}

@media (min-width: 641px) and (max-width: 1200px) {
    #buttonArea {
        display: none;
    }
    
    /* Slim Search */
    #searchArea{width:48px}
	#searchArea:hover{position:relative; background-color: #303030; width:280px !important; }
            #searchArea:hover input{display:inline-block !important;}
	#searchArea i{margin-left: 6px; margin-right:13px;}
	#searchArea:hover i{color:#FFD100;}
	#searchArea:hover input{border-bottom:0px; margin-bottom: -1px;}
    
    /* Slim Menu */
    #layout-menubar{width:50px; z-index:999 !important;}
		#layout-menubar .layout-menubarinner-box{padding:10px 9px;}
			#layout-menubar #layout-menubar-resize{float:left;}
                        #layout-menubar .layout-menubar-container{width:48px;}
                        #layout-menubar .layout-menubarinner-box{width:48px;}
                            #layout-menubar .layout-menubarinner-box input{display:none;}
			#layout-menubar .layout-menubar-container>li{height:50px; overflow:hidden;}
			#layout-menubar .layout-menubar-container>li:hover{position:relative; width:330px; min-height:50px !important; height:auto;}
                            #layout-menubar li a{width:300px; border-bottom: 1px solid #4D4D4D;}
                            #layout-menubar li a:hover{color:#FFD100; padding-left:25px;}
                            #layout-menubar li:hover a{display:block; width:auto;}
                                #layout-menubar .layout-menubar-container li a .i{ margin-right:20px;}
}
  
/* Landscape phone to portrait tablet */
@media (max-width: 640px) {
	#layout-logo{margin:15px 2%;}
	#layout-menubar, .slimmenu{position:fixed; background-color: #3D3D3D; width:100%; min-height:50px !important; max-height:50px; overflow:hidden; z-index:999;}
		.layout-menubar-container{width:100%;}
		#layout-menubar-resize{display:none;}
		#layout-menubar-resize2{display:inline-block;}
		#buttonArea{width:auto; padding:1% 2%; float:right;}
		.layout-menubar-container{margin-top: 55px;}
		.layout-menubar-search{border-bottom:none;}

		#sm-topmenu{padding:0px; width:50%; max-height:80%; position:fixed; top:50px; padding:14px; display: none;
						-webkit-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5); -moz-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5); box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.5);
						border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px;
						background-image:url("/sentinel/javax.faces.resource/images/popupBlueArrow.png.xhtml?ln=sentinel-layout"); background-repeat:no-repeat; background-position:right top;}
			#sm-topmenu>li{width:100%; border-radius:0px; -webkit-border-radius:0px; -moz-border-radius:0px; margin:0px; border-bottom:solid 1px #083359; min-height:47px; height:47px; font-size:11px; overflow:hidden;}
				#sm-topmenu>li>i{font-size:20px; overflow:hidden; margin:10px 0px; display:block;}
				#sm-topmenu>li .alertBubble{ margin:-20px 0px 0px 10px; right: 52%;}
				
				#sm-topmenu>li ul{width:auto; position: static; display: block; height: auto; margin: 0px; padding: 0px; webkit-box-shadow: none; -moz-box-shadow: none; box-shadow: none; top:0; right:0;
									  border-radius:0px; -webkit-border-radius:0px; -moz-border-radius:0px;}
                #sm-topmenu.DispBlock {
                    display: block;
                    overflow-y: auto;
                }
                
                #sm-topmenu li.active-topmenuitem {
                    height: auto;
                }  
                
	#layout-portlets-cover{padding-top:110px;}
        
        /* LOGIN */
        .login-back{ background-size: 100%; background-position: -200px;}
        #login-logo{ line-height: 26px;}
        #login-logo span{font-size: 26px !important;}
        #login-logo img{ height:26px;}
        .TabBtn{ font-size: 16px;}
        #login-box input[type="text"]{border: solid 1px #C0C5C7; padding: 10px !important; box-shadow: none; color:#00AEEF !important; text-align: center; font-size: 16px;}
        #login-box .ui-button{border:none !important; box-shadow: none; width:90%;}
            #login-box .ui-button .ui-button-text{padding:12px !important; font-size: 16px !important;}
            
    #sm-mobiletopmenu {
        display: block;
    }	
    
    #searchArea{width:auto !important; padding:2% !important; float:left !important;}
    .slimmenu #searchArea.layout-menubarinner-box input{display:inline-block;}
    #searchArea.slimsearch:hover{position:relative; background-color: transparent; width: auto;}
            #searchArea.slimsearch:hover input{display:inline-block !important;}
	#searchArea.slimsearch i{margin-left: 0px; margin-right:0px;}
	#searchArea.slimsearch:hover i{color:#5A5A5A;}
}
 
/* Portrait phones and down */
@media (min-width:320px) and (max-width: 480px) {
	#layout-logo{margin:15px 2%;}
	#layout-menubar, .slimmenu{position:fixed; background-color: #3D3D3D; width:100%; min-height:50px !important; max-height:50px; overflow:hidden; z-index:999;}
		.layout-menubar-container{width:100%;}
		#layout-menubar-resize{display:none;}
		#layout-menubar-resize2{display:inline-block;}
		#buttonArea{width:auto; padding:1.5% 2%; float:right;}
		#searchArea{width:auto; padding:2.5% 2% 2.5% 2%; float:right;}
		.layout-menubar-container{margin-top: 55px;}
		.layout-menubar-search{border-bottom:none;}
		        
        /* LOGIN */
        .login-back{ background-size: 300%; background-position: -300px;}
        #login-logo{ line-height: 20px;}
        #login-logo span{font-size: 20px !important;}
        #login-logo img{ height:20px;}
        .TabBtn{ font-size: 12px;}
        #login-box input[type="text"]{border: solid 1px #C0C5C7 !important; padding: 10px !important; box-shadow: none; color:#00AEEF !important; text-align: center; font-size: 14px !important;}
        #login-box .ui-button{border:none !important; box-shadow: none; width:90%;}
            #login-box .ui-button .ui-button-text{padding:12px !important; font-size: 14px !important;}
}

#layout-portlets-cover:before,#layout-portlets-cover:after {
	content: "";
	display: table;
	border-collapse: collapse;
}

#layout-portlets-cover:after {
    clear: both; 
}