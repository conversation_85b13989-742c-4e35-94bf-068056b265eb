<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="titillium_websemibold_italic" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="356" d="M68 0l63 276h225l-63 -276h-225zM188 504l205 930h234l-232 -930h-207z" />
<glyph unicode="&#x22;" horiz-adv-x="632" d="M336 922l115 493h387l-129 -493h-373z" />
<glyph unicode="#" d="M115 350l47 189h209l69 280h-209l48 187h208l88 352h195l-88 -352h258l88 352h195l-88 -352h208l-45 -187h-211l-69 -280h211l-47 -189h-209l-88 -350h-195l86 350h-256l-88 -350h-194l86 350h-209zM565 539h258l70 280h-258z" />
<glyph unicode="$" d="M186 47l37 160q111 -18 223 -29l187 440q-133 57 -190.5 127t-57.5 195q0 195 126 315.5t339 120.5q51 0 102 -6l92 219h150l-102 -239l153 -39l-41 -158q-88 14 -184 25l-176 -410q143 -63 195.5 -130t52.5 -192q0 -223 -135.5 -346t-366.5 -123q-29 0 -82 5l-94 -226 h-146l103 244zM590 170h2q274 0 274 252q0 53 -21.5 83t-86.5 60zM608 963q0 -53 22.5 -83t88.5 -61l155 371h-22q-115 0 -179.5 -60.5t-64.5 -166.5z" />
<glyph unicode="%" d="M262 12l811 1452l137 -47l-811 -1452zM303 1021q0 56 16 124q72 291 312 291q180 0 180 -170q0 -53 -16 -121q-74 -295 -310 -295q-115 0 -157 74q-25 41 -25 97zM471 1049q0 -60 47 -60q41 0 64.5 36t42 108.5t18.5 109.5q0 55 -47 55q-39 0 -63.5 -35.5t-43 -106.5 t-18.5 -107zM670 174q0 53 16 121q72 291 309 291q180 0 181 -170q0 -53 -17 -121q-73 -295 -309 -295q-180 0 -180 174zM836 195q0 -55 47 -56q39 0 63.5 37t43 108.5t18.5 108.5q0 55 -47 56q-39 0 -64 -36t-43 -109t-18 -109z" />
<glyph unicode="&#x26;" horiz-adv-x="1306" d="M139 335q0 175 99.5 294t310.5 200q-57 104 -57 211q0 190 109.5 287.5t289.5 97.5q332 0 332 -282q0 -150 -85 -244t-294 -176l186 -254q29 41 62.5 121t48.5 139h207q-25 -94 -86.5 -215t-114.5 -190l176 -213l-154 -134l-161 207q-197 -207 -461 -207 q-223 0 -315.5 91.5t-92.5 266.5zM371 346q0 -178 217 -178q82 0 169 44t140 110l-266 374q-145 -61 -202.5 -146t-57.5 -204zM715 1023q0 -103 49 -175q137 63 186 123.5t49 152.5q0 115 -112 115q-84 0 -128 -56.5t-44 -159.5z" />
<glyph unicode="'" horiz-adv-x="303" d="M262 922l115 493h207l-129 -493h-193z" />
<glyph unicode="(" horiz-adv-x="473" d="M131 254q0 184 49 383t119 352q145 319 258 475l49 68h207q-55 -82 -144 -253t-148.5 -317.5t-109 -343t-49.5 -344.5q0 -276 25 -462l8 -64h-211q-53 188 -53 506z" />
<glyph unicode=")" horiz-adv-x="475" d="M-41 -242q55 82 144.5 253t148.5 317.5t108.5 343t49.5 344.5q0 276 -25 463l-8 63h211q53 -188 53 -506q0 -184 -49 -382.5t-119 -352.5q-145 -319 -258 -475l-49 -68h-207z" />
<glyph unicode="*" horiz-adv-x="858" d="M342 938l217 162l-211 151l80 111l211 -154l82 252l131 -43l-80 -254h258v-131h-260l80 -250l-127 -39l-84 252l-215 -161z" />
<glyph unicode="+" d="M225 414v200h363v367h200v-367h369v-200h-369v-371h-200v371h-363z" />
<glyph unicode="," horiz-adv-x="366" d="M-72 -266l224 542h243l-284 -542h-183z" />
<glyph unicode="-" horiz-adv-x="608" d="M147 444l52 201h512l-49 -201h-515z" />
<glyph unicode="." horiz-adv-x="325" d="M51 0l62 276h225l-62 -276h-225z" />
<glyph unicode="/" horiz-adv-x="915" d="M80 37l881 1423l206 -74l-878 -1423z" />
<glyph unicode="0" d="M201 393q0 272 77.5 495.5t222 354.5t328.5 131q428 0 428 -419.5t-177 -698.5t-453 -279q-426 0 -426 416zM434 440.5q0 -145.5 46 -206t158 -60.5t205 124t137 293t44 316.5t-46 210t-158.5 62.5t-206 -128t-136.5 -297t-43 -314.5z" />
<glyph unicode="1" d="M430 1065l508 287h199l-312 -1352h-227l250 1081l-356 -196z" />
<glyph unicode="2" d="M133 0l45 186l457 392q196 167 279 262.5t83 180.5t-51 122t-145 37q-160 0 -320 -29l-53 -8l29 168q201 66 391 65q383 0 383 -307q0 -162 -87 -280.5t-272 -262.5l-426 -329h603l-48 -197h-868z" />
<glyph unicode="3" d="M131 49l49 160q229 -35 369.5 -35t232.5 71.5t92 196.5q0 74 -47 110t-155 38l-262 2l43 172l264 10q92 2 182 78t90 176q0 152 -205 152q-152 0 -311 -25l-53 -8l22 153q188 74 390 74t296.5 -69.5t94.5 -213t-62.5 -229.5t-197.5 -147q78 -41 110.5 -97.5t32.5 -164.5 q0 -215 -157.5 -345.5t-409.5 -130.5q-90 0 -191.5 17.5t-159.5 36.5z" />
<glyph unicode="4" d="M150 240l38 168l600 944h248l-604 -922h361l92 397h225l-92 -397h141l-45 -190h-139l-57 -248h-226l58 248h-600z" />
<glyph unicode="5" d="M135 57l51 176q233 -59 382 -59t245 93t96 225q0 164 -190 163q-109 0 -223 -45l-37 -16l-168 37l221 723h791l-46 -197h-608l-133 -379q163 74 272 74q354 0 355 -315q0 -242 -157 -401t-392 -159q-100 0 -215 19.5t-178 40.5z" />
<glyph unicode="6" d="M215 451q0 152 49 310.5t139.5 297.5t237.5 227t324 88q129 0 288 -33l52 -10l-70 -184q-162 33 -324 33q-283 0 -411 -398q174 78 315 78q367 0 367 -344q0 -242 -144.5 -390.5t-406.5 -148.5q-197 0 -306.5 119t-109.5 355zM449 425q0 -97 48 -174t146 -77 q150 0 227.5 84t77.5 231q0 174 -182 175q-147 0 -262 -50l-37 -14q-18 -78 -18 -175z" />
<glyph unicode="7" d="M266 31l742 1044l16 80h-604l45 197h831l-69 -303l-754 -1082z" />
<glyph unicode="8" d="M133 297q0 133 88 235.5t209 167.5q-72 57 -92.5 103.5t-20.5 111.5q0 184 155 322.5t431 138.5q197 0 300.5 -75.5t103.5 -196.5q0 -268 -271 -404q137 -80 137.5 -260t-168.5 -323.5t-454 -143.5q-418 0 -418 324zM381 342q0 -90 53 -131t186.5 -41t223.5 83t90 179 q0 66 -30 100.5t-103 59.5h-191q-98 -31 -163.5 -95.5t-65.5 -154.5zM551 938q0 -55 22.5 -89t83.5 -67h191q82 35 148.5 99.5t66.5 138.5q0 160 -211 160q-137 0 -219 -74t-82 -168z" />
<glyph unicode="9" d="M162 23l69 196q133 -33 290 -33t272.5 98.5t160.5 276.5q-221 -63 -336 -63q-344 0 -344 327q0 229 163 389t427 160q203 0 291 -147q70 -117 70 -314q0 -49 -4 -104q-12 -205 -77 -365.5t-167 -261.5q-209 -205 -504 -205q-135 0 -268 35zM498 852q0 -72 41 -111 t149.5 -39t261.5 35l51 11q21 91 21 167q0 252 -209 252q-121 0 -212 -75.5t-101 -198.5q-2 -23 -2 -41z" />
<glyph unicode=":" horiz-adv-x="333" d="M53 0l62 276h225l-61 -276h-226zM203 637l61 276h225l-61 -276h-225z" />
<glyph unicode=";" horiz-adv-x="380" d="M-63 -266l223 542h243l-284 -542h-182zM238 637l61 276h225l-61 -276h-225z" />
<glyph unicode="&#x3c;" d="M227 422v184l840 393v-227l-604 -254l604 -266v-229z" />
<glyph unicode="=" d="M244 211v201h897v-201h-897zM244 616v201h897v-201h-897z" />
<glyph unicode="&#x3e;" d="M313 23v229l605 266l-605 254v227l840 -393v-184z" />
<glyph unicode="?" horiz-adv-x="868" d="M264 0l68 299h225l-68 -299h-225zM348 1196l23 176q182 53 323 53q359 0 359 -297q0 -143 -65.5 -231t-256.5 -227q-123 -92 -139 -172l-19 -82h-208q0 86 14 159q20 111 199 242q139 102 189 159.5t50 129.5t-43 96.5t-117 24.5q-143 0 -264 -23z" />
<glyph unicode="@" horiz-adv-x="1941" d="M166 285q0 129 33 264q119 500 384 717t722 217q580 0 712 -365q39 -104 39 -221q0 -131 -41 -291q-88 -352 -202.5 -489t-296 -137t-226.5 100q-72 -37 -187.5 -70t-207.5 -33t-156.5 47.5t-85.5 120.5q-25 90 -24.5 175.5t16.5 152.5q111 573 518 574q109 0 457 -60 q-27 -106 -91.5 -396t-64.5 -330t22.5 -55.5t72.5 -15.5q78 0 136 94.5t117 344.5q37 156 37 238.5t-20.5 161.5t-83.5 138q-135 127 -485.5 127t-556 -183t-295.5 -562q-33 -135 -33 -244.5t31.5 -207t101.5 -171.5q147 -156 475 -155l281 18l-41 -188q-199 -18 -327 -18.5 t-260 37.5t-220 106q-250 195 -250 559zM850 340q0 -170 108 -172q123 0 285 90q8 94 121 569q-123 31 -205 31q-117 0 -184.5 -102.5t-106.5 -284.5q-18 -72 -18 -131z" />
<glyph unicode="A" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52z" />
<glyph unicode="B" horiz-adv-x="1177" d="M143 0l324 1403h473q354 0 354 -291q0 -256 -245 -389q84 -41 116.5 -97.5t32.5 -144.5q0 -213 -140 -347t-405 -134h-510zM418 197h280q123 0 194 75.5t71 202.5q-1 139 -177 139h-272zM557 807h256q106 0 177 73.5t71 188.5q0 72 -38 104.5t-120 32.5h-252z" />
<glyph unicode="C" horiz-adv-x="1019" d="M215 381q0 297 102 555q55 139 136.5 246.5t204 175t272.5 67.5q137 0 295 -41l51 -14l-53 -172q-139 29 -293 29t-264.5 -127t-162.5 -313.5t-52 -389.5q0 -221 202 -221q129 0 285 25l47 6l-16 -180q-186 -50 -344 -50q-410 0 -410 404z" />
<glyph unicode="D" horiz-adv-x="1241" d="M143 0l326 1403h444q430 0 430 -422q0 -362 -167 -657q-82 -145 -221.5 -234.5t-313.5 -89.5h-498zM420 199h244q111 0 200.5 80.5t140 203.5t77 252t26.5 247t-58.5 170t-181.5 52h-219z" />
<glyph unicode="E" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807z" />
<glyph unicode="F" horiz-adv-x="980" d="M143 0l326 1403h788l-47 -199h-561l-108 -481h471l-49 -199h-469l-121 -524h-230z" />
<glyph unicode="G" horiz-adv-x="1167" d="M219 399q0 326 113 584q125 279 352 385q125 57 246 57q219 0 389 -55l55 -18l-53 -166q-205 41 -387 41q-123 0 -218 -80t-151 -207q-110 -252 -110 -538v-5q0 -221 237 -221q82 0 156 17l24 4l86 370h-165l45 191h393l-170 -727q-155 -54 -395 -54q-446 0 -447 422z " />
<glyph unicode="H" horiz-adv-x="1273" d="M143 0l326 1403h227l-135 -590h526l138 590h227l-324 -1403h-227l139 614h-526l-141 -614h-230z" />
<glyph unicode="I" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230z" />
<glyph unicode="J" horiz-adv-x="563" d="M12 -143l39 200q104 2 140 36t67 173l264 1137h228l-265 -1141q-59 -268 -139 -336.5t-334 -68.5z" />
<glyph unicode="K" horiz-adv-x="1089" d="M143 0l326 1403h227l-145 -625l156 13l421 612h252l-491 -705l172 -698h-240l-139 590l-176 -12l-133 -578h-230z" />
<glyph unicode="L" horiz-adv-x="874" d="M143 0l326 1403h227l-276 -1204h489l-45 -199h-721z" />
<glyph unicode="M" horiz-adv-x="1607" d="M143 0l326 1403h362l41 -1094l547 1094h369l-324 -1403h-229l281 1204h-17l-571 -1147h-221l-43 1147h-15l-276 -1204h-230z" />
<glyph unicode="N" horiz-adv-x="1314" d="M143 0l326 1403h375l135 -1163h31l264 1163h221l-324 -1403h-358l-147 1178h-31l-275 -1178h-217z" />
<glyph unicode="O" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5z" />
<glyph unicode="P" horiz-adv-x="1120" d="M143 0l326 1403h420q207 0 301 -88t94 -281q0 -283 -153.5 -441.5t-425.5 -158.5h-232l-100 -434h-230zM518 633h219q150 0 232 108.5t82 290.5q0 88 -42 130t-139 42h-221z" />
<glyph unicode="Q" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-31 -27 -82 -51l99 -283l-215 -71l-90 291q-48 -7 -93 -7q-217 0 -327.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416 q0 131 -53.5 188.5t-175.5 57.5t-197 -68q-119 -109 -188.5 -327t-69.5 -380.5z" />
<glyph unicode="R" horiz-adv-x="1148" d="M143 0l326 1403h416q205 0 303 -88t98 -288t-97 -334t-237 -181l131 -512h-227l-115 481h-258l-110 -481h-230zM528 678h236q131 0 209 98t78 222t-41 166t-142 42h-217z" />
<glyph unicode="S" horiz-adv-x="1017" d="M109 49l36 166q236 -39 383 -39q285 0 285 260q0 72 -39 105.5t-185.5 93t-211 132.5t-64.5 206q0 203 131 327.5t353 124.5q92 0 193.5 -16t156.5 -33l57 -18l-41 -164q-221 37 -352 37t-198.5 -61.5t-67.5 -172.5q0 -70 40 -103.5t192.5 -95t211 -131t58.5 -204.5 q0 -231 -140.5 -358.5t-380.5 -127.5q-92 0 -196.5 17.5t-161.5 36.5z" />
<glyph unicode="T" horiz-adv-x="944" d="M297 1204l47 199h905l-47 -199h-340l-278 -1204h-228l277 1204h-336z" />
<glyph unicode="U" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308z" />
<glyph unicode="V" horiz-adv-x="1128" d="M350 0l8 1403h228l-25 -1204h78l545 1204h229l-649 -1403h-414z" />
<glyph unicode="W" horiz-adv-x="1738" d="M307 0l60 1403h229l-78 -1192h41l502 1171h250l-56 -1171h39l494 1192h227l-602 -1403h-350l55 1079l-450 -1079h-361z" />
<glyph unicode="X" horiz-adv-x="1058" d="M12 0l545 707l-203 696h242l135 -533l389 533h244l-537 -701l209 -702h-237l-146 537l-397 -537h-244z" />
<glyph unicode="Y" horiz-adv-x="1021" d="M334 1403h231l127 -594l402 594h241l-583 -807l-142 -596h-213l142 610z" />
<glyph unicode="Z" horiz-adv-x="1007" d="M76 0l55 248l805 905l12 51h-594l47 199h854l-57 -252l-809 -901l-12 -51h600l-47 -199h-854z" />
<glyph unicode="[" horiz-adv-x="530" d="M4 -250l416 1780h438l-45 -191h-209l-325 -1398h206l-47 -191h-434z" />
<glyph unicode="\" horiz-adv-x="940" d="M383 1430l233 8l269 -1430l-232 -8z" />
<glyph unicode="]" horiz-adv-x="528" d="M-33 -250l45 191h209l326 1398h-207l47 191h434l-415 -1780h-439z" />
<glyph unicode="^" d="M168 647l389 705h193l393 -705h-234l-254 488l-254 -488h-233z" />
<glyph unicode="_" horiz-adv-x="1067" d="M-10 -373l47 209h917l-47 -209h-917z" />
<glyph unicode="`" horiz-adv-x="1060" d="M532 1374l107 180l420 -188l-94 -162z" />
<glyph unicode="a" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447z" />
<glyph unicode="b" horiz-adv-x="1054" d="M129 39l324 1411h223l-123 -530q47 47 129 87t143 40q293 0 293 -324q0 -188 -57 -354t-180 -277.5t-291 -111.5q-184 0 -389 45zM385 190q111 -23 194 -23q2 1 4 1q85 0 147.5 60.5t94.5 148.5q66 180 66 323q0 72 -26.5 114t-89 42t-126 -32.5t-102.5 -65.5l-39 -35z " />
<glyph unicode="c" horiz-adv-x="833" d="M176 313q0 315 139.5 524.5t397.5 209.5q117 0 241 -39l43 -15l-57 -164q-147 25 -235 25q-145 0 -223.5 -148.5t-78.5 -342t179 -193.5q88 0 186 16l37 7l-17 -168q-146 -47 -292 -47q-1 0 -1 -1q-145 1 -232 89t-87 247z" />
<glyph unicode="d" horiz-adv-x="1056" d="M172 319.5q0 169.5 56.5 332.5t177 277.5t280.5 114.5q80 0 215 -28l43 -10l103 444h223l-336 -1450h-215l26 139q-14 -18 -40.5 -44.5t-104 -72t-149.5 -45.5q-137 0 -208 86.5t-71 256zM397 313q0 -147 125 -147q45 0 114 47t114 94l45 47l108 473q-125 29 -215 29 t-158.5 -92t-100.5 -214t-32 -237z" />
<glyph unicode="e" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-13 -47 -13 -115q-1 -67 47 -106t117 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102 q-213 0 -285 -268z" />
<glyph unicode="f" horiz-adv-x="655" d="M59 -442l297 1273h-102l45 193h100l15 61q49 213 127.5 300.5t234.5 87.5q84 0 158 -29l27 -10l-41 -166q-76 14 -137.5 14t-91 -47t-56.5 -152l-14 -59h241l-47 -190h-235l-195 -834l-127 -442h-199z" />
<glyph unicode="g" horiz-adv-x="976" d="M27 -201q0 100 71.5 168t186.5 125q-37 37 -37 89t29.5 101.5t58.5 77.5l31 29q-142 76 -142 249q0 172 115 290q115 119 305 119q82 0 191 -27l34 -8l301 12l-43 -182h-145q18 -47 18 -121q0 -156 -118.5 -272.5t-311.5 -116.5l-77 10q-45 -59 -45 -94t37.5 -48.5 t166.5 -30.5t207 -73.5t78 -177.5q0 -172 -135 -282.5t-348 -110.5q-428 0 -428 274zM231 -155.5q0 -55.5 50.5 -94.5t163 -39t197.5 45t85 138q0 49 -37 68.5t-152.5 36.5t-152.5 28q-57 -29 -105.5 -78t-48.5 -104.5zM432 651q0 -137 156 -137q94 0 151.5 65.5t57.5 145.5 q0 139 -162 139q-90 0 -146.5 -64.5t-56.5 -148.5z" />
<glyph unicode="h" horiz-adv-x="1038" d="M123 0l334 1450h221l-127 -559q16 18 43 44t102.5 69t143.5 43q125 0 185 -75t60 -180.5t-43 -283.5l-116 -508h-221l114 494q37 147 37 220.5t-23.5 106.5t-73.5 33t-112.5 -42t-103.5 -85l-41 -43l-158 -684h-221z" />
<glyph unicode="i" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM391 1176l55 239h226l-56 -239h-225z" />
<glyph unicode="j" horiz-adv-x="464" d="M-160 -297q170 86 212 131t71 166l235 1024h222l-236 -1024q-37 -156 -71.5 -225.5t-84.5 -102.5q-106 -74 -276 -147zM389 1165l55 238h224l-54 -238h-225z" />
<glyph unicode="k" horiz-adv-x="937" d="M123 0l334 1450h221l-191 -823l97 10l309 385h248l-391 -483l161 -539h-229l-131 453l-105 -9l-102 -444h-221z" />
<glyph unicode="l" horiz-adv-x="473" d="M125 0l336 1450h221l-334 -1450h-223z" />
<glyph unicode="m" horiz-adv-x="1587" d="M123 0l235 1024h213l-22 -133q6 6 15 16.5t41 38t67 47.5q86 53 171 53q1 0 2 1q84 0 137 -40q54 -40 67 -81l14 -41q18 18 50 44.5t118 72t160 45.5q125 0 184 -75t59 -177.5t-39 -286.5l-116 -508h-224l119 494q37 172 37 221q0 139 -102 139q-43 0 -102.5 -37 t-96.5 -74l-37 -36q-10 -111 -33 -199l-116 -508h-222l115 494q37 172 37 227q0 133 -109 133q-41 0 -102 -42t-102 -85l-39 -43l-158 -684h-221z" />
<glyph unicode="n" horiz-adv-x="1036" d="M123 0l235 1024h213l-22 -135q16 18 42.5 43.5t103.5 70t145 44.5q125 0 185 -75t60 -182.5t-43 -279.5l-116 -510h-221l114 494q37 172 37 233t-23.5 94t-75.5 33t-113.5 -42t-102.5 -85l-39 -43l-158 -684h-221z" />
<glyph unicode="o" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5z" />
<glyph unicode="p" horiz-adv-x="1054" d="M20 -440l338 1464h217l-28 -139q16 18 42.5 44.5t103.5 72t143 45.5q143 0 213.5 -88.5t70.5 -253t-56 -329.5t-177 -280.5t-281 -115.5q-104 0 -219 28l-41 10l-106 -458h-220zM389 199q125 -31 215 -31t158.5 93t100.5 213t32 233q0 149 -127 149q-43 0 -111.5 -47 t-115.5 -92l-45 -47z" />
<glyph unicode="q" horiz-adv-x="1052" d="M172 320.5q0 168.5 57.5 332.5t178 277.5t278.5 113.5q156 0 391 -45l82 -14l-325 -1421h-222l133 577q-6 -6 -15 -17t-41 -40t-64 -51q-86 -56 -172 -56q-137 0 -209 87.5t-72 256zM397 317q0 -149 123 -149h2q45 0 112.5 46t115.5 93l45 47l108 473q-125 29 -214 29 t-156.5 -89t-101.5 -211t-34 -239z" />
<glyph unicode="r" horiz-adv-x="667" d="M123 0l235 1024h220l-35 -150q70 55 169 107.5t173 65.5l-64 -230q-92 -27 -264 -108l-53 -25l-160 -684h-221z" />
<glyph unicode="s" horiz-adv-x="882" d="M98 37l41 166q195 -37 310 -37q219 0 219 149q0 39 -33 57.5t-150.5 49.5t-185.5 83t-68 152q0 178 121 282.5t297 104.5q170 0 309 -40l46 -13l-41 -166q-203 31 -336 31q-80 0 -126 -41t-46 -92t32.5 -71.5t120.5 -46t129 -42t82 -43.5q74 -47 74 -162 q0 -186 -125 -283.5t-328 -97.5q-156 0 -297 46z" />
<glyph unicode="t" horiz-adv-x="663" d="M215 181q0 89 31 216l102 437h-119l50 190h116l64 281h223l-66 -281h246l-47 -190h-244l-106 -455q-21 -86 -21 -137q0 -76 80 -76l148 12l-19 -162q-106 -39 -213.5 -39t-166 57.5t-58.5 146.5z" />
<glyph unicode="u" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200z" />
<glyph unicode="v" horiz-adv-x="940" d="M268 1024h219l-10 -840h49l392 840h225l-498 -1024h-369z" />
<glyph unicode="w" horiz-adv-x="1531" d="M244 0l41 1024h223l-45 -834h43l366 814h254l-18 -814h43l346 834h225l-438 -1024h-377l19 709l-312 -709h-370z" />
<glyph unicode="x" horiz-adv-x="870" d="M23 0l411 512l-170 512h228l104 -352l260 352h227l-413 -528l172 -496h-228l-102 342l-256 -342h-233z" />
<glyph unicode="y" horiz-adv-x="942" d="M211 -440l221 440h-156l-8 1024h219l-10 -840h45l396 840h225l-709 -1464h-223z" />
<glyph unicode="z" horiz-adv-x="851" d="M74 0l39 186l594 648h-443l47 190h705l-45 -184l-596 -650h450l-49 -190h-702z" />
<glyph unicode="{" horiz-adv-x="612" d="M147 571l39 164q129 33 172 75t64 128q8 31 31.5 158t25.5 135q51 168 156.5 238.5t304.5 78.5l-37 -192q-104 -8 -154.5 -54.5t-70.5 -132.5l-47 -251q-29 -121 -83 -181.5t-163 -97.5q127 -63 127 -172q0 -53 -47 -216t-47 -208q0 -117 145 -117l-53 -192 q-147 2 -229 65.5t-82 180.5q0 51 45 216.5t45 204.5q0 104 -142 170z" />
<glyph unicode="|" horiz-adv-x="665" d="M338 -440v1890h221v-1890h-221z" />
<glyph unicode="}" horiz-adv-x="610" d="M-35 -272l37 192q104 8 154.5 54t70.5 132l47 252q29 121 83 181.5t163 97.5q-127 63 -127 170q0 55 47 218t47 208q0 117 -145 117l53 192q147 -2 229.5 -65.5t82.5 -180.5q0 -51 -45.5 -217t-45.5 -205q0 -104 142 -169l-39 -164q-129 -33 -172 -75t-64 -128 q-8 -31 -31.5 -158t-25.5 -135q-51 -168 -156.5 -238.5t-304.5 -78.5z" />
<glyph unicode="~" d="M252 569q129 106 246 107q59 0 212.5 -52.5t194.5 -52.5q70 0 178 64l35 20l19 -178q-37 -37 -108 -71.5t-127 -34.5t-215 52t-197.5 52t-93 -21.5t-89.5 -43.5l-35 -21z" />
<glyph unicode="&#xa1;" horiz-adv-x="344" d="M-39 -410l232 930h206l-204 -930h-234zM231 748l64 276h225l-63 -276h-226z" />
<glyph unicode="&#xa2;" d="M307 403.5q0 38.5 21 106.5q53 211 170.5 322.5t283.5 121.5l52 224h180l-56 -228l162 -20l-55 -189q-156 10 -229 11q-127 0 -195 -58.5t-98 -183.5q-14 -55 -14.5 -102.5t20.5 -77.5q37 -57 178 -58l236 11l-37 -189q-94 -12 -174 -16l-54 -236h-178l49 224 q-92 4 -153.5 45t-83 103t-23.5 106.5t-2 83z" />
<glyph unicode="&#xa3;" d="M158 0l43 190h196l115 506h-160l45 191h158l23 88q55 225 140 320.5t261 95.5q82 0 238 -33l47 -10l-52 -170q-135 16 -202.5 16t-99 -15.5t-54.5 -54.5q-33 -59 -59 -176l-15 -61h355l-45 -191h-353l-116 -506h229l145 35l-4 -190l-155 -35h-680z" />
<glyph unicode="&#xa5;" d="M213 287l47 196h322l20 86l-8 27h-307l43 195h192l-225 561h246l213 -516l411 516h295l-461 -561h191l-45 -195h-295l-23 -29l-20 -84h313l-45 -196h-313l-68 -287h-227l70 287h-326z" />
<glyph unicode="&#xa8;" horiz-adv-x="1200" d="M465 1247l55 240h182l-55 -240h-182zM852 1247l53 240h185l-58 -240h-180z" />
<glyph unicode="&#xa9;" horiz-adv-x="1314" d="M309 891q0 244 158 406.5t399.5 162.5t396 -166t154.5 -408.5t-154.5 -405.5t-396 -163t-399.5 165t-158 409zM420 888.5q0 -192.5 126 -327.5t315.5 -135t316.5 134t127 327.5t-127 330t-315.5 136.5t-315.5 -136.5t-127 -329zM623 889q0 182 59.5 256t198.5 74 q78 0 161 -27l-12 -148q-74 14 -134 14.5t-79.5 -35.5t-19.5 -129t20.5 -134t73.5 -41l139 12l12 -141q-66 -33 -185.5 -33t-176.5 75t-57 257z" />
<glyph unicode="&#xab;" horiz-adv-x="954" d="M133 451l39 102l479 315l-63 -219l-248 -153l172 -174l-55 -201zM592 451l39 102l479 315l-63 -219l-248 -153l172 -174l-56 -201z" />
<glyph unicode="&#xad;" horiz-adv-x="608" d="M147 444l52 201h512l-49 -201h-515z" />
<glyph unicode="&#xae;" horiz-adv-x="1314" d="M309 891q0 244 157 406.5t399.5 162.5t397 -166t154.5 -408.5t-154.5 -405.5t-396 -163t-399.5 165t-158 409zM420 888.5q0 -192.5 128 -327.5t316.5 -135t314.5 134t126 327.5t-127 330t-314.5 136.5t-315.5 -136.5t-128 -329zM625 571v633h256q233 0 233 -205 q0 -78 -19.5 -121.5t-68.5 -76.5l96 -230h-172l-76 203h-86v-203h-163zM786 897h86q84 0 84 91t-96 91h-74v-182z" />
<glyph unicode="&#xb4;" horiz-adv-x="1132" d="M524 1374l508 189l19 -199l-508 -166z" />
<glyph unicode="&#xb8;" horiz-adv-x="1619" d="M616 -434l35 139q68 -4 109 -4q78 0 92 59q8 33 -9.5 43.5t-56.5 10.5h-59l45 196h94l-24 -88h12q182 0 182 -125q0 -23 -6 -51q-47 -199 -246 -199q-66 0 -145 15z" />
<glyph unicode="&#xbb;" horiz-adv-x="976" d="M78 121l63 219l248 154l-172 174l55 200l324 -329l-39 -103zM555 121l63 219l248 154l-172 174l56 200l323 -329l-39 -103z" />
<glyph unicode="&#xbf;" horiz-adv-x="862" d="M51 -104q0 143 65.5 231t256.5 227q123 92 139 172l18 82h209q0 -86 -14 -159q-20 -111 -199 -242q-139 -102 -189 -159.5t-50 -129.5t43 -96.5t116 -24.5q143 0 267 23l43 8l-23 -176q-182 -53 -323 -53q-359 0 -359 297zM547 725l67 299h226l-68 -299h-225z" />
<glyph unicode="&#xc0;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM657 1733l109 194l422 -188l-101 -172z" />
<glyph unicode="&#xc1;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM737 1759l508 189l21 -199l-510 -164z" />
<glyph unicode="&#xc2;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM588 1608l348 299h129l197 -299h-203l-78 135l-158 -135h-235z" />
<glyph unicode="&#xc3;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM594 1784q137 129 227 129q63 0 169 -49t126.5 -49t64.5 20t76 41l33 22l4 -155q-129 -125 -221 -125q-51 0 -158.5 49t-134.5 49q-45 0 -147 -65l-33 -23z" />
<glyph unicode="&#xc4;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM659 1636l56 240h184l-57 -240h-183zM1057 1636l55 240h184l-59 -240h-180z" />
<glyph unicode="&#xc5;" horiz-adv-x="1130" d="M37 0l641 1362q-10 27 -10 59.5t10 67.5q29 113 107.5 169t189 56t164.5 -55q37 -37 37 -84q0 -125 -70 -211l-12 -1364h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM819 1475q0 -63 92.5 -63.5t110.5 77.5q2 10 2 26.5t-22.5 35t-61.5 18.5q-96 0 -119 -80 q-2 -8 -2 -14z" />
<glyph unicode="&#xc6;" horiz-adv-x="1566" d="M39 0l663 1419h1119l-47 -217l-603 4l-94 -397h486l-47 -207h-488l-92 -387h600l-47 -215h-817l69 295h-344l-141 -295h-217zM494 506h299l163 698h-139z" />
<glyph unicode="&#xc7;" horiz-adv-x="1019" d="M215 381q0 297 102 555q55 139 136.5 246.5t204 175t272.5 67.5q137 0 295 -41l51 -14l-53 -172q-139 29 -293 29t-264.5 -127t-162.5 -313.5t-52 -389.5q0 -221 202 -221q129 0 285 25l47 6l-16 -180q-186 -50 -344 -50h-19l-14 -55h12q182 0 182 -125q0 -23 -6 -51 q-47 -199 -245 -199q-66 0 -146 15l-22 4l34 139q68 -4 109 -4q78 0 92 59q8 33 -9.5 43.5t-55.5 10.5h-60l39 172q-301 49 -301 395z" />
<glyph unicode="&#xc8;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM608 1733l109 194l422 -188l-101 -172z" />
<glyph unicode="&#xc9;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM721 1759l508 189l20 -199l-510 -164z" />
<glyph unicode="&#xca;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM606 1610l348 299h129l197 -299h-203l-78 135l-157 -135h-236z" />
<glyph unicode="&#xcb;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM627 1636l55 240h184l-57 -240h-182zM1024 1636l55 240h185l-60 -240h-180z" />
<glyph unicode="&#xcc;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM299 1733l109 194l421 -188l-100 -172z" />
<glyph unicode="&#xcd;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM459 1759l508 189l20 -199l-510 -164z" />
<glyph unicode="&#xce;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM281 1610l348 299h129l196 -299h-202l-78 135l-158 -135h-235z" />
<glyph unicode="&#xcf;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM336 1655l55 239h184l-57 -239h-182zM733 1655l55 239h185l-60 -239h-180z" />
<glyph unicode="&#xd1;" horiz-adv-x="1314" d="M143 0l326 1403h375l135 -1163h31l264 1163h221l-324 -1403h-358l-147 1178h-31l-275 -1178h-217zM678 1780q137 129 227 129q63 0 169 -49.5t126.5 -49.5t64.5 20.5t76 41.5l33 22l4 -155q-129 -125 -221 -125q-51 0 -158.5 49t-134.5 49q-45 0 -147 -65l-33 -23z" />
<glyph unicode="&#xd2;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM717 1733l108 194l422 -188l-100 -172z" />
<glyph unicode="&#xd3;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM809 1759l508 189l20 -199l-510 -164z" />
<glyph unicode="&#xd4;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM668 1608l348 299h129l196 -299h-202l-78 135l-158 -135h-235z" />
<glyph unicode="&#xd5;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM692 1780q137 129 228 129q63 0 168.5 -49.5t126 -49.5t64.5 20.5t77 41.5l33 22l4 -155q-130 -125 -222 -125q-51 0 -158.5 49t-133.5 49q-45 0 -148 -65l-33 -23z" />
<glyph unicode="&#xd6;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM702 1628l54 238h202l-57 -238h-199zM1106 1628l55 238h201l-57 -238h-199z" />
<glyph unicode="&#xd8;" horiz-adv-x="1269" d="M123 -172l184 272q-90 106 -90 301q0 266 83 506t241 377q158 141 385 141q147 0 248 -49l180 264l129 -53l-201 -293q90 -109 90 -293q0 -279 -85 -524.5t-251 -378.5q-156 -121 -381 -121q-143 0 -239 43l-178 -262zM453 422q0 -59 8 -94l590 866q-53 33 -159 33 t-181 -68q-119 -109 -188.5 -327t-69.5 -410zM541 205q57 -29 160.5 -29t189.5 76q115 102 180.5 313t65.5 416q0 53 -9 86z" />
<glyph unicode="&#xd9;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM690 1733l109 194l422 -188l-101 -172z" />
<glyph unicode="&#xda;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM741 1759l508 189l21 -199l-510 -164z" />
<glyph unicode="&#xdb;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM629 1608l348 299h129l197 -299h-203l-78 135l-158 -135h-235z" />
<glyph unicode="&#xdc;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM659 1628l56 238h200l-55 -238h-201zM1065 1628l53 238h203l-57 -238h-199z" />
<glyph unicode="&#xdd;" horiz-adv-x="1021" d="M334 1403h231l127 -594l402 594h241l-583 -807l-142 -596h-213l142 610zM688 1759l508 189l21 -199l-510 -164z" />
<glyph unicode="&#xdf;" horiz-adv-x="1132" d="M121 0l252 1073q53 225 165.5 312.5t308 87.5t291 -72t95.5 -193t-45 -192.5t-148.5 -115.5t-140.5 -70.5t-37 -62.5t24.5 -60.5t116 -82t126 -102.5t34.5 -141q0 -186 -115.5 -295t-318.5 -109q-119 0 -254 37l-45 13l47 166q139 -27 240.5 -27t161 52t59.5 134 q0 45 -24.5 72t-115.5 81t-125 100t-34 135t49 149.5t152.5 111t135.5 84t32 89.5q0 109 -172 108q-113 0 -172.5 -54t-88.5 -184l-245 -1044h-209z" />
<glyph unicode="&#xe0;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM604 1374l107 180l419 -188l-94 -162z" />
<glyph unicode="&#xe1;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM674 1374l508 189l18 -199l-508 -166z" />
<glyph unicode="&#xe2;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM459 1188l334 338h161l179 -338h-222l-69 151l-148 -151h-235z" />
<glyph unicode="&#xe3;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM563 1395q131 123 221 123q57 0 150.5 -46.5t114.5 -46.5q37 0 131 62l32 20l9 -157q-127 -121 -217 -121q-49 0 -143.5 47t-119.5 47q-49 0 -143 -61l-31 -23z" />
<glyph unicode="&#xe4;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM516 1247l55 240h203l-57 -240h-201zM922 1247l53 240h203l-58 -240h-198z" />
<glyph unicode="&#xe5;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM673.5 1272q0.5 41 10.5 78q21 90 89.5 150.5t159.5 60.5t128 -56q25 -37 24.5 -75.5t-6.5 -69.5q-16 -102 -86.5 -162.5t-165 -60.5t-131.5 59q-23 35 -22.5 76zM805 1325q0 -14 13 -33.5t45 -19.5t59 22q37 31 36 62 q0 66 -57 65q-33 0 -58.5 -20.5t-31.5 -41t-6 -34.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1492" d="M115 203q0 59 10 102q55 258 332 303q147 25 297 25q18 57 18 106q0 96 -88 97q-145 0 -311 -21l-60 -6l35 199q248 37 393 36q178 0 230 -112q115 113 301 112q307 0 307 -294q0 -94 -29 -187l-39 -147l-583 4q-10 -61 -10 -98q0 -141 157 -142q147 0 297 13l58 6 l-39 -174q-195 -47 -386.5 -47.5t-265.5 104.5q-74 -33 -198.5 -69t-198.5 -36q-129 0 -186 101q-41 66 -41 125zM336 268q0 -98 82 -98q96 0 274 49q-4 25 -4 79t27 165q-131 -8 -213 -19q-133 -14 -160 -133q-6 -23 -6 -43zM967 592l383 -4q18 66 18 100.5t-2 50.5 q-10 113 -143 113q-104 0 -164 -64.5t-92 -195.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="835" d="M178 313q0 315 139.5 524.5t397.5 209.5q117 0 243 -39l41 -15l-57 -164q-147 25 -235 25q-145 0 -223 -148.5t-78 -342t178 -193.5q88 0 186 16l37 7l-16 -168q-135 -43 -269 -48l-14 -55h12q182 0 182 -125q0 -23 -6 -51q-47 -199 -245 -199q-66 0 -146 15l-22 4 l34 139q68 -4 109 -4q78 0 92 59q8 33 -9 43.5t-56 10.5h-60l39 170q-121 18 -187.5 103t-66.5 226z" />
<glyph unicode="&#xe8;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM426 1374l106 180l420 -188l-94 -162zM436 596h96 q172 0 244 37t72 129q0 102 -127 102q-213 0 -285 -268z" />
<glyph unicode="&#xe9;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102 q-213 0 -285 -268zM575 1374l508 189l19 -199l-508 -166z" />
<glyph unicode="&#xea;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM410 1188l331 338h164l176 -338h-219l-71 151 l-146 -151h-235zM436 596h96q172 0 244 37t72 129q0 102 -127 102q-213 0 -285 -268z" />
<glyph unicode="&#xeb;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102 q-213 0 -285 -268zM469 1247l55 240h201l-55 -240h-201zM874 1247l54 240h202l-57 -240h-199z" />
<glyph unicode="&#xec;" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM152 1374l106 180l420 -188l-94 -162z" />
<glyph unicode="&#xed;" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM365 1374l507 189l19 -199l-508 -166z" />
<glyph unicode="&#xee;" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM154 1188l333 338h162l178 -338h-221l-69 151l-148 -151h-235z" />
<glyph unicode="&#xef;" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM195 1247l55 240h201l-56 -240h-200zM600 1247l53 240h203l-57 -240h-199z" />
<glyph unicode="&#xf1;" horiz-adv-x="1036" d="M123 0l235 1024h213l-22 -135l39 39q39 39 111.5 79t140.5 40q125 0 185 -75t60 -182.5t-43 -279.5l-116 -510h-221l114 494q37 172 37 233t-23.5 94t-75.5 33t-124 -51t-131 -119l-158 -684h-221zM512 1395q131 123 221 123q57 0 149.5 -46.5t112.5 -46.5q39 0 135 62 l31 20l8 -157q-127 -121 -217 -121q-49 0 -143 47t-119 47q-49 0 -141 -61l-33 -23z" />
<glyph unicode="&#xf2;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM485 1374l107 180l420 -188 l-94 -162z" />
<glyph unicode="&#xf3;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM578 1374l507 189l19 -199 l-508 -166z" />
<glyph unicode="&#xf4;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM440 1188l334 338h162l178 -338 h-221l-70 151l-147 -151h-236z" />
<glyph unicode="&#xf5;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM469 1395q131 123 221 123 q55 0 149.5 -46.5t114.5 -46.5q37 0 131 62l33 20l8 -157q-127 -121 -217 -121q-49 0 -143 47t-119 47q-49 0 -143 -61l-31 -23z" />
<glyph unicode="&#xf6;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM498 1247l55 240h182l-55 -240 h-182zM885 1247l53 240h184l-57 -240h-180z" />
<glyph unicode="&#xf8;" horiz-adv-x="1013" d="M119 -170l153 240q-96 90 -96 271t61.5 340t182 263.5t272.5 104.5q92 0 176 -27l133 217l121 -53l-145 -228q98 -94 98 -274t-57 -337t-179 -261t-284 -104q-100 0 -174 24l-141 -229zM397 344q0 -41 9 -68l165 263l191 309q-31 12 -70 12q-131 0 -213 -157.5 t-82 -358.5zM487 180q31 -10 68 -10q133 0 216 155.5t83 356.5q0 35 -10 70l-170 -267z" />
<glyph unicode="&#xf9;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM492 1374l106 180l420 -188l-94 -162z" />
<glyph unicode="&#xfa;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM592 1374l508 189l18 -199l-508 -166z" />
<glyph unicode="&#xfb;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM424 1188l332 338h164l176 -338h-219 l-72 151l-146 -151h-235z" />
<glyph unicode="&#xfc;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM471 1247l55 240h201l-55 -240h-201z M877 1247l53 240h200l-55 -240h-198z" />
<glyph unicode="&#xfd;" horiz-adv-x="942" d="M211 -440l221 440h-156l-8 1024h219l-10 -840h45l396 840h225l-709 -1464h-223zM555 1374l508 189l18 -199l-508 -166z" />
<glyph unicode="&#xff;" horiz-adv-x="942" d="M211 -440l221 440h-156l-8 1024h219l-10 -840h45l396 840h225l-709 -1464h-223zM432 1247l55 240h201l-55 -240h-201zM838 1247l53 240h201l-56 -240h-198z" />
<glyph unicode="&#x100;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM612 1673l41 178h641l-43 -178h-639z" />
<glyph unicode="&#x101;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM573 1294l41 181h605l-43 -181h-603z" />
<glyph unicode="&#x102;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1403h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM680 1833q0 29 4 61h201q-2 -14 -2 -24q0 -80 94 -80q49 0 87 28.5t54 75.5h201q-90 -281 -379 -280q-143 0 -201.5 63.5t-58.5 155.5z" />
<glyph unicode="&#x103;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM596 1440q0 35 6 73h199q-2 -14 -2 -37.5t20.5 -52t83.5 -28.5q121 0 160 118h199q-98 -303 -414 -303q-145 0 -207 80q-45 57 -45 150z" />
<glyph unicode="&#x104;" horiz-adv-x="1130" d="M37 0l661 1403h408l-12 -1395v-8h-13q-51 -37 -103 -99.5t-52 -98t37 -35.5l86 8l-23 -185q-104 -20 -180 -20t-118 38t-42 107.5t58.5 147.5t132.5 137h-11v293h-458l-142 -293h-229zM496 494h376l11 710h-52z" />
<glyph unicode="&#x105;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-18 -2q-49 -33 -104.5 -97.5t-55.5 -101t37 -36.5l86 8l-23 -185q-104 -20 -180 -20t-117.5 38t-41.5 109.5t61 151.5t137 139q0 20 31 174q-96 -127 -219 -182 q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440q-139 31 -221 31t-154 -92q-135 -170 -135 -447z" />
<glyph unicode="&#x106;" horiz-adv-x="1019" d="M215 381q0 297 102 555q55 139 136.5 246.5t204 175t272.5 67.5q137 0 295 -41l51 -14l-53 -172q-139 29 -293 29t-264.5 -127t-162.5 -313.5t-52 -389.5q0 -221 202 -221q129 0 285 25l47 6l-16 -180q-186 -50 -344 -50q-410 0 -410 404zM791 1759l507 189l21 -199 l-510 -164z" />
<glyph unicode="&#x107;" horiz-adv-x="833" d="M176 313q0 315 139.5 524.5t397.5 209.5q117 0 241 -39l43 -15l-57 -164q-147 25 -235 25q-145 0 -223.5 -148.5t-78.5 -342t179 -193.5q88 0 186 16l37 7l-17 -168q-145 -47 -291.5 -47.5t-233.5 88t-87 247.5zM555 1374l508 189l18 -199l-508 -166z" />
<glyph unicode="&#x108;" horiz-adv-x="1019" d="M215 381q0 297 102 555q55 139 136.5 246.5t204 175t272.5 67.5q137 0 295 -41l51 -14l-53 -172q-139 29 -293 29t-264.5 -127t-162.5 -313.5t-52 -389.5q0 -221 202 -221q129 0 285 25l47 6l-16 -180q-186 -50 -344 -50q-410 0 -410 404zM637 1608l348 299h129l197 -299 h-203l-78 135l-158 -135h-235z" />
<glyph unicode="&#x109;" horiz-adv-x="833" d="M176 313q0 315 139.5 524.5t397.5 209.5q117 0 241 -39l43 -15l-57 -164q-147 25 -235 25q-145 0 -223.5 -148.5t-78.5 -342t179 -193.5q88 0 186 16l37 7l-17 -168q-145 -47 -291.5 -47.5t-233.5 88t-87 247.5zM428 1188l332 338h129l176 -338h-203l-71 151l-146 -151 h-217z" />
<glyph unicode="&#x10a;" horiz-adv-x="1019" d="M215 381q0 297 102 555q55 139 136.5 246.5t204 175t272.5 67.5q137 0 295 -41l51 -14l-53 -172q-139 29 -293 29t-264.5 -127t-162.5 -313.5t-52 -389.5q0 -221 202 -221q129 0 285 25l47 6l-16 -180q-186 -50 -344 -50q-410 0 -410 404zM868 1597l56 242h194l-57 -242 h-193z" />
<glyph unicode="&#x10b;" horiz-adv-x="833" d="M176 313q0 315 139.5 524.5t397.5 209.5q117 0 241 -39l43 -15l-57 -164q-147 25 -235 25q-145 0 -223.5 -148.5t-78.5 -342t179 -193.5q88 0 186 16l37 7l-17 -168q-145 -47 -291.5 -47.5t-233.5 88t-87 247.5zM645 1212l55 238h191l-55 -238h-191z" />
<glyph unicode="&#x10c;" horiz-adv-x="1019" d="M215 381q0 297 102 555q55 139 136.5 246.5t204 175t272.5 67.5q137 0 295 -41l51 -14l-53 -172q-139 29 -293 29t-264.5 -127t-162.5 -313.5t-52 -389.5q0 -221 202 -221q129 0 285 25l47 6l-16 -180q-186 -50 -344 -50q-410 0 -410 404zM709 1901h219l86 -140l143 140 h219l-323 -297h-150z" />
<glyph unicode="&#x10d;" horiz-adv-x="833" d="M176 313q0 315 139.5 524.5t397.5 209.5q117 0 241 -39l43 -15l-57 -164q-147 25 -235 25q-145 0 -223.5 -148.5t-78.5 -342t179 -193.5q88 0 186 16l37 7l-17 -168q-145 -47 -291.5 -47.5t-233.5 88t-87 247.5zM520 1526h203l80 -156l141 156h217l-334 -338h-137z" />
<glyph unicode="&#x10e;" horiz-adv-x="1241" d="M143 0l326 1403h444q430 0 430 -422q0 -362 -167 -657q-82 -145 -221.5 -234.5t-313.5 -89.5h-498zM420 199h244q111 0 200.5 80.5t140 203.5t77 252t26.5 247t-58.5 170t-181.5 52h-219zM639 1901h219l86 -140l143 140h220l-324 -297h-149z" />
<glyph unicode="&#x10f;" horiz-adv-x="1228" d="M172 319.5q0 169.5 56.5 332.5t177 277.5t280.5 114.5q80 0 215 -28l43 -10l103 444h223l-336 -1450h-215l26 139q-14 -18 -40.5 -44.5t-104 -72t-149.5 -45.5q-137 0 -208 86.5t-71 256zM397 313q0 -147 125 -147q45 0 114 47t114 94l45 47l108 473q-125 29 -215 29 t-158.5 -92t-100.5 -214t-32 -237zM1251 905l121 498h201l-119 -498h-203z" />
<glyph unicode="&#x110;" horiz-adv-x="1253" d="M156 0l137 594h-121l47 225h127l135 584h445q430 0 430 -422q0 -362 -168 -657q-82 -145 -221.5 -234.5t-313.5 -89.5h-497zM432 199h244q111 0 201 80.5t140 203.5t76.5 252t26.5 247t-58 170t-181 52h-219l-89 -385h252l-49 -225h-254z" />
<glyph unicode="&#x111;" horiz-adv-x="1056" d="M172 319.5q0 169.5 56.5 332.5t177 277.5t280.5 114.5q80 0 215 -28l43 -10l35 151h-268l45 201h268l23 92h223l-23 -92h113l-47 -201h-111l-268 -1157h-215l26 139q-14 -18 -40.5 -44.5t-104 -72t-149.5 -45.5q-137 0 -208 86.5t-71 256zM397 313q0 -147 125 -147 q45 0 114 47t114 94l45 47l108 473q-125 29 -215 29t-158.5 -92t-100.5 -214t-32 -237z" />
<glyph unicode="&#x112;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM592 1667l41 178h641l-43 -178h-639z" />
<glyph unicode="&#x113;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102 q-213 0 -285 -268zM479 1294l41 181h604l-43 -181h-602z" />
<glyph unicode="&#x114;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM635 1833q0 29 4 61h201q-2 -14 -2 -24q0 -80 94 -80q49 0 87 28.5t54 75.5h201q-90 -281 -379 -280q-143 0 -201.5 63.5t-58.5 155.5z" />
<glyph unicode="&#x115;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102 q-213 0 -285 -268zM518 1440q0 35 6 73h199q-2 -14 -2 -37.5t20.5 -52t83.5 -28.5q121 0 160 118h199q-98 -303 -414 -303q-145 0 -207 80q-45 57 -45 150z" />
<glyph unicode="&#x116;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM797 1630l55 242h195l-58 -242h-192z" />
<glyph unicode="&#x117;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102 q-213 0 -285 -268zM655 1212l56 238h190l-55 -238h-191z" />
<glyph unicode="&#x118;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-30q-52 -37 -104 -99.5t-52 -98t37 -35.5l86 8l-23 -185q-104 -20 -180 -20t-118 38t-42 107.5t58.5 147.5t132.5 137h-572z" />
<glyph unicode="&#x119;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-14 -4 -33 -10q-51 -41 -94 -95.5t-43 -88.5t37 -34l86 9l-23 -185q-104 -20 -180 -20 t-118 38t-42 107q0 100 125 226q-61 -4 -92 -5q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102q-213 0 -285 -268z" />
<glyph unicode="&#x11a;" horiz-adv-x="1024" d="M143 0l324 1403h805l-47 -199h-576l-90 -397h463l-47 -201h-461l-94 -407h577l-47 -199h-807zM625 1927h219l86 -139l143 139h219l-323 -297h-150z" />
<glyph unicode="&#x11b;" horiz-adv-x="962" d="M174 309q0 291 129 502q66 106 176.5 169.5t251.5 63.5q336 0 336 -280q0 -188 -134 -262t-437 -74h-86q-12 -45 -12.5 -113.5t47 -107.5t116.5 -39q147 0 291 25l53 10l-22 -168q-186 -57 -377 -58q-332 0 -332 332zM436 596h96q172 0 244 37t72 129q0 102 -127 102 q-213 0 -285 -268zM522 1526h203l80 -156l141 156h217l-334 -338h-137z" />
<glyph unicode="&#x11c;" horiz-adv-x="1167" d="M219 399q0 326 113 584q125 279 352 385q125 57 246 57q219 0 389 -55l55 -18l-53 -166q-205 41 -387 41q-123 0 -218 -80t-151 -207q-111 -254 -110 -543q0 -221 237 -221q82 0 156 17l24 4l86 370h-165l45 191h393l-170 -727q-155 -54 -395 -54q-446 0 -447 422z M649 1567l332 338h164l176 -338h-219l-72 151l-145 -151h-236z" />
<glyph unicode="&#x11d;" horiz-adv-x="976" d="M27 -201q0 100 71.5 168t186.5 125q-37 37 -37 89t29.5 101.5t58.5 77.5l31 29q-141 76 -141.5 248t114.5 291t305 119q82 0 191 -27l34 -8l301 12l-43 -182h-145q18 -47 18 -121q0 -156 -118.5 -272.5t-311.5 -116.5l-77 10q-45 -59 -45 -94t37.5 -48.5t166.5 -30.5 t207 -73.5t78 -177.5q0 -172 -135 -282.5t-348 -110.5q-428 0 -428 274zM231 -155.5q0 -55.5 50.5 -94.5t163 -39t197.5 45t85 138q0 49 -37 68.5t-152.5 36.5t-152.5 28q-57 -29 -105.5 -78t-48.5 -104.5zM397 1188l332 338h129l176 -338h-203l-71 151l-146 -151h-217z M432 651q0 -137 156 -137q94 0 151.5 65.5t57.5 145.5q0 139 -162 139q-90 0 -146.5 -64.5t-56.5 -148.5z" />
<glyph unicode="&#x11e;" horiz-adv-x="1167" d="M219 399q0 326 113 584q125 279 352 385q125 57 246 57q219 0 389 -55l55 -18l-53 -166q-205 41 -387 41q-123 0 -218 -80t-151 -207q-111 -254 -110 -543q0 -221 237 -221q82 0 156 17l24 4l86 370h-165l45 191h393l-170 -727q-155 -54 -395 -54q-446 0 -447 422z M721 1829q0 31 4 65h217q-2 -6 -2 -32q4 -86 104 -86q121 0 160 118h215q-45 -147 -155.5 -234t-257 -87t-213.5 65q-72 70 -72 191z" />
<glyph unicode="&#x11f;" horiz-adv-x="976" d="M27 -201q0 100 71.5 168t186.5 125q-37 37 -37 89t29.5 101.5t58.5 77.5l31 29q-141 76 -141.5 248t114.5 291t305 119q82 0 191 -27l34 -8l301 12l-43 -182h-145q18 -47 18 -121q0 -156 -118.5 -272.5t-311.5 -116.5l-77 10q-45 -59 -45 -94t37.5 -48.5t166.5 -30.5 t207 -73.5t78 -177.5q0 -172 -135 -282.5t-348 -110.5q-428 0 -428 274zM231 -155.5q0 -55.5 50.5 -94.5t163 -39t197.5 45t85 138q0 49 -37 68.5t-152.5 36.5t-152.5 28q-57 -29 -105.5 -78t-48.5 -104.5zM432 651q0 -137 156 -137q94 0 151.5 65.5t57.5 145.5 q0 139 -162 139q-90 0 -146.5 -64.5t-56.5 -148.5zM489 1440q0 35 7 73h198q-2 -14 -2 -37.5t20.5 -52t84.5 -28.5q121 0 159 118h199q-98 -303 -414 -303q-145 0 -206 80q-45 57 -46 150z" />
<glyph unicode="&#x120;" horiz-adv-x="1167" d="M219 399q0 326 113 584q125 279 352 385q125 57 246 57q219 0 389 -55l55 -18l-53 -166q-205 41 -387 41q-123 0 -218 -80t-151 -207q-111 -254 -110 -543q0 -221 237 -221q82 0 156 17l24 4l86 370h-165l45 191h393l-170 -727q-155 -54 -395 -54q-446 0 -447 422z M899 1556l55 240h209l-55 -240h-209z" />
<glyph unicode="&#x121;" horiz-adv-x="976" d="M27 -201q0 100 71.5 168t186.5 125q-37 37 -37 89t29.5 101.5t58.5 77.5l31 29q-141 76 -141.5 248t114.5 291t305 119q82 0 191 -27l34 -8l301 12l-43 -182h-145q18 -47 18 -121q0 -156 -118.5 -272.5t-311.5 -116.5l-77 10q-45 -59 -45 -94t37.5 -48.5t166.5 -30.5 t207 -73.5t78 -177.5q0 -172 -135 -282.5t-348 -110.5q-428 0 -428 274zM231 -155.5q0 -55.5 50.5 -94.5t163 -39t197.5 45t85 138q0 49 -37 68.5t-152.5 36.5t-152.5 28q-57 -29 -105.5 -78t-48.5 -104.5zM432 651q0 -137 156 -137q94 0 151.5 65.5t57.5 145.5 q0 139 -162 139q-90 0 -146.5 -64.5t-56.5 -148.5zM629 1212l55 238h190l-55 -238h-190z" />
<glyph unicode="&#x122;" horiz-adv-x="1167" d="M219 399q0 326 113 584q125 279 352 385q125 57 246 57q219 0 389 -55l55 -18l-53 -166q-205 41 -387 41q-123 0 -218 -80t-151 -207q-111 -254 -110 -543q0 -221 237 -221q82 0 156 17l24 4l86 370h-165l45 191h393l-170 -727q-155 -54 -395 -54q-446 0 -447 422z M309 -610l191 463h202l-208 -463h-185z" />
<glyph unicode="&#x123;" horiz-adv-x="976" d="M27 -201q0 100 71.5 168t186.5 125q-37 37 -37 89t29.5 101.5t58.5 77.5l31 29q-141 76 -141.5 248t114.5 291t305 119q82 0 191 -27l34 -8l301 12l-43 -182h-145q18 -47 18 -121q0 -156 -118.5 -272.5t-311.5 -116.5l-77 10q-45 -59 -45 -94t37.5 -48.5t166.5 -30.5 t207 -73.5t78 -177.5q0 -172 -135 -282.5t-348 -110.5q-428 0 -428 274zM231 -155.5q0 -55.5 50.5 -94.5t163 -39t197.5 45t85 138q0 49 -37 68.5t-152.5 36.5t-152.5 28q-57 -29 -105.5 -78t-48.5 -104.5zM432 651q0 -137 156 -137q94 0 151.5 65.5t57.5 145.5 q0 139 -162 139q-90 0 -146.5 -64.5t-56.5 -148.5zM610 1190l209 459h187l-191 -459h-205z" />
<glyph unicode="&#x124;" horiz-adv-x="1273" d="M143 0l326 1403h227l-135 -590h526l138 590h227l-324 -1403h-227l139 614h-526l-141 -614h-230zM655 1608l349 299h129l196 -299h-203l-77 135l-158 -135h-236z" />
<glyph unicode="&#x125;" horiz-adv-x="1038" d="M123 0l334 1450h221l-127 -559q16 18 43 44t102.5 69t143.5 43q125 0 185 -75t60 -180.5t-43 -283.5l-116 -508h-221l114 494q37 147 37 220.5t-23.5 106.5t-73.5 33t-112.5 -42t-103.5 -85l-41 -43l-158 -684h-221zM575 1546l349 299h129l196 -299h-202l-78 135 l-158 -135h-236z" />
<glyph unicode="&#x126;" horiz-adv-x="1294" d="M156 0l231 1004h-145v6l45 194h145l47 199h219l-47 -199h549l45 199h219l-47 -199h144l-52 -200h-137l-231 -1004h-219l139 600h-549l-137 -600h-219zM561 813h549l43 191h-549z" />
<glyph unicode="&#x127;" horiz-adv-x="1038" d="M123 0l260 1128h-109l43 193h109l31 129h221l-29 -129h273l-48 -193h-270l-53 -237q16 18 43 44t102.5 69t143.5 43q125 0 185 -75t60 -180.5t-43 -283.5l-116 -508h-221l114 494q37 147 37 220.5t-23.5 106.5t-73.5 33t-112.5 -42t-103.5 -85l-41 -43l-158 -684h-221z " />
<glyph unicode="&#x128;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM305 1767q137 129 227 129q63 0 169 -49t126.5 -49t64.5 20.5t77 41.5l32 22l5 -156q-130 -124 -222 -124q-51 0 -158.5 49t-133.5 49q-45 0 -148 -66l-33 -22z" />
<glyph unicode="&#x129;" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM190 1395q131 123 222 123q55 0 149 -46.5t115 -46.5q37 0 131 62l33 20l8 -157q-127 -121 -217 -121q-49 0 -143.5 47t-118.5 47q-49 0 -144 -61l-30 -23z" />
<glyph unicode="&#x12a;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM309 1690l41 178h641l-43 -178h-639z" />
<glyph unicode="&#x12b;" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM209 1294l41 181h604l-43 -181h-602z" />
<glyph unicode="&#x12c;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM373 1833q0 29 4 61h201q-3 -14 -3 -24q0 -80 95 -80q49 0 87 28.5t54 75.5h201q-90 -281 -379 -280q-143 0 -201.5 63.5t-58.5 155.5z" />
<glyph unicode="&#x12d;" horiz-adv-x="464" d="M123 0l235 1024h222l-236 -1024h-221zM233 1440q0 35 7 73h198q-2 -14 -2 -37.5t20.5 -52t84.5 -28.5q121 0 159 118h199q-98 -303 -414 -303q-145 0 -206 80q-45 57 -46 150z" />
<glyph unicode="&#x12e;" horiz-adv-x="516" d="M-43 -284.5q0 69.5 58.5 147.5t131.5 137h-4l326 1403h227l-323 -1403q-56 -29 -116 -93.5t-60 -102t36 -37.5l86 8l-20 -185q-106 -20 -182 -20t-118 38t-42 107.5z" />
<glyph unicode="&#x12f;" horiz-adv-x="464" d="M-74 -285q0 129 199 291l233 1018h222l-236 -1024q-61 -37 -120.5 -97.5t-59.5 -98t37 -37.5l86 8l-21 -185q-106 -20 -182 -20t-117 38t-41 107zM391 1176l58 239h223l-56 -239h-225z" />
<glyph unicode="&#x130;" horiz-adv-x="516" d="M143 0l326 1403h227l-323 -1403h-230zM530 1630l56 242h194l-57 -242h-193z" />
<glyph unicode="&#x131;" horiz-adv-x="466" d="M123 0l235 1024h222l-236 -1024h-221z" />
<glyph unicode="&#x134;" horiz-adv-x="563" d="M12 -143l39 200q104 2 140 36t67 173l264 1137h228l-265 -1141q-59 -268 -139 -336.5t-334 -68.5zM322 1608l348 299h129l196 -299h-202l-78 135l-158 -135h-235z" />
<glyph unicode="&#x135;" horiz-adv-x="464" d="M-160 -297q170 86 212 131t71 166l235 1024h222l-236 -1024q-37 -156 -71.5 -225.5t-84.5 -102.5q-106 -74 -276 -147zM172 1188l332 338h129l176 -338h-203l-71 151l-146 -151h-217z" />
<glyph unicode="&#x136;" horiz-adv-x="1089" d="M143 0l326 1403h227l-145 -625l156 13l421 612h252l-491 -705l172 -698h-240l-139 590l-176 -12l-133 -578h-230zM264 -610l191 463h202l-208 -463h-185z" />
<glyph unicode="&#x137;" horiz-adv-x="937" d="M123 0l334 1450h221l-191 -823l97 10l309 385h248l-391 -483l161 -539h-229l-131 453l-105 -9l-102 -444h-221zM166 -610l190 463h203l-209 -463h-184z" />
<glyph unicode="&#x139;" horiz-adv-x="874" d="M143 0l326 1403h227l-276 -1204h489l-45 -199h-721zM610 1759l508 189l21 -199l-510 -164z" />
<glyph unicode="&#x13a;" horiz-adv-x="473" d="M125 0l336 1450h221l-334 -1450h-223zM475 1759l508 189l21 -199l-510 -164z" />
<glyph unicode="&#x13b;" horiz-adv-x="874" d="M143 0l326 1403h227l-276 -1204h489l-45 -199h-721zM186 -610l191 463h203l-209 -463h-185z" />
<glyph unicode="&#x13c;" horiz-adv-x="473" d="M-86 -610l190 463h203l-209 -463h-184zM125 0l336 1450h221l-334 -1450h-223z" />
<glyph unicode="&#x13d;" horiz-adv-x="940" d="M143 0l326 1403h227l-276 -1204h489l-45 -199h-721zM854 905l121 498h201l-117 -498h-205z" />
<glyph unicode="&#x13e;" horiz-adv-x="641" d="M125 0l336 1450h221l-334 -1450h-223zM666 905l118 498h203l-119 -498h-202z" />
<glyph unicode="&#x141;" horiz-adv-x="876" d="M111 586l200 125l162 692h227l-120 -524l241 151l70 -156l-365 -227l-102 -448h489l-45 -199h-721l111 479l-78 -49h-2z" />
<glyph unicode="&#x142;" horiz-adv-x="579" d="M117 596l225 139l166 715h221l-127 -553l107 66l69 -154l-229 -143l-154 -666h-223l115 502l-99 -62z" />
<glyph unicode="&#x143;" horiz-adv-x="1314" d="M143 0l326 1403h375l135 -1163h31l264 1163h221l-324 -1403h-358l-147 1178h-31l-275 -1178h-217zM791 1759l507 189l21 -199l-510 -164z" />
<glyph unicode="&#x144;" horiz-adv-x="1036" d="M123 0l235 1024h213l-22 -135l39 39q39 39 111.5 79t140.5 40q125 0 185 -75t60 -182.5t-43 -279.5l-116 -510h-221l114 494q37 172 37 233t-23.5 94t-75.5 33t-124 -51t-131 -119l-158 -684h-221zM582 1374l508 189l18 -199l-508 -166z" />
<glyph unicode="&#x145;" horiz-adv-x="1314" d="M143 0l326 1403h375l135 -1163h31l264 1163h221l-324 -1403h-358l-147 1178h-31l-275 -1178h-217zM385 -610l190 463h203l-209 -463h-184z" />
<glyph unicode="&#x146;" horiz-adv-x="1036" d="M123 0l235 1024h213l-22 -135q16 18 42.5 43.5t103.5 70t145 44.5q125 0 185 -75t60 -182.5t-43 -279.5l-116 -510h-221l114 494q37 172 37 233t-23.5 94t-75.5 33t-113.5 -42t-102.5 -85l-39 -43l-158 -684h-221zM223 -610l191 463h202l-208 -463h-185z" />
<glyph unicode="&#x147;" horiz-adv-x="1314" d="M143 0l326 1403h375l135 -1163h31l264 1163h221l-324 -1403h-358l-147 1178h-31l-275 -1178h-217zM725 1896h219l86 -139l144 139h219l-324 -297h-149z" />
<glyph unicode="&#x148;" horiz-adv-x="1036" d="M123 0l235 1024h213l-22 -135q16 18 42.5 43.5t103.5 70t145 44.5q125 0 185 -75t60 -182.5t-43 -279.5l-116 -510h-221l114 494q37 172 37 233t-23.5 94t-75.5 33t-113.5 -42t-102.5 -85l-39 -43l-158 -684h-221zM549 1526h203l79 -156l142 156h217l-334 -338h-137z" />
<glyph unicode="&#x14a;" horiz-adv-x="1314" d="M143 0l326 1403h375l135 -1163h29l266 1163h221l-324 -1403h-2v-6q-33 -143 -58.5 -214t-78.5 -119t-122.5 -59.5t-202.5 -11.5l38 201q104 2 140.5 36t66.5 173h-139l-147 1178h-31l-275 -1178h-217z" />
<glyph unicode="&#x14b;" horiz-adv-x="1019" d="M117 0l241 1022h211l-28 -113q162 135 284 135q244 0 244 -256q0 -102 -37 -256l-125 -524q-49 -199 -137 -300t-309 -202l-53 191q162 84 213 138t79 179l123 518q27 107 27 181q0 125 -113 125q-41 0 -100 -29t-100 -57l-39 -29l-170 -723h-211z" />
<glyph unicode="&#x14c;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM680 1667l41 178h641l-43 -178h-639z" />
<glyph unicode="&#x14d;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM477 1294l41 181h604l-43 -181 h-602z" />
<glyph unicode="&#x14e;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM729 1833q0 29 4 61h201q-2 -14 -2 -24q0 -80 94 -80q49 0 87 28.5t54 75.5h201q-90 -281 -379 -280q-143 0 -201.5 63.5t-58.5 155.5z" />
<glyph unicode="&#x14f;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM487 1440q0 35 7 73h198 q-2 -14 -2 -37.5t20.5 -52t84.5 -28.5q121 0 159 118h199q-98 -303 -414 -303q-145 0 -207 80q-45 57 -45 150z" />
<glyph unicode="&#x150;" horiz-adv-x="1269" d="M217 401q0 266 83 506t241 377q158 141 385 141q446 0 446 -424q0 -279 -85 -524.5t-251 -378.5q-156 -121 -377 -121t-331.5 102.5t-110.5 321.5zM453 451.5q0 -162.5 53 -219t176 -56.5t209 76q115 102 180.5 313t65.5 416q0 131 -53.5 188.5t-175.5 57.5t-197 -68 q-119 -109 -188.5 -327t-69.5 -380.5zM676 1616l276 348l168 -80l-291 -340zM1083 1616l273 346l170 -80l-291 -340z" />
<glyph unicode="&#x151;" horiz-adv-x="1013" d="M174 342q0 176 61.5 335t182 263t272.5 104q383 0 383 -368q0 -176 -57.5 -333t-179 -261.5t-283.5 -104.5q-379 0 -379 365zM395 340q0 -174 158 -174q133 0 216 155.5t83 356.5q0 84 -41 131t-121 47q-131 0 -213 -157.5t-82 -358.5zM426 1282l330 342l141 -115 l-344 -340zM811 1282l332 344l141 -117l-344 -340z" />
<glyph unicode="&#x152;" horiz-adv-x="1726" d="M205 354q0 152 47 353q90 377 254 556t424 179q106 0 241 -23h811l-49 -219h-600l-94 -389h481l-47 -209l-481 4l-92 -387h598l-51 -219h-805q-150 -23 -256 -23q-188 0 -281 86q-100 94 -100 291zM432 409.5q0 -106.5 54.5 -159.5t177.5 -53q53 0 221 14l237 997 q-117 12 -217 13q-168 0 -265 -118t-165 -394q-43 -193 -43 -299.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1589" d="M168 328.5q0 70.5 27 189.5q63 256 186 391t352.5 135t294.5 -145q127 145 338 145q293 0 311 -256q2 -25 2 -80t-24 -135l-49 -157l-584 4q-14 -53 -14 -82t2 -41q12 -117 162.5 -116.5t300.5 12.5l57 6l-39 -174q-195 -47 -392.5 -47.5t-262.5 131.5 q-127 -131 -340 -132q-297 0 -324 242q-4 39 -4 109.5zM383 350q0 -47 2 -63q12 -111 162.5 -111t220.5 156q37 78 62.5 179t25.5 143t-2 59q-8 133 -154 133q-119 0 -185 -82t-99 -224.5t-33 -189.5zM1061 592l389 -4q18 88 18 117q0 86 -35.5 111.5t-124.5 25.5 t-153.5 -60.5t-93.5 -189.5z" />
<glyph unicode="&#x154;" horiz-adv-x="1148" d="M143 0l326 1403h416q205 0 303 -88t98 -288t-97 -334t-237 -181l131 -512h-227l-115 481h-258l-110 -481h-230zM528 678h236q131 0 209 98t78 222t-41 166t-142 42h-217zM682 1759l508 189l20 -199l-510 -164z" />
<glyph unicode="&#x155;" horiz-adv-x="667" d="M123 0l235 1024h220l-35 -150q70 55 169 107.5t173 65.5l-64 -230q-92 -27 -264 -108l-53 -25l-160 -684h-221zM379 1374l508 189l18 -199l-508 -166z" />
<glyph unicode="&#x156;" horiz-adv-x="1148" d="M143 0l326 1403h416q205 0 303 -88t98 -288t-97 -334t-237 -181l131 -512h-227l-115 481h-258l-110 -481h-230zM299 -610l190 463h203l-209 -463h-184zM528 678h236q131 0 209 98t78 222t-41 166t-142 42h-217z" />
<glyph unicode="&#x157;" horiz-adv-x="667" d="M-76 -610l191 463h202l-208 -463h-185zM123 0l235 1024h220l-35 -150q70 55 169 107.5t173 65.5l-64 -230q-92 -27 -264 -108l-53 -25l-160 -684h-221z" />
<glyph unicode="&#x158;" horiz-adv-x="1148" d="M143 0l326 1403h416q205 0 303 -88t98 -288t-97 -334t-237 -181l131 -512h-227l-115 481h-258l-110 -481h-230zM528 678h236q131 0 209 98t78 222t-41 166t-142 42h-217zM604 1898h219l86 -139l144 139h219l-324 -296h-149z" />
<glyph unicode="&#x159;" horiz-adv-x="667" d="M123 0l235 1024h220l-35 -150q70 55 169 107.5t173 65.5l-64 -230q-92 -27 -264 -108l-53 -25l-160 -684h-221zM365 1526h202l80 -156l141 156h218l-334 -338h-137z" />
<glyph unicode="&#x15a;" horiz-adv-x="1017" d="M109 49l36 166q236 -39 383 -39q285 0 285 260q0 72 -39 105.5t-185.5 93t-211 132.5t-64.5 206q0 203 131 327.5t353 124.5q92 0 193.5 -16t156.5 -33l57 -18l-41 -164q-221 37 -352 37t-198.5 -61.5t-67.5 -172.5q0 -70 40 -103.5t192.5 -95t211 -131t58.5 -204.5 q0 -231 -140.5 -358.5t-380.5 -127.5q-92 0 -196.5 17.5t-161.5 36.5zM686 1759l508 189l20 -199l-509 -164z" />
<glyph unicode="&#x15b;" horiz-adv-x="882" d="M98 37l41 166q195 -37 310 -37q219 0 219 149q0 39 -33 57.5t-150.5 49.5t-185.5 83t-68 152q0 178 121 282.5t297 104.5q170 0 309 -40l46 -13l-41 -166q-203 31 -336 31q-80 0 -126 -41t-46 -92t32.5 -71.5t120.5 -46t129 -42t82 -43.5q74 -47 74 -162 q0 -186 -125 -283.5t-328 -97.5q-156 0 -297 46zM524 1374l508 189l19 -199l-508 -166z" />
<glyph unicode="&#x15c;" horiz-adv-x="1017" d="M109 49l36 166q236 -39 383 -39q285 0 285 260q0 72 -39 105.5t-185.5 93t-211 132.5t-64.5 206q0 203 131 327.5t353 124.5q92 0 193.5 -16t156.5 -33l57 -18l-41 -164q-221 37 -352 37t-198.5 -61.5t-67.5 -172.5q0 -70 40 -103.5t192.5 -95t211 -131t58.5 -204.5 q0 -231 -140.5 -358.5t-380.5 -127.5q-92 0 -196.5 17.5t-161.5 36.5zM532 1608l349 299h129l196 -299h-202l-78 135l-158 -135h-236z" />
<glyph unicode="&#x15d;" horiz-adv-x="882" d="M98 37l41 166q195 -37 310 -37q219 0 219 149q0 39 -33 57.5t-150.5 49.5t-185.5 83t-68 152q0 178 121 282.5t297 104.5q170 0 309 -40l46 -13l-41 -166q-203 31 -336 31q-80 0 -126 -41t-46 -92t32.5 -71.5t120.5 -46t129 -42t82 -43.5q74 -47 74 -162 q0 -186 -125 -283.5t-328 -97.5q-156 0 -297 46zM397 1188l332 338h129l176 -338h-203l-71 151l-146 -151h-217z" />
<glyph unicode="&#x15e;" horiz-adv-x="1017" d="M109 49l36 166q236 -39 383 -39q285 0 285 260q0 72 -39 105.5t-185.5 93t-211 132.5t-64.5 206q0 203 131 327.5t353 124.5q92 0 193.5 -16t156.5 -33l57 -18l-41 -164q-221 37 -352 37t-198.5 -61.5t-67.5 -172.5q0 -70 40 -103.5t192.5 -95t211 -131t58.5 -204.5 q0 -227 -135.5 -354t-368.5 -132l-15 -55h13q182 0 182 -125q0 -23 -6 -51q-47 -199 -246 -199q-68 0 -145 15l-23 4l35 139q68 -4 108 -4q78 0 93 59q8 33 -9.5 43.5t-56.5 10.5h-59l37 168q-170 12 -297 53z" />
<glyph unicode="&#x15f;" horiz-adv-x="884" d="M98 37l41 166q195 -37 310 -37q219 0 219 149q0 39 -33 57.5t-150.5 49.5t-185.5 83t-68 152q0 178 121 282.5t297 104.5q170 0 309 -40l46 -13l-41 -166q-203 31 -336 31q-80 0 -126 -41t-46 -92t32.5 -71.5t120.5 -46t129 -42t82 -43.5q74 -47 74 -162 q0 -182 -120 -279t-316 -102l-15 -55h13q182 0 182 -125q0 -23 -6 -51q-47 -199 -246 -199q-66 0 -145 15l-23 4l35 139q68 -4 108 -4q78 0 93 59q8 33 -9.5 43.5t-56.5 10.5h-59l37 168q-139 12 -234 43z" />
<glyph unicode="&#x160;" horiz-adv-x="1017" d="M109 49l36 166q236 -39 383 -39q285 0 285 260q0 72 -39 105.5t-185.5 93t-211 132.5t-64.5 206q0 203 131 327.5t353 124.5q92 0 193.5 -16t156.5 -33l57 -18l-41 -164q-221 37 -352 37t-198.5 -61.5t-67.5 -172.5q0 -70 40 -103.5t192.5 -95t211 -131t58.5 -204.5 q0 -231 -140.5 -358.5t-380.5 -127.5q-92 0 -196.5 17.5t-161.5 36.5zM623 1901h219l86 -140l143 140h219l-323 -297h-150z" />
<glyph unicode="&#x161;" horiz-adv-x="882" d="M98 37l41 166q195 -37 310 -37q219 0 219 149q0 39 -33 57.5t-150.5 49.5t-185.5 83t-68 152q0 178 121 282.5t297 104.5q170 0 309 -40l46 -13l-41 -166q-203 31 -336 31q-80 0 -126 -41t-46 -92t32.5 -71.5t120.5 -46t129 -42t82 -43.5q74 -47 74 -162 q0 -186 -125 -283.5t-328 -97.5q-156 0 -297 46zM473 1526h203l80 -156l141 156h217l-334 -338h-137z" />
<glyph unicode="&#x162;" horiz-adv-x="944" d="M274 -434l35 139q68 -4 109 -4q78 0 92 59q8 33 -9.5 43.5t-56.5 10.5h-59l43 186h-72l277 1204h-336l47 199h905l-47 -199h-340l-278 -1204h-64l-20 -78h12q182 0 182 -125q0 -23 -6 -51q-47 -199 -246 -199q-66 0 -145 15z" />
<glyph unicode="&#x163;" horiz-adv-x="663" d="M199 -434l34 139q68 -4 109 -4q78 0 92 59q8 33 -9 43.5t-56 10.5h-60l39 176q-133 43 -133 196q0 84 31 211l102 437h-119l50 190h116l64 281h223l-66 -281h246l-47 -190h-244l-106 -455q-20 -86 -21 -137q0 -76 80 -76l148 12l-19 -162q-106 -39 -215 -39l-14 -55h12 q182 0 182 -125q0 -23 -6 -51q-46 -199 -245 -199q-68 0 -146 15z" />
<glyph unicode="&#x164;" horiz-adv-x="944" d="M297 1204l47 199h905l-47 -199h-340l-278 -1204h-228l277 1204h-336zM541 1898h219l86 -139l143 139h219l-323 -296h-150z" />
<glyph unicode="&#x165;" horiz-adv-x="894" d="M215 181q0 89 31 216l102 437h-119l50 190h116l64 281h223l-66 -281h246l-47 -190h-244l-106 -455q-21 -86 -21 -137q0 -76 80 -76l148 12l-19 -162q-106 -39 -213.5 -39t-166 57.5t-58.5 146.5zM915 905l119 498h203l-119 -498h-203z" />
<glyph unicode="&#x166;" horiz-adv-x="954" d="M213 551l47 199h275l104 454h-336l47 199h905l-47 -199h-340l-106 -454h287l-52 -199h-280l-127 -551h-228l127 551h-276z" />
<glyph unicode="&#x167;" horiz-adv-x="671" d="M160 459l41 192h108l43 183h-119l50 190h116l64 281h223l-65 -281h245l-47 -190h-244l-43 -183h226l-43 -192h-228l-18 -80q-20 -86 -20 -137q0 -76 79 -76l148 12l-19 -162q-106 -39 -213.5 -39t-166 57.5t-58.5 146.5t31 216l14 62h-104z" />
<glyph unicode="&#x168;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM633 1782q137 129 227 129q63 0 169 -49.5t126 -49.5q39 0 141 64l33 20l4 -155 q-129 -125 -221 -125q-51 0 -158.5 49t-134.5 49q-51 0 -147 -65l-33 -23z" />
<glyph unicode="&#x169;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM479 1395q131 123 221 123 q57 0 149.5 -46.5t113.5 -46.5q39 0 135 62l30 20l9 -157q-127 -121 -217 -121q-49 0 -143.5 47t-119.5 47q-49 0 -141 -61l-33 -23z" />
<glyph unicode="&#x16a;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM647 1667l41 178h641l-43 -178h-639z" />
<glyph unicode="&#x16b;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM483 1294l41 181h604l-43 -181h-602z" />
<glyph unicode="&#x16c;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM686 1833q0 29 4 61h201q-2 -14 -2 -24q0 -80 94 -80q49 0 87 28.5t54 75.5h201 q-90 -281 -379 -280q-143 0 -201.5 63.5t-58.5 155.5z" />
<glyph unicode="&#x16d;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM508 1440q0 35 6 73h199q-2 -14 -2 -37.5 t20.5 -52t83.5 -28.5q121 0 160 118h199q-98 -303 -414 -303q-145 0 -207 80q-45 57 -45 150z" />
<glyph unicode="&#x16e;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM747.5 1634q0.5 45 10.5 82q29 113 108.5 169.5t189 56.5t165.5 -56q35 -37 34 -84 q1 -170 -114 -252q-80 -57 -191.5 -57t-167.5 59q-35 37 -34.5 82zM901 1716q-6 -35 14.5 -56.5t67.5 -21.5q98 0 119 78q2 10 2 26.5t-22.5 35t-61.5 18.5q-96 0 -119 -80z" />
<glyph unicode="&#x16f;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM602 1231q0 41 10 78q21 90 89.5 150.5 t159.5 60.5t128 -56q25 -37 25 -75.5t-6 -69.5q-16 -102 -87 -162.5t-165 -60.5t-131 59q-23 35 -23 76zM735 1309q-8 -31 8.5 -54.5t48 -23.5t57 21.5t35 53t-7 53t-47.5 21.5q-72 0 -94 -71z" />
<glyph unicode="&#x170;" horiz-adv-x="1216" d="M219 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -19 -152q0 -133 209 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-57 -250 -187.5 -364t-347.5 -114q-436 0 -436 308zM623 1602l276 348l168 -80l-291 -340zM1030 1602l273 346l170 -80l-291 -340z" />
<glyph unicode="&#x171;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024h-213l23 135q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200zM473 1282l330 342l141 -115l-344 -340z M858 1282l332 344l141 -117l-344 -340z" />
<glyph unicode="&#x172;" horiz-adv-x="1222" d="M221 285q0 86 25 209l211 909h229l-219 -942q-18 -82 -18 -152q0 -133 208 -133q141 0 206 65.5t100 213.5l219 948h229l-219 -948q-49 -215 -149.5 -328t-274.5 -139q-139 -119 -139 -187q0 -35 37 -34l86 8l-23 -185q-104 -20 -180 -20t-118 38t-42 107q0 117 168 267 q-336 31 -336 303z" />
<glyph unicode="&#x173;" horiz-adv-x="1036" d="M188 252q0 76 41 262l117 510h221l-112 -494q-37 -147 -37 -227q0 -133 106 -133q43 0 104.5 42t102.5 85l41 43l158 684h221l-236 -1024q-51 -31 -110.5 -94.5t-59.5 -101t37 -37.5l86 8l-22 -185q-106 -20 -182 -20t-117 38t-41 108.5t61.5 150.5t134.5 139l23 129 q-16 -18 -43 -43.5t-103.5 -70t-144.5 -44.5q-125 0 -185.5 75t-60.5 200z" />
<glyph unicode="&#x174;" horiz-adv-x="1738" d="M307 0l60 1403h229l-78 -1192h41l502 1171h250l-56 -1171h39l494 1192h227l-602 -1403h-350l55 1079l-450 -1079h-361zM885 1606l348 299h129l197 -299h-203l-78 135l-158 -135h-235z" />
<glyph unicode="&#x175;" horiz-adv-x="1531" d="M244 0l41 1024h223l-45 -834h43l366 814h254l-18 -814h43l346 834h225l-438 -1024h-377l19 709l-312 -709h-370zM702 1188l332 338h129l176 -338h-202l-72 151l-145 -151h-218z" />
<glyph unicode="&#x176;" horiz-adv-x="1021" d="M334 1403h231l127 -594l402 594h241l-583 -807l-142 -596h-213l142 610zM535 1606l348 299h129l196 -299h-202l-78 135l-158 -135h-235z" />
<glyph unicode="&#x177;" horiz-adv-x="942" d="M211 -440l221 440h-156l-8 1024h219l-10 -840h45l396 840h225l-709 -1464h-223zM408 1188l331 338h129l176 -338h-202l-72 151l-145 -151h-217z" />
<glyph unicode="&#x178;" horiz-adv-x="1021" d="M334 1403h231l127 -594l402 594h241l-583 -807l-142 -596h-213l142 610zM580 1636l55 240h184l-57 -240h-182zM977 1636l55 240h185l-60 -240h-180z" />
<glyph unicode="&#x179;" horiz-adv-x="1007" d="M76 0l55 248l805 905l12 51h-594l47 199h854l-57 -252l-809 -901l-12 -51h600l-47 -199h-854zM645 1759l508 189l21 -199l-510 -164z" />
<glyph unicode="&#x17a;" horiz-adv-x="851" d="M74 0l39 186l594 648h-443l47 190h705l-45 -184l-596 -650h450l-49 -190h-702zM487 1374l508 189l19 -199l-508 -166z" />
<glyph unicode="&#x17b;" horiz-adv-x="1007" d="M76 0l55 248l805 905l12 51h-594l47 199h854l-57 -252l-809 -901l-12 -51h600l-47 -199h-854zM774 1630l55 242h195l-57 -242h-193z" />
<glyph unicode="&#x17c;" horiz-adv-x="851" d="M74 0l39 186l594 648h-443l47 190h705l-45 -184l-596 -650h450l-49 -190h-702zM592 1212l55 238h191l-56 -238h-190z" />
<glyph unicode="&#x17d;" horiz-adv-x="1007" d="M76 0l55 248l805 905l12 51h-594l47 199h854l-57 -252l-809 -901l-12 -51h600l-47 -199h-854zM600 1896h219l86 -139l144 139h219l-324 -297h-149z" />
<glyph unicode="&#x17e;" horiz-adv-x="851" d="M74 0l39 186l594 648h-443l47 190h705l-45 -184l-596 -650h450l-49 -190h-702zM446 1526h203l80 -156l141 156h217l-333 -338h-138z" />
<glyph unicode="&#x192;" d="M43 -475l41 178q92 -6 147 -6q111 0 152 174l209 883h-193l45 207h197l18 69q61 229 141.5 315.5t214.5 86.5t257 -25l-45 -188q-131 6 -196.5 6t-102.5 -54.5t-60 -162.5l-10 -47h293l-49 -207h-293l-211 -887q-49 -207 -138 -286t-247 -79q-70 0 -143 17z" />
<glyph unicode="&#x1fa;" horiz-adv-x="1130" d="M37 0l641 1362q-10 27 -10 59.5t10 67.5q29 113 107.5 169t189 56t164.5 -55q37 -37 37 -84q0 -125 -70 -211l-12 -1364h-228v293h-458l-142 -293h-229zM496 494h376l11 710h-52zM737 1948l508 188l19 -199l-508 -165zM819 1475q0 -63 92.5 -63.5t110.5 77.5q2 10 2 26.5 t-22.5 35t-61.5 18.5q-96 0 -119 -80q-2 -8 -2 -14z" />
<glyph unicode="&#x1fb;" horiz-adv-x="1056" d="M172 298q0 138 32 261t91 234.5t162.5 182.5t232.5 71q115 0 385 -46l86 -14l-153 -663l-56 -320l-219 -8q23 147 31 186q-96 -127 -219 -182q-49 -23 -96 -23q-141 0 -209 91.5t-68 229.5zM397 319q0 -76 28 -113.5t86 -37.5t128 54t117 110l47 55l104 440 q-139 31 -221 31t-154 -92q-135 -170 -135 -447zM643 1231q0 41 10 78q21 90 89.5 150.5t159.5 60.5t128 -56q25 -37 25 -75.5t-6 -69.5q-16 -102 -87 -162.5t-165.5 -60.5t-130.5 59q-23 35 -23 76zM711 1755l508 189l20 -215l-510 -168zM774 1284.5q0 -14.5 13.5 -34 t45 -19.5t56.5 22q39 31 39 62q0 66 -58 65q-71 1 -94 -71q-2 -10 -2 -24.5z" />
<glyph unicode="&#x1fc;" horiz-adv-x="1566" d="M39 0l663 1419h1119l-47 -217l-603 4l-94 -397h486l-47 -207h-488l-92 -387h600l-47 -215h-817l69 295h-344l-141 -295h-217zM494 506h299l163 698h-139zM1040 1759l508 189l21 -199l-510 -164z" />
<glyph unicode="&#x1fd;" horiz-adv-x="1492" d="M115 203q0 59 10 102q55 258 332 303q147 25 297 25q18 57 18 106q0 96 -88 97q-145 0 -311 -21l-60 -6l35 199q248 37 393 36q178 0 230 -112q115 113 301 112q307 0 307 -294q0 -94 -29 -187l-39 -147l-583 4q-10 -61 -10 -98q0 -141 157 -142q147 0 297 13l58 6 l-39 -174q-195 -47 -386.5 -47.5t-265.5 104.5q-74 -33 -198.5 -69t-198.5 -36q-129 0 -186 101q-41 66 -41 125zM336 268q0 -98 82 -98q96 0 274 49q-4 25 -4 79t27 165q-131 -8 -213 -19q-133 -14 -160 -133q-6 -23 -6 -43zM813 1374l508 189l18 -199l-508 -166zM967 592 l383 -4q18 66 18 100.5t-2 50.5q-10 113 -143 113q-104 0 -164 -64.5t-92 -195.5z" />
<glyph unicode="&#x1fe;" horiz-adv-x="1273" d="M125 -172l184 272q-90 106 -90 301q0 266 83 506t241 377q158 141 385 141q145 0 248 -49l180 264l129 -53l-201 -293q90 -109 90 -293q0 -279 -85 -524.5t-251 -378.5q-156 -121 -381 -121q-145 0 -239 43l-178 -262zM455 422q0 -59 8 -94l590 866q-53 33 -159 33 t-181 -68q-119 -109 -188.5 -327t-69.5 -410zM543 205q57 -29 160.5 -29t189.5 76q115 102 180.5 313t65.5 416q0 53 -9 86zM803 1759l508 189l22 -215l-510 -166z" />
<glyph unicode="&#x1ff;" horiz-adv-x="1013" d="M121 -170l149 233q-96 90 -96 272.5t61.5 341.5t182 263t272.5 104q96 0 176 -26l138 221l122 -53l-149 -234q96 -96 96 -274t-57.5 -335t-179 -261.5t-283.5 -104.5q-100 0 -174 25l-137 -225zM395 340q0 -43 8 -68l170 267l187 305q-31 12 -70 12q-131 0 -213 -157.5 t-82 -358.5zM487 176q23 -10 66 -10q133 0 216 155.5t83 356.5q0 25 -8 65l-166 -258zM565 1374l508 189l19 -199l-508 -166z" />
<glyph unicode="&#x218;" horiz-adv-x="1017" d="M109 49l36 166q236 -39 383 -39q285 0 285 260q0 72 -39 105.5t-185.5 93t-211 132.5t-64.5 206q0 203 131 327.5t353 124.5q92 0 193.5 -16t156.5 -33l57 -18l-41 -164q-221 37 -352 37t-198.5 -61.5t-67.5 -172.5q0 -70 40 -103.5t192.5 -95t211 -131t58.5 -204.5 q0 -231 -140.5 -358.5t-380.5 -127.5q-92 0 -196.5 17.5t-161.5 36.5zM207 -610l190 463h203l-209 -463h-184z" />
<glyph unicode="&#x219;" horiz-adv-x="880" d="M98 37l41 166q195 -37 310 -37q219 0 219 149q0 39 -33 57.5t-150.5 49.5t-185.5 83t-68 152q0 178 121 282.5t297 104.5q170 0 309 -40l46 -13l-41 -166q-203 31 -336 31q-80 0 -126 -41t-46 -92t32.5 -71.5t120.5 -46t129 -42t82 -43.5q74 -47 74 -162 q0 -186 -125 -283.5t-328 -97.5q-156 0 -297 46zM143 -610l191 463h203l-209 -463h-185z" />
<glyph unicode="&#x21a;" horiz-adv-x="944" d="M156 -610l190 463h203l-209 -463h-184zM297 1204l47 199h905l-47 -199h-340l-278 -1204h-228l277 1204h-336z" />
<glyph unicode="&#x21b;" horiz-adv-x="663" d="M66 -610l190 463h203l-209 -463h-184zM215 181q0 89 31 216l102 437h-119l50 190h116l64 281h223l-66 -281h246l-47 -190h-244l-106 -455q-21 -86 -21 -137q0 -76 80 -76l148 12l-19 -162q-106 -39 -213.5 -39t-166 57.5t-58.5 146.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1619" d="M752 1188l331 338h129l177 -338h-203l-72 151l-145 -151h-217z" />
<glyph unicode="&#x2da;" horiz-adv-x="1619" d="M618 1231q0 41 11 78q20 90 88.5 150.5t160 60.5t128.5 -56q25 -37 24.5 -75.5t-6.5 -69.5q-16 -102 -87 -162.5t-165 -60.5t-131 59q-23 35 -23 76zM750 1284.5q0 -14.5 13 -34t45 -19.5t56 22q39 31 39 62q0 66 -57 65q-71 1 -94 -71q-2 -10 -2 -24.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="897" d="M424 1395q131 123 221 123q55 0 149.5 -46.5t114.5 -46.5q37 0 131 62l33 20l8 -157q-127 -121 -217 -121q-49 0 -143 47t-119 47q-49 0 -143 -61l-31 -23z" />
<glyph unicode="&#x2000;" horiz-adv-x="1068" />
<glyph unicode="&#x2001;" horiz-adv-x="2136" />
<glyph unicode="&#x2002;" horiz-adv-x="1068" />
<glyph unicode="&#x2003;" horiz-adv-x="2136" />
<glyph unicode="&#x2004;" horiz-adv-x="712" />
<glyph unicode="&#x2005;" horiz-adv-x="534" />
<glyph unicode="&#x2006;" horiz-adv-x="356" />
<glyph unicode="&#x2007;" horiz-adv-x="356" />
<glyph unicode="&#x2008;" horiz-adv-x="267" />
<glyph unicode="&#x2009;" horiz-adv-x="427" />
<glyph unicode="&#x200a;" horiz-adv-x="118" />
<glyph unicode="&#x2010;" horiz-adv-x="608" d="M147 444l52 201h512l-49 -201h-515z" />
<glyph unicode="&#x2011;" horiz-adv-x="608" d="M147 444l52 201h512l-49 -201h-515z" />
<glyph unicode="&#x2012;" horiz-adv-x="608" d="M147 444l52 201h512l-49 -201h-515z" />
<glyph unicode="&#x2013;" horiz-adv-x="1138" d="M184 451v190h1024v-190h-1024z" />
<glyph unicode="&#x2014;" horiz-adv-x="2162" d="M184 451v190h2048v-190h-2048z" />
<glyph unicode="&#x2018;" horiz-adv-x="362" d="M236 907l258 494h165l-202 -494h-221z" />
<glyph unicode="&#x2019;" horiz-adv-x="362" d="M238 909l202 494h222l-259 -494h-165z" />
<glyph unicode="&#x201a;" horiz-adv-x="327" d="M-90 -311l203 493h221l-258 -493h-166z" />
<glyph unicode="&#x201c;" horiz-adv-x="720" d="M233 909l259 494h165l-202 -494h-222zM594 909l258 494h166l-203 -494h-221z" />
<glyph unicode="&#x201d;" horiz-adv-x="722" d="M238 909l202 494h222l-259 -494h-165zM598 909l203 494h221l-258 -494h-166z" />
<glyph unicode="&#x201e;" horiz-adv-x="690" d="M-131 -317l258 493h166l-203 -493h-221zM270 -317l258 493h166l-202 -493h-222z" />
<glyph unicode="&#x2022;" horiz-adv-x="962" d="M340 211v577h512v-577h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1280" d="M51 0l62 276h225l-62 -276h-225zM528 0l62 276h225l-61 -276h-226zM1006 0l61 276h225l-61 -276h-225z" />
<glyph unicode="&#x202f;" horiz-adv-x="427" />
<glyph unicode="&#x2039;" horiz-adv-x="495" d="M133 451l39 102l479 315l-63 -219l-248 -153l172 -174l-55 -201z" />
<glyph unicode="&#x203a;" horiz-adv-x="499" d="M78 121l63 219l248 154l-172 174l55 200l324 -329l-39 -103z" />
<glyph unicode="&#x205f;" horiz-adv-x="534" />
<glyph unicode="&#x20ac;" d="M195 397l41 176h86q14 98 34 175h-84l43 176h101q86 205 230.5 327.5t350.5 122.5q141 0 285 -39l49 -14l-51 -166q-135 29 -293 29q-207 0 -338 -260h479l-38 -176h-506q-25 -86 -37 -175h504l-41 -176h-475v-14q0 -213 196 -213q139 0 275 23l45 6l-17 -172 q-180 -50 -332 -50q-395 0 -395 390v30h-112z" />
<glyph unicode="&#x2122;" horiz-adv-x="1419" d="M442 1169v138h410v-138h-107v-489h-151v489h-152zM909 678v629h183l106 -369l115 369h180v-629h-141v401l-99 -364h-100l-102 364v-401h-142z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1024" d="M0 0v1024h1024v-1024h-1024z" />
<hkern u1="&#x22;" u2="&#x135;" k="-80" />
<hkern u1="&#x22;" u2="&#x12d;" k="-82" />
<hkern u1="&#x22;" u2="&#x12b;" k="-74" />
<hkern u1="&#x22;" u2="&#x129;" k="-113" />
<hkern u1="&#x22;" u2="&#xef;" k="-84" />
<hkern u1="&#x22;" u2="&#xee;" k="-96" />
<hkern u1="&#x22;" u2="&#xec;" k="-145" />
<hkern u1="&#x22;" u2="&#x40;" k="16" />
<hkern u1="&#x22;" u2="&#x2f;" k="127" />
<hkern u1="&#x22;" u2="&#x26;" k="68" />
<hkern u1="&#x26;" u2="&#x201d;" k="90" />
<hkern u1="&#x26;" u2="&#x2019;" k="90" />
<hkern u1="&#x26;" u2="&#x21b;" k="6" />
<hkern u1="&#x26;" u2="&#x21a;" k="63" />
<hkern u1="&#x26;" u2="&#x1fe;" k="8" />
<hkern u1="&#x26;" u2="&#x1fc;" k="-10" />
<hkern u1="&#x26;" u2="&#x1fa;" k="-10" />
<hkern u1="&#x26;" u2="&#x178;" k="98" />
<hkern u1="&#x26;" u2="&#x177;" k="12" />
<hkern u1="&#x26;" u2="&#x176;" k="98" />
<hkern u1="&#x26;" u2="&#x175;" k="10" />
<hkern u1="&#x26;" u2="&#x174;" k="41" />
<hkern u1="&#x26;" u2="&#x172;" k="6" />
<hkern u1="&#x26;" u2="&#x170;" k="6" />
<hkern u1="&#x26;" u2="&#x16e;" k="6" />
<hkern u1="&#x26;" u2="&#x16c;" k="6" />
<hkern u1="&#x26;" u2="&#x16a;" k="6" />
<hkern u1="&#x26;" u2="&#x168;" k="6" />
<hkern u1="&#x26;" u2="&#x167;" k="6" />
<hkern u1="&#x26;" u2="&#x166;" k="63" />
<hkern u1="&#x26;" u2="&#x165;" k="6" />
<hkern u1="&#x26;" u2="&#x164;" k="63" />
<hkern u1="&#x26;" u2="&#x152;" k="8" />
<hkern u1="&#x26;" u2="&#x150;" k="8" />
<hkern u1="&#x26;" u2="&#x14e;" k="8" />
<hkern u1="&#x26;" u2="&#x14c;" k="8" />
<hkern u1="&#x26;" u2="&#x134;" k="-20" />
<hkern u1="&#x26;" u2="&#x122;" k="8" />
<hkern u1="&#x26;" u2="&#x120;" k="8" />
<hkern u1="&#x26;" u2="&#x11e;" k="8" />
<hkern u1="&#x26;" u2="&#x11c;" k="8" />
<hkern u1="&#x26;" u2="&#x10c;" k="8" />
<hkern u1="&#x26;" u2="&#x10a;" k="8" />
<hkern u1="&#x26;" u2="&#x108;" k="8" />
<hkern u1="&#x26;" u2="&#x106;" k="8" />
<hkern u1="&#x26;" u2="&#x104;" k="-10" />
<hkern u1="&#x26;" u2="&#x102;" k="-10" />
<hkern u1="&#x26;" u2="&#x100;" k="-10" />
<hkern u1="&#x26;" u2="&#xff;" k="12" />
<hkern u1="&#x26;" u2="&#xfd;" k="12" />
<hkern u1="&#x26;" u2="&#xdd;" k="98" />
<hkern u1="&#x26;" u2="&#xdc;" k="6" />
<hkern u1="&#x26;" u2="&#xdb;" k="6" />
<hkern u1="&#x26;" u2="&#xda;" k="6" />
<hkern u1="&#x26;" u2="&#xd9;" k="6" />
<hkern u1="&#x26;" u2="&#xd8;" k="8" />
<hkern u1="&#x26;" u2="&#xd6;" k="8" />
<hkern u1="&#x26;" u2="&#xd5;" k="8" />
<hkern u1="&#x26;" u2="&#xd4;" k="8" />
<hkern u1="&#x26;" u2="&#xd3;" k="8" />
<hkern u1="&#x26;" u2="&#xd2;" k="8" />
<hkern u1="&#x26;" u2="&#xc7;" k="8" />
<hkern u1="&#x26;" u2="&#xc6;" k="-10" />
<hkern u1="&#x26;" u2="&#xc5;" k="-10" />
<hkern u1="&#x26;" u2="&#xc4;" k="-10" />
<hkern u1="&#x26;" u2="&#xc3;" k="-10" />
<hkern u1="&#x26;" u2="&#xc2;" k="-10" />
<hkern u1="&#x26;" u2="&#xc1;" k="-10" />
<hkern u1="&#x26;" u2="&#xc0;" k="-10" />
<hkern u1="&#x26;" u2="y" k="12" />
<hkern u1="&#x26;" u2="w" k="10" />
<hkern u1="&#x26;" u2="v" k="12" />
<hkern u1="&#x26;" u2="t" k="6" />
<hkern u1="&#x26;" u2="Y" k="98" />
<hkern u1="&#x26;" u2="X" k="-8" />
<hkern u1="&#x26;" u2="W" k="41" />
<hkern u1="&#x26;" u2="V" k="55" />
<hkern u1="&#x26;" u2="U" k="6" />
<hkern u1="&#x26;" u2="T" k="63" />
<hkern u1="&#x26;" u2="Q" k="8" />
<hkern u1="&#x26;" u2="O" k="8" />
<hkern u1="&#x26;" u2="J" k="-20" />
<hkern u1="&#x26;" u2="G" k="8" />
<hkern u1="&#x26;" u2="C" k="8" />
<hkern u1="&#x26;" u2="A" k="-10" />
<hkern u1="&#x26;" u2="&#x27;" k="94" />
<hkern u1="&#x26;" u2="&#x22;" k="94" />
<hkern u1="&#x27;" u2="&#x135;" k="-80" />
<hkern u1="&#x27;" u2="&#x12d;" k="-82" />
<hkern u1="&#x27;" u2="&#x12b;" k="-74" />
<hkern u1="&#x27;" u2="&#x129;" k="-113" />
<hkern u1="&#x27;" u2="&#xef;" k="-84" />
<hkern u1="&#x27;" u2="&#xee;" k="-96" />
<hkern u1="&#x27;" u2="&#xec;" k="-145" />
<hkern u1="&#x27;" u2="&#x40;" k="16" />
<hkern u1="&#x27;" u2="&#x2f;" k="127" />
<hkern u1="&#x27;" u2="&#x26;" k="68" />
<hkern u1="&#x28;" u2="&#x21b;" k="37" />
<hkern u1="&#x28;" u2="&#x219;" k="39" />
<hkern u1="&#x28;" u2="&#x218;" k="33" />
<hkern u1="&#x28;" u2="&#x1ff;" k="55" />
<hkern u1="&#x28;" u2="&#x1fe;" k="51" />
<hkern u1="&#x28;" u2="&#x1fd;" k="55" />
<hkern u1="&#x28;" u2="&#x1fc;" k="29" />
<hkern u1="&#x28;" u2="&#x1fb;" k="55" />
<hkern u1="&#x28;" u2="&#x1fa;" k="29" />
<hkern u1="&#x28;" u2="&#x17e;" k="31" />
<hkern u1="&#x28;" u2="&#x17d;" k="16" />
<hkern u1="&#x28;" u2="&#x17c;" k="31" />
<hkern u1="&#x28;" u2="&#x17b;" k="16" />
<hkern u1="&#x28;" u2="&#x17a;" k="31" />
<hkern u1="&#x28;" u2="&#x179;" k="16" />
<hkern u1="&#x28;" u2="&#x177;" k="35" />
<hkern u1="&#x28;" u2="&#x175;" k="45" />
<hkern u1="&#x28;" u2="&#x173;" k="51" />
<hkern u1="&#x28;" u2="&#x172;" k="27" />
<hkern u1="&#x28;" u2="&#x171;" k="51" />
<hkern u1="&#x28;" u2="&#x170;" k="27" />
<hkern u1="&#x28;" u2="&#x16f;" k="51" />
<hkern u1="&#x28;" u2="&#x16e;" k="27" />
<hkern u1="&#x28;" u2="&#x16d;" k="51" />
<hkern u1="&#x28;" u2="&#x16c;" k="27" />
<hkern u1="&#x28;" u2="&#x16b;" k="51" />
<hkern u1="&#x28;" u2="&#x16a;" k="27" />
<hkern u1="&#x28;" u2="&#x169;" k="51" />
<hkern u1="&#x28;" u2="&#x168;" k="27" />
<hkern u1="&#x28;" u2="&#x167;" k="37" />
<hkern u1="&#x28;" u2="&#x165;" k="37" />
<hkern u1="&#x28;" u2="&#x161;" k="39" />
<hkern u1="&#x28;" u2="&#x160;" k="33" />
<hkern u1="&#x28;" u2="&#x15f;" k="39" />
<hkern u1="&#x28;" u2="&#x15e;" k="33" />
<hkern u1="&#x28;" u2="&#x15d;" k="39" />
<hkern u1="&#x28;" u2="&#x15c;" k="33" />
<hkern u1="&#x28;" u2="&#x15b;" k="39" />
<hkern u1="&#x28;" u2="&#x15a;" k="33" />
<hkern u1="&#x28;" u2="&#x159;" k="-23" />
<hkern u1="&#x28;" u2="&#x158;" k="29" />
<hkern u1="&#x28;" u2="&#x157;" k="41" />
<hkern u1="&#x28;" u2="&#x156;" k="29" />
<hkern u1="&#x28;" u2="&#x155;" k="41" />
<hkern u1="&#x28;" u2="&#x154;" k="29" />
<hkern u1="&#x28;" u2="&#x153;" k="55" />
<hkern u1="&#x28;" u2="&#x152;" k="51" />
<hkern u1="&#x28;" u2="&#x151;" k="55" />
<hkern u1="&#x28;" u2="&#x150;" k="51" />
<hkern u1="&#x28;" u2="&#x14f;" k="55" />
<hkern u1="&#x28;" u2="&#x14e;" k="51" />
<hkern u1="&#x28;" u2="&#x14d;" k="55" />
<hkern u1="&#x28;" u2="&#x14c;" k="51" />
<hkern u1="&#x28;" u2="&#x14b;" k="41" />
<hkern u1="&#x28;" u2="&#x14a;" k="29" />
<hkern u1="&#x28;" u2="&#x148;" k="41" />
<hkern u1="&#x28;" u2="&#x147;" k="29" />
<hkern u1="&#x28;" u2="&#x146;" k="41" />
<hkern u1="&#x28;" u2="&#x145;" k="29" />
<hkern u1="&#x28;" u2="&#x144;" k="41" />
<hkern u1="&#x28;" u2="&#x143;" k="29" />
<hkern u1="&#x28;" u2="&#x142;" k="20" />
<hkern u1="&#x28;" u2="&#x141;" k="29" />
<hkern u1="&#x28;" u2="&#x13e;" k="20" />
<hkern u1="&#x28;" u2="&#x13d;" k="29" />
<hkern u1="&#x28;" u2="&#x13c;" k="20" />
<hkern u1="&#x28;" u2="&#x13b;" k="29" />
<hkern u1="&#x28;" u2="&#x13a;" k="20" />
<hkern u1="&#x28;" u2="&#x139;" k="29" />
<hkern u1="&#x28;" u2="&#x137;" k="20" />
<hkern u1="&#x28;" u2="&#x136;" k="29" />
<hkern u1="&#x28;" u2="&#x135;" k="20" />
<hkern u1="&#x28;" u2="&#x131;" k="20" />
<hkern u1="&#x28;" u2="&#x130;" k="29" />
<hkern u1="&#x28;" u2="&#x12f;" k="20" />
<hkern u1="&#x28;" u2="&#x12e;" k="29" />
<hkern u1="&#x28;" u2="&#x12d;" k="20" />
<hkern u1="&#x28;" u2="&#x12c;" k="29" />
<hkern u1="&#x28;" u2="&#x12b;" k="20" />
<hkern u1="&#x28;" u2="&#x12a;" k="29" />
<hkern u1="&#x28;" u2="&#x129;" k="20" />
<hkern u1="&#x28;" u2="&#x128;" k="29" />
<hkern u1="&#x28;" u2="&#x127;" k="20" />
<hkern u1="&#x28;" u2="&#x126;" k="29" />
<hkern u1="&#x28;" u2="&#x125;" k="20" />
<hkern u1="&#x28;" u2="&#x124;" k="29" />
<hkern u1="&#x28;" u2="&#x122;" k="51" />
<hkern u1="&#x28;" u2="&#x120;" k="51" />
<hkern u1="&#x28;" u2="&#x11e;" k="51" />
<hkern u1="&#x28;" u2="&#x11c;" k="51" />
<hkern u1="&#x28;" u2="&#x11b;" k="55" />
<hkern u1="&#x28;" u2="&#x11a;" k="29" />
<hkern u1="&#x28;" u2="&#x119;" k="55" />
<hkern u1="&#x28;" u2="&#x118;" k="29" />
<hkern u1="&#x28;" u2="&#x117;" k="55" />
<hkern u1="&#x28;" u2="&#x116;" k="29" />
<hkern u1="&#x28;" u2="&#x115;" k="55" />
<hkern u1="&#x28;" u2="&#x114;" k="29" />
<hkern u1="&#x28;" u2="&#x113;" k="55" />
<hkern u1="&#x28;" u2="&#x112;" k="29" />
<hkern u1="&#x28;" u2="&#x111;" k="55" />
<hkern u1="&#x28;" u2="&#x110;" k="29" />
<hkern u1="&#x28;" u2="&#x10f;" k="55" />
<hkern u1="&#x28;" u2="&#x10e;" k="29" />
<hkern u1="&#x28;" u2="&#x10d;" k="55" />
<hkern u1="&#x28;" u2="&#x10c;" k="51" />
<hkern u1="&#x28;" u2="&#x10b;" k="55" />
<hkern u1="&#x28;" u2="&#x10a;" k="51" />
<hkern u1="&#x28;" u2="&#x109;" k="55" />
<hkern u1="&#x28;" u2="&#x108;" k="51" />
<hkern u1="&#x28;" u2="&#x107;" k="55" />
<hkern u1="&#x28;" u2="&#x106;" k="51" />
<hkern u1="&#x28;" u2="&#x105;" k="55" />
<hkern u1="&#x28;" u2="&#x104;" k="29" />
<hkern u1="&#x28;" u2="&#x103;" k="55" />
<hkern u1="&#x28;" u2="&#x102;" k="29" />
<hkern u1="&#x28;" u2="&#x101;" k="55" />
<hkern u1="&#x28;" u2="&#x100;" k="29" />
<hkern u1="&#x28;" u2="&#xff;" k="35" />
<hkern u1="&#x28;" u2="&#xfd;" k="35" />
<hkern u1="&#x28;" u2="&#xfc;" k="51" />
<hkern u1="&#x28;" u2="&#xfb;" k="51" />
<hkern u1="&#x28;" u2="&#xfa;" k="51" />
<hkern u1="&#x28;" u2="&#xf9;" k="51" />
<hkern u1="&#x28;" u2="&#xf8;" k="55" />
<hkern u1="&#x28;" u2="&#xf6;" k="55" />
<hkern u1="&#x28;" u2="&#xf5;" k="55" />
<hkern u1="&#x28;" u2="&#xf4;" k="55" />
<hkern u1="&#x28;" u2="&#xf3;" k="55" />
<hkern u1="&#x28;" u2="&#xf2;" k="55" />
<hkern u1="&#x28;" u2="&#xf1;" k="41" />
<hkern u1="&#x28;" u2="&#xef;" k="20" />
<hkern u1="&#x28;" u2="&#xee;" k="20" />
<hkern u1="&#x28;" u2="&#xed;" k="20" />
<hkern u1="&#x28;" u2="&#xec;" k="20" />
<hkern u1="&#x28;" u2="&#xeb;" k="55" />
<hkern u1="&#x28;" u2="&#xea;" k="55" />
<hkern u1="&#x28;" u2="&#xe9;" k="55" />
<hkern u1="&#x28;" u2="&#xe8;" k="55" />
<hkern u1="&#x28;" u2="&#xe7;" k="55" />
<hkern u1="&#x28;" u2="&#xe6;" k="55" />
<hkern u1="&#x28;" u2="&#xe5;" k="55" />
<hkern u1="&#x28;" u2="&#xe4;" k="55" />
<hkern u1="&#x28;" u2="&#xe3;" k="55" />
<hkern u1="&#x28;" u2="&#xe2;" k="55" />
<hkern u1="&#x28;" u2="&#xe1;" k="55" />
<hkern u1="&#x28;" u2="&#xe0;" k="55" />
<hkern u1="&#x28;" u2="&#xdf;" k="20" />
<hkern u1="&#x28;" u2="&#xdc;" k="27" />
<hkern u1="&#x28;" u2="&#xdb;" k="27" />
<hkern u1="&#x28;" u2="&#xda;" k="27" />
<hkern u1="&#x28;" u2="&#xd9;" k="27" />
<hkern u1="&#x28;" u2="&#xd8;" k="51" />
<hkern u1="&#x28;" u2="&#xd6;" k="51" />
<hkern u1="&#x28;" u2="&#xd5;" k="51" />
<hkern u1="&#x28;" u2="&#xd4;" k="51" />
<hkern u1="&#x28;" u2="&#xd3;" k="51" />
<hkern u1="&#x28;" u2="&#xd2;" k="51" />
<hkern u1="&#x28;" u2="&#xd1;" k="29" />
<hkern u1="&#x28;" u2="&#xcf;" k="29" />
<hkern u1="&#x28;" u2="&#xce;" k="29" />
<hkern u1="&#x28;" u2="&#xcd;" k="29" />
<hkern u1="&#x28;" u2="&#xcc;" k="29" />
<hkern u1="&#x28;" u2="&#xcb;" k="29" />
<hkern u1="&#x28;" u2="&#xca;" k="29" />
<hkern u1="&#x28;" u2="&#xc9;" k="29" />
<hkern u1="&#x28;" u2="&#xc8;" k="29" />
<hkern u1="&#x28;" u2="&#xc7;" k="51" />
<hkern u1="&#x28;" u2="&#xc6;" k="29" />
<hkern u1="&#x28;" u2="&#xc5;" k="29" />
<hkern u1="&#x28;" u2="&#xc4;" k="29" />
<hkern u1="&#x28;" u2="&#xc3;" k="29" />
<hkern u1="&#x28;" u2="&#xc2;" k="29" />
<hkern u1="&#x28;" u2="&#xc1;" k="29" />
<hkern u1="&#x28;" u2="&#xc0;" k="29" />
<hkern u1="&#x28;" u2="&#x7b;" k="43" />
<hkern u1="&#x28;" u2="z" k="31" />
<hkern u1="&#x28;" u2="y" k="35" />
<hkern u1="&#x28;" u2="w" k="45" />
<hkern u1="&#x28;" u2="v" k="41" />
<hkern u1="&#x28;" u2="u" k="51" />
<hkern u1="&#x28;" u2="t" k="37" />
<hkern u1="&#x28;" u2="s" k="39" />
<hkern u1="&#x28;" u2="r" k="41" />
<hkern u1="&#x28;" u2="q" k="55" />
<hkern u1="&#x28;" u2="p" k="41" />
<hkern u1="&#x28;" u2="o" k="55" />
<hkern u1="&#x28;" u2="n" k="41" />
<hkern u1="&#x28;" u2="m" k="41" />
<hkern u1="&#x28;" u2="l" k="20" />
<hkern u1="&#x28;" u2="k" k="20" />
<hkern u1="&#x28;" u2="j" k="20" />
<hkern u1="&#x28;" u2="i" k="20" />
<hkern u1="&#x28;" u2="h" k="20" />
<hkern u1="&#x28;" u2="f" k="29" />
<hkern u1="&#x28;" u2="e" k="55" />
<hkern u1="&#x28;" u2="d" k="55" />
<hkern u1="&#x28;" u2="c" k="55" />
<hkern u1="&#x28;" u2="b" k="18" />
<hkern u1="&#x28;" u2="a" k="55" />
<hkern u1="&#x28;" u2="Z" k="16" />
<hkern u1="&#x28;" u2="U" k="27" />
<hkern u1="&#x28;" u2="S" k="33" />
<hkern u1="&#x28;" u2="R" k="29" />
<hkern u1="&#x28;" u2="Q" k="51" />
<hkern u1="&#x28;" u2="P" k="29" />
<hkern u1="&#x28;" u2="O" k="51" />
<hkern u1="&#x28;" u2="N" k="29" />
<hkern u1="&#x28;" u2="M" k="29" />
<hkern u1="&#x28;" u2="L" k="29" />
<hkern u1="&#x28;" u2="K" k="29" />
<hkern u1="&#x28;" u2="I" k="29" />
<hkern u1="&#x28;" u2="H" k="29" />
<hkern u1="&#x28;" u2="G" k="51" />
<hkern u1="&#x28;" u2="F" k="29" />
<hkern u1="&#x28;" u2="E" k="29" />
<hkern u1="&#x28;" u2="D" k="29" />
<hkern u1="&#x28;" u2="C" k="51" />
<hkern u1="&#x28;" u2="B" k="29" />
<hkern u1="&#x28;" u2="A" k="29" />
<hkern u1="&#x28;" u2="&#x28;" k="35" />
<hkern u1="&#x29;" u2="&#x7d;" k="37" />
<hkern u1="&#x29;" u2="]" k="37" />
<hkern u1="&#x29;" u2="&#x29;" k="37" />
<hkern u1="&#x2a;" u2="&#x1ff;" k="6" />
<hkern u1="&#x2a;" u2="&#x1fd;" k="6" />
<hkern u1="&#x2a;" u2="&#x1fc;" k="70" />
<hkern u1="&#x2a;" u2="&#x1fb;" k="6" />
<hkern u1="&#x2a;" u2="&#x1fa;" k="70" />
<hkern u1="&#x2a;" u2="&#x17d;" k="16" />
<hkern u1="&#x2a;" u2="&#x17b;" k="16" />
<hkern u1="&#x2a;" u2="&#x179;" k="16" />
<hkern u1="&#x2a;" u2="&#x178;" k="8" />
<hkern u1="&#x2a;" u2="&#x177;" k="-16" />
<hkern u1="&#x2a;" u2="&#x176;" k="8" />
<hkern u1="&#x2a;" u2="&#x153;" k="6" />
<hkern u1="&#x2a;" u2="&#x151;" k="6" />
<hkern u1="&#x2a;" u2="&#x14f;" k="6" />
<hkern u1="&#x2a;" u2="&#x14d;" k="6" />
<hkern u1="&#x2a;" u2="&#x135;" k="-78" />
<hkern u1="&#x2a;" u2="&#x134;" k="25" />
<hkern u1="&#x2a;" u2="&#x129;" k="-43" />
<hkern u1="&#x2a;" u2="&#x127;" k="-18" />
<hkern u1="&#x2a;" u2="&#x126;" k="-37" />
<hkern u1="&#x2a;" u2="&#x11b;" k="6" />
<hkern u1="&#x2a;" u2="&#x119;" k="6" />
<hkern u1="&#x2a;" u2="&#x117;" k="6" />
<hkern u1="&#x2a;" u2="&#x115;" k="6" />
<hkern u1="&#x2a;" u2="&#x113;" k="6" />
<hkern u1="&#x2a;" u2="&#x111;" k="6" />
<hkern u1="&#x2a;" u2="&#x10f;" k="6" />
<hkern u1="&#x2a;" u2="&#x10d;" k="6" />
<hkern u1="&#x2a;" u2="&#x10b;" k="6" />
<hkern u1="&#x2a;" u2="&#x109;" k="6" />
<hkern u1="&#x2a;" u2="&#x107;" k="6" />
<hkern u1="&#x2a;" u2="&#x105;" k="6" />
<hkern u1="&#x2a;" u2="&#x104;" k="70" />
<hkern u1="&#x2a;" u2="&#x103;" k="6" />
<hkern u1="&#x2a;" u2="&#x102;" k="70" />
<hkern u1="&#x2a;" u2="&#x101;" k="6" />
<hkern u1="&#x2a;" u2="&#x100;" k="70" />
<hkern u1="&#x2a;" u2="&#xff;" k="-16" />
<hkern u1="&#x2a;" u2="&#xfd;" k="-16" />
<hkern u1="&#x2a;" u2="&#xf8;" k="6" />
<hkern u1="&#x2a;" u2="&#xf6;" k="6" />
<hkern u1="&#x2a;" u2="&#xf5;" k="6" />
<hkern u1="&#x2a;" u2="&#xf4;" k="6" />
<hkern u1="&#x2a;" u2="&#xf3;" k="6" />
<hkern u1="&#x2a;" u2="&#xf2;" k="6" />
<hkern u1="&#x2a;" u2="&#xef;" k="-23" />
<hkern u1="&#x2a;" u2="&#xee;" k="-96" />
<hkern u1="&#x2a;" u2="&#xeb;" k="6" />
<hkern u1="&#x2a;" u2="&#xea;" k="6" />
<hkern u1="&#x2a;" u2="&#xe9;" k="6" />
<hkern u1="&#x2a;" u2="&#xe8;" k="6" />
<hkern u1="&#x2a;" u2="&#xe7;" k="6" />
<hkern u1="&#x2a;" u2="&#xe6;" k="6" />
<hkern u1="&#x2a;" u2="&#xe5;" k="6" />
<hkern u1="&#x2a;" u2="&#xe4;" k="6" />
<hkern u1="&#x2a;" u2="&#xe3;" k="6" />
<hkern u1="&#x2a;" u2="&#xe2;" k="6" />
<hkern u1="&#x2a;" u2="&#xe1;" k="6" />
<hkern u1="&#x2a;" u2="&#xe0;" k="6" />
<hkern u1="&#x2a;" u2="&#xdd;" k="8" />
<hkern u1="&#x2a;" u2="&#xc6;" k="70" />
<hkern u1="&#x2a;" u2="&#xc5;" k="70" />
<hkern u1="&#x2a;" u2="&#xc4;" k="70" />
<hkern u1="&#x2a;" u2="&#xc3;" k="70" />
<hkern u1="&#x2a;" u2="&#xc2;" k="70" />
<hkern u1="&#x2a;" u2="&#xc1;" k="70" />
<hkern u1="&#x2a;" u2="&#xc0;" k="70" />
<hkern u1="&#x2a;" u2="y" k="-16" />
<hkern u1="&#x2a;" u2="x" k="-20" />
<hkern u1="&#x2a;" u2="v" k="-14" />
<hkern u1="&#x2a;" u2="q" k="6" />
<hkern u1="&#x2a;" u2="o" k="6" />
<hkern u1="&#x2a;" u2="e" k="6" />
<hkern u1="&#x2a;" u2="d" k="6" />
<hkern u1="&#x2a;" u2="c" k="6" />
<hkern u1="&#x2a;" u2="a" k="6" />
<hkern u1="&#x2a;" u2="Z" k="16" />
<hkern u1="&#x2a;" u2="Y" k="8" />
<hkern u1="&#x2a;" u2="X" k="8" />
<hkern u1="&#x2a;" u2="J" k="25" />
<hkern u1="&#x2a;" u2="A" k="70" />
<hkern u1="&#x2c;" u2="v" k="70" />
<hkern u1="&#x2c;" u2="f" k="29" />
<hkern u1="&#x2c;" u2="V" k="96" />
<hkern u1="&#x2d;" u2="&#x166;" k="86" />
<hkern u1="&#x2d;" u2="&#x142;" k="-29" />
<hkern u1="&#x2d;" u2="&#x141;" k="-35" />
<hkern u1="&#x2d;" u2="x" k="70" />
<hkern u1="&#x2d;" u2="v" k="33" />
<hkern u1="&#x2d;" u2="f" k="33" />
<hkern u1="&#x2d;" u2="X" k="51" />
<hkern u1="&#x2d;" u2="V" k="70" />
<hkern u1="&#x2e;" u2="v" k="70" />
<hkern u1="&#x2e;" u2="f" k="29" />
<hkern u1="&#x2e;" u2="V" k="96" />
<hkern u1="&#x2f;" u2="&#x219;" k="20" />
<hkern u1="&#x2f;" u2="&#x1ff;" k="37" />
<hkern u1="&#x2f;" u2="&#x1fe;" k="23" />
<hkern u1="&#x2f;" u2="&#x1fd;" k="37" />
<hkern u1="&#x2f;" u2="&#x1fc;" k="86" />
<hkern u1="&#x2f;" u2="&#x1fb;" k="37" />
<hkern u1="&#x2f;" u2="&#x1fa;" k="86" />
<hkern u1="&#x2f;" u2="&#x17e;" k="16" />
<hkern u1="&#x2f;" u2="&#x17c;" k="16" />
<hkern u1="&#x2f;" u2="&#x17a;" k="16" />
<hkern u1="&#x2f;" u2="&#x173;" k="20" />
<hkern u1="&#x2f;" u2="&#x171;" k="20" />
<hkern u1="&#x2f;" u2="&#x16f;" k="20" />
<hkern u1="&#x2f;" u2="&#x16d;" k="20" />
<hkern u1="&#x2f;" u2="&#x16b;" k="20" />
<hkern u1="&#x2f;" u2="&#x169;" k="20" />
<hkern u1="&#x2f;" u2="&#x161;" k="20" />
<hkern u1="&#x2f;" u2="&#x15f;" k="20" />
<hkern u1="&#x2f;" u2="&#x15d;" k="20" />
<hkern u1="&#x2f;" u2="&#x15b;" k="20" />
<hkern u1="&#x2f;" u2="&#x159;" k="31" />
<hkern u1="&#x2f;" u2="&#x157;" k="31" />
<hkern u1="&#x2f;" u2="&#x155;" k="31" />
<hkern u1="&#x2f;" u2="&#x153;" k="37" />
<hkern u1="&#x2f;" u2="&#x152;" k="23" />
<hkern u1="&#x2f;" u2="&#x151;" k="37" />
<hkern u1="&#x2f;" u2="&#x150;" k="23" />
<hkern u1="&#x2f;" u2="&#x14f;" k="37" />
<hkern u1="&#x2f;" u2="&#x14e;" k="23" />
<hkern u1="&#x2f;" u2="&#x14d;" k="37" />
<hkern u1="&#x2f;" u2="&#x14c;" k="23" />
<hkern u1="&#x2f;" u2="&#x14b;" k="31" />
<hkern u1="&#x2f;" u2="&#x148;" k="31" />
<hkern u1="&#x2f;" u2="&#x146;" k="31" />
<hkern u1="&#x2f;" u2="&#x144;" k="31" />
<hkern u1="&#x2f;" u2="&#x135;" k="-14" />
<hkern u1="&#x2f;" u2="&#x134;" k="14" />
<hkern u1="&#x2f;" u2="&#x12d;" k="-63" />
<hkern u1="&#x2f;" u2="&#x12b;" k="-70" />
<hkern u1="&#x2f;" u2="&#x129;" k="-104" />
<hkern u1="&#x2f;" u2="&#x123;" k="25" />
<hkern u1="&#x2f;" u2="&#x122;" k="23" />
<hkern u1="&#x2f;" u2="&#x121;" k="25" />
<hkern u1="&#x2f;" u2="&#x120;" k="23" />
<hkern u1="&#x2f;" u2="&#x11f;" k="25" />
<hkern u1="&#x2f;" u2="&#x11e;" k="23" />
<hkern u1="&#x2f;" u2="&#x11d;" k="25" />
<hkern u1="&#x2f;" u2="&#x11c;" k="23" />
<hkern u1="&#x2f;" u2="&#x11b;" k="37" />
<hkern u1="&#x2f;" u2="&#x119;" k="37" />
<hkern u1="&#x2f;" u2="&#x117;" k="37" />
<hkern u1="&#x2f;" u2="&#x115;" k="37" />
<hkern u1="&#x2f;" u2="&#x113;" k="37" />
<hkern u1="&#x2f;" u2="&#x111;" k="37" />
<hkern u1="&#x2f;" u2="&#x10f;" k="37" />
<hkern u1="&#x2f;" u2="&#x10d;" k="37" />
<hkern u1="&#x2f;" u2="&#x10c;" k="23" />
<hkern u1="&#x2f;" u2="&#x10b;" k="37" />
<hkern u1="&#x2f;" u2="&#x10a;" k="23" />
<hkern u1="&#x2f;" u2="&#x109;" k="37" />
<hkern u1="&#x2f;" u2="&#x108;" k="23" />
<hkern u1="&#x2f;" u2="&#x107;" k="37" />
<hkern u1="&#x2f;" u2="&#x106;" k="23" />
<hkern u1="&#x2f;" u2="&#x105;" k="37" />
<hkern u1="&#x2f;" u2="&#x104;" k="86" />
<hkern u1="&#x2f;" u2="&#x103;" k="37" />
<hkern u1="&#x2f;" u2="&#x102;" k="86" />
<hkern u1="&#x2f;" u2="&#x101;" k="37" />
<hkern u1="&#x2f;" u2="&#x100;" k="86" />
<hkern u1="&#x2f;" u2="&#xfc;" k="20" />
<hkern u1="&#x2f;" u2="&#xfb;" k="20" />
<hkern u1="&#x2f;" u2="&#xfa;" k="20" />
<hkern u1="&#x2f;" u2="&#xf9;" k="20" />
<hkern u1="&#x2f;" u2="&#xf8;" k="37" />
<hkern u1="&#x2f;" u2="&#xf6;" k="37" />
<hkern u1="&#x2f;" u2="&#xf5;" k="37" />
<hkern u1="&#x2f;" u2="&#xf4;" k="37" />
<hkern u1="&#x2f;" u2="&#xf3;" k="37" />
<hkern u1="&#x2f;" u2="&#xf2;" k="37" />
<hkern u1="&#x2f;" u2="&#xf1;" k="31" />
<hkern u1="&#x2f;" u2="&#xef;" k="-76" />
<hkern u1="&#x2f;" u2="&#xee;" k="-31" />
<hkern u1="&#x2f;" u2="&#xec;" k="-137" />
<hkern u1="&#x2f;" u2="&#xeb;" k="37" />
<hkern u1="&#x2f;" u2="&#xea;" k="37" />
<hkern u1="&#x2f;" u2="&#xe9;" k="37" />
<hkern u1="&#x2f;" u2="&#xe8;" k="37" />
<hkern u1="&#x2f;" u2="&#xe7;" k="37" />
<hkern u1="&#x2f;" u2="&#xe6;" k="37" />
<hkern u1="&#x2f;" u2="&#xe5;" k="37" />
<hkern u1="&#x2f;" u2="&#xe4;" k="37" />
<hkern u1="&#x2f;" u2="&#xe3;" k="37" />
<hkern u1="&#x2f;" u2="&#xe2;" k="37" />
<hkern u1="&#x2f;" u2="&#xe1;" k="37" />
<hkern u1="&#x2f;" u2="&#xe0;" k="37" />
<hkern u1="&#x2f;" u2="&#xd8;" k="23" />
<hkern u1="&#x2f;" u2="&#xd6;" k="23" />
<hkern u1="&#x2f;" u2="&#xd5;" k="23" />
<hkern u1="&#x2f;" u2="&#xd4;" k="23" />
<hkern u1="&#x2f;" u2="&#xd3;" k="23" />
<hkern u1="&#x2f;" u2="&#xd2;" k="23" />
<hkern u1="&#x2f;" u2="&#xc7;" k="23" />
<hkern u1="&#x2f;" u2="&#xc6;" k="86" />
<hkern u1="&#x2f;" u2="&#xc5;" k="86" />
<hkern u1="&#x2f;" u2="&#xc4;" k="86" />
<hkern u1="&#x2f;" u2="&#xc3;" k="86" />
<hkern u1="&#x2f;" u2="&#xc2;" k="86" />
<hkern u1="&#x2f;" u2="&#xc1;" k="86" />
<hkern u1="&#x2f;" u2="&#xc0;" k="86" />
<hkern u1="&#x2f;" u2="z" k="16" />
<hkern u1="&#x2f;" u2="u" k="20" />
<hkern u1="&#x2f;" u2="s" k="20" />
<hkern u1="&#x2f;" u2="r" k="31" />
<hkern u1="&#x2f;" u2="q" k="37" />
<hkern u1="&#x2f;" u2="p" k="31" />
<hkern u1="&#x2f;" u2="o" k="37" />
<hkern u1="&#x2f;" u2="n" k="31" />
<hkern u1="&#x2f;" u2="m" k="31" />
<hkern u1="&#x2f;" u2="g" k="25" />
<hkern u1="&#x2f;" u2="e" k="37" />
<hkern u1="&#x2f;" u2="d" k="37" />
<hkern u1="&#x2f;" u2="c" k="37" />
<hkern u1="&#x2f;" u2="a" k="37" />
<hkern u1="&#x2f;" u2="Q" k="23" />
<hkern u1="&#x2f;" u2="O" k="23" />
<hkern u1="&#x2f;" u2="J" k="14" />
<hkern u1="&#x2f;" u2="G" k="23" />
<hkern u1="&#x2f;" u2="C" k="23" />
<hkern u1="&#x2f;" u2="A" k="86" />
<hkern u1="&#x2f;" u2="&#x2f;" k="539" />
<hkern u1="&#x3a;" u2="&#x142;" k="-18" />
<hkern u1="&#x3a;" u2="&#x141;" k="-14" />
<hkern u1="&#x3a;" u2="V" k="43" />
<hkern u1="&#x3b;" u2="&#x142;" k="-14" />
<hkern u1="&#x3b;" u2="&#x141;" k="-12" />
<hkern u1="&#x3b;" u2="V" k="43" />
<hkern u1="&#x40;" u2="&#x21a;" k="33" />
<hkern u1="&#x40;" u2="&#x1fc;" k="6" />
<hkern u1="&#x40;" u2="&#x1fa;" k="6" />
<hkern u1="&#x40;" u2="&#x178;" k="66" />
<hkern u1="&#x40;" u2="&#x176;" k="66" />
<hkern u1="&#x40;" u2="&#x166;" k="33" />
<hkern u1="&#x40;" u2="&#x164;" k="33" />
<hkern u1="&#x40;" u2="&#x134;" k="6" />
<hkern u1="&#x40;" u2="&#x104;" k="6" />
<hkern u1="&#x40;" u2="&#x102;" k="6" />
<hkern u1="&#x40;" u2="&#x100;" k="6" />
<hkern u1="&#x40;" u2="&#xdd;" k="66" />
<hkern u1="&#x40;" u2="&#xc6;" k="6" />
<hkern u1="&#x40;" u2="&#xc5;" k="6" />
<hkern u1="&#x40;" u2="&#xc4;" k="6" />
<hkern u1="&#x40;" u2="&#xc3;" k="6" />
<hkern u1="&#x40;" u2="&#xc2;" k="6" />
<hkern u1="&#x40;" u2="&#xc1;" k="6" />
<hkern u1="&#x40;" u2="&#xc0;" k="6" />
<hkern u1="&#x40;" u2="Y" k="66" />
<hkern u1="&#x40;" u2="V" k="27" />
<hkern u1="&#x40;" u2="T" k="33" />
<hkern u1="&#x40;" u2="J" k="6" />
<hkern u1="&#x40;" u2="A" k="6" />
<hkern u1="A" u2="&#x2122;" k="78" />
<hkern u1="A" u2="&#xae;" k="66" />
<hkern u1="A" u2="&#x7d;" k="35" />
<hkern u1="A" u2="&#x7c;" k="88" />
<hkern u1="A" u2="v" k="37" />
<hkern u1="A" u2="f" k="14" />
<hkern u1="A" u2="]" k="16" />
<hkern u1="A" u2="\" k="92" />
<hkern u1="A" u2="V" k="66" />
<hkern u1="A" u2="&#x40;" k="6" />
<hkern u1="A" u2="&#x3f;" k="43" />
<hkern u1="A" u2="&#x2a;" k="72" />
<hkern u1="A" u2="&#x29;" k="25" />
<hkern u1="A" u2="&#x26;" k="6" />
<hkern u1="B" u2="&#x201d;" k="14" />
<hkern u1="B" u2="&#x201c;" k="16" />
<hkern u1="B" u2="&#x2019;" k="14" />
<hkern u1="B" u2="&#x2018;" k="16" />
<hkern u1="B" u2="&#x21a;" k="29" />
<hkern u1="B" u2="&#x1fc;" k="25" />
<hkern u1="B" u2="&#x1fa;" k="25" />
<hkern u1="B" u2="&#x178;" k="63" />
<hkern u1="B" u2="&#x176;" k="63" />
<hkern u1="B" u2="&#x174;" k="18" />
<hkern u1="B" u2="&#x166;" k="29" />
<hkern u1="B" u2="&#x164;" k="29" />
<hkern u1="B" u2="&#x134;" k="10" />
<hkern u1="B" u2="&#x123;" k="14" />
<hkern u1="B" u2="&#x121;" k="14" />
<hkern u1="B" u2="&#x11f;" k="14" />
<hkern u1="B" u2="&#x11d;" k="14" />
<hkern u1="B" u2="&#x104;" k="25" />
<hkern u1="B" u2="&#x102;" k="25" />
<hkern u1="B" u2="&#x100;" k="25" />
<hkern u1="B" u2="&#xee;" k="-14" />
<hkern u1="B" u2="&#xdd;" k="63" />
<hkern u1="B" u2="&#xc6;" k="25" />
<hkern u1="B" u2="&#xc5;" k="25" />
<hkern u1="B" u2="&#xc4;" k="25" />
<hkern u1="B" u2="&#xc3;" k="25" />
<hkern u1="B" u2="&#xc2;" k="25" />
<hkern u1="B" u2="&#xc1;" k="25" />
<hkern u1="B" u2="&#xc0;" k="25" />
<hkern u1="B" u2="&#x7d;" k="59" />
<hkern u1="B" u2="&#x7c;" k="41" />
<hkern u1="B" u2="g" k="14" />
<hkern u1="B" u2="]" k="72" />
<hkern u1="B" u2="\" k="39" />
<hkern u1="B" u2="Y" k="63" />
<hkern u1="B" u2="X" k="16" />
<hkern u1="B" u2="W" k="18" />
<hkern u1="B" u2="V" k="31" />
<hkern u1="B" u2="T" k="29" />
<hkern u1="B" u2="J" k="10" />
<hkern u1="B" u2="A" k="25" />
<hkern u1="B" u2="&#x3f;" k="10" />
<hkern u1="B" u2="&#x2f;" k="29" />
<hkern u1="B" u2="&#x29;" k="43" />
<hkern u1="B" u2="&#x27;" k="16" />
<hkern u1="B" u2="&#x22;" k="16" />
<hkern u1="C" u2="&#x135;" k="-61" />
<hkern u1="C" u2="&#x12d;" k="-55" />
<hkern u1="C" u2="&#x12b;" k="-74" />
<hkern u1="C" u2="&#x129;" k="-102" />
<hkern u1="C" u2="&#xef;" k="-76" />
<hkern u1="C" u2="&#xee;" k="-78" />
<hkern u1="C" u2="&#xec;" k="-104" />
<hkern u1="C" u2="&#x2a;" k="-25" />
<hkern u1="C" u2="&#x29;" k="18" />
<hkern u1="D" u2="&#x2122;" k="29" />
<hkern u1="D" u2="&#x7d;" k="66" />
<hkern u1="D" u2="&#x7c;" k="43" />
<hkern u1="D" u2="x" k="10" />
<hkern u1="D" u2="]" k="72" />
<hkern u1="D" u2="\" k="45" />
<hkern u1="D" u2="X" k="41" />
<hkern u1="D" u2="V" k="31" />
<hkern u1="D" u2="&#x3f;" k="10" />
<hkern u1="D" u2="&#x2f;" k="35" />
<hkern u1="D" u2="&#x29;" k="55" />
<hkern u1="E" u2="&#x135;" k="-61" />
<hkern u1="E" u2="&#x12d;" k="-57" />
<hkern u1="E" u2="&#x12b;" k="-55" />
<hkern u1="E" u2="&#x129;" k="-96" />
<hkern u1="E" u2="&#xef;" k="-61" />
<hkern u1="E" u2="&#xee;" k="-78" />
<hkern u1="E" u2="&#xec;" k="-133" />
<hkern u1="E" u2="v" k="14" />
<hkern u1="E" u2="&#x29;" k="14" />
<hkern u1="F" u2="&#x203a;" k="33" />
<hkern u1="F" u2="&#x2039;" k="27" />
<hkern u1="F" u2="&#x2026;" k="96" />
<hkern u1="F" u2="&#x201e;" k="96" />
<hkern u1="F" u2="&#x201c;" k="14" />
<hkern u1="F" u2="&#x201a;" k="96" />
<hkern u1="F" u2="&#x2018;" k="14" />
<hkern u1="F" u2="&#x2014;" k="20" />
<hkern u1="F" u2="&#x2013;" k="20" />
<hkern u1="F" u2="&#x219;" k="18" />
<hkern u1="F" u2="&#x218;" k="6" />
<hkern u1="F" u2="&#x1ff;" k="20" />
<hkern u1="F" u2="&#x1fe;" k="8" />
<hkern u1="F" u2="&#x1fd;" k="23" />
<hkern u1="F" u2="&#x1fc;" k="51" />
<hkern u1="F" u2="&#x1fb;" k="23" />
<hkern u1="F" u2="&#x1fa;" k="51" />
<hkern u1="F" u2="&#x17e;" k="12" />
<hkern u1="F" u2="&#x17c;" k="12" />
<hkern u1="F" u2="&#x17a;" k="12" />
<hkern u1="F" u2="&#x173;" k="18" />
<hkern u1="F" u2="&#x171;" k="18" />
<hkern u1="F" u2="&#x16f;" k="18" />
<hkern u1="F" u2="&#x16d;" k="18" />
<hkern u1="F" u2="&#x16b;" k="18" />
<hkern u1="F" u2="&#x169;" k="18" />
<hkern u1="F" u2="&#x161;" k="18" />
<hkern u1="F" u2="&#x160;" k="6" />
<hkern u1="F" u2="&#x15f;" k="18" />
<hkern u1="F" u2="&#x15e;" k="6" />
<hkern u1="F" u2="&#x15d;" k="18" />
<hkern u1="F" u2="&#x15c;" k="6" />
<hkern u1="F" u2="&#x15b;" k="18" />
<hkern u1="F" u2="&#x15a;" k="6" />
<hkern u1="F" u2="&#x159;" k="25" />
<hkern u1="F" u2="&#x157;" k="25" />
<hkern u1="F" u2="&#x155;" k="25" />
<hkern u1="F" u2="&#x153;" k="20" />
<hkern u1="F" u2="&#x152;" k="8" />
<hkern u1="F" u2="&#x151;" k="20" />
<hkern u1="F" u2="&#x150;" k="8" />
<hkern u1="F" u2="&#x14f;" k="20" />
<hkern u1="F" u2="&#x14e;" k="8" />
<hkern u1="F" u2="&#x14d;" k="20" />
<hkern u1="F" u2="&#x14c;" k="8" />
<hkern u1="F" u2="&#x14b;" k="25" />
<hkern u1="F" u2="&#x148;" k="25" />
<hkern u1="F" u2="&#x146;" k="25" />
<hkern u1="F" u2="&#x144;" k="25" />
<hkern u1="F" u2="&#x135;" k="-82" />
<hkern u1="F" u2="&#x134;" k="25" />
<hkern u1="F" u2="&#x131;" k="25" />
<hkern u1="F" u2="&#x12d;" k="-88" />
<hkern u1="F" u2="&#x12b;" k="-86" />
<hkern u1="F" u2="&#x129;" k="-125" />
<hkern u1="F" u2="&#x123;" k="18" />
<hkern u1="F" u2="&#x122;" k="8" />
<hkern u1="F" u2="&#x121;" k="18" />
<hkern u1="F" u2="&#x120;" k="8" />
<hkern u1="F" u2="&#x11f;" k="18" />
<hkern u1="F" u2="&#x11e;" k="8" />
<hkern u1="F" u2="&#x11d;" k="18" />
<hkern u1="F" u2="&#x11c;" k="8" />
<hkern u1="F" u2="&#x11b;" k="20" />
<hkern u1="F" u2="&#x119;" k="20" />
<hkern u1="F" u2="&#x117;" k="20" />
<hkern u1="F" u2="&#x115;" k="20" />
<hkern u1="F" u2="&#x113;" k="20" />
<hkern u1="F" u2="&#x111;" k="23" />
<hkern u1="F" u2="&#x10f;" k="23" />
<hkern u1="F" u2="&#x10d;" k="20" />
<hkern u1="F" u2="&#x10c;" k="8" />
<hkern u1="F" u2="&#x10b;" k="20" />
<hkern u1="F" u2="&#x10a;" k="8" />
<hkern u1="F" u2="&#x109;" k="20" />
<hkern u1="F" u2="&#x108;" k="8" />
<hkern u1="F" u2="&#x107;" k="20" />
<hkern u1="F" u2="&#x106;" k="8" />
<hkern u1="F" u2="&#x105;" k="23" />
<hkern u1="F" u2="&#x104;" k="51" />
<hkern u1="F" u2="&#x103;" k="23" />
<hkern u1="F" u2="&#x102;" k="51" />
<hkern u1="F" u2="&#x101;" k="23" />
<hkern u1="F" u2="&#x100;" k="51" />
<hkern u1="F" u2="&#xfc;" k="18" />
<hkern u1="F" u2="&#xfb;" k="18" />
<hkern u1="F" u2="&#xfa;" k="18" />
<hkern u1="F" u2="&#xf9;" k="18" />
<hkern u1="F" u2="&#xf8;" k="20" />
<hkern u1="F" u2="&#xf6;" k="20" />
<hkern u1="F" u2="&#xf5;" k="20" />
<hkern u1="F" u2="&#xf4;" k="20" />
<hkern u1="F" u2="&#xf3;" k="20" />
<hkern u1="F" u2="&#xf2;" k="20" />
<hkern u1="F" u2="&#xf1;" k="25" />
<hkern u1="F" u2="&#xef;" k="-92" />
<hkern u1="F" u2="&#xee;" k="-100" />
<hkern u1="F" u2="&#xec;" k="-164" />
<hkern u1="F" u2="&#xeb;" k="20" />
<hkern u1="F" u2="&#xea;" k="20" />
<hkern u1="F" u2="&#xe9;" k="20" />
<hkern u1="F" u2="&#xe8;" k="20" />
<hkern u1="F" u2="&#xe7;" k="20" />
<hkern u1="F" u2="&#xe6;" k="23" />
<hkern u1="F" u2="&#xe5;" k="23" />
<hkern u1="F" u2="&#xe4;" k="23" />
<hkern u1="F" u2="&#xe3;" k="23" />
<hkern u1="F" u2="&#xe2;" k="23" />
<hkern u1="F" u2="&#xe1;" k="23" />
<hkern u1="F" u2="&#xe0;" k="23" />
<hkern u1="F" u2="&#xd8;" k="8" />
<hkern u1="F" u2="&#xd6;" k="8" />
<hkern u1="F" u2="&#xd5;" k="8" />
<hkern u1="F" u2="&#xd4;" k="8" />
<hkern u1="F" u2="&#xd3;" k="8" />
<hkern u1="F" u2="&#xd2;" k="8" />
<hkern u1="F" u2="&#xc7;" k="8" />
<hkern u1="F" u2="&#xc6;" k="51" />
<hkern u1="F" u2="&#xc5;" k="51" />
<hkern u1="F" u2="&#xc4;" k="51" />
<hkern u1="F" u2="&#xc3;" k="51" />
<hkern u1="F" u2="&#xc2;" k="51" />
<hkern u1="F" u2="&#xc1;" k="51" />
<hkern u1="F" u2="&#xc0;" k="51" />
<hkern u1="F" u2="&#xbb;" k="33" />
<hkern u1="F" u2="&#xab;" k="27" />
<hkern u1="F" u2="z" k="12" />
<hkern u1="F" u2="x" k="6" />
<hkern u1="F" u2="u" k="18" />
<hkern u1="F" u2="s" k="18" />
<hkern u1="F" u2="r" k="25" />
<hkern u1="F" u2="q" k="23" />
<hkern u1="F" u2="p" k="25" />
<hkern u1="F" u2="o" k="20" />
<hkern u1="F" u2="n" k="25" />
<hkern u1="F" u2="m" k="25" />
<hkern u1="F" u2="g" k="18" />
<hkern u1="F" u2="e" k="20" />
<hkern u1="F" u2="d" k="23" />
<hkern u1="F" u2="c" k="20" />
<hkern u1="F" u2="a" k="23" />
<hkern u1="F" u2="X" k="6" />
<hkern u1="F" u2="S" k="6" />
<hkern u1="F" u2="Q" k="8" />
<hkern u1="F" u2="O" k="8" />
<hkern u1="F" u2="J" k="25" />
<hkern u1="F" u2="G" k="8" />
<hkern u1="F" u2="C" k="8" />
<hkern u1="F" u2="A" k="51" />
<hkern u1="F" u2="&#x3b;" k="23" />
<hkern u1="F" u2="&#x3a;" k="23" />
<hkern u1="F" u2="&#x2f;" k="66" />
<hkern u1="F" u2="&#x2e;" k="96" />
<hkern u1="F" u2="&#x2d;" k="20" />
<hkern u1="F" u2="&#x2c;" k="96" />
<hkern u1="F" u2="&#x2a;" k="-16" />
<hkern u1="G" u2="&#x135;" k="-33" />
<hkern u1="G" u2="&#x12d;" k="-20" />
<hkern u1="G" u2="&#x12b;" k="-31" />
<hkern u1="G" u2="&#x129;" k="-57" />
<hkern u1="G" u2="&#xef;" k="-39" />
<hkern u1="G" u2="&#xee;" k="-49" />
<hkern u1="G" u2="&#xec;" k="-61" />
<hkern u1="G" u2="&#x7d;" k="18" />
<hkern u1="G" u2="&#x7c;" k="18" />
<hkern u1="G" u2="f" k="8" />
<hkern u1="G" u2="]" k="23" />
<hkern u1="G" u2="\" k="14" />
<hkern u1="G" u2="V" k="23" />
<hkern u1="G" u2="&#x29;" k="33" />
<hkern u1="H" u2="&#x135;" k="-14" />
<hkern u1="H" u2="&#x129;" k="-33" />
<hkern u1="H" u2="&#xef;" k="-12" />
<hkern u1="H" u2="&#xee;" k="-31" />
<hkern u1="H" u2="&#xec;" k="-66" />
<hkern u1="H" u2="&#x7d;" k="20" />
<hkern u1="H" u2="&#x7c;" k="18" />
<hkern u1="H" u2="f" k="8" />
<hkern u1="H" u2="]" k="25" />
<hkern u1="H" u2="&#x29;" k="31" />
<hkern u1="I" u2="&#x135;" k="-14" />
<hkern u1="I" u2="&#x129;" k="-33" />
<hkern u1="I" u2="&#xef;" k="-12" />
<hkern u1="I" u2="&#xee;" k="-31" />
<hkern u1="I" u2="&#xec;" k="-66" />
<hkern u1="I" u2="&#x7d;" k="20" />
<hkern u1="I" u2="&#x7c;" k="18" />
<hkern u1="I" u2="f" k="8" />
<hkern u1="I" u2="]" k="25" />
<hkern u1="I" u2="&#x29;" k="31" />
<hkern u1="J" u2="&#x135;" k="-20" />
<hkern u1="J" u2="&#x12d;" k="-8" />
<hkern u1="J" u2="&#x129;" k="-37" />
<hkern u1="J" u2="&#xef;" k="-16" />
<hkern u1="J" u2="&#xee;" k="-37" />
<hkern u1="J" u2="&#xec;" k="-72" />
<hkern u1="J" u2="&#x7d;" k="16" />
<hkern u1="J" u2="&#x7c;" k="16" />
<hkern u1="J" u2="f" k="8" />
<hkern u1="J" u2="]" k="16" />
<hkern u1="J" u2="&#x29;" k="29" />
<hkern u1="K" u2="&#x135;" k="-37" />
<hkern u1="K" u2="&#x12d;" k="-100" />
<hkern u1="K" u2="&#x12b;" k="-94" />
<hkern u1="K" u2="&#x129;" k="-131" />
<hkern u1="K" u2="&#xef;" k="-104" />
<hkern u1="K" u2="&#xee;" k="-49" />
<hkern u1="K" u2="&#xec;" k="-152" />
<hkern u1="K" u2="v" k="27" />
<hkern u1="K" u2="f" k="10" />
<hkern u1="K" u2="&#x2a;" k="-14" />
<hkern u1="L" u2="&#x2122;" k="143" />
<hkern u1="L" u2="&#x201e;" k="-10" />
<hkern u1="L" u2="&#xae;" k="145" />
<hkern u1="L" u2="&#x7d;" k="6" />
<hkern u1="L" u2="&#x7c;" k="76" />
<hkern u1="L" u2="v" k="80" />
<hkern u1="L" u2="f" k="8" />
<hkern u1="L" u2="]" k="8" />
<hkern u1="L" u2="\" k="137" />
<hkern u1="L" u2="V" k="117" />
<hkern u1="L" u2="&#x3f;" k="29" />
<hkern u1="L" u2="&#x3b;" k="-10" />
<hkern u1="L" u2="&#x2c;" k="-8" />
<hkern u1="L" u2="&#x2a;" k="143" />
<hkern u1="M" u2="&#x135;" k="-14" />
<hkern u1="M" u2="&#x129;" k="-33" />
<hkern u1="M" u2="&#xef;" k="-12" />
<hkern u1="M" u2="&#xee;" k="-31" />
<hkern u1="M" u2="&#xec;" k="-66" />
<hkern u1="M" u2="&#x7d;" k="20" />
<hkern u1="M" u2="&#x7c;" k="18" />
<hkern u1="M" u2="f" k="8" />
<hkern u1="M" u2="]" k="25" />
<hkern u1="M" u2="&#x29;" k="31" />
<hkern u1="N" u2="&#x135;" k="-14" />
<hkern u1="N" u2="&#x129;" k="-33" />
<hkern u1="N" u2="&#xef;" k="-12" />
<hkern u1="N" u2="&#xee;" k="-31" />
<hkern u1="N" u2="&#xec;" k="-66" />
<hkern u1="N" u2="&#x7d;" k="20" />
<hkern u1="N" u2="&#x7c;" k="18" />
<hkern u1="N" u2="f" k="8" />
<hkern u1="N" u2="]" k="25" />
<hkern u1="N" u2="&#x29;" k="31" />
<hkern u1="O" u2="&#x2122;" k="27" />
<hkern u1="O" u2="&#x7d;" k="63" />
<hkern u1="O" u2="&#x7c;" k="43" />
<hkern u1="O" u2="x" k="10" />
<hkern u1="O" u2="]" k="72" />
<hkern u1="O" u2="\" k="45" />
<hkern u1="O" u2="X" k="35" />
<hkern u1="O" u2="V" k="31" />
<hkern u1="O" u2="&#x3f;" k="10" />
<hkern u1="O" u2="&#x2f;" k="33" />
<hkern u1="O" u2="&#x29;" k="55" />
<hkern u1="P" u2="&#x2039;" k="31" />
<hkern u1="P" u2="&#x2026;" k="113" />
<hkern u1="P" u2="&#x201e;" k="113" />
<hkern u1="P" u2="&#x201a;" k="113" />
<hkern u1="P" u2="&#x2014;" k="8" />
<hkern u1="P" u2="&#x2013;" k="8" />
<hkern u1="P" u2="&#x1fc;" k="51" />
<hkern u1="P" u2="&#x1fa;" k="51" />
<hkern u1="P" u2="&#x17d;" k="18" />
<hkern u1="P" u2="&#x17b;" k="18" />
<hkern u1="P" u2="&#x179;" k="18" />
<hkern u1="P" u2="&#x178;" k="51" />
<hkern u1="P" u2="&#x176;" k="51" />
<hkern u1="P" u2="&#x174;" k="16" />
<hkern u1="P" u2="&#x135;" k="-14" />
<hkern u1="P" u2="&#x134;" k="18" />
<hkern u1="P" u2="&#x104;" k="51" />
<hkern u1="P" u2="&#x102;" k="51" />
<hkern u1="P" u2="&#x100;" k="51" />
<hkern u1="P" u2="&#xee;" k="-31" />
<hkern u1="P" u2="&#xdd;" k="51" />
<hkern u1="P" u2="&#xc6;" k="51" />
<hkern u1="P" u2="&#xc5;" k="51" />
<hkern u1="P" u2="&#xc4;" k="51" />
<hkern u1="P" u2="&#xc3;" k="51" />
<hkern u1="P" u2="&#xc2;" k="51" />
<hkern u1="P" u2="&#xc1;" k="51" />
<hkern u1="P" u2="&#xc0;" k="51" />
<hkern u1="P" u2="&#xab;" k="31" />
<hkern u1="P" u2="&#x7d;" k="61" />
<hkern u1="P" u2="&#x7c;" k="33" />
<hkern u1="P" u2="]" k="70" />
<hkern u1="P" u2="\" k="27" />
<hkern u1="P" u2="Z" k="18" />
<hkern u1="P" u2="Y" k="51" />
<hkern u1="P" u2="X" k="47" />
<hkern u1="P" u2="W" k="16" />
<hkern u1="P" u2="V" k="27" />
<hkern u1="P" u2="J" k="18" />
<hkern u1="P" u2="A" k="51" />
<hkern u1="P" u2="&#x3f;" k="6" />
<hkern u1="P" u2="&#x2f;" k="66" />
<hkern u1="P" u2="&#x2e;" k="113" />
<hkern u1="P" u2="&#x2d;" k="8" />
<hkern u1="P" u2="&#x2c;" k="113" />
<hkern u1="P" u2="&#x29;" k="47" />
<hkern u1="Q" u2="&#x2122;" k="27" />
<hkern u1="Q" u2="&#x7d;" k="63" />
<hkern u1="Q" u2="&#x7c;" k="43" />
<hkern u1="Q" u2="x" k="10" />
<hkern u1="Q" u2="]" k="72" />
<hkern u1="Q" u2="\" k="45" />
<hkern u1="Q" u2="X" k="35" />
<hkern u1="Q" u2="V" k="31" />
<hkern u1="Q" u2="&#x3f;" k="10" />
<hkern u1="Q" u2="&#x2f;" k="33" />
<hkern u1="Q" u2="&#x29;" k="55" />
<hkern u1="R" u2="&#xee;" k="-12" />
<hkern u1="R" u2="&#x7d;" k="37" />
<hkern u1="R" u2="&#x7c;" k="39" />
<hkern u1="R" u2="]" k="16" />
<hkern u1="R" u2="\" k="37" />
<hkern u1="R" u2="V" k="29" />
<hkern u1="R" u2="&#x3f;" k="8" />
<hkern u1="R" u2="&#x2f;" k="25" />
<hkern u1="R" u2="&#x29;" k="27" />
<hkern u1="S" u2="&#x135;" k="-18" />
<hkern u1="S" u2="&#x129;" k="-35" />
<hkern u1="S" u2="&#xef;" k="-16" />
<hkern u1="S" u2="&#xee;" k="-35" />
<hkern u1="S" u2="&#xec;" k="-55" />
<hkern u1="S" u2="&#xae;" k="6" />
<hkern u1="S" u2="&#x7d;" k="27" />
<hkern u1="S" u2="&#x7c;" k="27" />
<hkern u1="S" u2="v" k="12" />
<hkern u1="S" u2="f" k="8" />
<hkern u1="S" u2="]" k="27" />
<hkern u1="S" u2="\" k="18" />
<hkern u1="S" u2="V" k="25" />
<hkern u1="S" u2="&#x29;" k="35" />
<hkern u1="T" u2="&#x15d;" k="98" />
<hkern u1="T" u2="&#x159;" k="76" />
<hkern u1="T" u2="&#x155;" k="49" />
<hkern u1="T" u2="&#x151;" k="104" />
<hkern u1="T" u2="&#x135;" k="-100" />
<hkern u1="T" u2="&#x131;" k="115" />
<hkern u1="T" u2="&#x12d;" k="-113" />
<hkern u1="T" u2="&#x12b;" k="-113" />
<hkern u1="T" u2="&#x129;" k="-150" />
<hkern u1="T" u2="&#x127;" k="-16" />
<hkern u1="T" u2="&#x11f;" k="129" />
<hkern u1="T" u2="&#x11d;" k="104" />
<hkern u1="T" u2="&#x109;" k="125" />
<hkern u1="T" u2="&#xf5;" k="131" />
<hkern u1="T" u2="&#xef;" k="-119" />
<hkern u1="T" u2="&#xee;" k="-117" />
<hkern u1="T" u2="&#xec;" k="-193" />
<hkern u1="T" u2="&#xea;" k="109" />
<hkern u1="T" u2="&#xe8;" k="82" />
<hkern u1="T" u2="x" k="92" />
<hkern u1="T" u2="v" k="96" />
<hkern u1="T" u2="f" k="20" />
<hkern u1="T" u2="&#x40;" k="16" />
<hkern u1="T" u2="&#x2f;" k="94" />
<hkern u1="T" u2="&#x2a;" k="-33" />
<hkern u1="T" u2="&#x26;" k="29" />
<hkern u1="U" u2="&#x135;" k="-23" />
<hkern u1="U" u2="&#x12d;" k="-14" />
<hkern u1="U" u2="&#x12b;" k="-10" />
<hkern u1="U" u2="&#x129;" k="-41" />
<hkern u1="U" u2="&#xef;" k="-20" />
<hkern u1="U" u2="&#xee;" k="-39" />
<hkern u1="U" u2="&#xec;" k="-78" />
<hkern u1="U" u2="&#x7d;" k="18" />
<hkern u1="U" u2="&#x7c;" k="14" />
<hkern u1="U" u2="f" k="8" />
<hkern u1="U" u2="]" k="23" />
<hkern u1="U" u2="&#x2f;" k="29" />
<hkern u1="U" u2="&#x29;" k="31" />
<hkern u1="V" u2="&#x203a;" k="57" />
<hkern u1="V" u2="&#x2039;" k="78" />
<hkern u1="V" u2="&#x2026;" k="102" />
<hkern u1="V" u2="&#x201e;" k="102" />
<hkern u1="V" u2="&#x201c;" k="27" />
<hkern u1="V" u2="&#x201a;" k="102" />
<hkern u1="V" u2="&#x2018;" k="27" />
<hkern u1="V" u2="&#x2014;" k="74" />
<hkern u1="V" u2="&#x2013;" k="74" />
<hkern u1="V" u2="&#x219;" k="43" />
<hkern u1="V" u2="&#x218;" k="18" />
<hkern u1="V" u2="&#x1ff;" k="57" />
<hkern u1="V" u2="&#x1fe;" k="33" />
<hkern u1="V" u2="&#x1fd;" k="57" />
<hkern u1="V" u2="&#x1fc;" k="63" />
<hkern u1="V" u2="&#x1fb;" k="57" />
<hkern u1="V" u2="&#x1fa;" k="63" />
<hkern u1="V" u2="&#x17e;" k="27" />
<hkern u1="V" u2="&#x17c;" k="27" />
<hkern u1="V" u2="&#x17a;" k="27" />
<hkern u1="V" u2="&#x177;" k="12" />
<hkern u1="V" u2="&#x175;" k="18" />
<hkern u1="V" u2="&#x173;" k="35" />
<hkern u1="V" u2="&#x171;" k="35" />
<hkern u1="V" u2="&#x16f;" k="35" />
<hkern u1="V" u2="&#x16d;" k="35" />
<hkern u1="V" u2="&#x16b;" k="35" />
<hkern u1="V" u2="&#x169;" k="35" />
<hkern u1="V" u2="&#x161;" k="43" />
<hkern u1="V" u2="&#x160;" k="18" />
<hkern u1="V" u2="&#x15f;" k="43" />
<hkern u1="V" u2="&#x15e;" k="18" />
<hkern u1="V" u2="&#x15d;" k="43" />
<hkern u1="V" u2="&#x15c;" k="18" />
<hkern u1="V" u2="&#x15b;" k="43" />
<hkern u1="V" u2="&#x15a;" k="18" />
<hkern u1="V" u2="&#x159;" k="23" />
<hkern u1="V" u2="&#x157;" k="39" />
<hkern u1="V" u2="&#x155;" k="23" />
<hkern u1="V" u2="&#x153;" k="57" />
<hkern u1="V" u2="&#x152;" k="33" />
<hkern u1="V" u2="&#x151;" k="57" />
<hkern u1="V" u2="&#x150;" k="33" />
<hkern u1="V" u2="&#x14f;" k="57" />
<hkern u1="V" u2="&#x14e;" k="33" />
<hkern u1="V" u2="&#x14d;" k="57" />
<hkern u1="V" u2="&#x14c;" k="33" />
<hkern u1="V" u2="&#x14b;" k="39" />
<hkern u1="V" u2="&#x148;" k="39" />
<hkern u1="V" u2="&#x146;" k="39" />
<hkern u1="V" u2="&#x144;" k="39" />
<hkern u1="V" u2="&#x135;" k="-63" />
<hkern u1="V" u2="&#x134;" k="31" />
<hkern u1="V" u2="&#x131;" k="39" />
<hkern u1="V" u2="&#x12d;" k="-94" />
<hkern u1="V" u2="&#x12b;" k="-90" />
<hkern u1="V" u2="&#x129;" k="-129" />
<hkern u1="V" u2="&#x127;" k="-8" />
<hkern u1="V" u2="&#x123;" k="45" />
<hkern u1="V" u2="&#x122;" k="33" />
<hkern u1="V" u2="&#x121;" k="45" />
<hkern u1="V" u2="&#x120;" k="33" />
<hkern u1="V" u2="&#x11f;" k="45" />
<hkern u1="V" u2="&#x11e;" k="33" />
<hkern u1="V" u2="&#x11d;" k="45" />
<hkern u1="V" u2="&#x11c;" k="33" />
<hkern u1="V" u2="&#x11b;" k="57" />
<hkern u1="V" u2="&#x119;" k="57" />
<hkern u1="V" u2="&#x117;" k="57" />
<hkern u1="V" u2="&#x115;" k="57" />
<hkern u1="V" u2="&#x113;" k="57" />
<hkern u1="V" u2="&#x111;" k="57" />
<hkern u1="V" u2="&#x10f;" k="57" />
<hkern u1="V" u2="&#x10d;" k="57" />
<hkern u1="V" u2="&#x10c;" k="33" />
<hkern u1="V" u2="&#x10b;" k="57" />
<hkern u1="V" u2="&#x10a;" k="33" />
<hkern u1="V" u2="&#x109;" k="57" />
<hkern u1="V" u2="&#x108;" k="33" />
<hkern u1="V" u2="&#x107;" k="57" />
<hkern u1="V" u2="&#x106;" k="33" />
<hkern u1="V" u2="&#x105;" k="57" />
<hkern u1="V" u2="&#x104;" k="63" />
<hkern u1="V" u2="&#x103;" k="57" />
<hkern u1="V" u2="&#x102;" k="63" />
<hkern u1="V" u2="&#x101;" k="57" />
<hkern u1="V" u2="&#x100;" k="63" />
<hkern u1="V" u2="&#xff;" k="12" />
<hkern u1="V" u2="&#xfd;" k="12" />
<hkern u1="V" u2="&#xfc;" k="35" />
<hkern u1="V" u2="&#xfb;" k="35" />
<hkern u1="V" u2="&#xfa;" k="35" />
<hkern u1="V" u2="&#xf9;" k="35" />
<hkern u1="V" u2="&#xf8;" k="57" />
<hkern u1="V" u2="&#xf6;" k="57" />
<hkern u1="V" u2="&#xf5;" k="57" />
<hkern u1="V" u2="&#xf4;" k="57" />
<hkern u1="V" u2="&#xf3;" k="57" />
<hkern u1="V" u2="&#xf2;" k="57" />
<hkern u1="V" u2="&#xf1;" k="35" />
<hkern u1="V" u2="&#xef;" k="-98" />
<hkern u1="V" u2="&#xee;" k="-78" />
<hkern u1="V" u2="&#xec;" k="-152" />
<hkern u1="V" u2="&#xeb;" k="57" />
<hkern u1="V" u2="&#xea;" k="57" />
<hkern u1="V" u2="&#xe9;" k="57" />
<hkern u1="V" u2="&#xe8;" k="57" />
<hkern u1="V" u2="&#xe7;" k="57" />
<hkern u1="V" u2="&#xe6;" k="57" />
<hkern u1="V" u2="&#xe5;" k="57" />
<hkern u1="V" u2="&#xe4;" k="57" />
<hkern u1="V" u2="&#xe3;" k="57" />
<hkern u1="V" u2="&#xe2;" k="57" />
<hkern u1="V" u2="&#xe1;" k="57" />
<hkern u1="V" u2="&#xe0;" k="57" />
<hkern u1="V" u2="&#xd8;" k="33" />
<hkern u1="V" u2="&#xd6;" k="33" />
<hkern u1="V" u2="&#xd5;" k="33" />
<hkern u1="V" u2="&#xd4;" k="33" />
<hkern u1="V" u2="&#xd3;" k="33" />
<hkern u1="V" u2="&#xd2;" k="33" />
<hkern u1="V" u2="&#xc7;" k="33" />
<hkern u1="V" u2="&#xc6;" k="63" />
<hkern u1="V" u2="&#xc5;" k="63" />
<hkern u1="V" u2="&#xc4;" k="63" />
<hkern u1="V" u2="&#xc3;" k="63" />
<hkern u1="V" u2="&#xc2;" k="63" />
<hkern u1="V" u2="&#xc1;" k="63" />
<hkern u1="V" u2="&#xc0;" k="63" />
<hkern u1="V" u2="&#xbb;" k="57" />
<hkern u1="V" u2="&#xae;" k="25" />
<hkern u1="V" u2="&#xab;" k="78" />
<hkern u1="V" u2="z" k="27" />
<hkern u1="V" u2="y" k="12" />
<hkern u1="V" u2="x" k="14" />
<hkern u1="V" u2="w" k="18" />
<hkern u1="V" u2="v" k="12" />
<hkern u1="V" u2="u" k="35" />
<hkern u1="V" u2="s" k="43" />
<hkern u1="V" u2="r" k="39" />
<hkern u1="V" u2="q" k="57" />
<hkern u1="V" u2="p" k="39" />
<hkern u1="V" u2="o" k="57" />
<hkern u1="V" u2="n" k="39" />
<hkern u1="V" u2="m" k="39" />
<hkern u1="V" u2="g" k="45" />
<hkern u1="V" u2="e" k="57" />
<hkern u1="V" u2="d" k="57" />
<hkern u1="V" u2="c" k="57" />
<hkern u1="V" u2="a" k="57" />
<hkern u1="V" u2="S" k="18" />
<hkern u1="V" u2="Q" k="33" />
<hkern u1="V" u2="O" k="33" />
<hkern u1="V" u2="J" k="31" />
<hkern u1="V" u2="G" k="33" />
<hkern u1="V" u2="C" k="33" />
<hkern u1="V" u2="A" k="63" />
<hkern u1="V" u2="&#x40;" k="37" />
<hkern u1="V" u2="&#x3b;" k="47" />
<hkern u1="V" u2="&#x3a;" k="47" />
<hkern u1="V" u2="&#x2f;" k="90" />
<hkern u1="V" u2="&#x2e;" k="102" />
<hkern u1="V" u2="&#x2d;" k="74" />
<hkern u1="V" u2="&#x2c;" k="102" />
<hkern u1="V" u2="&#x2a;" k="-29" />
<hkern u1="V" u2="&#x26;" k="45" />
<hkern u1="W" u2="&#x159;" k="20" />
<hkern u1="W" u2="&#x155;" k="20" />
<hkern u1="W" u2="&#x135;" k="-59" />
<hkern u1="W" u2="&#x131;" k="23" />
<hkern u1="W" u2="&#x12d;" k="-86" />
<hkern u1="W" u2="&#x12b;" k="-82" />
<hkern u1="W" u2="&#x129;" k="-123" />
<hkern u1="W" u2="&#xef;" k="-90" />
<hkern u1="W" u2="&#xee;" k="-76" />
<hkern u1="W" u2="&#xec;" k="-145" />
<hkern u1="W" u2="&#xae;" k="8" />
<hkern u1="W" u2="x" k="10" />
<hkern u1="W" u2="v" k="12" />
<hkern u1="W" u2="&#x40;" k="33" />
<hkern u1="W" u2="&#x2f;" k="78" />
<hkern u1="W" u2="&#x2a;" k="-27" />
<hkern u1="W" u2="&#x29;" k="18" />
<hkern u1="W" u2="&#x26;" k="39" />
<hkern u1="X" u2="&#x2039;" k="59" />
<hkern u1="X" u2="&#x201c;" k="27" />
<hkern u1="X" u2="&#x2018;" k="27" />
<hkern u1="X" u2="&#x2014;" k="68" />
<hkern u1="X" u2="&#x2013;" k="68" />
<hkern u1="X" u2="&#x21b;" k="8" />
<hkern u1="X" u2="&#x1ff;" k="53" />
<hkern u1="X" u2="&#x1fe;" k="37" />
<hkern u1="X" u2="&#x1fd;" k="47" />
<hkern u1="X" u2="&#x1fb;" k="47" />
<hkern u1="X" u2="&#x177;" k="25" />
<hkern u1="X" u2="&#x175;" k="29" />
<hkern u1="X" u2="&#x173;" k="37" />
<hkern u1="X" u2="&#x171;" k="37" />
<hkern u1="X" u2="&#x16f;" k="37" />
<hkern u1="X" u2="&#x16d;" k="37" />
<hkern u1="X" u2="&#x16b;" k="37" />
<hkern u1="X" u2="&#x169;" k="37" />
<hkern u1="X" u2="&#x167;" k="8" />
<hkern u1="X" u2="&#x165;" k="8" />
<hkern u1="X" u2="&#x153;" k="53" />
<hkern u1="X" u2="&#x152;" k="37" />
<hkern u1="X" u2="&#x151;" k="53" />
<hkern u1="X" u2="&#x150;" k="37" />
<hkern u1="X" u2="&#x14f;" k="53" />
<hkern u1="X" u2="&#x14e;" k="37" />
<hkern u1="X" u2="&#x14d;" k="53" />
<hkern u1="X" u2="&#x14c;" k="37" />
<hkern u1="X" u2="&#x135;" k="-37" />
<hkern u1="X" u2="&#x12d;" k="-115" />
<hkern u1="X" u2="&#x12b;" k="-106" />
<hkern u1="X" u2="&#x129;" k="-145" />
<hkern u1="X" u2="&#x127;" k="-12" />
<hkern u1="X" u2="&#x123;" k="37" />
<hkern u1="X" u2="&#x122;" k="37" />
<hkern u1="X" u2="&#x121;" k="37" />
<hkern u1="X" u2="&#x120;" k="37" />
<hkern u1="X" u2="&#x11f;" k="37" />
<hkern u1="X" u2="&#x11e;" k="37" />
<hkern u1="X" u2="&#x11d;" k="37" />
<hkern u1="X" u2="&#x11c;" k="37" />
<hkern u1="X" u2="&#x11b;" k="53" />
<hkern u1="X" u2="&#x119;" k="53" />
<hkern u1="X" u2="&#x117;" k="53" />
<hkern u1="X" u2="&#x115;" k="53" />
<hkern u1="X" u2="&#x113;" k="53" />
<hkern u1="X" u2="&#x111;" k="47" />
<hkern u1="X" u2="&#x10f;" k="47" />
<hkern u1="X" u2="&#x10d;" k="53" />
<hkern u1="X" u2="&#x10c;" k="37" />
<hkern u1="X" u2="&#x10b;" k="53" />
<hkern u1="X" u2="&#x10a;" k="37" />
<hkern u1="X" u2="&#x109;" k="53" />
<hkern u1="X" u2="&#x108;" k="37" />
<hkern u1="X" u2="&#x107;" k="53" />
<hkern u1="X" u2="&#x106;" k="37" />
<hkern u1="X" u2="&#x105;" k="47" />
<hkern u1="X" u2="&#x103;" k="47" />
<hkern u1="X" u2="&#x101;" k="47" />
<hkern u1="X" u2="&#xff;" k="25" />
<hkern u1="X" u2="&#xfd;" k="25" />
<hkern u1="X" u2="&#xfc;" k="37" />
<hkern u1="X" u2="&#xfb;" k="37" />
<hkern u1="X" u2="&#xfa;" k="37" />
<hkern u1="X" u2="&#xf9;" k="37" />
<hkern u1="X" u2="&#xf8;" k="53" />
<hkern u1="X" u2="&#xf6;" k="53" />
<hkern u1="X" u2="&#xf5;" k="53" />
<hkern u1="X" u2="&#xf4;" k="53" />
<hkern u1="X" u2="&#xf3;" k="53" />
<hkern u1="X" u2="&#xf2;" k="53" />
<hkern u1="X" u2="&#xef;" k="-117" />
<hkern u1="X" u2="&#xee;" k="-49" />
<hkern u1="X" u2="&#xec;" k="-164" />
<hkern u1="X" u2="&#xeb;" k="53" />
<hkern u1="X" u2="&#xea;" k="53" />
<hkern u1="X" u2="&#xe9;" k="53" />
<hkern u1="X" u2="&#xe8;" k="53" />
<hkern u1="X" u2="&#xe7;" k="53" />
<hkern u1="X" u2="&#xe6;" k="47" />
<hkern u1="X" u2="&#xe5;" k="47" />
<hkern u1="X" u2="&#xe4;" k="47" />
<hkern u1="X" u2="&#xe3;" k="47" />
<hkern u1="X" u2="&#xe2;" k="47" />
<hkern u1="X" u2="&#xe1;" k="47" />
<hkern u1="X" u2="&#xe0;" k="47" />
<hkern u1="X" u2="&#xd8;" k="37" />
<hkern u1="X" u2="&#xd6;" k="37" />
<hkern u1="X" u2="&#xd5;" k="37" />
<hkern u1="X" u2="&#xd4;" k="37" />
<hkern u1="X" u2="&#xd3;" k="37" />
<hkern u1="X" u2="&#xd2;" k="37" />
<hkern u1="X" u2="&#xc7;" k="37" />
<hkern u1="X" u2="&#xab;" k="59" />
<hkern u1="X" u2="&#x7c;" k="-8" />
<hkern u1="X" u2="y" k="25" />
<hkern u1="X" u2="w" k="29" />
<hkern u1="X" u2="v" k="25" />
<hkern u1="X" u2="u" k="37" />
<hkern u1="X" u2="t" k="8" />
<hkern u1="X" u2="q" k="47" />
<hkern u1="X" u2="o" k="53" />
<hkern u1="X" u2="g" k="37" />
<hkern u1="X" u2="f" k="8" />
<hkern u1="X" u2="e" k="53" />
<hkern u1="X" u2="d" k="47" />
<hkern u1="X" u2="c" k="53" />
<hkern u1="X" u2="a" k="47" />
<hkern u1="X" u2="Q" k="37" />
<hkern u1="X" u2="O" k="37" />
<hkern u1="X" u2="G" k="37" />
<hkern u1="X" u2="C" k="37" />
<hkern u1="X" u2="&#x3f;" k="-8" />
<hkern u1="X" u2="&#x2d;" k="68" />
<hkern u1="X" u2="&#x2a;" k="-23" />
<hkern u1="Y" u2="&#x17a;" k="84" />
<hkern u1="Y" u2="&#x177;" k="51" />
<hkern u1="Y" u2="&#x171;" k="94" />
<hkern u1="Y" u2="&#x16b;" k="94" />
<hkern u1="Y" u2="&#x169;" k="88" />
<hkern u1="Y" u2="&#x159;" k="53" />
<hkern u1="Y" u2="&#x155;" k="14" />
<hkern u1="Y" u2="&#x151;" k="96" />
<hkern u1="Y" u2="&#x14f;" k="100" />
<hkern u1="Y" u2="&#x14d;" k="121" />
<hkern u1="Y" u2="&#x135;" k="-43" />
<hkern u1="Y" u2="&#x131;" k="113" />
<hkern u1="Y" u2="&#x12d;" k="-125" />
<hkern u1="Y" u2="&#x12b;" k="-117" />
<hkern u1="Y" u2="&#x129;" k="-156" />
<hkern u1="Y" u2="&#x127;" k="-14" />
<hkern u1="Y" u2="&#x11f;" k="104" />
<hkern u1="Y" u2="&#x113;" k="111" />
<hkern u1="Y" u2="&#xff;" k="43" />
<hkern u1="Y" u2="&#xfc;" k="90" />
<hkern u1="Y" u2="&#xf5;" k="100" />
<hkern u1="Y" u2="&#xf2;" k="117" />
<hkern u1="Y" u2="&#xf1;" k="111" />
<hkern u1="Y" u2="&#xef;" k="-129" />
<hkern u1="Y" u2="&#xee;" k="-57" />
<hkern u1="Y" u2="&#xed;" k="-10" />
<hkern u1="Y" u2="&#xec;" k="-172" />
<hkern u1="Y" u2="&#xeb;" k="109" />
<hkern u1="Y" u2="&#xea;" k="125" />
<hkern u1="Y" u2="&#xe8;" k="72" />
<hkern u1="Y" u2="&#xae;" k="39" />
<hkern u1="Y" u2="&#x7d;" k="-8" />
<hkern u1="Y" u2="&#x7c;" k="-18" />
<hkern u1="Y" u2="x" k="70" />
<hkern u1="Y" u2="v" k="72" />
<hkern u1="Y" u2="f" k="31" />
<hkern u1="Y" u2="]" k="-8" />
<hkern u1="Y" u2="&#x40;" k="59" />
<hkern u1="Y" u2="&#x2f;" k="135" />
<hkern u1="Y" u2="&#x2a;" k="-33" />
<hkern u1="Y" u2="&#x26;" k="70" />
<hkern u1="Z" u2="&#x135;" k="-66" />
<hkern u1="Z" u2="&#x12d;" k="-57" />
<hkern u1="Z" u2="&#x12b;" k="-57" />
<hkern u1="Z" u2="&#x129;" k="-98" />
<hkern u1="Z" u2="&#xef;" k="-61" />
<hkern u1="Z" u2="&#xee;" k="-84" />
<hkern u1="Z" u2="&#xec;" k="-133" />
<hkern u1="Z" u2="&#x29;" k="18" />
<hkern u1="[" u2="&#x21b;" k="45" />
<hkern u1="[" u2="&#x219;" k="59" />
<hkern u1="[" u2="&#x218;" k="39" />
<hkern u1="[" u2="&#x1ff;" k="70" />
<hkern u1="[" u2="&#x1fe;" k="66" />
<hkern u1="[" u2="&#x1fd;" k="70" />
<hkern u1="[" u2="&#x1fc;" k="25" />
<hkern u1="[" u2="&#x1fb;" k="70" />
<hkern u1="[" u2="&#x1fa;" k="25" />
<hkern u1="[" u2="&#x17e;" k="33" />
<hkern u1="[" u2="&#x17c;" k="33" />
<hkern u1="[" u2="&#x17a;" k="33" />
<hkern u1="[" u2="&#x178;" k="-14" />
<hkern u1="[" u2="&#x177;" k="47" />
<hkern u1="[" u2="&#x176;" k="-14" />
<hkern u1="[" u2="&#x175;" k="53" />
<hkern u1="[" u2="&#x173;" k="61" />
<hkern u1="[" u2="&#x172;" k="14" />
<hkern u1="[" u2="&#x171;" k="61" />
<hkern u1="[" u2="&#x170;" k="14" />
<hkern u1="[" u2="&#x16f;" k="61" />
<hkern u1="[" u2="&#x16e;" k="14" />
<hkern u1="[" u2="&#x16d;" k="61" />
<hkern u1="[" u2="&#x16c;" k="14" />
<hkern u1="[" u2="&#x16b;" k="61" />
<hkern u1="[" u2="&#x16a;" k="14" />
<hkern u1="[" u2="&#x169;" k="61" />
<hkern u1="[" u2="&#x168;" k="14" />
<hkern u1="[" u2="&#x167;" k="45" />
<hkern u1="[" u2="&#x165;" k="45" />
<hkern u1="[" u2="&#x161;" k="59" />
<hkern u1="[" u2="&#x160;" k="39" />
<hkern u1="[" u2="&#x15f;" k="59" />
<hkern u1="[" u2="&#x15e;" k="39" />
<hkern u1="[" u2="&#x15d;" k="59" />
<hkern u1="[" u2="&#x15c;" k="39" />
<hkern u1="[" u2="&#x15b;" k="59" />
<hkern u1="[" u2="&#x15a;" k="39" />
<hkern u1="[" u2="&#x159;" k="10" />
<hkern u1="[" u2="&#x158;" k="16" />
<hkern u1="[" u2="&#x157;" k="45" />
<hkern u1="[" u2="&#x156;" k="16" />
<hkern u1="[" u2="&#x155;" k="45" />
<hkern u1="[" u2="&#x154;" k="16" />
<hkern u1="[" u2="&#x153;" k="70" />
<hkern u1="[" u2="&#x152;" k="66" />
<hkern u1="[" u2="&#x151;" k="70" />
<hkern u1="[" u2="&#x150;" k="66" />
<hkern u1="[" u2="&#x14f;" k="70" />
<hkern u1="[" u2="&#x14e;" k="66" />
<hkern u1="[" u2="&#x14d;" k="70" />
<hkern u1="[" u2="&#x14c;" k="66" />
<hkern u1="[" u2="&#x14b;" k="45" />
<hkern u1="[" u2="&#x14a;" k="16" />
<hkern u1="[" u2="&#x148;" k="45" />
<hkern u1="[" u2="&#x147;" k="16" />
<hkern u1="[" u2="&#x146;" k="45" />
<hkern u1="[" u2="&#x145;" k="16" />
<hkern u1="[" u2="&#x144;" k="45" />
<hkern u1="[" u2="&#x143;" k="16" />
<hkern u1="[" u2="&#x141;" k="16" />
<hkern u1="[" u2="&#x13d;" k="16" />
<hkern u1="[" u2="&#x13b;" k="16" />
<hkern u1="[" u2="&#x139;" k="16" />
<hkern u1="[" u2="&#x136;" k="16" />
<hkern u1="[" u2="&#x135;" k="-59" />
<hkern u1="[" u2="&#x130;" k="16" />
<hkern u1="[" u2="&#x12f;" k="-16" />
<hkern u1="[" u2="&#x12e;" k="16" />
<hkern u1="[" u2="&#x12d;" k="-113" />
<hkern u1="[" u2="&#x12c;" k="16" />
<hkern u1="[" u2="&#x12b;" k="-78" />
<hkern u1="[" u2="&#x12a;" k="16" />
<hkern u1="[" u2="&#x129;" k="-115" />
<hkern u1="[" u2="&#x128;" k="16" />
<hkern u1="[" u2="&#x126;" k="16" />
<hkern u1="[" u2="&#x124;" k="16" />
<hkern u1="[" u2="&#x122;" k="66" />
<hkern u1="[" u2="&#x120;" k="66" />
<hkern u1="[" u2="&#x11e;" k="66" />
<hkern u1="[" u2="&#x11c;" k="66" />
<hkern u1="[" u2="&#x11b;" k="70" />
<hkern u1="[" u2="&#x11a;" k="16" />
<hkern u1="[" u2="&#x119;" k="70" />
<hkern u1="[" u2="&#x118;" k="16" />
<hkern u1="[" u2="&#x117;" k="70" />
<hkern u1="[" u2="&#x116;" k="16" />
<hkern u1="[" u2="&#x115;" k="70" />
<hkern u1="[" u2="&#x114;" k="16" />
<hkern u1="[" u2="&#x113;" k="70" />
<hkern u1="[" u2="&#x112;" k="16" />
<hkern u1="[" u2="&#x111;" k="70" />
<hkern u1="[" u2="&#x110;" k="16" />
<hkern u1="[" u2="&#x10f;" k="70" />
<hkern u1="[" u2="&#x10e;" k="16" />
<hkern u1="[" u2="&#x10d;" k="70" />
<hkern u1="[" u2="&#x10c;" k="66" />
<hkern u1="[" u2="&#x10b;" k="70" />
<hkern u1="[" u2="&#x10a;" k="66" />
<hkern u1="[" u2="&#x109;" k="70" />
<hkern u1="[" u2="&#x108;" k="66" />
<hkern u1="[" u2="&#x107;" k="70" />
<hkern u1="[" u2="&#x106;" k="66" />
<hkern u1="[" u2="&#x105;" k="70" />
<hkern u1="[" u2="&#x104;" k="25" />
<hkern u1="[" u2="&#x103;" k="70" />
<hkern u1="[" u2="&#x102;" k="25" />
<hkern u1="[" u2="&#x101;" k="70" />
<hkern u1="[" u2="&#x100;" k="25" />
<hkern u1="[" u2="&#xff;" k="47" />
<hkern u1="[" u2="&#xfd;" k="47" />
<hkern u1="[" u2="&#xfc;" k="61" />
<hkern u1="[" u2="&#xfb;" k="61" />
<hkern u1="[" u2="&#xfa;" k="61" />
<hkern u1="[" u2="&#xf9;" k="61" />
<hkern u1="[" u2="&#xf8;" k="70" />
<hkern u1="[" u2="&#xf6;" k="70" />
<hkern u1="[" u2="&#xf5;" k="70" />
<hkern u1="[" u2="&#xf4;" k="70" />
<hkern u1="[" u2="&#xf3;" k="70" />
<hkern u1="[" u2="&#xf2;" k="70" />
<hkern u1="[" u2="&#xf1;" k="45" />
<hkern u1="[" u2="&#xef;" k="-86" />
<hkern u1="[" u2="&#xee;" k="-37" />
<hkern u1="[" u2="&#xec;" k="-162" />
<hkern u1="[" u2="&#xeb;" k="70" />
<hkern u1="[" u2="&#xea;" k="70" />
<hkern u1="[" u2="&#xe9;" k="70" />
<hkern u1="[" u2="&#xe8;" k="70" />
<hkern u1="[" u2="&#xe7;" k="70" />
<hkern u1="[" u2="&#xe6;" k="70" />
<hkern u1="[" u2="&#xe5;" k="70" />
<hkern u1="[" u2="&#xe4;" k="70" />
<hkern u1="[" u2="&#xe3;" k="70" />
<hkern u1="[" u2="&#xe2;" k="70" />
<hkern u1="[" u2="&#xe1;" k="70" />
<hkern u1="[" u2="&#xe0;" k="70" />
<hkern u1="[" u2="&#xdd;" k="-14" />
<hkern u1="[" u2="&#xdc;" k="14" />
<hkern u1="[" u2="&#xdb;" k="14" />
<hkern u1="[" u2="&#xda;" k="14" />
<hkern u1="[" u2="&#xd9;" k="14" />
<hkern u1="[" u2="&#xd8;" k="66" />
<hkern u1="[" u2="&#xd6;" k="66" />
<hkern u1="[" u2="&#xd5;" k="66" />
<hkern u1="[" u2="&#xd4;" k="66" />
<hkern u1="[" u2="&#xd3;" k="66" />
<hkern u1="[" u2="&#xd2;" k="66" />
<hkern u1="[" u2="&#xd1;" k="16" />
<hkern u1="[" u2="&#xcf;" k="16" />
<hkern u1="[" u2="&#xce;" k="16" />
<hkern u1="[" u2="&#xcd;" k="16" />
<hkern u1="[" u2="&#xcc;" k="16" />
<hkern u1="[" u2="&#xcb;" k="16" />
<hkern u1="[" u2="&#xca;" k="16" />
<hkern u1="[" u2="&#xc9;" k="16" />
<hkern u1="[" u2="&#xc8;" k="16" />
<hkern u1="[" u2="&#xc7;" k="66" />
<hkern u1="[" u2="&#xc6;" k="25" />
<hkern u1="[" u2="&#xc5;" k="25" />
<hkern u1="[" u2="&#xc4;" k="25" />
<hkern u1="[" u2="&#xc3;" k="25" />
<hkern u1="[" u2="&#xc2;" k="25" />
<hkern u1="[" u2="&#xc1;" k="25" />
<hkern u1="[" u2="&#xc0;" k="25" />
<hkern u1="[" u2="&#x7b;" k="53" />
<hkern u1="[" u2="z" k="33" />
<hkern u1="[" u2="y" k="47" />
<hkern u1="[" u2="x" k="16" />
<hkern u1="[" u2="w" k="53" />
<hkern u1="[" u2="v" k="49" />
<hkern u1="[" u2="u" k="61" />
<hkern u1="[" u2="t" k="45" />
<hkern u1="[" u2="s" k="59" />
<hkern u1="[" u2="r" k="45" />
<hkern u1="[" u2="q" k="70" />
<hkern u1="[" u2="p" k="45" />
<hkern u1="[" u2="o" k="70" />
<hkern u1="[" u2="n" k="45" />
<hkern u1="[" u2="m" k="45" />
<hkern u1="[" u2="j" k="-59" />
<hkern u1="[" u2="f" k="27" />
<hkern u1="[" u2="e" k="70" />
<hkern u1="[" u2="d" k="70" />
<hkern u1="[" u2="c" k="70" />
<hkern u1="[" u2="a" k="70" />
<hkern u1="[" u2="Y" k="-14" />
<hkern u1="[" u2="U" k="14" />
<hkern u1="[" u2="S" k="39" />
<hkern u1="[" u2="R" k="16" />
<hkern u1="[" u2="Q" k="66" />
<hkern u1="[" u2="P" k="16" />
<hkern u1="[" u2="O" k="66" />
<hkern u1="[" u2="N" k="16" />
<hkern u1="[" u2="M" k="16" />
<hkern u1="[" u2="L" k="16" />
<hkern u1="[" u2="K" k="16" />
<hkern u1="[" u2="I" k="16" />
<hkern u1="[" u2="H" k="16" />
<hkern u1="[" u2="G" k="66" />
<hkern u1="[" u2="F" k="16" />
<hkern u1="[" u2="E" k="16" />
<hkern u1="[" u2="D" k="16" />
<hkern u1="[" u2="C" k="66" />
<hkern u1="[" u2="B" k="16" />
<hkern u1="[" u2="A" k="25" />
<hkern u1="[" u2="&#x28;" k="37" />
<hkern u1="\" u2="&#x201d;" k="121" />
<hkern u1="\" u2="&#x2019;" k="121" />
<hkern u1="\" u2="&#x21b;" k="27" />
<hkern u1="\" u2="&#x21a;" k="92" />
<hkern u1="\" u2="&#x1ff;" k="14" />
<hkern u1="\" u2="&#x1fe;" k="39" />
<hkern u1="\" u2="&#x178;" k="129" />
<hkern u1="\" u2="&#x177;" k="39" />
<hkern u1="\" u2="&#x176;" k="129" />
<hkern u1="\" u2="&#x175;" k="31" />
<hkern u1="\" u2="&#x174;" k="70" />
<hkern u1="\" u2="&#x172;" k="35" />
<hkern u1="\" u2="&#x170;" k="35" />
<hkern u1="\" u2="&#x16e;" k="35" />
<hkern u1="\" u2="&#x16c;" k="35" />
<hkern u1="\" u2="&#x16a;" k="35" />
<hkern u1="\" u2="&#x168;" k="35" />
<hkern u1="\" u2="&#x167;" k="27" />
<hkern u1="\" u2="&#x166;" k="92" />
<hkern u1="\" u2="&#x165;" k="27" />
<hkern u1="\" u2="&#x164;" k="92" />
<hkern u1="\" u2="&#x153;" k="14" />
<hkern u1="\" u2="&#x152;" k="39" />
<hkern u1="\" u2="&#x151;" k="14" />
<hkern u1="\" u2="&#x150;" k="39" />
<hkern u1="\" u2="&#x14f;" k="14" />
<hkern u1="\" u2="&#x14e;" k="39" />
<hkern u1="\" u2="&#x14d;" k="14" />
<hkern u1="\" u2="&#x14c;" k="39" />
<hkern u1="\" u2="&#x122;" k="39" />
<hkern u1="\" u2="&#x120;" k="39" />
<hkern u1="\" u2="&#x11e;" k="39" />
<hkern u1="\" u2="&#x11c;" k="39" />
<hkern u1="\" u2="&#x11b;" k="14" />
<hkern u1="\" u2="&#x119;" k="14" />
<hkern u1="\" u2="&#x117;" k="14" />
<hkern u1="\" u2="&#x115;" k="14" />
<hkern u1="\" u2="&#x113;" k="14" />
<hkern u1="\" u2="&#x10d;" k="14" />
<hkern u1="\" u2="&#x10c;" k="39" />
<hkern u1="\" u2="&#x10b;" k="14" />
<hkern u1="\" u2="&#x10a;" k="39" />
<hkern u1="\" u2="&#x109;" k="14" />
<hkern u1="\" u2="&#x108;" k="39" />
<hkern u1="\" u2="&#x107;" k="14" />
<hkern u1="\" u2="&#x106;" k="39" />
<hkern u1="\" u2="&#xff;" k="39" />
<hkern u1="\" u2="&#xfd;" k="39" />
<hkern u1="\" u2="&#xf8;" k="14" />
<hkern u1="\" u2="&#xf6;" k="14" />
<hkern u1="\" u2="&#xf5;" k="14" />
<hkern u1="\" u2="&#xf4;" k="14" />
<hkern u1="\" u2="&#xf3;" k="14" />
<hkern u1="\" u2="&#xf2;" k="14" />
<hkern u1="\" u2="&#xeb;" k="14" />
<hkern u1="\" u2="&#xea;" k="14" />
<hkern u1="\" u2="&#xe9;" k="14" />
<hkern u1="\" u2="&#xe8;" k="14" />
<hkern u1="\" u2="&#xe7;" k="14" />
<hkern u1="\" u2="&#xdd;" k="129" />
<hkern u1="\" u2="&#xdc;" k="35" />
<hkern u1="\" u2="&#xdb;" k="35" />
<hkern u1="\" u2="&#xda;" k="35" />
<hkern u1="\" u2="&#xd9;" k="35" />
<hkern u1="\" u2="&#xd8;" k="39" />
<hkern u1="\" u2="&#xd6;" k="39" />
<hkern u1="\" u2="&#xd5;" k="39" />
<hkern u1="\" u2="&#xd4;" k="39" />
<hkern u1="\" u2="&#xd3;" k="39" />
<hkern u1="\" u2="&#xd2;" k="39" />
<hkern u1="\" u2="&#xc7;" k="39" />
<hkern u1="\" u2="y" k="39" />
<hkern u1="\" u2="w" k="31" />
<hkern u1="\" u2="v" k="39" />
<hkern u1="\" u2="t" k="27" />
<hkern u1="\" u2="o" k="14" />
<hkern u1="\" u2="e" k="14" />
<hkern u1="\" u2="c" k="14" />
<hkern u1="\" u2="Y" k="129" />
<hkern u1="\" u2="W" k="70" />
<hkern u1="\" u2="V" k="88" />
<hkern u1="\" u2="U" k="35" />
<hkern u1="\" u2="T" k="92" />
<hkern u1="\" u2="Q" k="39" />
<hkern u1="\" u2="O" k="39" />
<hkern u1="\" u2="G" k="39" />
<hkern u1="\" u2="C" k="39" />
<hkern u1="\" u2="&#x27;" k="127" />
<hkern u1="\" u2="&#x22;" k="127" />
<hkern u1="a" u2="&#x2122;" k="45" />
<hkern u1="a" u2="&#x7d;" k="49" />
<hkern u1="a" u2="&#x7c;" k="47" />
<hkern u1="a" u2="]" k="43" />
<hkern u1="a" u2="\" k="61" />
<hkern u1="a" u2="V" k="55" />
<hkern u1="a" u2="&#x3f;" k="33" />
<hkern u1="a" u2="&#x2a;" k="16" />
<hkern u1="a" u2="&#x29;" k="33" />
<hkern u1="b" u2="&#x2122;" k="66" />
<hkern u1="b" u2="&#xae;" k="33" />
<hkern u1="b" u2="&#x7d;" k="63" />
<hkern u1="b" u2="&#x7c;" k="61" />
<hkern u1="b" u2="x" k="25" />
<hkern u1="b" u2="v" k="16" />
<hkern u1="b" u2="f" k="6" />
<hkern u1="b" u2="]" k="68" />
<hkern u1="b" u2="\" k="88" />
<hkern u1="b" u2="X" k="18" />
<hkern u1="b" u2="V" k="70" />
<hkern u1="b" u2="&#x3f;" k="45" />
<hkern u1="b" u2="&#x2a;" k="41" />
<hkern u1="b" u2="&#x29;" k="51" />
<hkern u1="c" u2="&#x2122;" k="31" />
<hkern u1="c" u2="&#x7d;" k="37" />
<hkern u1="c" u2="&#x7c;" k="33" />
<hkern u1="c" u2="]" k="31" />
<hkern u1="c" u2="\" k="43" />
<hkern u1="c" u2="V" k="29" />
<hkern u1="c" u2="&#x3f;" k="10" />
<hkern u1="c" u2="&#x29;" k="25" />
<hkern u1="d" u2="&#x135;" k="-29" />
<hkern u1="d" u2="&#x12d;" k="-31" />
<hkern u1="d" u2="&#x12b;" k="-18" />
<hkern u1="d" u2="&#x129;" k="-51" />
<hkern u1="d" u2="&#xef;" k="-27" />
<hkern u1="d" u2="&#xee;" k="-45" />
<hkern u1="d" u2="&#xec;" k="-88" />
<hkern u1="d" u2="&#x29;" k="23" />
<hkern u1="e" u2="&#x2122;" k="51" />
<hkern u1="e" u2="&#x142;" k="-20" />
<hkern u1="e" u2="&#x7d;" k="53" />
<hkern u1="e" u2="&#x7c;" k="51" />
<hkern u1="e" u2="v" k="12" />
<hkern u1="e" u2="]" k="47" />
<hkern u1="e" u2="\" k="72" />
<hkern u1="e" u2="V" k="53" />
<hkern u1="e" u2="&#x3f;" k="35" />
<hkern u1="e" u2="&#x2a;" k="27" />
<hkern u1="e" u2="&#x29;" k="35" />
<hkern u1="f" u2="&#x203a;" k="16" />
<hkern u1="f" u2="&#x2039;" k="72" />
<hkern u1="f" u2="&#x2026;" k="74" />
<hkern u1="f" u2="&#x201e;" k="74" />
<hkern u1="f" u2="&#x201a;" k="74" />
<hkern u1="f" u2="&#x2014;" k="68" />
<hkern u1="f" u2="&#x2013;" k="68" />
<hkern u1="f" u2="&#x1ff;" k="8" />
<hkern u1="f" u2="&#x1fd;" k="8" />
<hkern u1="f" u2="&#x1fc;" k="41" />
<hkern u1="f" u2="&#x1fb;" k="8" />
<hkern u1="f" u2="&#x1fa;" k="41" />
<hkern u1="f" u2="&#x17d;" k="8" />
<hkern u1="f" u2="&#x17b;" k="8" />
<hkern u1="f" u2="&#x179;" k="8" />
<hkern u1="f" u2="&#x178;" k="-12" />
<hkern u1="f" u2="&#x176;" k="-12" />
<hkern u1="f" u2="&#x153;" k="8" />
<hkern u1="f" u2="&#x151;" k="8" />
<hkern u1="f" u2="&#x14f;" k="8" />
<hkern u1="f" u2="&#x14d;" k="8" />
<hkern u1="f" u2="&#x135;" k="-72" />
<hkern u1="f" u2="&#x12d;" k="-113" />
<hkern u1="f" u2="&#x12b;" k="-78" />
<hkern u1="f" u2="&#x129;" k="-127" />
<hkern u1="f" u2="&#x11b;" k="8" />
<hkern u1="f" u2="&#x119;" k="8" />
<hkern u1="f" u2="&#x117;" k="8" />
<hkern u1="f" u2="&#x115;" k="8" />
<hkern u1="f" u2="&#x113;" k="8" />
<hkern u1="f" u2="&#x111;" k="8" />
<hkern u1="f" u2="&#x10f;" k="8" />
<hkern u1="f" u2="&#x10d;" k="8" />
<hkern u1="f" u2="&#x10b;" k="8" />
<hkern u1="f" u2="&#x109;" k="8" />
<hkern u1="f" u2="&#x107;" k="8" />
<hkern u1="f" u2="&#x105;" k="8" />
<hkern u1="f" u2="&#x104;" k="41" />
<hkern u1="f" u2="&#x103;" k="8" />
<hkern u1="f" u2="&#x102;" k="41" />
<hkern u1="f" u2="&#x101;" k="8" />
<hkern u1="f" u2="&#x100;" k="41" />
<hkern u1="f" u2="&#xf8;" k="8" />
<hkern u1="f" u2="&#xf6;" k="8" />
<hkern u1="f" u2="&#xf5;" k="8" />
<hkern u1="f" u2="&#xf4;" k="8" />
<hkern u1="f" u2="&#xf3;" k="8" />
<hkern u1="f" u2="&#xf2;" k="8" />
<hkern u1="f" u2="&#xef;" k="-88" />
<hkern u1="f" u2="&#xee;" k="-88" />
<hkern u1="f" u2="&#xec;" k="-184" />
<hkern u1="f" u2="&#xeb;" k="8" />
<hkern u1="f" u2="&#xea;" k="8" />
<hkern u1="f" u2="&#xe9;" k="8" />
<hkern u1="f" u2="&#xe8;" k="8" />
<hkern u1="f" u2="&#xe7;" k="8" />
<hkern u1="f" u2="&#xe6;" k="8" />
<hkern u1="f" u2="&#xe5;" k="8" />
<hkern u1="f" u2="&#xe4;" k="8" />
<hkern u1="f" u2="&#xe3;" k="8" />
<hkern u1="f" u2="&#xe2;" k="8" />
<hkern u1="f" u2="&#xe1;" k="8" />
<hkern u1="f" u2="&#xe0;" k="8" />
<hkern u1="f" u2="&#xdd;" k="-12" />
<hkern u1="f" u2="&#xc6;" k="41" />
<hkern u1="f" u2="&#xc5;" k="41" />
<hkern u1="f" u2="&#xc4;" k="41" />
<hkern u1="f" u2="&#xc3;" k="41" />
<hkern u1="f" u2="&#xc2;" k="41" />
<hkern u1="f" u2="&#xc1;" k="41" />
<hkern u1="f" u2="&#xc0;" k="41" />
<hkern u1="f" u2="&#xbb;" k="16" />
<hkern u1="f" u2="&#xab;" k="72" />
<hkern u1="f" u2="q" k="8" />
<hkern u1="f" u2="o" k="8" />
<hkern u1="f" u2="e" k="8" />
<hkern u1="f" u2="d" k="8" />
<hkern u1="f" u2="c" k="8" />
<hkern u1="f" u2="a" k="8" />
<hkern u1="f" u2="Z" k="8" />
<hkern u1="f" u2="Y" k="-12" />
<hkern u1="f" u2="X" k="8" />
<hkern u1="f" u2="A" k="41" />
<hkern u1="f" u2="&#x2f;" k="37" />
<hkern u1="f" u2="&#x2e;" k="74" />
<hkern u1="f" u2="&#x2d;" k="68" />
<hkern u1="f" u2="&#x2c;" k="74" />
<hkern u1="f" u2="&#x2a;" k="-12" />
<hkern u1="g" u2="&#x2122;" k="18" />
<hkern u1="g" u2="&#x201e;" k="-55" />
<hkern u1="g" u2="&#x201a;" k="-33" />
<hkern u1="g" u2="&#x135;" k="-80" />
<hkern u1="g" u2="&#x12f;" k="-43" />
<hkern u1="g" u2="&#x7d;" k="-8" />
<hkern u1="g" u2="&#x7c;" k="27" />
<hkern u1="g" u2="j" k="-80" />
<hkern u1="g" u2="]" k="-8" />
<hkern u1="g" u2="\" k="29" />
<hkern u1="g" u2="V" k="12" />
<hkern u1="g" u2="&#x3b;" k="-37" />
<hkern u1="g" u2="&#x2c;" k="-35" />
<hkern u1="g" u2="&#x29;" k="-12" />
<hkern u1="h" u2="&#x2122;" k="61" />
<hkern u1="h" u2="&#xae;" k="31" />
<hkern u1="h" u2="&#x7d;" k="49" />
<hkern u1="h" u2="&#x7c;" k="63" />
<hkern u1="h" u2="v" k="12" />
<hkern u1="h" u2="f" k="6" />
<hkern u1="h" u2="]" k="35" />
<hkern u1="h" u2="\" k="86" />
<hkern u1="h" u2="V" k="63" />
<hkern u1="h" u2="&#x3f;" k="41" />
<hkern u1="h" u2="&#x2a;" k="35" />
<hkern u1="h" u2="&#x29;" k="33" />
<hkern u1="i" u2="&#x135;" k="-31" />
<hkern u1="i" u2="&#x12d;" k="-27" />
<hkern u1="i" u2="&#x12b;" k="-18" />
<hkern u1="i" u2="&#x129;" k="-53" />
<hkern u1="i" u2="&#xef;" k="-27" />
<hkern u1="i" u2="&#xee;" k="-47" />
<hkern u1="i" u2="&#xec;" k="-90" />
<hkern u1="i" u2="&#x29;" k="25" />
<hkern u1="j" u2="&#x135;" k="-31" />
<hkern u1="j" u2="&#x12d;" k="-20" />
<hkern u1="j" u2="&#x12b;" k="-18" />
<hkern u1="j" u2="&#x129;" k="-53" />
<hkern u1="j" u2="&#xef;" k="-27" />
<hkern u1="j" u2="&#xee;" k="-47" />
<hkern u1="j" u2="&#xec;" k="-90" />
<hkern u1="j" u2="&#x29;" k="25" />
<hkern u1="k" u2="&#x2122;" k="25" />
<hkern u1="k" u2="&#x7d;" k="10" />
<hkern u1="k" u2="&#x7c;" k="23" />
<hkern u1="k" u2="]" k="10" />
<hkern u1="k" u2="\" k="27" />
<hkern u1="k" u2="V" k="14" />
<hkern u1="k" u2="&#x3f;" k="8" />
<hkern u1="l" u2="&#x135;" k="-29" />
<hkern u1="l" u2="&#x12d;" k="-31" />
<hkern u1="l" u2="&#x12b;" k="-18" />
<hkern u1="l" u2="&#x129;" k="-49" />
<hkern u1="l" u2="&#xef;" k="-27" />
<hkern u1="l" u2="&#xee;" k="-45" />
<hkern u1="l" u2="&#xec;" k="-86" />
<hkern u1="l" u2="&#x29;" k="23" />
<hkern u1="m" u2="&#x2122;" k="61" />
<hkern u1="m" u2="&#xae;" k="31" />
<hkern u1="m" u2="&#x7d;" k="49" />
<hkern u1="m" u2="&#x7c;" k="63" />
<hkern u1="m" u2="v" k="12" />
<hkern u1="m" u2="f" k="6" />
<hkern u1="m" u2="]" k="35" />
<hkern u1="m" u2="\" k="86" />
<hkern u1="m" u2="V" k="63" />
<hkern u1="m" u2="&#x3f;" k="41" />
<hkern u1="m" u2="&#x2a;" k="35" />
<hkern u1="m" u2="&#x29;" k="33" />
<hkern u1="n" u2="&#x2122;" k="61" />
<hkern u1="n" u2="&#xae;" k="31" />
<hkern u1="n" u2="&#x7d;" k="49" />
<hkern u1="n" u2="&#x7c;" k="63" />
<hkern u1="n" u2="v" k="12" />
<hkern u1="n" u2="f" k="6" />
<hkern u1="n" u2="]" k="35" />
<hkern u1="n" u2="\" k="86" />
<hkern u1="n" u2="V" k="63" />
<hkern u1="n" u2="&#x3f;" k="41" />
<hkern u1="n" u2="&#x2a;" k="35" />
<hkern u1="n" u2="&#x29;" k="33" />
<hkern u1="o" u2="&#x2122;" k="66" />
<hkern u1="o" u2="&#xae;" k="37" />
<hkern u1="o" u2="&#x7d;" k="63" />
<hkern u1="o" u2="&#x7c;" k="63" />
<hkern u1="o" u2="x" k="27" />
<hkern u1="o" u2="v" k="20" />
<hkern u1="o" u2="f" k="10" />
<hkern u1="o" u2="]" k="70" />
<hkern u1="o" u2="\" k="92" />
<hkern u1="o" u2="X" k="20" />
<hkern u1="o" u2="V" k="74" />
<hkern u1="o" u2="&#x3f;" k="43" />
<hkern u1="o" u2="&#x2a;" k="43" />
<hkern u1="o" u2="&#x29;" k="51" />
<hkern u1="p" u2="&#x2122;" k="66" />
<hkern u1="p" u2="&#xae;" k="33" />
<hkern u1="p" u2="&#x7d;" k="63" />
<hkern u1="p" u2="&#x7c;" k="61" />
<hkern u1="p" u2="x" k="25" />
<hkern u1="p" u2="v" k="16" />
<hkern u1="p" u2="f" k="6" />
<hkern u1="p" u2="]" k="68" />
<hkern u1="p" u2="\" k="88" />
<hkern u1="p" u2="X" k="18" />
<hkern u1="p" u2="V" k="70" />
<hkern u1="p" u2="&#x3f;" k="45" />
<hkern u1="p" u2="&#x2a;" k="41" />
<hkern u1="p" u2="&#x29;" k="51" />
<hkern u1="q" u2="&#x2122;" k="39" />
<hkern u1="q" u2="&#x201e;" k="-12" />
<hkern u1="q" u2="&#x7d;" k="51" />
<hkern u1="q" u2="&#x7c;" k="45" />
<hkern u1="q" u2="]" k="39" />
<hkern u1="q" u2="\" k="55" />
<hkern u1="q" u2="V" k="51" />
<hkern u1="q" u2="&#x3f;" k="31" />
<hkern u1="q" u2="&#x29;" k="35" />
<hkern u1="r" u2="&#x7d;" k="49" />
<hkern u1="r" u2="&#x7c;" k="16" />
<hkern u1="r" u2="]" k="61" />
<hkern u1="r" u2="\" k="18" />
<hkern u1="r" u2="X" k="51" />
<hkern u1="r" u2="&#x3f;" k="10" />
<hkern u1="r" u2="&#x2f;" k="47" />
<hkern u1="r" u2="&#x29;" k="35" />
<hkern u1="s" u2="&#x2122;" k="43" />
<hkern u1="s" u2="&#x7d;" k="57" />
<hkern u1="s" u2="&#x7c;" k="43" />
<hkern u1="s" u2="v" k="12" />
<hkern u1="s" u2="]" k="55" />
<hkern u1="s" u2="\" k="59" />
<hkern u1="s" u2="V" k="49" />
<hkern u1="s" u2="&#x3f;" k="29" />
<hkern u1="s" u2="&#x2a;" k="12" />
<hkern u1="s" u2="&#x29;" k="35" />
<hkern u1="t" u2="&#x2122;" k="16" />
<hkern u1="t" u2="&#x7d;" k="20" />
<hkern u1="t" u2="&#x7c;" k="27" />
<hkern u1="t" u2="]" k="8" />
<hkern u1="t" u2="\" k="27" />
<hkern u1="t" u2="V" k="8" />
<hkern u1="t" u2="&#x3f;" k="6" />
<hkern u1="t" u2="&#x29;" k="16" />
<hkern u1="u" u2="&#x2122;" k="39" />
<hkern u1="u" u2="&#x7d;" k="51" />
<hkern u1="u" u2="&#x7c;" k="45" />
<hkern u1="u" u2="]" k="39" />
<hkern u1="u" u2="\" k="55" />
<hkern u1="u" u2="V" k="51" />
<hkern u1="u" u2="&#x3f;" k="31" />
<hkern u1="u" u2="&#x29;" k="35" />
<hkern u1="v" u2="&#x2122;" k="25" />
<hkern u1="v" u2="&#x203a;" k="23" />
<hkern u1="v" u2="&#x2039;" k="47" />
<hkern u1="v" u2="&#x2026;" k="78" />
<hkern u1="v" u2="&#x201e;" k="78" />
<hkern u1="v" u2="&#x201a;" k="78" />
<hkern u1="v" u2="&#x2014;" k="41" />
<hkern u1="v" u2="&#x2013;" k="41" />
<hkern u1="v" u2="&#x21a;" k="121" />
<hkern u1="v" u2="&#x219;" k="18" />
<hkern u1="v" u2="&#x1ff;" k="25" />
<hkern u1="v" u2="&#x1fd;" k="25" />
<hkern u1="v" u2="&#x1fc;" k="23" />
<hkern u1="v" u2="&#x1fb;" k="25" />
<hkern u1="v" u2="&#x1fa;" k="23" />
<hkern u1="v" u2="&#x17d;" k="18" />
<hkern u1="v" u2="&#x17b;" k="18" />
<hkern u1="v" u2="&#x179;" k="18" />
<hkern u1="v" u2="&#x178;" k="70" />
<hkern u1="v" u2="&#x176;" k="70" />
<hkern u1="v" u2="&#x174;" k="10" />
<hkern u1="v" u2="&#x166;" k="121" />
<hkern u1="v" u2="&#x164;" k="121" />
<hkern u1="v" u2="&#x161;" k="18" />
<hkern u1="v" u2="&#x15f;" k="18" />
<hkern u1="v" u2="&#x15d;" k="18" />
<hkern u1="v" u2="&#x15b;" k="18" />
<hkern u1="v" u2="&#x153;" k="25" />
<hkern u1="v" u2="&#x151;" k="25" />
<hkern u1="v" u2="&#x14f;" k="25" />
<hkern u1="v" u2="&#x14d;" k="25" />
<hkern u1="v" u2="&#x134;" k="31" />
<hkern u1="v" u2="&#x123;" k="16" />
<hkern u1="v" u2="&#x121;" k="16" />
<hkern u1="v" u2="&#x11f;" k="16" />
<hkern u1="v" u2="&#x11d;" k="16" />
<hkern u1="v" u2="&#x11b;" k="25" />
<hkern u1="v" u2="&#x119;" k="25" />
<hkern u1="v" u2="&#x117;" k="25" />
<hkern u1="v" u2="&#x115;" k="25" />
<hkern u1="v" u2="&#x113;" k="25" />
<hkern u1="v" u2="&#x111;" k="25" />
<hkern u1="v" u2="&#x10f;" k="25" />
<hkern u1="v" u2="&#x10d;" k="25" />
<hkern u1="v" u2="&#x10b;" k="25" />
<hkern u1="v" u2="&#x109;" k="25" />
<hkern u1="v" u2="&#x107;" k="25" />
<hkern u1="v" u2="&#x105;" k="25" />
<hkern u1="v" u2="&#x104;" k="23" />
<hkern u1="v" u2="&#x103;" k="25" />
<hkern u1="v" u2="&#x102;" k="23" />
<hkern u1="v" u2="&#x101;" k="25" />
<hkern u1="v" u2="&#x100;" k="23" />
<hkern u1="v" u2="&#xf8;" k="25" />
<hkern u1="v" u2="&#xf6;" k="25" />
<hkern u1="v" u2="&#xf5;" k="25" />
<hkern u1="v" u2="&#xf4;" k="25" />
<hkern u1="v" u2="&#xf3;" k="25" />
<hkern u1="v" u2="&#xf2;" k="25" />
<hkern u1="v" u2="&#xeb;" k="25" />
<hkern u1="v" u2="&#xea;" k="25" />
<hkern u1="v" u2="&#xe9;" k="25" />
<hkern u1="v" u2="&#xe8;" k="25" />
<hkern u1="v" u2="&#xe7;" k="25" />
<hkern u1="v" u2="&#xe6;" k="25" />
<hkern u1="v" u2="&#xe5;" k="25" />
<hkern u1="v" u2="&#xe4;" k="25" />
<hkern u1="v" u2="&#xe3;" k="25" />
<hkern u1="v" u2="&#xe2;" k="25" />
<hkern u1="v" u2="&#xe1;" k="25" />
<hkern u1="v" u2="&#xe0;" k="25" />
<hkern u1="v" u2="&#xdd;" k="70" />
<hkern u1="v" u2="&#xc6;" k="23" />
<hkern u1="v" u2="&#xc5;" k="23" />
<hkern u1="v" u2="&#xc4;" k="23" />
<hkern u1="v" u2="&#xc3;" k="23" />
<hkern u1="v" u2="&#xc2;" k="23" />
<hkern u1="v" u2="&#xc1;" k="23" />
<hkern u1="v" u2="&#xc0;" k="23" />
<hkern u1="v" u2="&#xbb;" k="23" />
<hkern u1="v" u2="&#xab;" k="47" />
<hkern u1="v" u2="&#x7d;" k="57" />
<hkern u1="v" u2="&#x7c;" k="27" />
<hkern u1="v" u2="s" k="18" />
<hkern u1="v" u2="q" k="25" />
<hkern u1="v" u2="o" k="25" />
<hkern u1="v" u2="g" k="16" />
<hkern u1="v" u2="e" k="25" />
<hkern u1="v" u2="d" k="25" />
<hkern u1="v" u2="c" k="25" />
<hkern u1="v" u2="a" k="25" />
<hkern u1="v" u2="]" k="68" />
<hkern u1="v" u2="\" k="41" />
<hkern u1="v" u2="Z" k="18" />
<hkern u1="v" u2="Y" k="70" />
<hkern u1="v" u2="X" k="33" />
<hkern u1="v" u2="W" k="10" />
<hkern u1="v" u2="V" k="16" />
<hkern u1="v" u2="T" k="121" />
<hkern u1="v" u2="J" k="31" />
<hkern u1="v" u2="A" k="23" />
<hkern u1="v" u2="&#x3f;" k="10" />
<hkern u1="v" u2="&#x2f;" k="39" />
<hkern u1="v" u2="&#x2e;" k="78" />
<hkern u1="v" u2="&#x2d;" k="41" />
<hkern u1="v" u2="&#x2c;" k="78" />
<hkern u1="v" u2="&#x29;" k="45" />
<hkern u1="w" u2="&#x2122;" k="27" />
<hkern u1="w" u2="&#x7d;" k="57" />
<hkern u1="w" u2="&#x7c;" k="25" />
<hkern u1="w" u2="]" k="66" />
<hkern u1="w" u2="\" k="37" />
<hkern u1="w" u2="X" k="29" />
<hkern u1="w" u2="V" k="18" />
<hkern u1="w" u2="&#x3f;" k="10" />
<hkern u1="w" u2="&#x2f;" k="27" />
<hkern u1="w" u2="&#x29;" k="45" />
<hkern u1="x" u2="&#x2122;" k="18" />
<hkern u1="x" u2="&#x2039;" k="68" />
<hkern u1="x" u2="&#x2014;" k="63" />
<hkern u1="x" u2="&#x2013;" k="63" />
<hkern u1="x" u2="&#x21a;" k="121" />
<hkern u1="x" u2="&#x1ff;" k="27" />
<hkern u1="x" u2="&#x1fd;" k="25" />
<hkern u1="x" u2="&#x1fb;" k="25" />
<hkern u1="x" u2="&#x178;" k="61" />
<hkern u1="x" u2="&#x176;" k="61" />
<hkern u1="x" u2="&#x166;" k="121" />
<hkern u1="x" u2="&#x164;" k="121" />
<hkern u1="x" u2="&#x153;" k="27" />
<hkern u1="x" u2="&#x151;" k="27" />
<hkern u1="x" u2="&#x14f;" k="27" />
<hkern u1="x" u2="&#x14d;" k="27" />
<hkern u1="x" u2="&#x11b;" k="27" />
<hkern u1="x" u2="&#x119;" k="27" />
<hkern u1="x" u2="&#x117;" k="27" />
<hkern u1="x" u2="&#x115;" k="27" />
<hkern u1="x" u2="&#x113;" k="27" />
<hkern u1="x" u2="&#x111;" k="25" />
<hkern u1="x" u2="&#x10f;" k="25" />
<hkern u1="x" u2="&#x10d;" k="27" />
<hkern u1="x" u2="&#x10b;" k="27" />
<hkern u1="x" u2="&#x109;" k="27" />
<hkern u1="x" u2="&#x107;" k="27" />
<hkern u1="x" u2="&#x105;" k="25" />
<hkern u1="x" u2="&#x103;" k="25" />
<hkern u1="x" u2="&#x101;" k="25" />
<hkern u1="x" u2="&#xf8;" k="27" />
<hkern u1="x" u2="&#xf6;" k="27" />
<hkern u1="x" u2="&#xf5;" k="27" />
<hkern u1="x" u2="&#xf4;" k="27" />
<hkern u1="x" u2="&#xf3;" k="27" />
<hkern u1="x" u2="&#xf2;" k="27" />
<hkern u1="x" u2="&#xeb;" k="27" />
<hkern u1="x" u2="&#xea;" k="27" />
<hkern u1="x" u2="&#xe9;" k="27" />
<hkern u1="x" u2="&#xe8;" k="27" />
<hkern u1="x" u2="&#xe7;" k="27" />
<hkern u1="x" u2="&#xe6;" k="25" />
<hkern u1="x" u2="&#xe5;" k="25" />
<hkern u1="x" u2="&#xe4;" k="25" />
<hkern u1="x" u2="&#xe3;" k="25" />
<hkern u1="x" u2="&#xe2;" k="25" />
<hkern u1="x" u2="&#xe1;" k="25" />
<hkern u1="x" u2="&#xe0;" k="25" />
<hkern u1="x" u2="&#xdd;" k="61" />
<hkern u1="x" u2="&#xab;" k="68" />
<hkern u1="x" u2="&#x7d;" k="10" />
<hkern u1="x" u2="&#x7c;" k="18" />
<hkern u1="x" u2="q" k="25" />
<hkern u1="x" u2="o" k="27" />
<hkern u1="x" u2="e" k="27" />
<hkern u1="x" u2="d" k="25" />
<hkern u1="x" u2="c" k="27" />
<hkern u1="x" u2="a" k="25" />
<hkern u1="x" u2="]" k="12" />
<hkern u1="x" u2="\" k="20" />
<hkern u1="x" u2="Y" k="61" />
<hkern u1="x" u2="V" k="10" />
<hkern u1="x" u2="T" k="121" />
<hkern u1="x" u2="&#x3f;" k="6" />
<hkern u1="x" u2="&#x2d;" k="63" />
<hkern u1="y" u2="&#x2122;" k="25" />
<hkern u1="y" u2="&#x7d;" k="55" />
<hkern u1="y" u2="&#x7c;" k="27" />
<hkern u1="y" u2="]" k="66" />
<hkern u1="y" u2="\" k="41" />
<hkern u1="y" u2="X" k="31" />
<hkern u1="y" u2="V" k="16" />
<hkern u1="y" u2="&#x3f;" k="10" />
<hkern u1="y" u2="&#x2f;" k="39" />
<hkern u1="y" u2="&#x29;" k="39" />
<hkern u1="z" u2="&#x2122;" k="33" />
<hkern u1="z" u2="&#x7d;" k="41" />
<hkern u1="z" u2="&#x7c;" k="33" />
<hkern u1="z" u2="]" k="14" />
<hkern u1="z" u2="\" k="43" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="z" u2="&#x3f;" k="8" />
<hkern u1="z" u2="&#x29;" k="29" />
<hkern u1="&#x7b;" u2="&#x21b;" k="41" />
<hkern u1="&#x7b;" u2="&#x219;" k="57" />
<hkern u1="&#x7b;" u2="&#x218;" k="35" />
<hkern u1="&#x7b;" u2="&#x1ff;" k="66" />
<hkern u1="&#x7b;" u2="&#x1fe;" k="61" />
<hkern u1="&#x7b;" u2="&#x1fd;" k="66" />
<hkern u1="&#x7b;" u2="&#x1fc;" k="53" />
<hkern u1="&#x7b;" u2="&#x1fb;" k="66" />
<hkern u1="&#x7b;" u2="&#x1fa;" k="53" />
<hkern u1="&#x7b;" u2="&#x17e;" k="49" />
<hkern u1="&#x7b;" u2="&#x17c;" k="49" />
<hkern u1="&#x7b;" u2="&#x17a;" k="49" />
<hkern u1="&#x7b;" u2="&#x178;" k="-14" />
<hkern u1="&#x7b;" u2="&#x177;" k="41" />
<hkern u1="&#x7b;" u2="&#x176;" k="-14" />
<hkern u1="&#x7b;" u2="&#x175;" k="51" />
<hkern u1="&#x7b;" u2="&#x173;" k="57" />
<hkern u1="&#x7b;" u2="&#x171;" k="57" />
<hkern u1="&#x7b;" u2="&#x16f;" k="57" />
<hkern u1="&#x7b;" u2="&#x16d;" k="57" />
<hkern u1="&#x7b;" u2="&#x16b;" k="57" />
<hkern u1="&#x7b;" u2="&#x169;" k="57" />
<hkern u1="&#x7b;" u2="&#x167;" k="41" />
<hkern u1="&#x7b;" u2="&#x165;" k="41" />
<hkern u1="&#x7b;" u2="&#x161;" k="57" />
<hkern u1="&#x7b;" u2="&#x160;" k="35" />
<hkern u1="&#x7b;" u2="&#x15f;" k="57" />
<hkern u1="&#x7b;" u2="&#x15e;" k="35" />
<hkern u1="&#x7b;" u2="&#x15d;" k="57" />
<hkern u1="&#x7b;" u2="&#x15c;" k="35" />
<hkern u1="&#x7b;" u2="&#x15b;" k="57" />
<hkern u1="&#x7b;" u2="&#x15a;" k="35" />
<hkern u1="&#x7b;" u2="&#x159;" k="6" />
<hkern u1="&#x7b;" u2="&#x158;" k="16" />
<hkern u1="&#x7b;" u2="&#x157;" k="57" />
<hkern u1="&#x7b;" u2="&#x156;" k="16" />
<hkern u1="&#x7b;" u2="&#x155;" k="37" />
<hkern u1="&#x7b;" u2="&#x154;" k="16" />
<hkern u1="&#x7b;" u2="&#x153;" k="66" />
<hkern u1="&#x7b;" u2="&#x152;" k="61" />
<hkern u1="&#x7b;" u2="&#x151;" k="66" />
<hkern u1="&#x7b;" u2="&#x150;" k="61" />
<hkern u1="&#x7b;" u2="&#x14f;" k="66" />
<hkern u1="&#x7b;" u2="&#x14e;" k="61" />
<hkern u1="&#x7b;" u2="&#x14d;" k="66" />
<hkern u1="&#x7b;" u2="&#x14c;" k="61" />
<hkern u1="&#x7b;" u2="&#x14b;" k="57" />
<hkern u1="&#x7b;" u2="&#x14a;" k="16" />
<hkern u1="&#x7b;" u2="&#x148;" k="57" />
<hkern u1="&#x7b;" u2="&#x147;" k="16" />
<hkern u1="&#x7b;" u2="&#x146;" k="57" />
<hkern u1="&#x7b;" u2="&#x145;" k="16" />
<hkern u1="&#x7b;" u2="&#x144;" k="57" />
<hkern u1="&#x7b;" u2="&#x143;" k="16" />
<hkern u1="&#x7b;" u2="&#x141;" k="16" />
<hkern u1="&#x7b;" u2="&#x13d;" k="16" />
<hkern u1="&#x7b;" u2="&#x13b;" k="16" />
<hkern u1="&#x7b;" u2="&#x139;" k="16" />
<hkern u1="&#x7b;" u2="&#x136;" k="16" />
<hkern u1="&#x7b;" u2="&#x135;" k="-61" />
<hkern u1="&#x7b;" u2="&#x130;" k="16" />
<hkern u1="&#x7b;" u2="&#x12f;" k="-12" />
<hkern u1="&#x7b;" u2="&#x12e;" k="16" />
<hkern u1="&#x7b;" u2="&#x12d;" k="-111" />
<hkern u1="&#x7b;" u2="&#x12c;" k="16" />
<hkern u1="&#x7b;" u2="&#x12b;" k="-80" />
<hkern u1="&#x7b;" u2="&#x12a;" k="16" />
<hkern u1="&#x7b;" u2="&#x129;" k="-117" />
<hkern u1="&#x7b;" u2="&#x128;" k="16" />
<hkern u1="&#x7b;" u2="&#x127;" k="-8" />
<hkern u1="&#x7b;" u2="&#x126;" k="16" />
<hkern u1="&#x7b;" u2="&#x124;" k="16" />
<hkern u1="&#x7b;" u2="&#x122;" k="61" />
<hkern u1="&#x7b;" u2="&#x120;" k="61" />
<hkern u1="&#x7b;" u2="&#x11e;" k="61" />
<hkern u1="&#x7b;" u2="&#x11c;" k="61" />
<hkern u1="&#x7b;" u2="&#x11b;" k="66" />
<hkern u1="&#x7b;" u2="&#x11a;" k="16" />
<hkern u1="&#x7b;" u2="&#x119;" k="66" />
<hkern u1="&#x7b;" u2="&#x118;" k="16" />
<hkern u1="&#x7b;" u2="&#x117;" k="66" />
<hkern u1="&#x7b;" u2="&#x116;" k="16" />
<hkern u1="&#x7b;" u2="&#x115;" k="66" />
<hkern u1="&#x7b;" u2="&#x114;" k="16" />
<hkern u1="&#x7b;" u2="&#x113;" k="66" />
<hkern u1="&#x7b;" u2="&#x112;" k="16" />
<hkern u1="&#x7b;" u2="&#x111;" k="66" />
<hkern u1="&#x7b;" u2="&#x110;" k="16" />
<hkern u1="&#x7b;" u2="&#x10f;" k="66" />
<hkern u1="&#x7b;" u2="&#x10e;" k="16" />
<hkern u1="&#x7b;" u2="&#x10d;" k="66" />
<hkern u1="&#x7b;" u2="&#x10c;" k="61" />
<hkern u1="&#x7b;" u2="&#x10b;" k="66" />
<hkern u1="&#x7b;" u2="&#x10a;" k="61" />
<hkern u1="&#x7b;" u2="&#x109;" k="66" />
<hkern u1="&#x7b;" u2="&#x108;" k="61" />
<hkern u1="&#x7b;" u2="&#x107;" k="66" />
<hkern u1="&#x7b;" u2="&#x106;" k="61" />
<hkern u1="&#x7b;" u2="&#x105;" k="66" />
<hkern u1="&#x7b;" u2="&#x104;" k="53" />
<hkern u1="&#x7b;" u2="&#x103;" k="66" />
<hkern u1="&#x7b;" u2="&#x102;" k="53" />
<hkern u1="&#x7b;" u2="&#x101;" k="66" />
<hkern u1="&#x7b;" u2="&#x100;" k="53" />
<hkern u1="&#x7b;" u2="&#xff;" k="41" />
<hkern u1="&#x7b;" u2="&#xfd;" k="41" />
<hkern u1="&#x7b;" u2="&#xfc;" k="57" />
<hkern u1="&#x7b;" u2="&#xfb;" k="57" />
<hkern u1="&#x7b;" u2="&#xfa;" k="57" />
<hkern u1="&#x7b;" u2="&#xf9;" k="57" />
<hkern u1="&#x7b;" u2="&#xf8;" k="66" />
<hkern u1="&#x7b;" u2="&#xf6;" k="66" />
<hkern u1="&#x7b;" u2="&#xf5;" k="66" />
<hkern u1="&#x7b;" u2="&#xf4;" k="66" />
<hkern u1="&#x7b;" u2="&#xf3;" k="66" />
<hkern u1="&#x7b;" u2="&#xf2;" k="66" />
<hkern u1="&#x7b;" u2="&#xf1;" k="57" />
<hkern u1="&#x7b;" u2="&#xef;" k="-90" />
<hkern u1="&#x7b;" u2="&#xee;" k="-35" />
<hkern u1="&#x7b;" u2="&#xec;" k="-166" />
<hkern u1="&#x7b;" u2="&#xeb;" k="66" />
<hkern u1="&#x7b;" u2="&#xea;" k="66" />
<hkern u1="&#x7b;" u2="&#xe9;" k="66" />
<hkern u1="&#x7b;" u2="&#xe8;" k="66" />
<hkern u1="&#x7b;" u2="&#xe7;" k="66" />
<hkern u1="&#x7b;" u2="&#xe6;" k="66" />
<hkern u1="&#x7b;" u2="&#xe5;" k="66" />
<hkern u1="&#x7b;" u2="&#xe4;" k="66" />
<hkern u1="&#x7b;" u2="&#xe3;" k="66" />
<hkern u1="&#x7b;" u2="&#xe2;" k="66" />
<hkern u1="&#x7b;" u2="&#xe1;" k="66" />
<hkern u1="&#x7b;" u2="&#xe0;" k="66" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-14" />
<hkern u1="&#x7b;" u2="&#xd8;" k="61" />
<hkern u1="&#x7b;" u2="&#xd6;" k="61" />
<hkern u1="&#x7b;" u2="&#xd5;" k="61" />
<hkern u1="&#x7b;" u2="&#xd4;" k="61" />
<hkern u1="&#x7b;" u2="&#xd3;" k="61" />
<hkern u1="&#x7b;" u2="&#xd2;" k="61" />
<hkern u1="&#x7b;" u2="&#xd1;" k="16" />
<hkern u1="&#x7b;" u2="&#xcf;" k="16" />
<hkern u1="&#x7b;" u2="&#xce;" k="16" />
<hkern u1="&#x7b;" u2="&#xcd;" k="16" />
<hkern u1="&#x7b;" u2="&#xcc;" k="16" />
<hkern u1="&#x7b;" u2="&#xcb;" k="16" />
<hkern u1="&#x7b;" u2="&#xca;" k="16" />
<hkern u1="&#x7b;" u2="&#xc9;" k="16" />
<hkern u1="&#x7b;" u2="&#xc8;" k="16" />
<hkern u1="&#x7b;" u2="&#xc7;" k="61" />
<hkern u1="&#x7b;" u2="&#xc6;" k="53" />
<hkern u1="&#x7b;" u2="&#xc5;" k="53" />
<hkern u1="&#x7b;" u2="&#xc4;" k="53" />
<hkern u1="&#x7b;" u2="&#xc3;" k="53" />
<hkern u1="&#x7b;" u2="&#xc2;" k="53" />
<hkern u1="&#x7b;" u2="&#xc1;" k="53" />
<hkern u1="&#x7b;" u2="&#xc0;" k="53" />
<hkern u1="&#x7b;" u2="&#x7b;" k="53" />
<hkern u1="&#x7b;" u2="z" k="49" />
<hkern u1="&#x7b;" u2="y" k="41" />
<hkern u1="&#x7b;" u2="x" k="12" />
<hkern u1="&#x7b;" u2="w" k="51" />
<hkern u1="&#x7b;" u2="v" k="45" />
<hkern u1="&#x7b;" u2="u" k="57" />
<hkern u1="&#x7b;" u2="t" k="41" />
<hkern u1="&#x7b;" u2="s" k="57" />
<hkern u1="&#x7b;" u2="r" k="57" />
<hkern u1="&#x7b;" u2="q" k="66" />
<hkern u1="&#x7b;" u2="p" k="57" />
<hkern u1="&#x7b;" u2="o" k="66" />
<hkern u1="&#x7b;" u2="n" k="57" />
<hkern u1="&#x7b;" u2="m" k="57" />
<hkern u1="&#x7b;" u2="j" k="-61" />
<hkern u1="&#x7b;" u2="f" k="27" />
<hkern u1="&#x7b;" u2="e" k="66" />
<hkern u1="&#x7b;" u2="d" k="66" />
<hkern u1="&#x7b;" u2="c" k="66" />
<hkern u1="&#x7b;" u2="a" k="66" />
<hkern u1="&#x7b;" u2="Y" k="-14" />
<hkern u1="&#x7b;" u2="S" k="35" />
<hkern u1="&#x7b;" u2="R" k="16" />
<hkern u1="&#x7b;" u2="Q" k="61" />
<hkern u1="&#x7b;" u2="P" k="16" />
<hkern u1="&#x7b;" u2="O" k="61" />
<hkern u1="&#x7b;" u2="N" k="16" />
<hkern u1="&#x7b;" u2="M" k="16" />
<hkern u1="&#x7b;" u2="L" k="16" />
<hkern u1="&#x7b;" u2="K" k="16" />
<hkern u1="&#x7b;" u2="I" k="16" />
<hkern u1="&#x7b;" u2="H" k="16" />
<hkern u1="&#x7b;" u2="G" k="61" />
<hkern u1="&#x7b;" u2="F" k="16" />
<hkern u1="&#x7b;" u2="E" k="16" />
<hkern u1="&#x7b;" u2="D" k="16" />
<hkern u1="&#x7b;" u2="C" k="61" />
<hkern u1="&#x7b;" u2="B" k="16" />
<hkern u1="&#x7b;" u2="A" k="53" />
<hkern u1="&#x7b;" u2="&#x28;" k="37" />
<hkern u1="&#x7c;" u2="&#x201d;" k="94" />
<hkern u1="&#x7c;" u2="&#x2019;" k="94" />
<hkern u1="&#x7c;" u2="&#x21b;" k="76" />
<hkern u1="&#x7c;" u2="&#x21a;" k="106" />
<hkern u1="&#x7c;" u2="&#x219;" k="41" />
<hkern u1="&#x7c;" u2="&#x218;" k="43" />
<hkern u1="&#x7c;" u2="&#x1ff;" k="63" />
<hkern u1="&#x7c;" u2="&#x1fe;" k="76" />
<hkern u1="&#x7c;" u2="&#x1fd;" k="61" />
<hkern u1="&#x7c;" u2="&#x1fc;" k="33" />
<hkern u1="&#x7c;" u2="&#x1fb;" k="61" />
<hkern u1="&#x7c;" u2="&#x1fa;" k="33" />
<hkern u1="&#x7c;" u2="&#x17e;" k="35" />
<hkern u1="&#x7c;" u2="&#x17d;" k="37" />
<hkern u1="&#x7c;" u2="&#x17c;" k="35" />
<hkern u1="&#x7c;" u2="&#x17b;" k="37" />
<hkern u1="&#x7c;" u2="&#x17a;" k="35" />
<hkern u1="&#x7c;" u2="&#x179;" k="37" />
<hkern u1="&#x7c;" u2="&#x178;" k="143" />
<hkern u1="&#x7c;" u2="&#x177;" k="78" />
<hkern u1="&#x7c;" u2="&#x176;" k="143" />
<hkern u1="&#x7c;" u2="&#x175;" k="80" />
<hkern u1="&#x7c;" u2="&#x174;" k="109" />
<hkern u1="&#x7c;" u2="&#x173;" k="63" />
<hkern u1="&#x7c;" u2="&#x172;" k="74" />
<hkern u1="&#x7c;" u2="&#x171;" k="63" />
<hkern u1="&#x7c;" u2="&#x170;" k="74" />
<hkern u1="&#x7c;" u2="&#x16f;" k="63" />
<hkern u1="&#x7c;" u2="&#x16e;" k="74" />
<hkern u1="&#x7c;" u2="&#x16d;" k="63" />
<hkern u1="&#x7c;" u2="&#x16c;" k="74" />
<hkern u1="&#x7c;" u2="&#x16b;" k="63" />
<hkern u1="&#x7c;" u2="&#x16a;" k="74" />
<hkern u1="&#x7c;" u2="&#x169;" k="63" />
<hkern u1="&#x7c;" u2="&#x168;" k="74" />
<hkern u1="&#x7c;" u2="&#x167;" k="76" />
<hkern u1="&#x7c;" u2="&#x166;" k="106" />
<hkern u1="&#x7c;" u2="&#x165;" k="76" />
<hkern u1="&#x7c;" u2="&#x164;" k="106" />
<hkern u1="&#x7c;" u2="&#x161;" k="41" />
<hkern u1="&#x7c;" u2="&#x160;" k="43" />
<hkern u1="&#x7c;" u2="&#x15f;" k="41" />
<hkern u1="&#x7c;" u2="&#x15e;" k="43" />
<hkern u1="&#x7c;" u2="&#x15d;" k="41" />
<hkern u1="&#x7c;" u2="&#x15c;" k="43" />
<hkern u1="&#x7c;" u2="&#x15b;" k="41" />
<hkern u1="&#x7c;" u2="&#x15a;" k="43" />
<hkern u1="&#x7c;" u2="&#x159;" k="45" />
<hkern u1="&#x7c;" u2="&#x158;" k="51" />
<hkern u1="&#x7c;" u2="&#x157;" k="45" />
<hkern u1="&#x7c;" u2="&#x156;" k="51" />
<hkern u1="&#x7c;" u2="&#x155;" k="45" />
<hkern u1="&#x7c;" u2="&#x154;" k="51" />
<hkern u1="&#x7c;" u2="&#x153;" k="63" />
<hkern u1="&#x7c;" u2="&#x152;" k="76" />
<hkern u1="&#x7c;" u2="&#x151;" k="63" />
<hkern u1="&#x7c;" u2="&#x150;" k="76" />
<hkern u1="&#x7c;" u2="&#x14f;" k="63" />
<hkern u1="&#x7c;" u2="&#x14e;" k="76" />
<hkern u1="&#x7c;" u2="&#x14d;" k="63" />
<hkern u1="&#x7c;" u2="&#x14c;" k="76" />
<hkern u1="&#x7c;" u2="&#x14b;" k="45" />
<hkern u1="&#x7c;" u2="&#x14a;" k="51" />
<hkern u1="&#x7c;" u2="&#x148;" k="45" />
<hkern u1="&#x7c;" u2="&#x147;" k="51" />
<hkern u1="&#x7c;" u2="&#x146;" k="45" />
<hkern u1="&#x7c;" u2="&#x145;" k="51" />
<hkern u1="&#x7c;" u2="&#x144;" k="45" />
<hkern u1="&#x7c;" u2="&#x143;" k="51" />
<hkern u1="&#x7c;" u2="&#x142;" k="45" />
<hkern u1="&#x7c;" u2="&#x141;" k="51" />
<hkern u1="&#x7c;" u2="&#x13e;" k="45" />
<hkern u1="&#x7c;" u2="&#x13d;" k="51" />
<hkern u1="&#x7c;" u2="&#x13c;" k="45" />
<hkern u1="&#x7c;" u2="&#x13b;" k="51" />
<hkern u1="&#x7c;" u2="&#x13a;" k="45" />
<hkern u1="&#x7c;" u2="&#x139;" k="51" />
<hkern u1="&#x7c;" u2="&#x137;" k="45" />
<hkern u1="&#x7c;" u2="&#x136;" k="51" />
<hkern u1="&#x7c;" u2="&#x135;" k="45" />
<hkern u1="&#x7c;" u2="&#x131;" k="45" />
<hkern u1="&#x7c;" u2="&#x130;" k="51" />
<hkern u1="&#x7c;" u2="&#x12f;" k="45" />
<hkern u1="&#x7c;" u2="&#x12e;" k="27" />
<hkern u1="&#x7c;" u2="&#x12d;" k="45" />
<hkern u1="&#x7c;" u2="&#x12c;" k="51" />
<hkern u1="&#x7c;" u2="&#x12b;" k="45" />
<hkern u1="&#x7c;" u2="&#x12a;" k="51" />
<hkern u1="&#x7c;" u2="&#x129;" k="45" />
<hkern u1="&#x7c;" u2="&#x128;" k="51" />
<hkern u1="&#x7c;" u2="&#x127;" k="45" />
<hkern u1="&#x7c;" u2="&#x126;" k="51" />
<hkern u1="&#x7c;" u2="&#x125;" k="45" />
<hkern u1="&#x7c;" u2="&#x124;" k="51" />
<hkern u1="&#x7c;" u2="&#x123;" k="16" />
<hkern u1="&#x7c;" u2="&#x122;" k="76" />
<hkern u1="&#x7c;" u2="&#x121;" k="16" />
<hkern u1="&#x7c;" u2="&#x120;" k="76" />
<hkern u1="&#x7c;" u2="&#x11f;" k="16" />
<hkern u1="&#x7c;" u2="&#x11e;" k="76" />
<hkern u1="&#x7c;" u2="&#x11d;" k="16" />
<hkern u1="&#x7c;" u2="&#x11c;" k="76" />
<hkern u1="&#x7c;" u2="&#x11b;" k="63" />
<hkern u1="&#x7c;" u2="&#x11a;" k="51" />
<hkern u1="&#x7c;" u2="&#x119;" k="63" />
<hkern u1="&#x7c;" u2="&#x118;" k="51" />
<hkern u1="&#x7c;" u2="&#x117;" k="63" />
<hkern u1="&#x7c;" u2="&#x116;" k="51" />
<hkern u1="&#x7c;" u2="&#x115;" k="63" />
<hkern u1="&#x7c;" u2="&#x114;" k="51" />
<hkern u1="&#x7c;" u2="&#x113;" k="63" />
<hkern u1="&#x7c;" u2="&#x112;" k="51" />
<hkern u1="&#x7c;" u2="&#x111;" k="61" />
<hkern u1="&#x7c;" u2="&#x110;" k="51" />
<hkern u1="&#x7c;" u2="&#x10f;" k="61" />
<hkern u1="&#x7c;" u2="&#x10e;" k="51" />
<hkern u1="&#x7c;" u2="&#x10d;" k="63" />
<hkern u1="&#x7c;" u2="&#x10c;" k="76" />
<hkern u1="&#x7c;" u2="&#x10b;" k="63" />
<hkern u1="&#x7c;" u2="&#x10a;" k="76" />
<hkern u1="&#x7c;" u2="&#x109;" k="63" />
<hkern u1="&#x7c;" u2="&#x108;" k="76" />
<hkern u1="&#x7c;" u2="&#x107;" k="63" />
<hkern u1="&#x7c;" u2="&#x106;" k="76" />
<hkern u1="&#x7c;" u2="&#x105;" k="61" />
<hkern u1="&#x7c;" u2="&#x104;" k="33" />
<hkern u1="&#x7c;" u2="&#x103;" k="61" />
<hkern u1="&#x7c;" u2="&#x102;" k="33" />
<hkern u1="&#x7c;" u2="&#x101;" k="61" />
<hkern u1="&#x7c;" u2="&#x100;" k="33" />
<hkern u1="&#x7c;" u2="&#xff;" k="78" />
<hkern u1="&#x7c;" u2="&#xfd;" k="78" />
<hkern u1="&#x7c;" u2="&#xfc;" k="63" />
<hkern u1="&#x7c;" u2="&#xfb;" k="63" />
<hkern u1="&#x7c;" u2="&#xfa;" k="63" />
<hkern u1="&#x7c;" u2="&#xf9;" k="63" />
<hkern u1="&#x7c;" u2="&#xf8;" k="63" />
<hkern u1="&#x7c;" u2="&#xf6;" k="63" />
<hkern u1="&#x7c;" u2="&#xf5;" k="63" />
<hkern u1="&#x7c;" u2="&#xf4;" k="63" />
<hkern u1="&#x7c;" u2="&#xf3;" k="63" />
<hkern u1="&#x7c;" u2="&#xf2;" k="63" />
<hkern u1="&#x7c;" u2="&#xf1;" k="45" />
<hkern u1="&#x7c;" u2="&#xef;" k="45" />
<hkern u1="&#x7c;" u2="&#xee;" k="45" />
<hkern u1="&#x7c;" u2="&#xed;" k="45" />
<hkern u1="&#x7c;" u2="&#xec;" k="45" />
<hkern u1="&#x7c;" u2="&#xeb;" k="63" />
<hkern u1="&#x7c;" u2="&#xea;" k="63" />
<hkern u1="&#x7c;" u2="&#xe9;" k="63" />
<hkern u1="&#x7c;" u2="&#xe8;" k="63" />
<hkern u1="&#x7c;" u2="&#xe7;" k="63" />
<hkern u1="&#x7c;" u2="&#xe6;" k="61" />
<hkern u1="&#x7c;" u2="&#xe5;" k="61" />
<hkern u1="&#x7c;" u2="&#xe4;" k="61" />
<hkern u1="&#x7c;" u2="&#xe3;" k="61" />
<hkern u1="&#x7c;" u2="&#xe2;" k="61" />
<hkern u1="&#x7c;" u2="&#xe1;" k="61" />
<hkern u1="&#x7c;" u2="&#xe0;" k="61" />
<hkern u1="&#x7c;" u2="&#xdf;" k="45" />
<hkern u1="&#x7c;" u2="&#xdd;" k="143" />
<hkern u1="&#x7c;" u2="&#xdc;" k="74" />
<hkern u1="&#x7c;" u2="&#xdb;" k="74" />
<hkern u1="&#x7c;" u2="&#xda;" k="74" />
<hkern u1="&#x7c;" u2="&#xd9;" k="74" />
<hkern u1="&#x7c;" u2="&#xd8;" k="76" />
<hkern u1="&#x7c;" u2="&#xd6;" k="76" />
<hkern u1="&#x7c;" u2="&#xd5;" k="76" />
<hkern u1="&#x7c;" u2="&#xd4;" k="76" />
<hkern u1="&#x7c;" u2="&#xd3;" k="76" />
<hkern u1="&#x7c;" u2="&#xd2;" k="76" />
<hkern u1="&#x7c;" u2="&#xd1;" k="51" />
<hkern u1="&#x7c;" u2="&#xcf;" k="51" />
<hkern u1="&#x7c;" u2="&#xce;" k="51" />
<hkern u1="&#x7c;" u2="&#xcd;" k="51" />
<hkern u1="&#x7c;" u2="&#xcc;" k="51" />
<hkern u1="&#x7c;" u2="&#xcb;" k="51" />
<hkern u1="&#x7c;" u2="&#xca;" k="51" />
<hkern u1="&#x7c;" u2="&#xc9;" k="51" />
<hkern u1="&#x7c;" u2="&#xc8;" k="51" />
<hkern u1="&#x7c;" u2="&#xc7;" k="76" />
<hkern u1="&#x7c;" u2="&#xc6;" k="33" />
<hkern u1="&#x7c;" u2="&#xc5;" k="33" />
<hkern u1="&#x7c;" u2="&#xc4;" k="33" />
<hkern u1="&#x7c;" u2="&#xc3;" k="33" />
<hkern u1="&#x7c;" u2="&#xc2;" k="33" />
<hkern u1="&#x7c;" u2="&#xc1;" k="33" />
<hkern u1="&#x7c;" u2="&#xc0;" k="33" />
<hkern u1="&#x7c;" u2="z" k="35" />
<hkern u1="&#x7c;" u2="y" k="78" />
<hkern u1="&#x7c;" u2="x" k="16" />
<hkern u1="&#x7c;" u2="w" k="80" />
<hkern u1="&#x7c;" u2="v" k="86" />
<hkern u1="&#x7c;" u2="u" k="63" />
<hkern u1="&#x7c;" u2="t" k="76" />
<hkern u1="&#x7c;" u2="s" k="41" />
<hkern u1="&#x7c;" u2="r" k="45" />
<hkern u1="&#x7c;" u2="q" k="61" />
<hkern u1="&#x7c;" u2="p" k="45" />
<hkern u1="&#x7c;" u2="o" k="63" />
<hkern u1="&#x7c;" u2="n" k="45" />
<hkern u1="&#x7c;" u2="m" k="45" />
<hkern u1="&#x7c;" u2="l" k="45" />
<hkern u1="&#x7c;" u2="k" k="45" />
<hkern u1="&#x7c;" u2="j" k="45" />
<hkern u1="&#x7c;" u2="i" k="45" />
<hkern u1="&#x7c;" u2="h" k="45" />
<hkern u1="&#x7c;" u2="g" k="16" />
<hkern u1="&#x7c;" u2="f" k="27" />
<hkern u1="&#x7c;" u2="e" k="63" />
<hkern u1="&#x7c;" u2="d" k="61" />
<hkern u1="&#x7c;" u2="c" k="63" />
<hkern u1="&#x7c;" u2="b" k="49" />
<hkern u1="&#x7c;" u2="a" k="61" />
<hkern u1="&#x7c;" u2="Z" k="37" />
<hkern u1="&#x7c;" u2="Y" k="143" />
<hkern u1="&#x7c;" u2="W" k="109" />
<hkern u1="&#x7c;" u2="V" k="121" />
<hkern u1="&#x7c;" u2="U" k="74" />
<hkern u1="&#x7c;" u2="T" k="106" />
<hkern u1="&#x7c;" u2="S" k="43" />
<hkern u1="&#x7c;" u2="R" k="51" />
<hkern u1="&#x7c;" u2="Q" k="76" />
<hkern u1="&#x7c;" u2="P" k="51" />
<hkern u1="&#x7c;" u2="O" k="76" />
<hkern u1="&#x7c;" u2="N" k="51" />
<hkern u1="&#x7c;" u2="M" k="51" />
<hkern u1="&#x7c;" u2="L" k="51" />
<hkern u1="&#x7c;" u2="K" k="51" />
<hkern u1="&#x7c;" u2="I" k="51" />
<hkern u1="&#x7c;" u2="H" k="51" />
<hkern u1="&#x7c;" u2="G" k="76" />
<hkern u1="&#x7c;" u2="F" k="51" />
<hkern u1="&#x7c;" u2="E" k="51" />
<hkern u1="&#x7c;" u2="D" k="51" />
<hkern u1="&#x7c;" u2="C" k="76" />
<hkern u1="&#x7c;" u2="B" k="51" />
<hkern u1="&#x7c;" u2="A" k="33" />
<hkern u1="&#x7c;" u2="&#x27;" k="100" />
<hkern u1="&#x7c;" u2="&#x22;" k="100" />
<hkern u1="&#x7d;" u2="&#x7d;" k="51" />
<hkern u1="&#x7d;" u2="]" k="51" />
<hkern u1="&#x7d;" u2="&#x29;" k="45" />
<hkern u1="&#xa1;" u2="&#x21a;" k="80" />
<hkern u1="&#xa1;" u2="&#x178;" k="74" />
<hkern u1="&#xa1;" u2="&#x176;" k="74" />
<hkern u1="&#xa1;" u2="&#x174;" k="25" />
<hkern u1="&#xa1;" u2="&#x166;" k="80" />
<hkern u1="&#xa1;" u2="&#x164;" k="80" />
<hkern u1="&#xa1;" u2="&#x135;" k="-61" />
<hkern u1="&#xa1;" u2="&#x12f;" k="-10" />
<hkern u1="&#xa1;" u2="&#xdd;" k="74" />
<hkern u1="&#xa1;" u2="j" k="-61" />
<hkern u1="&#xa1;" u2="Y" k="74" />
<hkern u1="&#xa1;" u2="W" k="25" />
<hkern u1="&#xa1;" u2="V" k="37" />
<hkern u1="&#xa1;" u2="T" k="80" />
<hkern u1="&#xab;" u2="V" k="47" />
<hkern u1="&#xae;" u2="&#x21a;" k="41" />
<hkern u1="&#xae;" u2="&#x1fc;" k="43" />
<hkern u1="&#xae;" u2="&#x1fa;" k="43" />
<hkern u1="&#xae;" u2="&#x17d;" k="39" />
<hkern u1="&#xae;" u2="&#x17b;" k="39" />
<hkern u1="&#xae;" u2="&#x179;" k="39" />
<hkern u1="&#xae;" u2="&#x178;" k="74" />
<hkern u1="&#xae;" u2="&#x176;" k="74" />
<hkern u1="&#xae;" u2="&#x174;" k="23" />
<hkern u1="&#xae;" u2="&#x166;" k="41" />
<hkern u1="&#xae;" u2="&#x164;" k="41" />
<hkern u1="&#xae;" u2="&#x141;" k="-10" />
<hkern u1="&#xae;" u2="&#x134;" k="31" />
<hkern u1="&#xae;" u2="&#x104;" k="43" />
<hkern u1="&#xae;" u2="&#x102;" k="43" />
<hkern u1="&#xae;" u2="&#x100;" k="43" />
<hkern u1="&#xae;" u2="&#xdd;" k="74" />
<hkern u1="&#xae;" u2="&#xc6;" k="43" />
<hkern u1="&#xae;" u2="&#xc5;" k="43" />
<hkern u1="&#xae;" u2="&#xc4;" k="43" />
<hkern u1="&#xae;" u2="&#xc3;" k="43" />
<hkern u1="&#xae;" u2="&#xc2;" k="43" />
<hkern u1="&#xae;" u2="&#xc1;" k="43" />
<hkern u1="&#xae;" u2="&#xc0;" k="43" />
<hkern u1="&#xae;" u2="Z" k="39" />
<hkern u1="&#xae;" u2="Y" k="74" />
<hkern u1="&#xae;" u2="X" k="47" />
<hkern u1="&#xae;" u2="W" k="23" />
<hkern u1="&#xae;" u2="V" k="31" />
<hkern u1="&#xae;" u2="T" k="41" />
<hkern u1="&#xae;" u2="J" k="31" />
<hkern u1="&#xae;" u2="A" k="43" />
<hkern u1="&#xae;" u2="&#x27;" k="16" />
<hkern u1="&#xae;" u2="&#x22;" k="16" />
<hkern u1="&#xbb;" u2="&#x166;" k="82" />
<hkern u1="&#xbb;" u2="&#x142;" k="-10" />
<hkern u1="&#xbb;" u2="&#x141;" k="-20" />
<hkern u1="&#xbb;" u2="x" k="66" />
<hkern u1="&#xbb;" u2="v" k="35" />
<hkern u1="&#xbb;" u2="f" k="29" />
<hkern u1="&#xbb;" u2="X" k="35" />
<hkern u1="&#xbb;" u2="V" k="72" />
<hkern u1="&#xbf;" u2="&#x21b;" k="43" />
<hkern u1="&#xbf;" u2="&#x21a;" k="123" />
<hkern u1="&#xbf;" u2="&#x219;" k="47" />
<hkern u1="&#xbf;" u2="&#x218;" k="45" />
<hkern u1="&#xbf;" u2="&#x1ff;" k="47" />
<hkern u1="&#xbf;" u2="&#x1fe;" k="45" />
<hkern u1="&#xbf;" u2="&#x1fd;" k="45" />
<hkern u1="&#xbf;" u2="&#x1fc;" k="49" />
<hkern u1="&#xbf;" u2="&#x1fb;" k="45" />
<hkern u1="&#xbf;" u2="&#x1fa;" k="49" />
<hkern u1="&#xbf;" u2="&#x17e;" k="45" />
<hkern u1="&#xbf;" u2="&#x17d;" k="43" />
<hkern u1="&#xbf;" u2="&#x17c;" k="45" />
<hkern u1="&#xbf;" u2="&#x17b;" k="43" />
<hkern u1="&#xbf;" u2="&#x17a;" k="45" />
<hkern u1="&#xbf;" u2="&#x179;" k="43" />
<hkern u1="&#xbf;" u2="&#x178;" k="115" />
<hkern u1="&#xbf;" u2="&#x177;" k="49" />
<hkern u1="&#xbf;" u2="&#x176;" k="115" />
<hkern u1="&#xbf;" u2="&#x175;" k="49" />
<hkern u1="&#xbf;" u2="&#x174;" k="70" />
<hkern u1="&#xbf;" u2="&#x173;" k="45" />
<hkern u1="&#xbf;" u2="&#x172;" k="47" />
<hkern u1="&#xbf;" u2="&#x171;" k="45" />
<hkern u1="&#xbf;" u2="&#x170;" k="47" />
<hkern u1="&#xbf;" u2="&#x16f;" k="45" />
<hkern u1="&#xbf;" u2="&#x16e;" k="47" />
<hkern u1="&#xbf;" u2="&#x16d;" k="45" />
<hkern u1="&#xbf;" u2="&#x16c;" k="47" />
<hkern u1="&#xbf;" u2="&#x16b;" k="45" />
<hkern u1="&#xbf;" u2="&#x16a;" k="47" />
<hkern u1="&#xbf;" u2="&#x169;" k="45" />
<hkern u1="&#xbf;" u2="&#x168;" k="47" />
<hkern u1="&#xbf;" u2="&#x167;" k="43" />
<hkern u1="&#xbf;" u2="&#x166;" k="123" />
<hkern u1="&#xbf;" u2="&#x165;" k="43" />
<hkern u1="&#xbf;" u2="&#x164;" k="123" />
<hkern u1="&#xbf;" u2="&#x161;" k="47" />
<hkern u1="&#xbf;" u2="&#x160;" k="45" />
<hkern u1="&#xbf;" u2="&#x15f;" k="47" />
<hkern u1="&#xbf;" u2="&#x15e;" k="45" />
<hkern u1="&#xbf;" u2="&#x15d;" k="47" />
<hkern u1="&#xbf;" u2="&#x15c;" k="45" />
<hkern u1="&#xbf;" u2="&#x15b;" k="47" />
<hkern u1="&#xbf;" u2="&#x15a;" k="45" />
<hkern u1="&#xbf;" u2="&#x159;" k="41" />
<hkern u1="&#xbf;" u2="&#x158;" k="41" />
<hkern u1="&#xbf;" u2="&#x157;" k="41" />
<hkern u1="&#xbf;" u2="&#x156;" k="41" />
<hkern u1="&#xbf;" u2="&#x155;" k="41" />
<hkern u1="&#xbf;" u2="&#x154;" k="41" />
<hkern u1="&#xbf;" u2="&#x153;" k="47" />
<hkern u1="&#xbf;" u2="&#x152;" k="45" />
<hkern u1="&#xbf;" u2="&#x151;" k="47" />
<hkern u1="&#xbf;" u2="&#x150;" k="45" />
<hkern u1="&#xbf;" u2="&#x14f;" k="47" />
<hkern u1="&#xbf;" u2="&#x14e;" k="45" />
<hkern u1="&#xbf;" u2="&#x14d;" k="47" />
<hkern u1="&#xbf;" u2="&#x14c;" k="45" />
<hkern u1="&#xbf;" u2="&#x14b;" k="41" />
<hkern u1="&#xbf;" u2="&#x14a;" k="41" />
<hkern u1="&#xbf;" u2="&#x148;" k="41" />
<hkern u1="&#xbf;" u2="&#x147;" k="41" />
<hkern u1="&#xbf;" u2="&#x146;" k="41" />
<hkern u1="&#xbf;" u2="&#x145;" k="41" />
<hkern u1="&#xbf;" u2="&#x144;" k="41" />
<hkern u1="&#xbf;" u2="&#x143;" k="41" />
<hkern u1="&#xbf;" u2="&#x142;" k="41" />
<hkern u1="&#xbf;" u2="&#x141;" k="41" />
<hkern u1="&#xbf;" u2="&#x13e;" k="41" />
<hkern u1="&#xbf;" u2="&#x13d;" k="41" />
<hkern u1="&#xbf;" u2="&#x13c;" k="41" />
<hkern u1="&#xbf;" u2="&#x13b;" k="41" />
<hkern u1="&#xbf;" u2="&#x13a;" k="41" />
<hkern u1="&#xbf;" u2="&#x139;" k="41" />
<hkern u1="&#xbf;" u2="&#x137;" k="41" />
<hkern u1="&#xbf;" u2="&#x136;" k="41" />
<hkern u1="&#xbf;" u2="&#x135;" k="41" />
<hkern u1="&#xbf;" u2="&#x134;" k="18" />
<hkern u1="&#xbf;" u2="&#x131;" k="41" />
<hkern u1="&#xbf;" u2="&#x130;" k="41" />
<hkern u1="&#xbf;" u2="&#x12f;" k="41" />
<hkern u1="&#xbf;" u2="&#x12e;" k="35" />
<hkern u1="&#xbf;" u2="&#x12d;" k="41" />
<hkern u1="&#xbf;" u2="&#x12c;" k="41" />
<hkern u1="&#xbf;" u2="&#x12b;" k="41" />
<hkern u1="&#xbf;" u2="&#x12a;" k="41" />
<hkern u1="&#xbf;" u2="&#x129;" k="41" />
<hkern u1="&#xbf;" u2="&#x128;" k="41" />
<hkern u1="&#xbf;" u2="&#x127;" k="41" />
<hkern u1="&#xbf;" u2="&#x126;" k="41" />
<hkern u1="&#xbf;" u2="&#x125;" k="41" />
<hkern u1="&#xbf;" u2="&#x124;" k="41" />
<hkern u1="&#xbf;" u2="&#x122;" k="45" />
<hkern u1="&#xbf;" u2="&#x120;" k="45" />
<hkern u1="&#xbf;" u2="&#x11e;" k="45" />
<hkern u1="&#xbf;" u2="&#x11c;" k="45" />
<hkern u1="&#xbf;" u2="&#x11b;" k="47" />
<hkern u1="&#xbf;" u2="&#x11a;" k="41" />
<hkern u1="&#xbf;" u2="&#x119;" k="47" />
<hkern u1="&#xbf;" u2="&#x118;" k="41" />
<hkern u1="&#xbf;" u2="&#x117;" k="47" />
<hkern u1="&#xbf;" u2="&#x116;" k="41" />
<hkern u1="&#xbf;" u2="&#x115;" k="47" />
<hkern u1="&#xbf;" u2="&#x114;" k="41" />
<hkern u1="&#xbf;" u2="&#x113;" k="47" />
<hkern u1="&#xbf;" u2="&#x112;" k="41" />
<hkern u1="&#xbf;" u2="&#x111;" k="45" />
<hkern u1="&#xbf;" u2="&#x110;" k="41" />
<hkern u1="&#xbf;" u2="&#x10f;" k="45" />
<hkern u1="&#xbf;" u2="&#x10e;" k="41" />
<hkern u1="&#xbf;" u2="&#x10d;" k="47" />
<hkern u1="&#xbf;" u2="&#x10c;" k="45" />
<hkern u1="&#xbf;" u2="&#x10b;" k="47" />
<hkern u1="&#xbf;" u2="&#x10a;" k="45" />
<hkern u1="&#xbf;" u2="&#x109;" k="47" />
<hkern u1="&#xbf;" u2="&#x108;" k="45" />
<hkern u1="&#xbf;" u2="&#x107;" k="47" />
<hkern u1="&#xbf;" u2="&#x106;" k="45" />
<hkern u1="&#xbf;" u2="&#x105;" k="45" />
<hkern u1="&#xbf;" u2="&#x104;" k="49" />
<hkern u1="&#xbf;" u2="&#x103;" k="45" />
<hkern u1="&#xbf;" u2="&#x102;" k="49" />
<hkern u1="&#xbf;" u2="&#x101;" k="45" />
<hkern u1="&#xbf;" u2="&#x100;" k="49" />
<hkern u1="&#xbf;" u2="&#xff;" k="49" />
<hkern u1="&#xbf;" u2="&#xfd;" k="49" />
<hkern u1="&#xbf;" u2="&#xfc;" k="45" />
<hkern u1="&#xbf;" u2="&#xfb;" k="45" />
<hkern u1="&#xbf;" u2="&#xfa;" k="45" />
<hkern u1="&#xbf;" u2="&#xf9;" k="45" />
<hkern u1="&#xbf;" u2="&#xf8;" k="47" />
<hkern u1="&#xbf;" u2="&#xf6;" k="47" />
<hkern u1="&#xbf;" u2="&#xf5;" k="47" />
<hkern u1="&#xbf;" u2="&#xf4;" k="47" />
<hkern u1="&#xbf;" u2="&#xf3;" k="47" />
<hkern u1="&#xbf;" u2="&#xf2;" k="47" />
<hkern u1="&#xbf;" u2="&#xf1;" k="41" />
<hkern u1="&#xbf;" u2="&#xef;" k="41" />
<hkern u1="&#xbf;" u2="&#xee;" k="41" />
<hkern u1="&#xbf;" u2="&#xed;" k="41" />
<hkern u1="&#xbf;" u2="&#xec;" k="41" />
<hkern u1="&#xbf;" u2="&#xeb;" k="47" />
<hkern u1="&#xbf;" u2="&#xea;" k="47" />
<hkern u1="&#xbf;" u2="&#xe9;" k="47" />
<hkern u1="&#xbf;" u2="&#xe8;" k="47" />
<hkern u1="&#xbf;" u2="&#xe7;" k="47" />
<hkern u1="&#xbf;" u2="&#xe6;" k="45" />
<hkern u1="&#xbf;" u2="&#xe5;" k="45" />
<hkern u1="&#xbf;" u2="&#xe4;" k="45" />
<hkern u1="&#xbf;" u2="&#xe3;" k="45" />
<hkern u1="&#xbf;" u2="&#xe2;" k="45" />
<hkern u1="&#xbf;" u2="&#xe1;" k="45" />
<hkern u1="&#xbf;" u2="&#xe0;" k="45" />
<hkern u1="&#xbf;" u2="&#xdf;" k="41" />
<hkern u1="&#xbf;" u2="&#xdd;" k="115" />
<hkern u1="&#xbf;" u2="&#xdc;" k="47" />
<hkern u1="&#xbf;" u2="&#xdb;" k="47" />
<hkern u1="&#xbf;" u2="&#xda;" k="47" />
<hkern u1="&#xbf;" u2="&#xd9;" k="47" />
<hkern u1="&#xbf;" u2="&#xd8;" k="45" />
<hkern u1="&#xbf;" u2="&#xd6;" k="45" />
<hkern u1="&#xbf;" u2="&#xd5;" k="45" />
<hkern u1="&#xbf;" u2="&#xd4;" k="45" />
<hkern u1="&#xbf;" u2="&#xd3;" k="45" />
<hkern u1="&#xbf;" u2="&#xd2;" k="45" />
<hkern u1="&#xbf;" u2="&#xd1;" k="41" />
<hkern u1="&#xbf;" u2="&#xcf;" k="41" />
<hkern u1="&#xbf;" u2="&#xce;" k="41" />
<hkern u1="&#xbf;" u2="&#xcd;" k="41" />
<hkern u1="&#xbf;" u2="&#xcc;" k="41" />
<hkern u1="&#xbf;" u2="&#xcb;" k="41" />
<hkern u1="&#xbf;" u2="&#xca;" k="41" />
<hkern u1="&#xbf;" u2="&#xc9;" k="41" />
<hkern u1="&#xbf;" u2="&#xc8;" k="41" />
<hkern u1="&#xbf;" u2="&#xc7;" k="45" />
<hkern u1="&#xbf;" u2="&#xc6;" k="49" />
<hkern u1="&#xbf;" u2="&#xc5;" k="49" />
<hkern u1="&#xbf;" u2="&#xc4;" k="49" />
<hkern u1="&#xbf;" u2="&#xc3;" k="49" />
<hkern u1="&#xbf;" u2="&#xc2;" k="49" />
<hkern u1="&#xbf;" u2="&#xc1;" k="49" />
<hkern u1="&#xbf;" u2="&#xc0;" k="49" />
<hkern u1="&#xbf;" u2="z" k="45" />
<hkern u1="&#xbf;" u2="y" k="49" />
<hkern u1="&#xbf;" u2="x" k="43" />
<hkern u1="&#xbf;" u2="w" k="49" />
<hkern u1="&#xbf;" u2="v" k="51" />
<hkern u1="&#xbf;" u2="u" k="45" />
<hkern u1="&#xbf;" u2="t" k="43" />
<hkern u1="&#xbf;" u2="s" k="47" />
<hkern u1="&#xbf;" u2="r" k="41" />
<hkern u1="&#xbf;" u2="q" k="45" />
<hkern u1="&#xbf;" u2="p" k="41" />
<hkern u1="&#xbf;" u2="o" k="47" />
<hkern u1="&#xbf;" u2="n" k="41" />
<hkern u1="&#xbf;" u2="m" k="41" />
<hkern u1="&#xbf;" u2="l" k="41" />
<hkern u1="&#xbf;" u2="k" k="41" />
<hkern u1="&#xbf;" u2="j" k="41" />
<hkern u1="&#xbf;" u2="i" k="41" />
<hkern u1="&#xbf;" u2="h" k="41" />
<hkern u1="&#xbf;" u2="e" k="47" />
<hkern u1="&#xbf;" u2="d" k="45" />
<hkern u1="&#xbf;" u2="c" k="47" />
<hkern u1="&#xbf;" u2="b" k="45" />
<hkern u1="&#xbf;" u2="a" k="45" />
<hkern u1="&#xbf;" u2="Z" k="43" />
<hkern u1="&#xbf;" u2="Y" k="115" />
<hkern u1="&#xbf;" u2="X" k="41" />
<hkern u1="&#xbf;" u2="W" k="70" />
<hkern u1="&#xbf;" u2="V" k="78" />
<hkern u1="&#xbf;" u2="U" k="47" />
<hkern u1="&#xbf;" u2="T" k="123" />
<hkern u1="&#xbf;" u2="S" k="45" />
<hkern u1="&#xbf;" u2="R" k="41" />
<hkern u1="&#xbf;" u2="Q" k="45" />
<hkern u1="&#xbf;" u2="P" k="41" />
<hkern u1="&#xbf;" u2="O" k="45" />
<hkern u1="&#xbf;" u2="N" k="41" />
<hkern u1="&#xbf;" u2="M" k="41" />
<hkern u1="&#xbf;" u2="L" k="41" />
<hkern u1="&#xbf;" u2="K" k="41" />
<hkern u1="&#xbf;" u2="J" k="18" />
<hkern u1="&#xbf;" u2="I" k="41" />
<hkern u1="&#xbf;" u2="H" k="41" />
<hkern u1="&#xbf;" u2="G" k="45" />
<hkern u1="&#xbf;" u2="F" k="41" />
<hkern u1="&#xbf;" u2="E" k="41" />
<hkern u1="&#xbf;" u2="D" k="41" />
<hkern u1="&#xbf;" u2="C" k="45" />
<hkern u1="&#xbf;" u2="B" k="41" />
<hkern u1="&#xbf;" u2="A" k="49" />
<hkern u1="&#xc0;" u2="&#x2122;" k="78" />
<hkern u1="&#xc0;" u2="&#xae;" k="66" />
<hkern u1="&#xc0;" u2="&#x7d;" k="35" />
<hkern u1="&#xc0;" u2="&#x7c;" k="88" />
<hkern u1="&#xc0;" u2="v" k="37" />
<hkern u1="&#xc0;" u2="f" k="14" />
<hkern u1="&#xc0;" u2="]" k="16" />
<hkern u1="&#xc0;" u2="\" k="92" />
<hkern u1="&#xc0;" u2="V" k="66" />
<hkern u1="&#xc0;" u2="&#x40;" k="6" />
<hkern u1="&#xc0;" u2="&#x3f;" k="43" />
<hkern u1="&#xc0;" u2="&#x2a;" k="72" />
<hkern u1="&#xc0;" u2="&#x29;" k="25" />
<hkern u1="&#xc0;" u2="&#x26;" k="6" />
<hkern u1="&#xc1;" u2="&#x2122;" k="78" />
<hkern u1="&#xc1;" u2="&#xae;" k="66" />
<hkern u1="&#xc1;" u2="&#x7d;" k="35" />
<hkern u1="&#xc1;" u2="&#x7c;" k="88" />
<hkern u1="&#xc1;" u2="v" k="37" />
<hkern u1="&#xc1;" u2="f" k="14" />
<hkern u1="&#xc1;" u2="]" k="16" />
<hkern u1="&#xc1;" u2="\" k="92" />
<hkern u1="&#xc1;" u2="V" k="66" />
<hkern u1="&#xc1;" u2="&#x40;" k="6" />
<hkern u1="&#xc1;" u2="&#x3f;" k="43" />
<hkern u1="&#xc1;" u2="&#x2a;" k="72" />
<hkern u1="&#xc1;" u2="&#x29;" k="25" />
<hkern u1="&#xc1;" u2="&#x26;" k="6" />
<hkern u1="&#xc2;" u2="&#x2122;" k="78" />
<hkern u1="&#xc2;" u2="&#xae;" k="66" />
<hkern u1="&#xc2;" u2="&#x7d;" k="35" />
<hkern u1="&#xc2;" u2="&#x7c;" k="88" />
<hkern u1="&#xc2;" u2="v" k="37" />
<hkern u1="&#xc2;" u2="f" k="14" />
<hkern u1="&#xc2;" u2="]" k="16" />
<hkern u1="&#xc2;" u2="\" k="92" />
<hkern u1="&#xc2;" u2="V" k="66" />
<hkern u1="&#xc2;" u2="&#x40;" k="6" />
<hkern u1="&#xc2;" u2="&#x3f;" k="43" />
<hkern u1="&#xc2;" u2="&#x2a;" k="72" />
<hkern u1="&#xc2;" u2="&#x29;" k="25" />
<hkern u1="&#xc2;" u2="&#x26;" k="6" />
<hkern u1="&#xc3;" u2="&#x2122;" k="78" />
<hkern u1="&#xc3;" u2="&#xae;" k="66" />
<hkern u1="&#xc3;" u2="&#x7d;" k="35" />
<hkern u1="&#xc3;" u2="&#x7c;" k="88" />
<hkern u1="&#xc3;" u2="v" k="37" />
<hkern u1="&#xc3;" u2="f" k="14" />
<hkern u1="&#xc3;" u2="]" k="16" />
<hkern u1="&#xc3;" u2="\" k="92" />
<hkern u1="&#xc3;" u2="V" k="66" />
<hkern u1="&#xc3;" u2="&#x40;" k="6" />
<hkern u1="&#xc3;" u2="&#x3f;" k="43" />
<hkern u1="&#xc3;" u2="&#x2a;" k="72" />
<hkern u1="&#xc3;" u2="&#x29;" k="25" />
<hkern u1="&#xc3;" u2="&#x26;" k="6" />
<hkern u1="&#xc4;" u2="&#x2122;" k="78" />
<hkern u1="&#xc4;" u2="&#xae;" k="66" />
<hkern u1="&#xc4;" u2="&#x7d;" k="35" />
<hkern u1="&#xc4;" u2="&#x7c;" k="88" />
<hkern u1="&#xc4;" u2="v" k="37" />
<hkern u1="&#xc4;" u2="f" k="14" />
<hkern u1="&#xc4;" u2="]" k="16" />
<hkern u1="&#xc4;" u2="\" k="92" />
<hkern u1="&#xc4;" u2="V" k="66" />
<hkern u1="&#xc4;" u2="&#x40;" k="6" />
<hkern u1="&#xc4;" u2="&#x3f;" k="43" />
<hkern u1="&#xc4;" u2="&#x2a;" k="72" />
<hkern u1="&#xc4;" u2="&#x29;" k="25" />
<hkern u1="&#xc4;" u2="&#x26;" k="6" />
<hkern u1="&#xc5;" u2="&#x2122;" k="78" />
<hkern u1="&#xc5;" u2="&#xae;" k="66" />
<hkern u1="&#xc5;" u2="&#x7d;" k="35" />
<hkern u1="&#xc5;" u2="&#x7c;" k="88" />
<hkern u1="&#xc5;" u2="v" k="37" />
<hkern u1="&#xc5;" u2="f" k="14" />
<hkern u1="&#xc5;" u2="]" k="16" />
<hkern u1="&#xc5;" u2="\" k="92" />
<hkern u1="&#xc5;" u2="V" k="66" />
<hkern u1="&#xc5;" u2="&#x40;" k="6" />
<hkern u1="&#xc5;" u2="&#x3f;" k="43" />
<hkern u1="&#xc5;" u2="&#x2a;" k="72" />
<hkern u1="&#xc5;" u2="&#x29;" k="25" />
<hkern u1="&#xc5;" u2="&#x26;" k="6" />
<hkern u1="&#xc6;" u2="&#x135;" k="-61" />
<hkern u1="&#xc6;" u2="&#x12d;" k="-57" />
<hkern u1="&#xc6;" u2="&#x12b;" k="-55" />
<hkern u1="&#xc6;" u2="&#x129;" k="-96" />
<hkern u1="&#xc6;" u2="&#xef;" k="-61" />
<hkern u1="&#xc6;" u2="&#xee;" k="-78" />
<hkern u1="&#xc6;" u2="&#xec;" k="-133" />
<hkern u1="&#xc6;" u2="v" k="14" />
<hkern u1="&#xc6;" u2="&#x29;" k="14" />
<hkern u1="&#xc7;" u2="&#x135;" k="-61" />
<hkern u1="&#xc7;" u2="&#x12d;" k="-55" />
<hkern u1="&#xc7;" u2="&#x12b;" k="-74" />
<hkern u1="&#xc7;" u2="&#x129;" k="-102" />
<hkern u1="&#xc7;" u2="&#xef;" k="-76" />
<hkern u1="&#xc7;" u2="&#xee;" k="-78" />
<hkern u1="&#xc7;" u2="&#xec;" k="-104" />
<hkern u1="&#xc7;" u2="&#x2a;" k="-25" />
<hkern u1="&#xc7;" u2="&#x29;" k="18" />
<hkern u1="&#xc8;" u2="&#x135;" k="-61" />
<hkern u1="&#xc8;" u2="&#x12d;" k="-57" />
<hkern u1="&#xc8;" u2="&#x12b;" k="-55" />
<hkern u1="&#xc8;" u2="&#x129;" k="-96" />
<hkern u1="&#xc8;" u2="&#xef;" k="-61" />
<hkern u1="&#xc8;" u2="&#xee;" k="-78" />
<hkern u1="&#xc8;" u2="&#xec;" k="-133" />
<hkern u1="&#xc8;" u2="v" k="14" />
<hkern u1="&#xc8;" u2="&#x29;" k="14" />
<hkern u1="&#xc9;" u2="&#x135;" k="-61" />
<hkern u1="&#xc9;" u2="&#x12d;" k="-57" />
<hkern u1="&#xc9;" u2="&#x12b;" k="-55" />
<hkern u1="&#xc9;" u2="&#x129;" k="-96" />
<hkern u1="&#xc9;" u2="&#xef;" k="-61" />
<hkern u1="&#xc9;" u2="&#xee;" k="-78" />
<hkern u1="&#xc9;" u2="&#xec;" k="-133" />
<hkern u1="&#xc9;" u2="v" k="14" />
<hkern u1="&#xc9;" u2="&#x29;" k="14" />
<hkern u1="&#xca;" u2="&#x135;" k="-61" />
<hkern u1="&#xca;" u2="&#x12d;" k="-57" />
<hkern u1="&#xca;" u2="&#x12b;" k="-55" />
<hkern u1="&#xca;" u2="&#x129;" k="-96" />
<hkern u1="&#xca;" u2="&#xef;" k="-61" />
<hkern u1="&#xca;" u2="&#xee;" k="-78" />
<hkern u1="&#xca;" u2="&#xec;" k="-133" />
<hkern u1="&#xca;" u2="v" k="14" />
<hkern u1="&#xca;" u2="&#x29;" k="14" />
<hkern u1="&#xcb;" u2="&#x135;" k="-61" />
<hkern u1="&#xcb;" u2="&#x12d;" k="-57" />
<hkern u1="&#xcb;" u2="&#x12b;" k="-55" />
<hkern u1="&#xcb;" u2="&#x129;" k="-96" />
<hkern u1="&#xcb;" u2="&#xef;" k="-61" />
<hkern u1="&#xcb;" u2="&#xee;" k="-78" />
<hkern u1="&#xcb;" u2="&#xec;" k="-133" />
<hkern u1="&#xcb;" u2="v" k="14" />
<hkern u1="&#xcb;" u2="&#x29;" k="14" />
<hkern u1="&#xcc;" u2="&#x135;" k="-14" />
<hkern u1="&#xcc;" u2="&#x129;" k="-33" />
<hkern u1="&#xcc;" u2="&#xef;" k="-12" />
<hkern u1="&#xcc;" u2="&#xee;" k="-31" />
<hkern u1="&#xcc;" u2="&#xec;" k="-66" />
<hkern u1="&#xcc;" u2="&#x7d;" k="20" />
<hkern u1="&#xcc;" u2="&#x7c;" k="18" />
<hkern u1="&#xcc;" u2="f" k="8" />
<hkern u1="&#xcc;" u2="]" k="25" />
<hkern u1="&#xcc;" u2="&#x29;" k="31" />
<hkern u1="&#xcd;" u2="&#x135;" k="-14" />
<hkern u1="&#xcd;" u2="&#x129;" k="-33" />
<hkern u1="&#xcd;" u2="&#xef;" k="-12" />
<hkern u1="&#xcd;" u2="&#xee;" k="-31" />
<hkern u1="&#xcd;" u2="&#xec;" k="-66" />
<hkern u1="&#xcd;" u2="&#x7d;" k="20" />
<hkern u1="&#xcd;" u2="&#x7c;" k="18" />
<hkern u1="&#xcd;" u2="f" k="8" />
<hkern u1="&#xcd;" u2="]" k="25" />
<hkern u1="&#xcd;" u2="&#x29;" k="31" />
<hkern u1="&#xce;" u2="&#x135;" k="-14" />
<hkern u1="&#xce;" u2="&#x129;" k="-33" />
<hkern u1="&#xce;" u2="&#xef;" k="-12" />
<hkern u1="&#xce;" u2="&#xee;" k="-31" />
<hkern u1="&#xce;" u2="&#xec;" k="-66" />
<hkern u1="&#xce;" u2="&#x7d;" k="20" />
<hkern u1="&#xce;" u2="&#x7c;" k="18" />
<hkern u1="&#xce;" u2="f" k="8" />
<hkern u1="&#xce;" u2="]" k="25" />
<hkern u1="&#xce;" u2="&#x29;" k="31" />
<hkern u1="&#xcf;" u2="&#x135;" k="-14" />
<hkern u1="&#xcf;" u2="&#x129;" k="-33" />
<hkern u1="&#xcf;" u2="&#xef;" k="-12" />
<hkern u1="&#xcf;" u2="&#xee;" k="-31" />
<hkern u1="&#xcf;" u2="&#xec;" k="-66" />
<hkern u1="&#xcf;" u2="&#x7d;" k="20" />
<hkern u1="&#xcf;" u2="&#x7c;" k="18" />
<hkern u1="&#xcf;" u2="f" k="8" />
<hkern u1="&#xcf;" u2="]" k="25" />
<hkern u1="&#xcf;" u2="&#x29;" k="31" />
<hkern u1="&#xd1;" u2="&#x135;" k="-14" />
<hkern u1="&#xd1;" u2="&#x129;" k="-33" />
<hkern u1="&#xd1;" u2="&#xef;" k="-12" />
<hkern u1="&#xd1;" u2="&#xee;" k="-31" />
<hkern u1="&#xd1;" u2="&#xec;" k="-66" />
<hkern u1="&#xd1;" u2="&#x7d;" k="20" />
<hkern u1="&#xd1;" u2="&#x7c;" k="18" />
<hkern u1="&#xd1;" u2="f" k="8" />
<hkern u1="&#xd1;" u2="]" k="25" />
<hkern u1="&#xd1;" u2="&#x29;" k="31" />
<hkern u1="&#xd2;" u2="&#x2122;" k="27" />
<hkern u1="&#xd2;" u2="&#x7d;" k="63" />
<hkern u1="&#xd2;" u2="&#x7c;" k="43" />
<hkern u1="&#xd2;" u2="x" k="10" />
<hkern u1="&#xd2;" u2="]" k="72" />
<hkern u1="&#xd2;" u2="\" k="45" />
<hkern u1="&#xd2;" u2="X" k="35" />
<hkern u1="&#xd2;" u2="V" k="31" />
<hkern u1="&#xd2;" u2="&#x3f;" k="10" />
<hkern u1="&#xd2;" u2="&#x2f;" k="33" />
<hkern u1="&#xd2;" u2="&#x29;" k="55" />
<hkern u1="&#xd3;" u2="&#x2122;" k="27" />
<hkern u1="&#xd3;" u2="&#x7d;" k="63" />
<hkern u1="&#xd3;" u2="&#x7c;" k="43" />
<hkern u1="&#xd3;" u2="x" k="10" />
<hkern u1="&#xd3;" u2="]" k="72" />
<hkern u1="&#xd3;" u2="\" k="45" />
<hkern u1="&#xd3;" u2="X" k="35" />
<hkern u1="&#xd3;" u2="V" k="31" />
<hkern u1="&#xd3;" u2="&#x3f;" k="10" />
<hkern u1="&#xd3;" u2="&#x2f;" k="33" />
<hkern u1="&#xd3;" u2="&#x29;" k="55" />
<hkern u1="&#xd4;" u2="&#x2122;" k="27" />
<hkern u1="&#xd4;" u2="&#x7d;" k="63" />
<hkern u1="&#xd4;" u2="&#x7c;" k="43" />
<hkern u1="&#xd4;" u2="x" k="10" />
<hkern u1="&#xd4;" u2="]" k="72" />
<hkern u1="&#xd4;" u2="\" k="45" />
<hkern u1="&#xd4;" u2="X" k="35" />
<hkern u1="&#xd4;" u2="V" k="31" />
<hkern u1="&#xd4;" u2="&#x3f;" k="10" />
<hkern u1="&#xd4;" u2="&#x2f;" k="33" />
<hkern u1="&#xd4;" u2="&#x29;" k="55" />
<hkern u1="&#xd5;" u2="&#x2122;" k="27" />
<hkern u1="&#xd5;" u2="&#x7d;" k="63" />
<hkern u1="&#xd5;" u2="&#x7c;" k="43" />
<hkern u1="&#xd5;" u2="x" k="10" />
<hkern u1="&#xd5;" u2="]" k="72" />
<hkern u1="&#xd5;" u2="\" k="45" />
<hkern u1="&#xd5;" u2="X" k="35" />
<hkern u1="&#xd5;" u2="V" k="31" />
<hkern u1="&#xd5;" u2="&#x3f;" k="10" />
<hkern u1="&#xd5;" u2="&#x2f;" k="33" />
<hkern u1="&#xd5;" u2="&#x29;" k="55" />
<hkern u1="&#xd6;" u2="&#x2122;" k="27" />
<hkern u1="&#xd6;" u2="&#x7d;" k="63" />
<hkern u1="&#xd6;" u2="&#x7c;" k="43" />
<hkern u1="&#xd6;" u2="x" k="10" />
<hkern u1="&#xd6;" u2="]" k="72" />
<hkern u1="&#xd6;" u2="\" k="45" />
<hkern u1="&#xd6;" u2="X" k="35" />
<hkern u1="&#xd6;" u2="V" k="31" />
<hkern u1="&#xd6;" u2="&#x3f;" k="10" />
<hkern u1="&#xd6;" u2="&#x2f;" k="33" />
<hkern u1="&#xd6;" u2="&#x29;" k="55" />
<hkern u1="&#xd8;" u2="&#x2122;" k="27" />
<hkern u1="&#xd8;" u2="&#x7d;" k="63" />
<hkern u1="&#xd8;" u2="&#x7c;" k="43" />
<hkern u1="&#xd8;" u2="x" k="10" />
<hkern u1="&#xd8;" u2="]" k="72" />
<hkern u1="&#xd8;" u2="\" k="45" />
<hkern u1="&#xd8;" u2="X" k="35" />
<hkern u1="&#xd8;" u2="V" k="31" />
<hkern u1="&#xd8;" u2="&#x3f;" k="10" />
<hkern u1="&#xd8;" u2="&#x2f;" k="33" />
<hkern u1="&#xd8;" u2="&#x29;" k="55" />
<hkern u1="&#xd9;" u2="&#x135;" k="-23" />
<hkern u1="&#xd9;" u2="&#x12d;" k="-14" />
<hkern u1="&#xd9;" u2="&#x12b;" k="-10" />
<hkern u1="&#xd9;" u2="&#x129;" k="-41" />
<hkern u1="&#xd9;" u2="&#xef;" k="-20" />
<hkern u1="&#xd9;" u2="&#xee;" k="-39" />
<hkern u1="&#xd9;" u2="&#xec;" k="-78" />
<hkern u1="&#xd9;" u2="&#x7d;" k="18" />
<hkern u1="&#xd9;" u2="&#x7c;" k="14" />
<hkern u1="&#xd9;" u2="f" k="8" />
<hkern u1="&#xd9;" u2="]" k="23" />
<hkern u1="&#xd9;" u2="&#x2f;" k="29" />
<hkern u1="&#xd9;" u2="&#x29;" k="31" />
<hkern u1="&#xda;" u2="&#x135;" k="-23" />
<hkern u1="&#xda;" u2="&#x12d;" k="-14" />
<hkern u1="&#xda;" u2="&#x12b;" k="-10" />
<hkern u1="&#xda;" u2="&#x129;" k="-41" />
<hkern u1="&#xda;" u2="&#xef;" k="-20" />
<hkern u1="&#xda;" u2="&#xee;" k="-39" />
<hkern u1="&#xda;" u2="&#xec;" k="-78" />
<hkern u1="&#xda;" u2="&#x7d;" k="18" />
<hkern u1="&#xda;" u2="&#x7c;" k="14" />
<hkern u1="&#xda;" u2="f" k="8" />
<hkern u1="&#xda;" u2="]" k="23" />
<hkern u1="&#xda;" u2="&#x2f;" k="29" />
<hkern u1="&#xda;" u2="&#x29;" k="31" />
<hkern u1="&#xdb;" u2="&#x135;" k="-23" />
<hkern u1="&#xdb;" u2="&#x12d;" k="-14" />
<hkern u1="&#xdb;" u2="&#x12b;" k="-10" />
<hkern u1="&#xdb;" u2="&#x129;" k="-41" />
<hkern u1="&#xdb;" u2="&#xef;" k="-20" />
<hkern u1="&#xdb;" u2="&#xee;" k="-39" />
<hkern u1="&#xdb;" u2="&#xec;" k="-78" />
<hkern u1="&#xdb;" u2="&#x7d;" k="18" />
<hkern u1="&#xdb;" u2="&#x7c;" k="14" />
<hkern u1="&#xdb;" u2="f" k="8" />
<hkern u1="&#xdb;" u2="]" k="23" />
<hkern u1="&#xdb;" u2="&#x2f;" k="29" />
<hkern u1="&#xdb;" u2="&#x29;" k="31" />
<hkern u1="&#xdc;" u2="&#x135;" k="-23" />
<hkern u1="&#xdc;" u2="&#x12d;" k="-14" />
<hkern u1="&#xdc;" u2="&#x12b;" k="-10" />
<hkern u1="&#xdc;" u2="&#x129;" k="-41" />
<hkern u1="&#xdc;" u2="&#xef;" k="-20" />
<hkern u1="&#xdc;" u2="&#xee;" k="-39" />
<hkern u1="&#xdc;" u2="&#xec;" k="-78" />
<hkern u1="&#xdc;" u2="&#x7d;" k="18" />
<hkern u1="&#xdc;" u2="&#x7c;" k="14" />
<hkern u1="&#xdc;" u2="f" k="8" />
<hkern u1="&#xdc;" u2="]" k="23" />
<hkern u1="&#xdc;" u2="&#x2f;" k="29" />
<hkern u1="&#xdc;" u2="&#x29;" k="31" />
<hkern u1="&#xdd;" u2="&#x17a;" k="84" />
<hkern u1="&#xdd;" u2="&#x177;" k="51" />
<hkern u1="&#xdd;" u2="&#x171;" k="94" />
<hkern u1="&#xdd;" u2="&#x16b;" k="94" />
<hkern u1="&#xdd;" u2="&#x169;" k="88" />
<hkern u1="&#xdd;" u2="&#x159;" k="53" />
<hkern u1="&#xdd;" u2="&#x155;" k="14" />
<hkern u1="&#xdd;" u2="&#x151;" k="96" />
<hkern u1="&#xdd;" u2="&#x14f;" k="100" />
<hkern u1="&#xdd;" u2="&#x14d;" k="121" />
<hkern u1="&#xdd;" u2="&#x135;" k="-43" />
<hkern u1="&#xdd;" u2="&#x131;" k="113" />
<hkern u1="&#xdd;" u2="&#x12d;" k="-125" />
<hkern u1="&#xdd;" u2="&#x12b;" k="-117" />
<hkern u1="&#xdd;" u2="&#x129;" k="-156" />
<hkern u1="&#xdd;" u2="&#x127;" k="-14" />
<hkern u1="&#xdd;" u2="&#x11f;" k="104" />
<hkern u1="&#xdd;" u2="&#x113;" k="111" />
<hkern u1="&#xdd;" u2="&#xff;" k="43" />
<hkern u1="&#xdd;" u2="&#xfc;" k="90" />
<hkern u1="&#xdd;" u2="&#xf5;" k="100" />
<hkern u1="&#xdd;" u2="&#xf2;" k="117" />
<hkern u1="&#xdd;" u2="&#xf1;" k="111" />
<hkern u1="&#xdd;" u2="&#xef;" k="-129" />
<hkern u1="&#xdd;" u2="&#xee;" k="-57" />
<hkern u1="&#xdd;" u2="&#xed;" k="-10" />
<hkern u1="&#xdd;" u2="&#xec;" k="-172" />
<hkern u1="&#xdd;" u2="&#xeb;" k="109" />
<hkern u1="&#xdd;" u2="&#xea;" k="125" />
<hkern u1="&#xdd;" u2="&#xe8;" k="72" />
<hkern u1="&#xdd;" u2="&#xae;" k="39" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-8" />
<hkern u1="&#xdd;" u2="&#x7c;" k="-18" />
<hkern u1="&#xdd;" u2="x" k="70" />
<hkern u1="&#xdd;" u2="v" k="72" />
<hkern u1="&#xdd;" u2="f" k="31" />
<hkern u1="&#xdd;" u2="]" k="-8" />
<hkern u1="&#xdd;" u2="&#x40;" k="59" />
<hkern u1="&#xdd;" u2="&#x2f;" k="135" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-33" />
<hkern u1="&#xdd;" u2="&#x26;" k="70" />
<hkern u1="&#xdf;" u2="&#x2122;" k="18" />
<hkern u1="&#xdf;" u2="&#x201d;" k="29" />
<hkern u1="&#xdf;" u2="&#x201c;" k="31" />
<hkern u1="&#xdf;" u2="&#x2019;" k="29" />
<hkern u1="&#xdf;" u2="&#x2018;" k="31" />
<hkern u1="&#xdf;" u2="&#x21b;" k="6" />
<hkern u1="&#xdf;" u2="&#x21a;" k="41" />
<hkern u1="&#xdf;" u2="&#x1fe;" k="12" />
<hkern u1="&#xdf;" u2="&#x178;" k="84" />
<hkern u1="&#xdf;" u2="&#x177;" k="27" />
<hkern u1="&#xdf;" u2="&#x176;" k="84" />
<hkern u1="&#xdf;" u2="&#x175;" k="18" />
<hkern u1="&#xdf;" u2="&#x174;" k="43" />
<hkern u1="&#xdf;" u2="&#x172;" k="14" />
<hkern u1="&#xdf;" u2="&#x170;" k="14" />
<hkern u1="&#xdf;" u2="&#x16e;" k="14" />
<hkern u1="&#xdf;" u2="&#x16c;" k="14" />
<hkern u1="&#xdf;" u2="&#x16a;" k="14" />
<hkern u1="&#xdf;" u2="&#x168;" k="14" />
<hkern u1="&#xdf;" u2="&#x167;" k="6" />
<hkern u1="&#xdf;" u2="&#x166;" k="41" />
<hkern u1="&#xdf;" u2="&#x165;" k="6" />
<hkern u1="&#xdf;" u2="&#x164;" k="41" />
<hkern u1="&#xdf;" u2="&#x152;" k="12" />
<hkern u1="&#xdf;" u2="&#x150;" k="12" />
<hkern u1="&#xdf;" u2="&#x14e;" k="12" />
<hkern u1="&#xdf;" u2="&#x14c;" k="12" />
<hkern u1="&#xdf;" u2="&#x134;" k="10" />
<hkern u1="&#xdf;" u2="&#x123;" k="16" />
<hkern u1="&#xdf;" u2="&#x122;" k="12" />
<hkern u1="&#xdf;" u2="&#x121;" k="16" />
<hkern u1="&#xdf;" u2="&#x120;" k="12" />
<hkern u1="&#xdf;" u2="&#x11f;" k="16" />
<hkern u1="&#xdf;" u2="&#x11e;" k="12" />
<hkern u1="&#xdf;" u2="&#x11d;" k="16" />
<hkern u1="&#xdf;" u2="&#x11c;" k="12" />
<hkern u1="&#xdf;" u2="&#x10c;" k="12" />
<hkern u1="&#xdf;" u2="&#x10a;" k="12" />
<hkern u1="&#xdf;" u2="&#x108;" k="12" />
<hkern u1="&#xdf;" u2="&#x106;" k="12" />
<hkern u1="&#xdf;" u2="&#xff;" k="27" />
<hkern u1="&#xdf;" u2="&#xfd;" k="27" />
<hkern u1="&#xdf;" u2="&#xee;" k="-14" />
<hkern u1="&#xdf;" u2="&#xdd;" k="84" />
<hkern u1="&#xdf;" u2="&#xdc;" k="14" />
<hkern u1="&#xdf;" u2="&#xdb;" k="14" />
<hkern u1="&#xdf;" u2="&#xda;" k="14" />
<hkern u1="&#xdf;" u2="&#xd9;" k="14" />
<hkern u1="&#xdf;" u2="&#xd8;" k="12" />
<hkern u1="&#xdf;" u2="&#xd6;" k="12" />
<hkern u1="&#xdf;" u2="&#xd5;" k="12" />
<hkern u1="&#xdf;" u2="&#xd4;" k="12" />
<hkern u1="&#xdf;" u2="&#xd3;" k="12" />
<hkern u1="&#xdf;" u2="&#xd2;" k="12" />
<hkern u1="&#xdf;" u2="&#xc7;" k="12" />
<hkern u1="&#xdf;" u2="&#xae;" k="29" />
<hkern u1="&#xdf;" u2="&#x7d;" k="49" />
<hkern u1="&#xdf;" u2="&#x7c;" k="51" />
<hkern u1="&#xdf;" u2="y" k="27" />
<hkern u1="&#xdf;" u2="w" k="18" />
<hkern u1="&#xdf;" u2="v" k="27" />
<hkern u1="&#xdf;" u2="t" k="6" />
<hkern u1="&#xdf;" u2="g" k="16" />
<hkern u1="&#xdf;" u2="f" k="8" />
<hkern u1="&#xdf;" u2="]" k="49" />
<hkern u1="&#xdf;" u2="\" k="45" />
<hkern u1="&#xdf;" u2="Y" k="84" />
<hkern u1="&#xdf;" u2="W" k="43" />
<hkern u1="&#xdf;" u2="V" k="53" />
<hkern u1="&#xdf;" u2="U" k="14" />
<hkern u1="&#xdf;" u2="T" k="41" />
<hkern u1="&#xdf;" u2="Q" k="12" />
<hkern u1="&#xdf;" u2="O" k="12" />
<hkern u1="&#xdf;" u2="J" k="10" />
<hkern u1="&#xdf;" u2="G" k="12" />
<hkern u1="&#xdf;" u2="C" k="12" />
<hkern u1="&#xdf;" u2="&#x3f;" k="8" />
<hkern u1="&#xdf;" u2="&#x2a;" k="20" />
<hkern u1="&#xdf;" u2="&#x29;" k="33" />
<hkern u1="&#xdf;" u2="&#x27;" k="25" />
<hkern u1="&#xdf;" u2="&#x22;" k="25" />
<hkern u1="&#xe0;" u2="&#x2122;" k="45" />
<hkern u1="&#xe0;" u2="&#x7d;" k="49" />
<hkern u1="&#xe0;" u2="&#x7c;" k="47" />
<hkern u1="&#xe0;" u2="]" k="43" />
<hkern u1="&#xe0;" u2="\" k="61" />
<hkern u1="&#xe0;" u2="V" k="55" />
<hkern u1="&#xe0;" u2="&#x3f;" k="33" />
<hkern u1="&#xe0;" u2="&#x2a;" k="16" />
<hkern u1="&#xe0;" u2="&#x29;" k="33" />
<hkern u1="&#xe1;" u2="&#x2122;" k="45" />
<hkern u1="&#xe1;" u2="&#x7d;" k="49" />
<hkern u1="&#xe1;" u2="&#x7c;" k="47" />
<hkern u1="&#xe1;" u2="]" k="43" />
<hkern u1="&#xe1;" u2="\" k="61" />
<hkern u1="&#xe1;" u2="V" k="55" />
<hkern u1="&#xe1;" u2="&#x3f;" k="33" />
<hkern u1="&#xe1;" u2="&#x2a;" k="16" />
<hkern u1="&#xe1;" u2="&#x29;" k="33" />
<hkern u1="&#xe2;" u2="&#x2122;" k="45" />
<hkern u1="&#xe2;" u2="&#x7d;" k="49" />
<hkern u1="&#xe2;" u2="&#x7c;" k="47" />
<hkern u1="&#xe2;" u2="]" k="43" />
<hkern u1="&#xe2;" u2="\" k="61" />
<hkern u1="&#xe2;" u2="V" k="55" />
<hkern u1="&#xe2;" u2="&#x3f;" k="33" />
<hkern u1="&#xe2;" u2="&#x2a;" k="16" />
<hkern u1="&#xe2;" u2="&#x29;" k="33" />
<hkern u1="&#xe3;" u2="&#x2122;" k="45" />
<hkern u1="&#xe3;" u2="&#x7d;" k="49" />
<hkern u1="&#xe3;" u2="&#x7c;" k="47" />
<hkern u1="&#xe3;" u2="]" k="43" />
<hkern u1="&#xe3;" u2="\" k="61" />
<hkern u1="&#xe3;" u2="V" k="55" />
<hkern u1="&#xe3;" u2="&#x3f;" k="33" />
<hkern u1="&#xe3;" u2="&#x2a;" k="16" />
<hkern u1="&#xe3;" u2="&#x29;" k="33" />
<hkern u1="&#xe4;" u2="&#x2122;" k="45" />
<hkern u1="&#xe4;" u2="&#x7d;" k="49" />
<hkern u1="&#xe4;" u2="&#x7c;" k="47" />
<hkern u1="&#xe4;" u2="]" k="43" />
<hkern u1="&#xe4;" u2="\" k="61" />
<hkern u1="&#xe4;" u2="V" k="55" />
<hkern u1="&#xe4;" u2="&#x3f;" k="33" />
<hkern u1="&#xe4;" u2="&#x2a;" k="16" />
<hkern u1="&#xe4;" u2="&#x29;" k="33" />
<hkern u1="&#xe5;" u2="&#x2122;" k="45" />
<hkern u1="&#xe5;" u2="&#x7d;" k="49" />
<hkern u1="&#xe5;" u2="&#x7c;" k="47" />
<hkern u1="&#xe5;" u2="]" k="43" />
<hkern u1="&#xe5;" u2="\" k="61" />
<hkern u1="&#xe5;" u2="V" k="55" />
<hkern u1="&#xe5;" u2="&#x3f;" k="33" />
<hkern u1="&#xe5;" u2="&#x2a;" k="16" />
<hkern u1="&#xe5;" u2="&#x29;" k="33" />
<hkern u1="&#xe6;" u2="&#x2122;" k="51" />
<hkern u1="&#xe6;" u2="&#x142;" k="-12" />
<hkern u1="&#xe6;" u2="&#x7d;" k="53" />
<hkern u1="&#xe6;" u2="&#x7c;" k="51" />
<hkern u1="&#xe6;" u2="v" k="12" />
<hkern u1="&#xe6;" u2="]" k="47" />
<hkern u1="&#xe6;" u2="\" k="72" />
<hkern u1="&#xe6;" u2="V" k="53" />
<hkern u1="&#xe6;" u2="&#x3f;" k="35" />
<hkern u1="&#xe6;" u2="&#x2a;" k="27" />
<hkern u1="&#xe6;" u2="&#x29;" k="35" />
<hkern u1="&#xe7;" u2="&#x2122;" k="31" />
<hkern u1="&#xe7;" u2="&#x201e;" k="-37" />
<hkern u1="&#xe7;" u2="&#x7d;" k="37" />
<hkern u1="&#xe7;" u2="&#x7c;" k="33" />
<hkern u1="&#xe7;" u2="j" k="-49" />
<hkern u1="&#xe7;" u2="]" k="31" />
<hkern u1="&#xe7;" u2="\" k="43" />
<hkern u1="&#xe7;" u2="V" k="29" />
<hkern u1="&#xe7;" u2="&#x3f;" k="10" />
<hkern u1="&#xe7;" u2="&#x29;" k="25" />
<hkern u1="&#xe8;" u2="&#x2122;" k="51" />
<hkern u1="&#xe8;" u2="&#x142;" k="-20" />
<hkern u1="&#xe8;" u2="&#x7d;" k="53" />
<hkern u1="&#xe8;" u2="&#x7c;" k="51" />
<hkern u1="&#xe8;" u2="v" k="12" />
<hkern u1="&#xe8;" u2="]" k="47" />
<hkern u1="&#xe8;" u2="\" k="72" />
<hkern u1="&#xe8;" u2="V" k="53" />
<hkern u1="&#xe8;" u2="&#x3f;" k="35" />
<hkern u1="&#xe8;" u2="&#x2a;" k="27" />
<hkern u1="&#xe8;" u2="&#x29;" k="35" />
<hkern u1="&#xe9;" u2="&#x2122;" k="51" />
<hkern u1="&#xe9;" u2="&#x142;" k="-20" />
<hkern u1="&#xe9;" u2="&#x7d;" k="53" />
<hkern u1="&#xe9;" u2="&#x7c;" k="51" />
<hkern u1="&#xe9;" u2="v" k="12" />
<hkern u1="&#xe9;" u2="]" k="47" />
<hkern u1="&#xe9;" u2="\" k="72" />
<hkern u1="&#xe9;" u2="V" k="53" />
<hkern u1="&#xe9;" u2="&#x3f;" k="35" />
<hkern u1="&#xe9;" u2="&#x2a;" k="27" />
<hkern u1="&#xe9;" u2="&#x29;" k="35" />
<hkern u1="&#xea;" u2="&#x2122;" k="51" />
<hkern u1="&#xea;" u2="&#x142;" k="-20" />
<hkern u1="&#xea;" u2="&#x7d;" k="53" />
<hkern u1="&#xea;" u2="&#x7c;" k="51" />
<hkern u1="&#xea;" u2="v" k="12" />
<hkern u1="&#xea;" u2="]" k="47" />
<hkern u1="&#xea;" u2="\" k="72" />
<hkern u1="&#xea;" u2="V" k="53" />
<hkern u1="&#xea;" u2="&#x3f;" k="35" />
<hkern u1="&#xea;" u2="&#x2a;" k="27" />
<hkern u1="&#xea;" u2="&#x29;" k="35" />
<hkern u1="&#xeb;" u2="&#x2122;" k="51" />
<hkern u1="&#xeb;" u2="&#x142;" k="-20" />
<hkern u1="&#xeb;" u2="&#x7d;" k="53" />
<hkern u1="&#xeb;" u2="&#x7c;" k="51" />
<hkern u1="&#xeb;" u2="v" k="12" />
<hkern u1="&#xeb;" u2="]" k="47" />
<hkern u1="&#xeb;" u2="\" k="72" />
<hkern u1="&#xeb;" u2="V" k="53" />
<hkern u1="&#xeb;" u2="&#x3f;" k="35" />
<hkern u1="&#xeb;" u2="&#x2a;" k="27" />
<hkern u1="&#xeb;" u2="&#x29;" k="35" />
<hkern u1="&#xec;" u2="&#x135;" k="-31" />
<hkern u1="&#xec;" u2="&#x12d;" k="-27" />
<hkern u1="&#xec;" u2="&#x12b;" k="-18" />
<hkern u1="&#xec;" u2="&#x129;" k="-53" />
<hkern u1="&#xec;" u2="&#xef;" k="-27" />
<hkern u1="&#xec;" u2="&#xee;" k="-47" />
<hkern u1="&#xec;" u2="&#xec;" k="-90" />
<hkern u1="&#xec;" u2="&#x29;" k="25" />
<hkern u1="&#xed;" u2="&#x2122;" k="-37" />
<hkern u1="&#xed;" u2="&#x201d;" k="-33" />
<hkern u1="&#xed;" u2="&#x2019;" k="-33" />
<hkern u1="&#xed;" u2="&#x159;" k="-90" />
<hkern u1="&#xed;" u2="&#x142;" k="-29" />
<hkern u1="&#xed;" u2="&#x13e;" k="-29" />
<hkern u1="&#xed;" u2="&#x13c;" k="-29" />
<hkern u1="&#xed;" u2="&#x13a;" k="-29" />
<hkern u1="&#xed;" u2="&#x137;" k="-33" />
<hkern u1="&#xed;" u2="&#x135;" k="-31" />
<hkern u1="&#xed;" u2="&#x131;" k="-31" />
<hkern u1="&#xed;" u2="&#x12f;" k="-31" />
<hkern u1="&#xed;" u2="&#x12d;" k="-27" />
<hkern u1="&#xed;" u2="&#x12b;" k="-18" />
<hkern u1="&#xed;" u2="&#x129;" k="-31" />
<hkern u1="&#xed;" u2="&#x127;" k="-33" />
<hkern u1="&#xed;" u2="&#x125;" k="-33" />
<hkern u1="&#xed;" u2="&#xef;" k="-27" />
<hkern u1="&#xed;" u2="&#xee;" k="-31" />
<hkern u1="&#xed;" u2="&#xed;" k="-31" />
<hkern u1="&#xed;" u2="&#xec;" k="-31" />
<hkern u1="&#xed;" u2="&#xdf;" k="-33" />
<hkern u1="&#xed;" u2="&#x7d;" k="-102" />
<hkern u1="&#xed;" u2="&#x7c;" k="-115" />
<hkern u1="&#xed;" u2="l" k="-29" />
<hkern u1="&#xed;" u2="k" k="-33" />
<hkern u1="&#xed;" u2="j" k="-31" />
<hkern u1="&#xed;" u2="i" k="-31" />
<hkern u1="&#xed;" u2="h" k="-33" />
<hkern u1="&#xed;" u2="b" k="-37" />
<hkern u1="&#xed;" u2="]" k="-102" />
<hkern u1="&#xed;" u2="\" k="-78" />
<hkern u1="&#xed;" u2="&#x3f;" k="-100" />
<hkern u1="&#xed;" u2="&#x2a;" k="-90" />
<hkern u1="&#xed;" u2="&#x29;" k="25" />
<hkern u1="&#xed;" u2="&#x27;" k="-86" />
<hkern u1="&#xed;" u2="&#x22;" k="-86" />
<hkern u1="&#xed;" u2="&#x21;" k="-76" />
<hkern u1="&#xee;" u2="&#x2122;" k="-20" />
<hkern u1="&#xee;" u2="&#x201d;" k="-49" />
<hkern u1="&#xee;" u2="&#x201c;" k="-18" />
<hkern u1="&#xee;" u2="&#x2019;" k="-49" />
<hkern u1="&#xee;" u2="&#x2018;" k="-18" />
<hkern u1="&#xee;" u2="&#x142;" k="-23" />
<hkern u1="&#xee;" u2="&#x13e;" k="-23" />
<hkern u1="&#xee;" u2="&#x13c;" k="-23" />
<hkern u1="&#xee;" u2="&#x13a;" k="-23" />
<hkern u1="&#xee;" u2="&#x137;" k="-23" />
<hkern u1="&#xee;" u2="&#x135;" k="-23" />
<hkern u1="&#xee;" u2="&#x131;" k="-23" />
<hkern u1="&#xee;" u2="&#x12f;" k="-23" />
<hkern u1="&#xee;" u2="&#x12d;" k="-23" />
<hkern u1="&#xee;" u2="&#x12b;" k="-18" />
<hkern u1="&#xee;" u2="&#x129;" k="-23" />
<hkern u1="&#xee;" u2="&#x127;" k="-23" />
<hkern u1="&#xee;" u2="&#x125;" k="-23" />
<hkern u1="&#xee;" u2="&#xef;" k="-23" />
<hkern u1="&#xee;" u2="&#xee;" k="-23" />
<hkern u1="&#xee;" u2="&#xed;" k="-23" />
<hkern u1="&#xee;" u2="&#xec;" k="-23" />
<hkern u1="&#xee;" u2="&#xdf;" k="-12" />
<hkern u1="&#xee;" u2="&#xae;" k="-37" />
<hkern u1="&#xee;" u2="&#x7c;" k="-49" />
<hkern u1="&#xee;" u2="l" k="-23" />
<hkern u1="&#xee;" u2="k" k="-23" />
<hkern u1="&#xee;" u2="j" k="-23" />
<hkern u1="&#xee;" u2="i" k="-23" />
<hkern u1="&#xee;" u2="h" k="-23" />
<hkern u1="&#xee;" u2="b" k="-27" />
<hkern u1="&#xee;" u2="]" k="-10" />
<hkern u1="&#xee;" u2="&#x3f;" k="-57" />
<hkern u1="&#xee;" u2="&#x2a;" k="-78" />
<hkern u1="&#xee;" u2="&#x29;" k="25" />
<hkern u1="&#xee;" u2="&#x27;" k="-74" />
<hkern u1="&#xee;" u2="&#x22;" k="-74" />
<hkern u1="&#xee;" u2="&#x21;" k="-68" />
<hkern u1="&#xef;" u2="&#x2122;" k="-20" />
<hkern u1="&#xef;" u2="&#x201d;" k="-16" />
<hkern u1="&#xef;" u2="&#x2019;" k="-16" />
<hkern u1="&#xef;" u2="&#x135;" k="-31" />
<hkern u1="&#xef;" u2="&#x12d;" k="-27" />
<hkern u1="&#xef;" u2="&#x12b;" k="-18" />
<hkern u1="&#xef;" u2="&#x129;" k="-53" />
<hkern u1="&#xef;" u2="&#xef;" k="-27" />
<hkern u1="&#xef;" u2="&#xee;" k="-47" />
<hkern u1="&#xef;" u2="&#xec;" k="-90" />
<hkern u1="&#xef;" u2="&#xe8;" k="-14" />
<hkern u1="&#xef;" u2="&#x7d;" k="-53" />
<hkern u1="&#xef;" u2="&#x7c;" k="-68" />
<hkern u1="&#xef;" u2="]" k="-55" />
<hkern u1="&#xef;" u2="\" k="-45" />
<hkern u1="&#xef;" u2="&#x3f;" k="-47" />
<hkern u1="&#xef;" u2="&#x2a;" k="-84" />
<hkern u1="&#xef;" u2="&#x29;" k="25" />
<hkern u1="&#xef;" u2="&#x27;" k="-49" />
<hkern u1="&#xef;" u2="&#x22;" k="-49" />
<hkern u1="&#xef;" u2="&#x21;" k="-49" />
<hkern u1="&#xf1;" u2="&#x2122;" k="61" />
<hkern u1="&#xf1;" u2="&#xae;" k="31" />
<hkern u1="&#xf1;" u2="&#x7d;" k="49" />
<hkern u1="&#xf1;" u2="&#x7c;" k="63" />
<hkern u1="&#xf1;" u2="v" k="12" />
<hkern u1="&#xf1;" u2="f" k="6" />
<hkern u1="&#xf1;" u2="]" k="35" />
<hkern u1="&#xf1;" u2="\" k="86" />
<hkern u1="&#xf1;" u2="V" k="63" />
<hkern u1="&#xf1;" u2="&#x3f;" k="41" />
<hkern u1="&#xf1;" u2="&#x2a;" k="35" />
<hkern u1="&#xf1;" u2="&#x29;" k="33" />
<hkern u1="&#xf2;" u2="&#x2122;" k="66" />
<hkern u1="&#xf2;" u2="&#xae;" k="37" />
<hkern u1="&#xf2;" u2="&#x7d;" k="63" />
<hkern u1="&#xf2;" u2="&#x7c;" k="63" />
<hkern u1="&#xf2;" u2="x" k="27" />
<hkern u1="&#xf2;" u2="v" k="20" />
<hkern u1="&#xf2;" u2="f" k="10" />
<hkern u1="&#xf2;" u2="]" k="70" />
<hkern u1="&#xf2;" u2="\" k="92" />
<hkern u1="&#xf2;" u2="X" k="20" />
<hkern u1="&#xf2;" u2="V" k="74" />
<hkern u1="&#xf2;" u2="&#x3f;" k="43" />
<hkern u1="&#xf2;" u2="&#x2a;" k="43" />
<hkern u1="&#xf2;" u2="&#x29;" k="51" />
<hkern u1="&#xf3;" u2="&#x2122;" k="66" />
<hkern u1="&#xf3;" u2="&#xae;" k="37" />
<hkern u1="&#xf3;" u2="&#x7d;" k="63" />
<hkern u1="&#xf3;" u2="&#x7c;" k="63" />
<hkern u1="&#xf3;" u2="x" k="27" />
<hkern u1="&#xf3;" u2="v" k="20" />
<hkern u1="&#xf3;" u2="f" k="10" />
<hkern u1="&#xf3;" u2="]" k="70" />
<hkern u1="&#xf3;" u2="\" k="92" />
<hkern u1="&#xf3;" u2="X" k="20" />
<hkern u1="&#xf3;" u2="V" k="74" />
<hkern u1="&#xf3;" u2="&#x3f;" k="43" />
<hkern u1="&#xf3;" u2="&#x2a;" k="43" />
<hkern u1="&#xf3;" u2="&#x29;" k="51" />
<hkern u1="&#xf4;" u2="&#x2122;" k="66" />
<hkern u1="&#xf4;" u2="&#xae;" k="37" />
<hkern u1="&#xf4;" u2="&#x7d;" k="63" />
<hkern u1="&#xf4;" u2="&#x7c;" k="63" />
<hkern u1="&#xf4;" u2="x" k="27" />
<hkern u1="&#xf4;" u2="v" k="20" />
<hkern u1="&#xf4;" u2="f" k="10" />
<hkern u1="&#xf4;" u2="]" k="70" />
<hkern u1="&#xf4;" u2="\" k="92" />
<hkern u1="&#xf4;" u2="X" k="20" />
<hkern u1="&#xf4;" u2="V" k="74" />
<hkern u1="&#xf4;" u2="&#x3f;" k="43" />
<hkern u1="&#xf4;" u2="&#x2a;" k="43" />
<hkern u1="&#xf4;" u2="&#x29;" k="51" />
<hkern u1="&#xf5;" u2="&#x2122;" k="66" />
<hkern u1="&#xf5;" u2="&#xae;" k="37" />
<hkern u1="&#xf5;" u2="&#x7d;" k="63" />
<hkern u1="&#xf5;" u2="&#x7c;" k="63" />
<hkern u1="&#xf5;" u2="x" k="27" />
<hkern u1="&#xf5;" u2="v" k="20" />
<hkern u1="&#xf5;" u2="f" k="10" />
<hkern u1="&#xf5;" u2="]" k="70" />
<hkern u1="&#xf5;" u2="\" k="92" />
<hkern u1="&#xf5;" u2="X" k="20" />
<hkern u1="&#xf5;" u2="V" k="74" />
<hkern u1="&#xf5;" u2="&#x3f;" k="43" />
<hkern u1="&#xf5;" u2="&#x2a;" k="43" />
<hkern u1="&#xf5;" u2="&#x29;" k="51" />
<hkern u1="&#xf6;" u2="&#x2122;" k="66" />
<hkern u1="&#xf6;" u2="&#xae;" k="37" />
<hkern u1="&#xf6;" u2="&#x7d;" k="63" />
<hkern u1="&#xf6;" u2="&#x7c;" k="63" />
<hkern u1="&#xf6;" u2="x" k="27" />
<hkern u1="&#xf6;" u2="v" k="20" />
<hkern u1="&#xf6;" u2="f" k="10" />
<hkern u1="&#xf6;" u2="]" k="70" />
<hkern u1="&#xf6;" u2="\" k="92" />
<hkern u1="&#xf6;" u2="X" k="20" />
<hkern u1="&#xf6;" u2="V" k="74" />
<hkern u1="&#xf6;" u2="&#x3f;" k="43" />
<hkern u1="&#xf6;" u2="&#x2a;" k="43" />
<hkern u1="&#xf6;" u2="&#x29;" k="51" />
<hkern u1="&#xf8;" u2="&#x2122;" k="66" />
<hkern u1="&#xf8;" u2="&#xae;" k="37" />
<hkern u1="&#xf8;" u2="&#x7d;" k="63" />
<hkern u1="&#xf8;" u2="&#x7c;" k="63" />
<hkern u1="&#xf8;" u2="x" k="27" />
<hkern u1="&#xf8;" u2="v" k="20" />
<hkern u1="&#xf8;" u2="f" k="10" />
<hkern u1="&#xf8;" u2="]" k="70" />
<hkern u1="&#xf8;" u2="\" k="92" />
<hkern u1="&#xf8;" u2="X" k="20" />
<hkern u1="&#xf8;" u2="V" k="74" />
<hkern u1="&#xf8;" u2="&#x3f;" k="43" />
<hkern u1="&#xf8;" u2="&#x2a;" k="43" />
<hkern u1="&#xf8;" u2="&#x29;" k="51" />
<hkern u1="&#xf9;" u2="&#x2122;" k="39" />
<hkern u1="&#xf9;" u2="&#x7d;" k="51" />
<hkern u1="&#xf9;" u2="&#x7c;" k="45" />
<hkern u1="&#xf9;" u2="]" k="39" />
<hkern u1="&#xf9;" u2="\" k="55" />
<hkern u1="&#xf9;" u2="V" k="51" />
<hkern u1="&#xf9;" u2="&#x3f;" k="31" />
<hkern u1="&#xf9;" u2="&#x29;" k="35" />
<hkern u1="&#xfa;" u2="&#x2122;" k="39" />
<hkern u1="&#xfa;" u2="&#x7d;" k="51" />
<hkern u1="&#xfa;" u2="&#x7c;" k="45" />
<hkern u1="&#xfa;" u2="]" k="39" />
<hkern u1="&#xfa;" u2="\" k="55" />
<hkern u1="&#xfa;" u2="V" k="51" />
<hkern u1="&#xfa;" u2="&#x3f;" k="31" />
<hkern u1="&#xfa;" u2="&#x29;" k="35" />
<hkern u1="&#xfb;" u2="&#x2122;" k="39" />
<hkern u1="&#xfb;" u2="&#x7d;" k="51" />
<hkern u1="&#xfb;" u2="&#x7c;" k="45" />
<hkern u1="&#xfb;" u2="]" k="39" />
<hkern u1="&#xfb;" u2="\" k="55" />
<hkern u1="&#xfb;" u2="V" k="51" />
<hkern u1="&#xfb;" u2="&#x3f;" k="31" />
<hkern u1="&#xfb;" u2="&#x29;" k="35" />
<hkern u1="&#xfc;" u2="&#x2122;" k="39" />
<hkern u1="&#xfc;" u2="&#x7d;" k="51" />
<hkern u1="&#xfc;" u2="&#x7c;" k="45" />
<hkern u1="&#xfc;" u2="]" k="39" />
<hkern u1="&#xfc;" u2="\" k="55" />
<hkern u1="&#xfc;" u2="V" k="51" />
<hkern u1="&#xfc;" u2="&#x3f;" k="31" />
<hkern u1="&#xfc;" u2="&#x29;" k="35" />
<hkern u1="&#xfd;" u2="&#x2122;" k="25" />
<hkern u1="&#xfd;" u2="&#x7d;" k="55" />
<hkern u1="&#xfd;" u2="&#x7c;" k="27" />
<hkern u1="&#xfd;" u2="]" k="66" />
<hkern u1="&#xfd;" u2="\" k="41" />
<hkern u1="&#xfd;" u2="X" k="31" />
<hkern u1="&#xfd;" u2="V" k="16" />
<hkern u1="&#xfd;" u2="&#x3f;" k="10" />
<hkern u1="&#xfd;" u2="&#x2f;" k="39" />
<hkern u1="&#xfd;" u2="&#x29;" k="39" />
<hkern u1="&#xff;" u2="&#x2122;" k="25" />
<hkern u1="&#xff;" u2="&#x7d;" k="55" />
<hkern u1="&#xff;" u2="&#x7c;" k="27" />
<hkern u1="&#xff;" u2="]" k="66" />
<hkern u1="&#xff;" u2="\" k="41" />
<hkern u1="&#xff;" u2="X" k="31" />
<hkern u1="&#xff;" u2="V" k="16" />
<hkern u1="&#xff;" u2="&#x3f;" k="10" />
<hkern u1="&#xff;" u2="&#x2f;" k="39" />
<hkern u1="&#xff;" u2="&#x29;" k="39" />
<hkern u1="&#x100;" u2="&#x2122;" k="78" />
<hkern u1="&#x100;" u2="&#xae;" k="66" />
<hkern u1="&#x100;" u2="&#x7d;" k="35" />
<hkern u1="&#x100;" u2="&#x7c;" k="88" />
<hkern u1="&#x100;" u2="v" k="37" />
<hkern u1="&#x100;" u2="f" k="14" />
<hkern u1="&#x100;" u2="]" k="16" />
<hkern u1="&#x100;" u2="\" k="92" />
<hkern u1="&#x100;" u2="V" k="66" />
<hkern u1="&#x100;" u2="&#x40;" k="6" />
<hkern u1="&#x100;" u2="&#x3f;" k="43" />
<hkern u1="&#x100;" u2="&#x2a;" k="72" />
<hkern u1="&#x100;" u2="&#x29;" k="25" />
<hkern u1="&#x100;" u2="&#x26;" k="6" />
<hkern u1="&#x101;" u2="&#x2122;" k="45" />
<hkern u1="&#x101;" u2="&#x7d;" k="49" />
<hkern u1="&#x101;" u2="&#x7c;" k="47" />
<hkern u1="&#x101;" u2="]" k="43" />
<hkern u1="&#x101;" u2="\" k="61" />
<hkern u1="&#x101;" u2="V" k="55" />
<hkern u1="&#x101;" u2="&#x3f;" k="33" />
<hkern u1="&#x101;" u2="&#x2a;" k="16" />
<hkern u1="&#x101;" u2="&#x29;" k="33" />
<hkern u1="&#x102;" u2="&#x2122;" k="78" />
<hkern u1="&#x102;" u2="&#xae;" k="66" />
<hkern u1="&#x102;" u2="&#x7d;" k="35" />
<hkern u1="&#x102;" u2="&#x7c;" k="88" />
<hkern u1="&#x102;" u2="v" k="37" />
<hkern u1="&#x102;" u2="f" k="14" />
<hkern u1="&#x102;" u2="]" k="16" />
<hkern u1="&#x102;" u2="\" k="92" />
<hkern u1="&#x102;" u2="V" k="66" />
<hkern u1="&#x102;" u2="&#x40;" k="6" />
<hkern u1="&#x102;" u2="&#x3f;" k="43" />
<hkern u1="&#x102;" u2="&#x2a;" k="72" />
<hkern u1="&#x102;" u2="&#x29;" k="25" />
<hkern u1="&#x102;" u2="&#x26;" k="6" />
<hkern u1="&#x103;" u2="&#x2122;" k="45" />
<hkern u1="&#x103;" u2="&#x7d;" k="49" />
<hkern u1="&#x103;" u2="&#x7c;" k="47" />
<hkern u1="&#x103;" u2="]" k="43" />
<hkern u1="&#x103;" u2="\" k="61" />
<hkern u1="&#x103;" u2="V" k="55" />
<hkern u1="&#x103;" u2="&#x3f;" k="33" />
<hkern u1="&#x103;" u2="&#x2a;" k="16" />
<hkern u1="&#x103;" u2="&#x29;" k="33" />
<hkern u1="&#x104;" u2="&#x2122;" k="78" />
<hkern u1="&#x104;" u2="&#x201e;" k="-78" />
<hkern u1="&#x104;" u2="&#x201a;" k="-43" />
<hkern u1="&#x104;" u2="&#xae;" k="66" />
<hkern u1="&#x104;" u2="&#x7d;" k="25" />
<hkern u1="&#x104;" u2="&#x7c;" k="88" />
<hkern u1="&#x104;" u2="v" k="37" />
<hkern u1="&#x104;" u2="j" k="-90" />
<hkern u1="&#x104;" u2="f" k="14" />
<hkern u1="&#x104;" u2="]" k="16" />
<hkern u1="&#x104;" u2="\" k="92" />
<hkern u1="&#x104;" u2="V" k="66" />
<hkern u1="&#x104;" u2="&#x40;" k="6" />
<hkern u1="&#x104;" u2="&#x3f;" k="43" />
<hkern u1="&#x104;" u2="&#x3b;" k="-31" />
<hkern u1="&#x104;" u2="&#x2c;" k="-29" />
<hkern u1="&#x104;" u2="&#x2a;" k="72" />
<hkern u1="&#x104;" u2="&#x29;" k="25" />
<hkern u1="&#x104;" u2="&#x26;" k="6" />
<hkern u1="&#x105;" u2="&#x2122;" k="45" />
<hkern u1="&#x105;" u2="&#x201e;" k="-25" />
<hkern u1="&#x105;" u2="&#x7d;" k="49" />
<hkern u1="&#x105;" u2="&#x7c;" k="47" />
<hkern u1="&#x105;" u2="j" k="-18" />
<hkern u1="&#x105;" u2="]" k="43" />
<hkern u1="&#x105;" u2="\" k="61" />
<hkern u1="&#x105;" u2="V" k="55" />
<hkern u1="&#x105;" u2="&#x3f;" k="33" />
<hkern u1="&#x105;" u2="&#x2a;" k="16" />
<hkern u1="&#x105;" u2="&#x29;" k="33" />
<hkern u1="&#x106;" u2="&#x135;" k="-61" />
<hkern u1="&#x106;" u2="&#x12d;" k="-55" />
<hkern u1="&#x106;" u2="&#x12b;" k="-74" />
<hkern u1="&#x106;" u2="&#x129;" k="-102" />
<hkern u1="&#x106;" u2="&#xef;" k="-76" />
<hkern u1="&#x106;" u2="&#xee;" k="-78" />
<hkern u1="&#x106;" u2="&#xec;" k="-104" />
<hkern u1="&#x106;" u2="&#x2a;" k="-25" />
<hkern u1="&#x106;" u2="&#x29;" k="18" />
<hkern u1="&#x107;" u2="&#x2122;" k="31" />
<hkern u1="&#x107;" u2="&#x7d;" k="37" />
<hkern u1="&#x107;" u2="&#x7c;" k="33" />
<hkern u1="&#x107;" u2="]" k="31" />
<hkern u1="&#x107;" u2="\" k="43" />
<hkern u1="&#x107;" u2="V" k="29" />
<hkern u1="&#x107;" u2="&#x3f;" k="10" />
<hkern u1="&#x107;" u2="&#x29;" k="25" />
<hkern u1="&#x108;" u2="&#x135;" k="-61" />
<hkern u1="&#x108;" u2="&#x12d;" k="-55" />
<hkern u1="&#x108;" u2="&#x12b;" k="-74" />
<hkern u1="&#x108;" u2="&#x129;" k="-102" />
<hkern u1="&#x108;" u2="&#xef;" k="-76" />
<hkern u1="&#x108;" u2="&#xee;" k="-78" />
<hkern u1="&#x108;" u2="&#xec;" k="-104" />
<hkern u1="&#x108;" u2="&#x2a;" k="-25" />
<hkern u1="&#x108;" u2="&#x29;" k="18" />
<hkern u1="&#x109;" u2="&#x2122;" k="31" />
<hkern u1="&#x109;" u2="&#x7d;" k="37" />
<hkern u1="&#x109;" u2="&#x7c;" k="33" />
<hkern u1="&#x109;" u2="]" k="31" />
<hkern u1="&#x109;" u2="\" k="43" />
<hkern u1="&#x109;" u2="V" k="29" />
<hkern u1="&#x109;" u2="&#x3f;" k="10" />
<hkern u1="&#x109;" u2="&#x29;" k="25" />
<hkern u1="&#x10a;" u2="&#x135;" k="-61" />
<hkern u1="&#x10a;" u2="&#x12d;" k="-55" />
<hkern u1="&#x10a;" u2="&#x12b;" k="-74" />
<hkern u1="&#x10a;" u2="&#x129;" k="-102" />
<hkern u1="&#x10a;" u2="&#xef;" k="-76" />
<hkern u1="&#x10a;" u2="&#xee;" k="-78" />
<hkern u1="&#x10a;" u2="&#xec;" k="-104" />
<hkern u1="&#x10a;" u2="&#x2a;" k="-25" />
<hkern u1="&#x10a;" u2="&#x29;" k="18" />
<hkern u1="&#x10b;" u2="&#x2122;" k="31" />
<hkern u1="&#x10b;" u2="&#x7d;" k="37" />
<hkern u1="&#x10b;" u2="&#x7c;" k="33" />
<hkern u1="&#x10b;" u2="]" k="31" />
<hkern u1="&#x10b;" u2="\" k="43" />
<hkern u1="&#x10b;" u2="V" k="29" />
<hkern u1="&#x10b;" u2="&#x3f;" k="10" />
<hkern u1="&#x10b;" u2="&#x29;" k="25" />
<hkern u1="&#x10c;" u2="&#x135;" k="-61" />
<hkern u1="&#x10c;" u2="&#x12d;" k="-55" />
<hkern u1="&#x10c;" u2="&#x12b;" k="-74" />
<hkern u1="&#x10c;" u2="&#x129;" k="-102" />
<hkern u1="&#x10c;" u2="&#xef;" k="-76" />
<hkern u1="&#x10c;" u2="&#xee;" k="-78" />
<hkern u1="&#x10c;" u2="&#xec;" k="-104" />
<hkern u1="&#x10c;" u2="&#x2a;" k="-25" />
<hkern u1="&#x10c;" u2="&#x29;" k="18" />
<hkern u1="&#x10d;" u2="&#x2122;" k="31" />
<hkern u1="&#x10d;" u2="&#x12b;" k="-78" />
<hkern u1="&#x10d;" u2="&#x7d;" k="20" />
<hkern u1="&#x10d;" u2="&#x7c;" k="33" />
<hkern u1="&#x10d;" u2="]" k="20" />
<hkern u1="&#x10d;" u2="\" k="43" />
<hkern u1="&#x10d;" u2="V" k="29" />
<hkern u1="&#x10d;" u2="&#x3f;" k="10" />
<hkern u1="&#x10d;" u2="&#x29;" k="-8" />
<hkern u1="&#x10e;" u2="&#x2122;" k="29" />
<hkern u1="&#x10e;" u2="&#x7d;" k="66" />
<hkern u1="&#x10e;" u2="&#x7c;" k="43" />
<hkern u1="&#x10e;" u2="x" k="10" />
<hkern u1="&#x10e;" u2="]" k="72" />
<hkern u1="&#x10e;" u2="\" k="45" />
<hkern u1="&#x10e;" u2="X" k="41" />
<hkern u1="&#x10e;" u2="V" k="31" />
<hkern u1="&#x10e;" u2="&#x3f;" k="10" />
<hkern u1="&#x10e;" u2="&#x2f;" k="35" />
<hkern u1="&#x10e;" u2="&#x29;" k="55" />
<hkern u1="&#x10f;" u2="&#x7d;" k="-20" />
<hkern u1="&#x10f;" u2="&#x7c;" k="-35" />
<hkern u1="&#x10f;" u2="x" k="-33" />
<hkern u1="&#x10f;" u2="v" k="-31" />
<hkern u1="&#x10f;" u2="]" k="-20" />
<hkern u1="&#x10f;" u2="&#x3f;" k="-14" />
<hkern u1="&#x10f;" u2="&#x2f;" k="53" />
<hkern u1="&#x10f;" u2="&#x2a;" k="-59" />
<hkern u1="&#x10f;" u2="&#x21;" k="-14" />
<hkern u1="&#x110;" u2="&#x2122;" k="29" />
<hkern u1="&#x110;" u2="&#x7d;" k="66" />
<hkern u1="&#x110;" u2="&#x7c;" k="43" />
<hkern u1="&#x110;" u2="x" k="10" />
<hkern u1="&#x110;" u2="]" k="72" />
<hkern u1="&#x110;" u2="\" k="45" />
<hkern u1="&#x110;" u2="X" k="41" />
<hkern u1="&#x110;" u2="V" k="31" />
<hkern u1="&#x110;" u2="&#x3f;" k="10" />
<hkern u1="&#x110;" u2="&#x2f;" k="35" />
<hkern u1="&#x110;" u2="&#x29;" k="55" />
<hkern u1="&#x111;" u2="&#x135;" k="-29" />
<hkern u1="&#x111;" u2="&#x12d;" k="-31" />
<hkern u1="&#x111;" u2="&#x12b;" k="-18" />
<hkern u1="&#x111;" u2="&#x129;" k="-51" />
<hkern u1="&#x111;" u2="&#xef;" k="-27" />
<hkern u1="&#x111;" u2="&#xee;" k="-45" />
<hkern u1="&#x111;" u2="&#xec;" k="-88" />
<hkern u1="&#x111;" u2="&#x7c;" k="-8" />
<hkern u1="&#x111;" u2="&#x2a;" k="-33" />
<hkern u1="&#x111;" u2="&#x29;" k="23" />
<hkern u1="&#x112;" u2="&#x135;" k="-61" />
<hkern u1="&#x112;" u2="&#x12d;" k="-57" />
<hkern u1="&#x112;" u2="&#x12b;" k="-55" />
<hkern u1="&#x112;" u2="&#x129;" k="-96" />
<hkern u1="&#x112;" u2="&#xef;" k="-61" />
<hkern u1="&#x112;" u2="&#xee;" k="-78" />
<hkern u1="&#x112;" u2="&#xec;" k="-133" />
<hkern u1="&#x112;" u2="v" k="14" />
<hkern u1="&#x112;" u2="&#x29;" k="14" />
<hkern u1="&#x113;" u2="&#x2122;" k="51" />
<hkern u1="&#x113;" u2="&#x142;" k="-20" />
<hkern u1="&#x113;" u2="&#x7d;" k="53" />
<hkern u1="&#x113;" u2="&#x7c;" k="51" />
<hkern u1="&#x113;" u2="v" k="12" />
<hkern u1="&#x113;" u2="]" k="47" />
<hkern u1="&#x113;" u2="\" k="72" />
<hkern u1="&#x113;" u2="V" k="53" />
<hkern u1="&#x113;" u2="&#x3f;" k="35" />
<hkern u1="&#x113;" u2="&#x2a;" k="27" />
<hkern u1="&#x113;" u2="&#x29;" k="35" />
<hkern u1="&#x114;" u2="&#x135;" k="-61" />
<hkern u1="&#x114;" u2="&#x12d;" k="-57" />
<hkern u1="&#x114;" u2="&#x12b;" k="-55" />
<hkern u1="&#x114;" u2="&#x129;" k="-96" />
<hkern u1="&#x114;" u2="&#xef;" k="-61" />
<hkern u1="&#x114;" u2="&#xee;" k="-78" />
<hkern u1="&#x114;" u2="&#xec;" k="-133" />
<hkern u1="&#x114;" u2="v" k="14" />
<hkern u1="&#x114;" u2="&#x29;" k="14" />
<hkern u1="&#x115;" u2="&#x2122;" k="51" />
<hkern u1="&#x115;" u2="&#x142;" k="-20" />
<hkern u1="&#x115;" u2="&#x7d;" k="53" />
<hkern u1="&#x115;" u2="&#x7c;" k="51" />
<hkern u1="&#x115;" u2="v" k="12" />
<hkern u1="&#x115;" u2="]" k="47" />
<hkern u1="&#x115;" u2="\" k="72" />
<hkern u1="&#x115;" u2="V" k="53" />
<hkern u1="&#x115;" u2="&#x3f;" k="35" />
<hkern u1="&#x115;" u2="&#x2a;" k="27" />
<hkern u1="&#x115;" u2="&#x29;" k="35" />
<hkern u1="&#x116;" u2="&#x135;" k="-61" />
<hkern u1="&#x116;" u2="&#x12d;" k="-57" />
<hkern u1="&#x116;" u2="&#x12b;" k="-55" />
<hkern u1="&#x116;" u2="&#x129;" k="-96" />
<hkern u1="&#x116;" u2="&#xef;" k="-61" />
<hkern u1="&#x116;" u2="&#xee;" k="-78" />
<hkern u1="&#x116;" u2="&#xec;" k="-133" />
<hkern u1="&#x116;" u2="v" k="14" />
<hkern u1="&#x116;" u2="&#x29;" k="14" />
<hkern u1="&#x117;" u2="&#x2122;" k="51" />
<hkern u1="&#x117;" u2="&#x142;" k="-20" />
<hkern u1="&#x117;" u2="&#x7d;" k="53" />
<hkern u1="&#x117;" u2="&#x7c;" k="51" />
<hkern u1="&#x117;" u2="v" k="12" />
<hkern u1="&#x117;" u2="]" k="47" />
<hkern u1="&#x117;" u2="\" k="72" />
<hkern u1="&#x117;" u2="V" k="53" />
<hkern u1="&#x117;" u2="&#x3f;" k="35" />
<hkern u1="&#x117;" u2="&#x2a;" k="27" />
<hkern u1="&#x117;" u2="&#x29;" k="35" />
<hkern u1="&#x118;" u2="&#x201e;" k="-47" />
<hkern u1="&#x118;" u2="&#x201a;" k="-12" />
<hkern u1="&#x118;" u2="&#x135;" k="-61" />
<hkern u1="&#x118;" u2="&#x12d;" k="-57" />
<hkern u1="&#x118;" u2="&#x12b;" k="-55" />
<hkern u1="&#x118;" u2="&#x129;" k="-96" />
<hkern u1="&#x118;" u2="&#xef;" k="-61" />
<hkern u1="&#x118;" u2="&#xee;" k="-78" />
<hkern u1="&#x118;" u2="&#xec;" k="-133" />
<hkern u1="&#x118;" u2="v" k="14" />
<hkern u1="&#x118;" u2="j" k="-43" />
<hkern u1="&#x118;" u2="&#x29;" k="14" />
<hkern u1="&#x119;" u2="&#x2122;" k="51" />
<hkern u1="&#x119;" u2="&#x201e;" k="-43" />
<hkern u1="&#x119;" u2="&#x142;" k="-20" />
<hkern u1="&#x119;" u2="&#x7d;" k="53" />
<hkern u1="&#x119;" u2="&#x7c;" k="51" />
<hkern u1="&#x119;" u2="v" k="12" />
<hkern u1="&#x119;" u2="]" k="47" />
<hkern u1="&#x119;" u2="\" k="72" />
<hkern u1="&#x119;" u2="V" k="53" />
<hkern u1="&#x119;" u2="&#x3f;" k="35" />
<hkern u1="&#x119;" u2="&#x2a;" k="27" />
<hkern u1="&#x119;" u2="&#x29;" k="35" />
<hkern u1="&#x11a;" u2="&#x135;" k="-61" />
<hkern u1="&#x11a;" u2="&#x12d;" k="-57" />
<hkern u1="&#x11a;" u2="&#x12b;" k="-55" />
<hkern u1="&#x11a;" u2="&#x129;" k="-96" />
<hkern u1="&#x11a;" u2="&#xef;" k="-61" />
<hkern u1="&#x11a;" u2="&#xee;" k="-78" />
<hkern u1="&#x11a;" u2="&#xec;" k="-133" />
<hkern u1="&#x11a;" u2="v" k="14" />
<hkern u1="&#x11a;" u2="&#x29;" k="14" />
<hkern u1="&#x11b;" u2="&#x2122;" k="51" />
<hkern u1="&#x11b;" u2="&#x142;" k="-20" />
<hkern u1="&#x11b;" u2="&#x7d;" k="53" />
<hkern u1="&#x11b;" u2="&#x7c;" k="51" />
<hkern u1="&#x11b;" u2="v" k="12" />
<hkern u1="&#x11b;" u2="]" k="47" />
<hkern u1="&#x11b;" u2="\" k="72" />
<hkern u1="&#x11b;" u2="V" k="53" />
<hkern u1="&#x11b;" u2="&#x3f;" k="35" />
<hkern u1="&#x11b;" u2="&#x2a;" k="27" />
<hkern u1="&#x11b;" u2="&#x29;" k="35" />
<hkern u1="&#x11c;" u2="&#x135;" k="-33" />
<hkern u1="&#x11c;" u2="&#x12d;" k="-20" />
<hkern u1="&#x11c;" u2="&#x12b;" k="-31" />
<hkern u1="&#x11c;" u2="&#x129;" k="-57" />
<hkern u1="&#x11c;" u2="&#xef;" k="-39" />
<hkern u1="&#x11c;" u2="&#xee;" k="-49" />
<hkern u1="&#x11c;" u2="&#xec;" k="-61" />
<hkern u1="&#x11c;" u2="&#x7d;" k="18" />
<hkern u1="&#x11c;" u2="&#x7c;" k="18" />
<hkern u1="&#x11c;" u2="f" k="8" />
<hkern u1="&#x11c;" u2="]" k="23" />
<hkern u1="&#x11c;" u2="\" k="14" />
<hkern u1="&#x11c;" u2="V" k="23" />
<hkern u1="&#x11c;" u2="&#x29;" k="33" />
<hkern u1="&#x11d;" u2="&#x2122;" k="18" />
<hkern u1="&#x11d;" u2="&#x201e;" k="-55" />
<hkern u1="&#x11d;" u2="&#x201a;" k="-33" />
<hkern u1="&#x11d;" u2="&#x135;" k="-80" />
<hkern u1="&#x11d;" u2="&#x12f;" k="-43" />
<hkern u1="&#x11d;" u2="&#x7d;" k="-8" />
<hkern u1="&#x11d;" u2="&#x7c;" k="27" />
<hkern u1="&#x11d;" u2="j" k="-80" />
<hkern u1="&#x11d;" u2="]" k="-8" />
<hkern u1="&#x11d;" u2="\" k="29" />
<hkern u1="&#x11d;" u2="V" k="12" />
<hkern u1="&#x11d;" u2="&#x3b;" k="-37" />
<hkern u1="&#x11d;" u2="&#x2c;" k="-35" />
<hkern u1="&#x11d;" u2="&#x29;" k="-12" />
<hkern u1="&#x11e;" u2="&#x135;" k="-33" />
<hkern u1="&#x11e;" u2="&#x12d;" k="-20" />
<hkern u1="&#x11e;" u2="&#x12b;" k="-31" />
<hkern u1="&#x11e;" u2="&#x129;" k="-57" />
<hkern u1="&#x11e;" u2="&#xef;" k="-39" />
<hkern u1="&#x11e;" u2="&#xee;" k="-49" />
<hkern u1="&#x11e;" u2="&#xec;" k="-61" />
<hkern u1="&#x11e;" u2="&#x7d;" k="18" />
<hkern u1="&#x11e;" u2="&#x7c;" k="18" />
<hkern u1="&#x11e;" u2="f" k="8" />
<hkern u1="&#x11e;" u2="]" k="23" />
<hkern u1="&#x11e;" u2="\" k="14" />
<hkern u1="&#x11e;" u2="V" k="23" />
<hkern u1="&#x11e;" u2="&#x29;" k="33" />
<hkern u1="&#x11f;" u2="&#x2122;" k="18" />
<hkern u1="&#x11f;" u2="&#x201e;" k="-55" />
<hkern u1="&#x11f;" u2="&#x201a;" k="-33" />
<hkern u1="&#x11f;" u2="&#x135;" k="-80" />
<hkern u1="&#x11f;" u2="&#x12f;" k="-43" />
<hkern u1="&#x11f;" u2="&#x7d;" k="-8" />
<hkern u1="&#x11f;" u2="&#x7c;" k="27" />
<hkern u1="&#x11f;" u2="j" k="-80" />
<hkern u1="&#x11f;" u2="]" k="-8" />
<hkern u1="&#x11f;" u2="\" k="29" />
<hkern u1="&#x11f;" u2="V" k="12" />
<hkern u1="&#x11f;" u2="&#x3b;" k="-37" />
<hkern u1="&#x11f;" u2="&#x2c;" k="-35" />
<hkern u1="&#x11f;" u2="&#x29;" k="-12" />
<hkern u1="&#x120;" u2="&#x135;" k="-33" />
<hkern u1="&#x120;" u2="&#x12d;" k="-20" />
<hkern u1="&#x120;" u2="&#x12b;" k="-31" />
<hkern u1="&#x120;" u2="&#x129;" k="-57" />
<hkern u1="&#x120;" u2="&#xef;" k="-39" />
<hkern u1="&#x120;" u2="&#xee;" k="-49" />
<hkern u1="&#x120;" u2="&#xec;" k="-61" />
<hkern u1="&#x120;" u2="&#x7d;" k="18" />
<hkern u1="&#x120;" u2="&#x7c;" k="18" />
<hkern u1="&#x120;" u2="f" k="8" />
<hkern u1="&#x120;" u2="]" k="23" />
<hkern u1="&#x120;" u2="\" k="14" />
<hkern u1="&#x120;" u2="V" k="23" />
<hkern u1="&#x120;" u2="&#x29;" k="33" />
<hkern u1="&#x121;" u2="&#x2122;" k="18" />
<hkern u1="&#x121;" u2="&#x201e;" k="-55" />
<hkern u1="&#x121;" u2="&#x201a;" k="-33" />
<hkern u1="&#x121;" u2="&#x135;" k="-80" />
<hkern u1="&#x121;" u2="&#x12f;" k="-43" />
<hkern u1="&#x121;" u2="&#x7d;" k="-8" />
<hkern u1="&#x121;" u2="&#x7c;" k="27" />
<hkern u1="&#x121;" u2="j" k="-80" />
<hkern u1="&#x121;" u2="]" k="-8" />
<hkern u1="&#x121;" u2="\" k="29" />
<hkern u1="&#x121;" u2="V" k="12" />
<hkern u1="&#x121;" u2="&#x3b;" k="-37" />
<hkern u1="&#x121;" u2="&#x2c;" k="-35" />
<hkern u1="&#x121;" u2="&#x29;" k="-12" />
<hkern u1="&#x122;" u2="&#x135;" k="-33" />
<hkern u1="&#x122;" u2="&#x12d;" k="-20" />
<hkern u1="&#x122;" u2="&#x12b;" k="-31" />
<hkern u1="&#x122;" u2="&#x129;" k="-57" />
<hkern u1="&#x122;" u2="&#xef;" k="-39" />
<hkern u1="&#x122;" u2="&#xee;" k="-49" />
<hkern u1="&#x122;" u2="&#xec;" k="-61" />
<hkern u1="&#x122;" u2="&#x7d;" k="18" />
<hkern u1="&#x122;" u2="&#x7c;" k="18" />
<hkern u1="&#x122;" u2="f" k="8" />
<hkern u1="&#x122;" u2="]" k="23" />
<hkern u1="&#x122;" u2="\" k="14" />
<hkern u1="&#x122;" u2="V" k="23" />
<hkern u1="&#x122;" u2="&#x29;" k="33" />
<hkern u1="&#x123;" u2="&#x2122;" k="18" />
<hkern u1="&#x123;" u2="&#x201e;" k="-55" />
<hkern u1="&#x123;" u2="&#x201a;" k="-33" />
<hkern u1="&#x123;" u2="&#x135;" k="-80" />
<hkern u1="&#x123;" u2="&#x12f;" k="-43" />
<hkern u1="&#x123;" u2="&#x7d;" k="-8" />
<hkern u1="&#x123;" u2="&#x7c;" k="27" />
<hkern u1="&#x123;" u2="j" k="-80" />
<hkern u1="&#x123;" u2="]" k="-8" />
<hkern u1="&#x123;" u2="\" k="29" />
<hkern u1="&#x123;" u2="V" k="12" />
<hkern u1="&#x123;" u2="&#x3b;" k="-37" />
<hkern u1="&#x123;" u2="&#x2c;" k="-35" />
<hkern u1="&#x123;" u2="&#x29;" k="-12" />
<hkern u1="&#x124;" u2="&#x135;" k="-14" />
<hkern u1="&#x124;" u2="&#x129;" k="-33" />
<hkern u1="&#x124;" u2="&#xef;" k="-12" />
<hkern u1="&#x124;" u2="&#xee;" k="-31" />
<hkern u1="&#x124;" u2="&#xec;" k="-66" />
<hkern u1="&#x124;" u2="&#x7d;" k="20" />
<hkern u1="&#x124;" u2="&#x7c;" k="18" />
<hkern u1="&#x124;" u2="f" k="8" />
<hkern u1="&#x124;" u2="]" k="25" />
<hkern u1="&#x124;" u2="&#x29;" k="31" />
<hkern u1="&#x125;" u2="&#x2122;" k="61" />
<hkern u1="&#x125;" u2="&#xae;" k="31" />
<hkern u1="&#x125;" u2="&#x7d;" k="49" />
<hkern u1="&#x125;" u2="&#x7c;" k="63" />
<hkern u1="&#x125;" u2="v" k="12" />
<hkern u1="&#x125;" u2="f" k="6" />
<hkern u1="&#x125;" u2="]" k="35" />
<hkern u1="&#x125;" u2="\" k="86" />
<hkern u1="&#x125;" u2="V" k="63" />
<hkern u1="&#x125;" u2="&#x3f;" k="41" />
<hkern u1="&#x125;" u2="&#x2a;" k="35" />
<hkern u1="&#x125;" u2="&#x29;" k="33" />
<hkern u1="&#x126;" u2="&#x135;" k="-14" />
<hkern u1="&#x126;" u2="&#x129;" k="-33" />
<hkern u1="&#x126;" u2="&#xef;" k="-12" />
<hkern u1="&#x126;" u2="&#xee;" k="-31" />
<hkern u1="&#x126;" u2="&#xec;" k="-66" />
<hkern u1="&#x126;" u2="&#x7d;" k="20" />
<hkern u1="&#x126;" u2="&#x7c;" k="18" />
<hkern u1="&#x126;" u2="f" k="8" />
<hkern u1="&#x126;" u2="]" k="25" />
<hkern u1="&#x126;" u2="&#x2a;" k="-49" />
<hkern u1="&#x126;" u2="&#x29;" k="31" />
<hkern u1="&#x127;" u2="&#x2122;" k="61" />
<hkern u1="&#x127;" u2="&#xae;" k="31" />
<hkern u1="&#x127;" u2="&#x7d;" k="49" />
<hkern u1="&#x127;" u2="&#x7c;" k="63" />
<hkern u1="&#x127;" u2="v" k="12" />
<hkern u1="&#x127;" u2="f" k="6" />
<hkern u1="&#x127;" u2="]" k="35" />
<hkern u1="&#x127;" u2="\" k="86" />
<hkern u1="&#x127;" u2="V" k="63" />
<hkern u1="&#x127;" u2="&#x3f;" k="41" />
<hkern u1="&#x127;" u2="&#x2a;" k="35" />
<hkern u1="&#x127;" u2="&#x29;" k="33" />
<hkern u1="&#x128;" u2="&#x135;" k="-14" />
<hkern u1="&#x128;" u2="&#x129;" k="-33" />
<hkern u1="&#x128;" u2="&#xef;" k="-12" />
<hkern u1="&#x128;" u2="&#xee;" k="-31" />
<hkern u1="&#x128;" u2="&#xec;" k="-66" />
<hkern u1="&#x128;" u2="&#x7d;" k="20" />
<hkern u1="&#x128;" u2="&#x7c;" k="18" />
<hkern u1="&#x128;" u2="f" k="8" />
<hkern u1="&#x128;" u2="]" k="25" />
<hkern u1="&#x128;" u2="&#x29;" k="31" />
<hkern u1="&#x129;" u2="&#x2122;" k="-14" />
<hkern u1="&#x129;" u2="&#x135;" k="-31" />
<hkern u1="&#x129;" u2="&#x12d;" k="-27" />
<hkern u1="&#x129;" u2="&#x12b;" k="-18" />
<hkern u1="&#x129;" u2="&#x129;" k="-53" />
<hkern u1="&#x129;" u2="&#xef;" k="-27" />
<hkern u1="&#x129;" u2="&#xee;" k="-47" />
<hkern u1="&#x129;" u2="&#xec;" k="-90" />
<hkern u1="&#x129;" u2="&#x7d;" k="-55" />
<hkern u1="&#x129;" u2="&#x7c;" k="-72" />
<hkern u1="&#x129;" u2="]" k="-55" />
<hkern u1="&#x129;" u2="\" k="-45" />
<hkern u1="&#x129;" u2="&#x3f;" k="-63" />
<hkern u1="&#x129;" u2="&#x2a;" k="-66" />
<hkern u1="&#x129;" u2="&#x29;" k="25" />
<hkern u1="&#x129;" u2="&#x27;" k="-51" />
<hkern u1="&#x129;" u2="&#x22;" k="-51" />
<hkern u1="&#x129;" u2="&#x21;" k="-49" />
<hkern u1="&#x12a;" u2="&#x135;" k="-14" />
<hkern u1="&#x12a;" u2="&#x129;" k="-33" />
<hkern u1="&#x12a;" u2="&#xef;" k="-12" />
<hkern u1="&#x12a;" u2="&#xee;" k="-31" />
<hkern u1="&#x12a;" u2="&#xec;" k="-66" />
<hkern u1="&#x12a;" u2="&#x7d;" k="20" />
<hkern u1="&#x12a;" u2="&#x7c;" k="18" />
<hkern u1="&#x12a;" u2="f" k="8" />
<hkern u1="&#x12a;" u2="]" k="25" />
<hkern u1="&#x12a;" u2="&#x29;" k="31" />
<hkern u1="&#x12b;" u2="&#x135;" k="-31" />
<hkern u1="&#x12b;" u2="&#x12d;" k="-27" />
<hkern u1="&#x12b;" u2="&#x12b;" k="-18" />
<hkern u1="&#x12b;" u2="&#x129;" k="-53" />
<hkern u1="&#x12b;" u2="&#xef;" k="-27" />
<hkern u1="&#x12b;" u2="&#xee;" k="-47" />
<hkern u1="&#x12b;" u2="&#xec;" k="-90" />
<hkern u1="&#x12b;" u2="&#x7d;" k="-37" />
<hkern u1="&#x12b;" u2="&#x7c;" k="-63" />
<hkern u1="&#x12b;" u2="]" k="-39" />
<hkern u1="&#x12b;" u2="\" k="-39" />
<hkern u1="&#x12b;" u2="&#x3f;" k="-43" />
<hkern u1="&#x12b;" u2="&#x2a;" k="-63" />
<hkern u1="&#x12b;" u2="&#x29;" k="25" />
<hkern u1="&#x12b;" u2="&#x27;" k="-35" />
<hkern u1="&#x12b;" u2="&#x22;" k="-35" />
<hkern u1="&#x12b;" u2="&#x21;" k="-33" />
<hkern u1="&#x12c;" u2="&#x135;" k="-14" />
<hkern u1="&#x12c;" u2="&#x129;" k="-33" />
<hkern u1="&#x12c;" u2="&#xef;" k="-12" />
<hkern u1="&#x12c;" u2="&#xee;" k="-31" />
<hkern u1="&#x12c;" u2="&#xec;" k="-66" />
<hkern u1="&#x12c;" u2="&#x7d;" k="20" />
<hkern u1="&#x12c;" u2="&#x7c;" k="18" />
<hkern u1="&#x12c;" u2="f" k="8" />
<hkern u1="&#x12c;" u2="]" k="25" />
<hkern u1="&#x12c;" u2="&#x29;" k="31" />
<hkern u1="&#x12d;" u2="&#x2122;" k="-10" />
<hkern u1="&#x12d;" u2="&#x135;" k="-31" />
<hkern u1="&#x12d;" u2="&#x12d;" k="-27" />
<hkern u1="&#x12d;" u2="&#x12b;" k="-18" />
<hkern u1="&#x12d;" u2="&#x129;" k="-53" />
<hkern u1="&#x12d;" u2="&#xef;" k="-27" />
<hkern u1="&#x12d;" u2="&#xee;" k="-47" />
<hkern u1="&#x12d;" u2="&#xec;" k="-90" />
<hkern u1="&#x12d;" u2="&#x7d;" k="-72" />
<hkern u1="&#x12d;" u2="&#x7c;" k="-90" />
<hkern u1="&#x12d;" u2="]" k="-76" />
<hkern u1="&#x12d;" u2="\" k="-59" />
<hkern u1="&#x12d;" u2="&#x3f;" k="-41" />
<hkern u1="&#x12d;" u2="&#x2a;" k="-45" />
<hkern u1="&#x12d;" u2="&#x29;" k="25" />
<hkern u1="&#x12d;" u2="&#x27;" k="-47" />
<hkern u1="&#x12d;" u2="&#x22;" k="-47" />
<hkern u1="&#x12d;" u2="&#x21;" k="-51" />
<hkern u1="&#x12e;" u2="&#x135;" k="-14" />
<hkern u1="&#x12e;" u2="&#x129;" k="-33" />
<hkern u1="&#x12e;" u2="&#xef;" k="-12" />
<hkern u1="&#x12e;" u2="&#xee;" k="-31" />
<hkern u1="&#x12e;" u2="&#xec;" k="-66" />
<hkern u1="&#x12e;" u2="&#x7d;" k="20" />
<hkern u1="&#x12e;" u2="&#x7c;" k="18" />
<hkern u1="&#x12e;" u2="f" k="8" />
<hkern u1="&#x12e;" u2="]" k="25" />
<hkern u1="&#x12e;" u2="&#x29;" k="31" />
<hkern u1="&#x12f;" u2="&#x135;" k="-31" />
<hkern u1="&#x12f;" u2="&#x12d;" k="-27" />
<hkern u1="&#x12f;" u2="&#x12b;" k="-18" />
<hkern u1="&#x12f;" u2="&#x129;" k="-53" />
<hkern u1="&#x12f;" u2="&#xef;" k="-27" />
<hkern u1="&#x12f;" u2="&#xee;" k="-47" />
<hkern u1="&#x12f;" u2="&#xec;" k="-90" />
<hkern u1="&#x12f;" u2="&#x29;" k="25" />
<hkern u1="&#x130;" u2="&#x135;" k="-14" />
<hkern u1="&#x130;" u2="&#x129;" k="-33" />
<hkern u1="&#x130;" u2="&#xef;" k="-12" />
<hkern u1="&#x130;" u2="&#xee;" k="-31" />
<hkern u1="&#x130;" u2="&#xec;" k="-66" />
<hkern u1="&#x130;" u2="&#x7d;" k="20" />
<hkern u1="&#x130;" u2="&#x7c;" k="18" />
<hkern u1="&#x130;" u2="f" k="8" />
<hkern u1="&#x130;" u2="]" k="25" />
<hkern u1="&#x130;" u2="&#x29;" k="31" />
<hkern u1="&#x131;" u2="&#x135;" k="-31" />
<hkern u1="&#x131;" u2="&#x12d;" k="-27" />
<hkern u1="&#x131;" u2="&#x12b;" k="-18" />
<hkern u1="&#x131;" u2="&#x129;" k="-53" />
<hkern u1="&#x131;" u2="&#xef;" k="-27" />
<hkern u1="&#x131;" u2="&#xee;" k="-47" />
<hkern u1="&#x131;" u2="&#xec;" k="-90" />
<hkern u1="&#x131;" u2="&#x29;" k="25" />
<hkern u1="&#x134;" u2="&#x135;" k="-20" />
<hkern u1="&#x134;" u2="&#x12d;" k="-8" />
<hkern u1="&#x134;" u2="&#x129;" k="-37" />
<hkern u1="&#x134;" u2="&#xef;" k="-16" />
<hkern u1="&#x134;" u2="&#xee;" k="-37" />
<hkern u1="&#x134;" u2="&#xec;" k="-72" />
<hkern u1="&#x134;" u2="&#x7d;" k="16" />
<hkern u1="&#x134;" u2="&#x7c;" k="16" />
<hkern u1="&#x134;" u2="f" k="8" />
<hkern u1="&#x134;" u2="]" k="16" />
<hkern u1="&#x134;" u2="&#x29;" k="29" />
<hkern u1="&#x135;" u2="&#x201d;" k="-35" />
<hkern u1="&#x135;" u2="&#x201c;" k="-8" />
<hkern u1="&#x135;" u2="&#x2019;" k="-35" />
<hkern u1="&#x135;" u2="&#x142;" k="-8" />
<hkern u1="&#x135;" u2="&#x13e;" k="-8" />
<hkern u1="&#x135;" u2="&#x13c;" k="-8" />
<hkern u1="&#x135;" u2="&#x13a;" k="-8" />
<hkern u1="&#x135;" u2="&#x137;" k="-8" />
<hkern u1="&#x135;" u2="&#x135;" k="-8" />
<hkern u1="&#x135;" u2="&#x131;" k="-8" />
<hkern u1="&#x135;" u2="&#x12f;" k="-8" />
<hkern u1="&#x135;" u2="&#x12d;" k="-8" />
<hkern u1="&#x135;" u2="&#x12b;" k="-8" />
<hkern u1="&#x135;" u2="&#x129;" k="-8" />
<hkern u1="&#x135;" u2="&#x127;" k="-8" />
<hkern u1="&#x135;" u2="&#x125;" k="-8" />
<hkern u1="&#x135;" u2="&#xef;" k="-8" />
<hkern u1="&#x135;" u2="&#xee;" k="-8" />
<hkern u1="&#x135;" u2="&#xed;" k="-8" />
<hkern u1="&#x135;" u2="&#xec;" k="-8" />
<hkern u1="&#x135;" u2="&#xdf;" k="-8" />
<hkern u1="&#x135;" u2="&#xae;" k="-25" />
<hkern u1="&#x135;" u2="&#x7c;" k="-35" />
<hkern u1="&#x135;" u2="l" k="-8" />
<hkern u1="&#x135;" u2="k" k="-8" />
<hkern u1="&#x135;" u2="j" k="-8" />
<hkern u1="&#x135;" u2="i" k="-8" />
<hkern u1="&#x135;" u2="h" k="-8" />
<hkern u1="&#x135;" u2="b" k="-12" />
<hkern u1="&#x135;" u2="&#x3f;" k="-43" />
<hkern u1="&#x135;" u2="&#x2a;" k="-63" />
<hkern u1="&#x135;" u2="&#x29;" k="8" />
<hkern u1="&#x135;" u2="&#x27;" k="-59" />
<hkern u1="&#x135;" u2="&#x22;" k="-59" />
<hkern u1="&#x135;" u2="&#x21;" k="-55" />
<hkern u1="&#x136;" u2="&#x135;" k="-37" />
<hkern u1="&#x136;" u2="&#x12d;" k="-100" />
<hkern u1="&#x136;" u2="&#x12b;" k="-94" />
<hkern u1="&#x136;" u2="&#x129;" k="-131" />
<hkern u1="&#x136;" u2="&#xef;" k="-104" />
<hkern u1="&#x136;" u2="&#xee;" k="-49" />
<hkern u1="&#x136;" u2="&#xec;" k="-152" />
<hkern u1="&#x136;" u2="v" k="27" />
<hkern u1="&#x136;" u2="f" k="10" />
<hkern u1="&#x136;" u2="&#x2a;" k="-14" />
<hkern u1="&#x137;" u2="&#x2122;" k="25" />
<hkern u1="&#x137;" u2="&#x7d;" k="10" />
<hkern u1="&#x137;" u2="&#x7c;" k="23" />
<hkern u1="&#x137;" u2="]" k="10" />
<hkern u1="&#x137;" u2="\" k="27" />
<hkern u1="&#x137;" u2="V" k="14" />
<hkern u1="&#x137;" u2="&#x3f;" k="8" />
<hkern u1="&#x139;" u2="&#x2122;" k="143" />
<hkern u1="&#x139;" u2="&#x201e;" k="-10" />
<hkern u1="&#x139;" u2="&#xae;" k="145" />
<hkern u1="&#x139;" u2="&#x7d;" k="6" />
<hkern u1="&#x139;" u2="&#x7c;" k="76" />
<hkern u1="&#x139;" u2="v" k="80" />
<hkern u1="&#x139;" u2="f" k="8" />
<hkern u1="&#x139;" u2="]" k="8" />
<hkern u1="&#x139;" u2="\" k="137" />
<hkern u1="&#x139;" u2="V" k="117" />
<hkern u1="&#x139;" u2="&#x3f;" k="29" />
<hkern u1="&#x139;" u2="&#x3b;" k="-10" />
<hkern u1="&#x139;" u2="&#x2c;" k="-8" />
<hkern u1="&#x139;" u2="&#x2a;" k="143" />
<hkern u1="&#x13a;" u2="&#x135;" k="-29" />
<hkern u1="&#x13a;" u2="&#x12d;" k="-31" />
<hkern u1="&#x13a;" u2="&#x12b;" k="-18" />
<hkern u1="&#x13a;" u2="&#x129;" k="-49" />
<hkern u1="&#x13a;" u2="&#xef;" k="-27" />
<hkern u1="&#x13a;" u2="&#xee;" k="-45" />
<hkern u1="&#x13a;" u2="&#xec;" k="-86" />
<hkern u1="&#x13a;" u2="&#x29;" k="23" />
<hkern u1="&#x13b;" u2="&#x2122;" k="143" />
<hkern u1="&#x13b;" u2="&#x201e;" k="-10" />
<hkern u1="&#x13b;" u2="&#xae;" k="145" />
<hkern u1="&#x13b;" u2="&#x7d;" k="6" />
<hkern u1="&#x13b;" u2="&#x7c;" k="76" />
<hkern u1="&#x13b;" u2="v" k="80" />
<hkern u1="&#x13b;" u2="f" k="8" />
<hkern u1="&#x13b;" u2="]" k="8" />
<hkern u1="&#x13b;" u2="\" k="137" />
<hkern u1="&#x13b;" u2="V" k="117" />
<hkern u1="&#x13b;" u2="&#x3f;" k="29" />
<hkern u1="&#x13b;" u2="&#x3b;" k="-10" />
<hkern u1="&#x13b;" u2="&#x2c;" k="-8" />
<hkern u1="&#x13b;" u2="&#x2a;" k="143" />
<hkern u1="&#x13c;" u2="&#x135;" k="-29" />
<hkern u1="&#x13c;" u2="&#x12d;" k="-31" />
<hkern u1="&#x13c;" u2="&#x12b;" k="-18" />
<hkern u1="&#x13c;" u2="&#x129;" k="-49" />
<hkern u1="&#x13c;" u2="&#xef;" k="-27" />
<hkern u1="&#x13c;" u2="&#xee;" k="-45" />
<hkern u1="&#x13c;" u2="&#xec;" k="-86" />
<hkern u1="&#x13c;" u2="&#x29;" k="23" />
<hkern u1="&#x13d;" u2="&#x2122;" k="111" />
<hkern u1="&#x13d;" u2="&#x201e;" k="-10" />
<hkern u1="&#x13d;" u2="&#x201d;" k="78" />
<hkern u1="&#x13d;" u2="&#x201c;" k="78" />
<hkern u1="&#x13d;" u2="&#x2019;" k="78" />
<hkern u1="&#x13d;" u2="&#x2018;" k="80" />
<hkern u1="&#x13d;" u2="&#x21a;" k="49" />
<hkern u1="&#x13d;" u2="&#x178;" k="41" />
<hkern u1="&#x13d;" u2="&#x177;" k="59" />
<hkern u1="&#x13d;" u2="&#x176;" k="41" />
<hkern u1="&#x13d;" u2="&#x174;" k="55" />
<hkern u1="&#x13d;" u2="&#x166;" k="49" />
<hkern u1="&#x13d;" u2="&#x164;" k="49" />
<hkern u1="&#x13d;" u2="&#xff;" k="59" />
<hkern u1="&#x13d;" u2="&#xfd;" k="59" />
<hkern u1="&#x13d;" u2="&#xdd;" k="41" />
<hkern u1="&#x13d;" u2="&#xae;" k="111" />
<hkern u1="&#x13d;" u2="&#x7d;" k="6" />
<hkern u1="&#x13d;" u2="&#x7c;" k="43" />
<hkern u1="&#x13d;" u2="y" k="59" />
<hkern u1="&#x13d;" u2="v" k="59" />
<hkern u1="&#x13d;" u2="f" k="8" />
<hkern u1="&#x13d;" u2="]" k="8" />
<hkern u1="&#x13d;" u2="\" k="94" />
<hkern u1="&#x13d;" u2="Y" k="41" />
<hkern u1="&#x13d;" u2="W" k="55" />
<hkern u1="&#x13d;" u2="V" k="57" />
<hkern u1="&#x13d;" u2="T" k="49" />
<hkern u1="&#x13d;" u2="&#x3f;" k="29" />
<hkern u1="&#x13d;" u2="&#x3b;" k="-10" />
<hkern u1="&#x13d;" u2="&#x2c;" k="-8" />
<hkern u1="&#x13d;" u2="&#x2a;" k="35" />
<hkern u1="&#x13d;" u2="&#x27;" k="90" />
<hkern u1="&#x13d;" u2="&#x22;" k="90" />
<hkern u1="&#x13e;" u2="&#x7d;" k="-20" />
<hkern u1="&#x13e;" u2="&#x7c;" k="-35" />
<hkern u1="&#x13e;" u2="x" k="-33" />
<hkern u1="&#x13e;" u2="v" k="-31" />
<hkern u1="&#x13e;" u2="]" k="-20" />
<hkern u1="&#x13e;" u2="&#x3f;" k="-14" />
<hkern u1="&#x13e;" u2="&#x2f;" k="53" />
<hkern u1="&#x13e;" u2="&#x2a;" k="-59" />
<hkern u1="&#x13e;" u2="&#x21;" k="-14" />
<hkern u1="&#x141;" u2="&#x2122;" k="143" />
<hkern u1="&#x141;" u2="&#x201e;" k="-10" />
<hkern u1="&#x141;" u2="&#x201a;" k="-8" />
<hkern u1="&#x141;" u2="&#xae;" k="145" />
<hkern u1="&#x141;" u2="&#x7d;" k="6" />
<hkern u1="&#x141;" u2="&#x7c;" k="76" />
<hkern u1="&#x141;" u2="v" k="80" />
<hkern u1="&#x141;" u2="f" k="8" />
<hkern u1="&#x141;" u2="]" k="8" />
<hkern u1="&#x141;" u2="\" k="137" />
<hkern u1="&#x141;" u2="V" k="117" />
<hkern u1="&#x141;" u2="&#x3f;" k="29" />
<hkern u1="&#x141;" u2="&#x3b;" k="-10" />
<hkern u1="&#x141;" u2="&#x2c;" k="-8" />
<hkern u1="&#x141;" u2="&#x2a;" k="143" />
<hkern u1="&#x142;" u2="&#x21b;" k="-31" />
<hkern u1="&#x142;" u2="&#x219;" k="-8" />
<hkern u1="&#x142;" u2="&#x167;" k="-31" />
<hkern u1="&#x142;" u2="&#x165;" k="-31" />
<hkern u1="&#x142;" u2="&#x161;" k="-8" />
<hkern u1="&#x142;" u2="&#x15f;" k="-8" />
<hkern u1="&#x142;" u2="&#x15d;" k="-8" />
<hkern u1="&#x142;" u2="&#x15b;" k="-8" />
<hkern u1="&#x142;" u2="&#x135;" k="-29" />
<hkern u1="&#x142;" u2="&#x12d;" k="-31" />
<hkern u1="&#x142;" u2="&#x12b;" k="-18" />
<hkern u1="&#x142;" u2="&#x129;" k="-49" />
<hkern u1="&#x142;" u2="&#x123;" k="-14" />
<hkern u1="&#x142;" u2="&#x121;" k="-14" />
<hkern u1="&#x142;" u2="&#x11f;" k="-14" />
<hkern u1="&#x142;" u2="&#x11d;" k="-14" />
<hkern u1="&#x142;" u2="&#xef;" k="-27" />
<hkern u1="&#x142;" u2="&#xee;" k="-45" />
<hkern u1="&#x142;" u2="&#xec;" k="-86" />
<hkern u1="&#x142;" u2="t" k="-31" />
<hkern u1="&#x142;" u2="s" k="-8" />
<hkern u1="&#x142;" u2="g" k="-14" />
<hkern u1="&#x142;" u2="&#x3b;" k="-18" />
<hkern u1="&#x142;" u2="&#x3a;" k="-18" />
<hkern u1="&#x142;" u2="&#x29;" k="23" />
<hkern u1="&#x142;" u2="&#x21;" k="-12" />
<hkern u1="&#x143;" u2="&#x135;" k="-14" />
<hkern u1="&#x143;" u2="&#x129;" k="-33" />
<hkern u1="&#x143;" u2="&#xef;" k="-12" />
<hkern u1="&#x143;" u2="&#xee;" k="-31" />
<hkern u1="&#x143;" u2="&#xec;" k="-66" />
<hkern u1="&#x143;" u2="&#x7d;" k="20" />
<hkern u1="&#x143;" u2="&#x7c;" k="18" />
<hkern u1="&#x143;" u2="f" k="8" />
<hkern u1="&#x143;" u2="]" k="25" />
<hkern u1="&#x143;" u2="&#x29;" k="31" />
<hkern u1="&#x144;" u2="&#x2122;" k="61" />
<hkern u1="&#x144;" u2="&#xae;" k="31" />
<hkern u1="&#x144;" u2="&#x7d;" k="49" />
<hkern u1="&#x144;" u2="&#x7c;" k="63" />
<hkern u1="&#x144;" u2="v" k="12" />
<hkern u1="&#x144;" u2="f" k="6" />
<hkern u1="&#x144;" u2="]" k="35" />
<hkern u1="&#x144;" u2="\" k="86" />
<hkern u1="&#x144;" u2="V" k="63" />
<hkern u1="&#x144;" u2="&#x3f;" k="41" />
<hkern u1="&#x144;" u2="&#x2a;" k="35" />
<hkern u1="&#x144;" u2="&#x29;" k="33" />
<hkern u1="&#x145;" u2="&#x135;" k="-14" />
<hkern u1="&#x145;" u2="&#x129;" k="-33" />
<hkern u1="&#x145;" u2="&#xef;" k="-12" />
<hkern u1="&#x145;" u2="&#xee;" k="-31" />
<hkern u1="&#x145;" u2="&#xec;" k="-66" />
<hkern u1="&#x145;" u2="&#x7d;" k="20" />
<hkern u1="&#x145;" u2="&#x7c;" k="18" />
<hkern u1="&#x145;" u2="f" k="8" />
<hkern u1="&#x145;" u2="]" k="25" />
<hkern u1="&#x145;" u2="&#x29;" k="31" />
<hkern u1="&#x146;" u2="&#x2122;" k="61" />
<hkern u1="&#x146;" u2="&#xae;" k="31" />
<hkern u1="&#x146;" u2="&#x7d;" k="49" />
<hkern u1="&#x146;" u2="&#x7c;" k="63" />
<hkern u1="&#x146;" u2="v" k="12" />
<hkern u1="&#x146;" u2="f" k="6" />
<hkern u1="&#x146;" u2="]" k="35" />
<hkern u1="&#x146;" u2="\" k="86" />
<hkern u1="&#x146;" u2="V" k="63" />
<hkern u1="&#x146;" u2="&#x3f;" k="41" />
<hkern u1="&#x146;" u2="&#x2a;" k="35" />
<hkern u1="&#x146;" u2="&#x29;" k="33" />
<hkern u1="&#x147;" u2="&#x135;" k="-14" />
<hkern u1="&#x147;" u2="&#x129;" k="-33" />
<hkern u1="&#x147;" u2="&#xef;" k="-12" />
<hkern u1="&#x147;" u2="&#xee;" k="-31" />
<hkern u1="&#x147;" u2="&#xec;" k="-66" />
<hkern u1="&#x147;" u2="&#x7d;" k="20" />
<hkern u1="&#x147;" u2="&#x7c;" k="18" />
<hkern u1="&#x147;" u2="f" k="8" />
<hkern u1="&#x147;" u2="]" k="25" />
<hkern u1="&#x147;" u2="&#x29;" k="31" />
<hkern u1="&#x148;" u2="&#x2122;" k="61" />
<hkern u1="&#x148;" u2="&#xae;" k="31" />
<hkern u1="&#x148;" u2="&#x7d;" k="49" />
<hkern u1="&#x148;" u2="&#x7c;" k="63" />
<hkern u1="&#x148;" u2="v" k="12" />
<hkern u1="&#x148;" u2="f" k="6" />
<hkern u1="&#x148;" u2="]" k="35" />
<hkern u1="&#x148;" u2="\" k="86" />
<hkern u1="&#x148;" u2="V" k="63" />
<hkern u1="&#x148;" u2="&#x3f;" k="41" />
<hkern u1="&#x148;" u2="&#x2a;" k="35" />
<hkern u1="&#x148;" u2="&#x29;" k="33" />
<hkern u1="&#x14a;" u2="&#x135;" k="-14" />
<hkern u1="&#x14a;" u2="&#x129;" k="-33" />
<hkern u1="&#x14a;" u2="&#xef;" k="-12" />
<hkern u1="&#x14a;" u2="&#xee;" k="-31" />
<hkern u1="&#x14a;" u2="&#xec;" k="-66" />
<hkern u1="&#x14a;" u2="&#x7d;" k="20" />
<hkern u1="&#x14a;" u2="&#x7c;" k="18" />
<hkern u1="&#x14a;" u2="f" k="8" />
<hkern u1="&#x14a;" u2="]" k="25" />
<hkern u1="&#x14a;" u2="&#x29;" k="31" />
<hkern u1="&#x14b;" u2="&#x2122;" k="61" />
<hkern u1="&#x14b;" u2="&#xae;" k="31" />
<hkern u1="&#x14b;" u2="&#x7d;" k="49" />
<hkern u1="&#x14b;" u2="&#x7c;" k="63" />
<hkern u1="&#x14b;" u2="v" k="12" />
<hkern u1="&#x14b;" u2="f" k="6" />
<hkern u1="&#x14b;" u2="]" k="35" />
<hkern u1="&#x14b;" u2="\" k="86" />
<hkern u1="&#x14b;" u2="V" k="63" />
<hkern u1="&#x14b;" u2="&#x3f;" k="41" />
<hkern u1="&#x14b;" u2="&#x2a;" k="35" />
<hkern u1="&#x14b;" u2="&#x29;" k="33" />
<hkern u1="&#x14c;" u2="&#x2122;" k="27" />
<hkern u1="&#x14c;" u2="&#x7d;" k="63" />
<hkern u1="&#x14c;" u2="&#x7c;" k="43" />
<hkern u1="&#x14c;" u2="x" k="10" />
<hkern u1="&#x14c;" u2="]" k="72" />
<hkern u1="&#x14c;" u2="\" k="45" />
<hkern u1="&#x14c;" u2="X" k="35" />
<hkern u1="&#x14c;" u2="V" k="31" />
<hkern u1="&#x14c;" u2="&#x3f;" k="10" />
<hkern u1="&#x14c;" u2="&#x2f;" k="33" />
<hkern u1="&#x14c;" u2="&#x29;" k="55" />
<hkern u1="&#x14d;" u2="&#x2122;" k="66" />
<hkern u1="&#x14d;" u2="&#xae;" k="37" />
<hkern u1="&#x14d;" u2="&#x7d;" k="63" />
<hkern u1="&#x14d;" u2="&#x7c;" k="63" />
<hkern u1="&#x14d;" u2="x" k="27" />
<hkern u1="&#x14d;" u2="v" k="20" />
<hkern u1="&#x14d;" u2="f" k="10" />
<hkern u1="&#x14d;" u2="]" k="70" />
<hkern u1="&#x14d;" u2="\" k="92" />
<hkern u1="&#x14d;" u2="X" k="20" />
<hkern u1="&#x14d;" u2="V" k="74" />
<hkern u1="&#x14d;" u2="&#x3f;" k="43" />
<hkern u1="&#x14d;" u2="&#x2a;" k="43" />
<hkern u1="&#x14d;" u2="&#x29;" k="51" />
<hkern u1="&#x14e;" u2="&#x2122;" k="27" />
<hkern u1="&#x14e;" u2="&#x7d;" k="63" />
<hkern u1="&#x14e;" u2="&#x7c;" k="43" />
<hkern u1="&#x14e;" u2="x" k="10" />
<hkern u1="&#x14e;" u2="]" k="72" />
<hkern u1="&#x14e;" u2="\" k="45" />
<hkern u1="&#x14e;" u2="X" k="35" />
<hkern u1="&#x14e;" u2="V" k="31" />
<hkern u1="&#x14e;" u2="&#x3f;" k="10" />
<hkern u1="&#x14e;" u2="&#x2f;" k="33" />
<hkern u1="&#x14e;" u2="&#x29;" k="55" />
<hkern u1="&#x14f;" u2="&#x2122;" k="66" />
<hkern u1="&#x14f;" u2="&#xae;" k="37" />
<hkern u1="&#x14f;" u2="&#x7d;" k="63" />
<hkern u1="&#x14f;" u2="&#x7c;" k="63" />
<hkern u1="&#x14f;" u2="x" k="27" />
<hkern u1="&#x14f;" u2="v" k="20" />
<hkern u1="&#x14f;" u2="f" k="10" />
<hkern u1="&#x14f;" u2="]" k="70" />
<hkern u1="&#x14f;" u2="\" k="92" />
<hkern u1="&#x14f;" u2="X" k="20" />
<hkern u1="&#x14f;" u2="V" k="74" />
<hkern u1="&#x14f;" u2="&#x3f;" k="43" />
<hkern u1="&#x14f;" u2="&#x2a;" k="43" />
<hkern u1="&#x14f;" u2="&#x29;" k="51" />
<hkern u1="&#x150;" u2="&#x2122;" k="27" />
<hkern u1="&#x150;" u2="&#x7d;" k="63" />
<hkern u1="&#x150;" u2="&#x7c;" k="43" />
<hkern u1="&#x150;" u2="x" k="10" />
<hkern u1="&#x150;" u2="]" k="72" />
<hkern u1="&#x150;" u2="\" k="45" />
<hkern u1="&#x150;" u2="X" k="35" />
<hkern u1="&#x150;" u2="V" k="31" />
<hkern u1="&#x150;" u2="&#x3f;" k="10" />
<hkern u1="&#x150;" u2="&#x2f;" k="33" />
<hkern u1="&#x150;" u2="&#x29;" k="55" />
<hkern u1="&#x151;" u2="&#x2122;" k="66" />
<hkern u1="&#x151;" u2="&#xae;" k="37" />
<hkern u1="&#x151;" u2="&#x7d;" k="63" />
<hkern u1="&#x151;" u2="&#x7c;" k="63" />
<hkern u1="&#x151;" u2="x" k="27" />
<hkern u1="&#x151;" u2="v" k="20" />
<hkern u1="&#x151;" u2="f" k="10" />
<hkern u1="&#x151;" u2="]" k="70" />
<hkern u1="&#x151;" u2="\" k="92" />
<hkern u1="&#x151;" u2="X" k="20" />
<hkern u1="&#x151;" u2="V" k="74" />
<hkern u1="&#x151;" u2="&#x3f;" k="43" />
<hkern u1="&#x151;" u2="&#x2a;" k="43" />
<hkern u1="&#x151;" u2="&#x29;" k="51" />
<hkern u1="&#x152;" u2="&#x135;" k="-61" />
<hkern u1="&#x152;" u2="&#x12d;" k="-57" />
<hkern u1="&#x152;" u2="&#x12b;" k="-55" />
<hkern u1="&#x152;" u2="&#x129;" k="-96" />
<hkern u1="&#x152;" u2="&#xef;" k="-61" />
<hkern u1="&#x152;" u2="&#xee;" k="-78" />
<hkern u1="&#x152;" u2="&#xec;" k="-133" />
<hkern u1="&#x152;" u2="v" k="14" />
<hkern u1="&#x152;" u2="&#x29;" k="14" />
<hkern u1="&#x153;" u2="&#x2122;" k="51" />
<hkern u1="&#x153;" u2="&#x142;" k="-14" />
<hkern u1="&#x153;" u2="&#x7d;" k="53" />
<hkern u1="&#x153;" u2="&#x7c;" k="51" />
<hkern u1="&#x153;" u2="v" k="12" />
<hkern u1="&#x153;" u2="]" k="47" />
<hkern u1="&#x153;" u2="\" k="72" />
<hkern u1="&#x153;" u2="V" k="53" />
<hkern u1="&#x153;" u2="&#x3f;" k="35" />
<hkern u1="&#x153;" u2="&#x2a;" k="27" />
<hkern u1="&#x153;" u2="&#x29;" k="35" />
<hkern u1="&#x154;" u2="&#xee;" k="-12" />
<hkern u1="&#x154;" u2="&#x7d;" k="37" />
<hkern u1="&#x154;" u2="&#x7c;" k="39" />
<hkern u1="&#x154;" u2="]" k="16" />
<hkern u1="&#x154;" u2="\" k="37" />
<hkern u1="&#x154;" u2="V" k="29" />
<hkern u1="&#x154;" u2="&#x3f;" k="8" />
<hkern u1="&#x154;" u2="&#x2f;" k="25" />
<hkern u1="&#x154;" u2="&#x29;" k="27" />
<hkern u1="&#x155;" u2="&#x7d;" k="49" />
<hkern u1="&#x155;" u2="&#x7c;" k="16" />
<hkern u1="&#x155;" u2="]" k="61" />
<hkern u1="&#x155;" u2="\" k="18" />
<hkern u1="&#x155;" u2="X" k="51" />
<hkern u1="&#x155;" u2="&#x3f;" k="10" />
<hkern u1="&#x155;" u2="&#x2f;" k="47" />
<hkern u1="&#x155;" u2="&#x29;" k="35" />
<hkern u1="&#x156;" u2="&#xee;" k="-12" />
<hkern u1="&#x156;" u2="&#x7d;" k="37" />
<hkern u1="&#x156;" u2="&#x7c;" k="39" />
<hkern u1="&#x156;" u2="]" k="16" />
<hkern u1="&#x156;" u2="\" k="37" />
<hkern u1="&#x156;" u2="V" k="29" />
<hkern u1="&#x156;" u2="&#x3f;" k="8" />
<hkern u1="&#x156;" u2="&#x2f;" k="25" />
<hkern u1="&#x156;" u2="&#x29;" k="27" />
<hkern u1="&#x157;" u2="&#x7d;" k="49" />
<hkern u1="&#x157;" u2="&#x7c;" k="16" />
<hkern u1="&#x157;" u2="]" k="61" />
<hkern u1="&#x157;" u2="\" k="18" />
<hkern u1="&#x157;" u2="X" k="51" />
<hkern u1="&#x157;" u2="&#x3f;" k="10" />
<hkern u1="&#x157;" u2="&#x2f;" k="47" />
<hkern u1="&#x157;" u2="&#x29;" k="35" />
<hkern u1="&#x158;" u2="&#xee;" k="-12" />
<hkern u1="&#x158;" u2="&#x7d;" k="37" />
<hkern u1="&#x158;" u2="&#x7c;" k="39" />
<hkern u1="&#x158;" u2="]" k="16" />
<hkern u1="&#x158;" u2="\" k="37" />
<hkern u1="&#x158;" u2="V" k="29" />
<hkern u1="&#x158;" u2="&#x3f;" k="8" />
<hkern u1="&#x158;" u2="&#x2f;" k="25" />
<hkern u1="&#x158;" u2="&#x29;" k="27" />
<hkern u1="&#x159;" u2="&#x159;" k="-29" />
<hkern u1="&#x159;" u2="&#x7d;" k="25" />
<hkern u1="&#x159;" u2="&#x7c;" k="16" />
<hkern u1="&#x159;" u2="]" k="27" />
<hkern u1="&#x159;" u2="\" k="18" />
<hkern u1="&#x159;" u2="X" k="51" />
<hkern u1="&#x159;" u2="&#x3f;" k="10" />
<hkern u1="&#x159;" u2="&#x2f;" k="47" />
<hkern u1="&#x159;" u2="&#x29;" k="-6" />
<hkern u1="&#x15a;" u2="&#x135;" k="-18" />
<hkern u1="&#x15a;" u2="&#x129;" k="-35" />
<hkern u1="&#x15a;" u2="&#xef;" k="-16" />
<hkern u1="&#x15a;" u2="&#xee;" k="-35" />
<hkern u1="&#x15a;" u2="&#xec;" k="-55" />
<hkern u1="&#x15a;" u2="&#xae;" k="6" />
<hkern u1="&#x15a;" u2="&#x7d;" k="27" />
<hkern u1="&#x15a;" u2="&#x7c;" k="27" />
<hkern u1="&#x15a;" u2="v" k="12" />
<hkern u1="&#x15a;" u2="f" k="8" />
<hkern u1="&#x15a;" u2="]" k="27" />
<hkern u1="&#x15a;" u2="\" k="18" />
<hkern u1="&#x15a;" u2="V" k="25" />
<hkern u1="&#x15a;" u2="&#x29;" k="35" />
<hkern u1="&#x15b;" u2="&#x2122;" k="43" />
<hkern u1="&#x15b;" u2="&#x7d;" k="57" />
<hkern u1="&#x15b;" u2="&#x7c;" k="43" />
<hkern u1="&#x15b;" u2="v" k="12" />
<hkern u1="&#x15b;" u2="]" k="55" />
<hkern u1="&#x15b;" u2="\" k="59" />
<hkern u1="&#x15b;" u2="V" k="49" />
<hkern u1="&#x15b;" u2="&#x3f;" k="29" />
<hkern u1="&#x15b;" u2="&#x2a;" k="12" />
<hkern u1="&#x15b;" u2="&#x29;" k="35" />
<hkern u1="&#x15c;" u2="&#x135;" k="-18" />
<hkern u1="&#x15c;" u2="&#x129;" k="-35" />
<hkern u1="&#x15c;" u2="&#xef;" k="-16" />
<hkern u1="&#x15c;" u2="&#xee;" k="-35" />
<hkern u1="&#x15c;" u2="&#xec;" k="-55" />
<hkern u1="&#x15c;" u2="&#xae;" k="6" />
<hkern u1="&#x15c;" u2="&#x7d;" k="27" />
<hkern u1="&#x15c;" u2="&#x7c;" k="27" />
<hkern u1="&#x15c;" u2="v" k="12" />
<hkern u1="&#x15c;" u2="f" k="8" />
<hkern u1="&#x15c;" u2="]" k="27" />
<hkern u1="&#x15c;" u2="\" k="18" />
<hkern u1="&#x15c;" u2="V" k="25" />
<hkern u1="&#x15c;" u2="&#x29;" k="35" />
<hkern u1="&#x15d;" u2="&#x2122;" k="43" />
<hkern u1="&#x15d;" u2="&#x7d;" k="57" />
<hkern u1="&#x15d;" u2="&#x7c;" k="43" />
<hkern u1="&#x15d;" u2="v" k="12" />
<hkern u1="&#x15d;" u2="]" k="55" />
<hkern u1="&#x15d;" u2="\" k="59" />
<hkern u1="&#x15d;" u2="V" k="49" />
<hkern u1="&#x15d;" u2="&#x3f;" k="29" />
<hkern u1="&#x15d;" u2="&#x2a;" k="12" />
<hkern u1="&#x15d;" u2="&#x29;" k="35" />
<hkern u1="&#x15e;" u2="&#x135;" k="-18" />
<hkern u1="&#x15e;" u2="&#x129;" k="-35" />
<hkern u1="&#x15e;" u2="&#xef;" k="-16" />
<hkern u1="&#x15e;" u2="&#xee;" k="-35" />
<hkern u1="&#x15e;" u2="&#xec;" k="-55" />
<hkern u1="&#x15e;" u2="&#xae;" k="6" />
<hkern u1="&#x15e;" u2="&#x7d;" k="27" />
<hkern u1="&#x15e;" u2="&#x7c;" k="27" />
<hkern u1="&#x15e;" u2="v" k="12" />
<hkern u1="&#x15e;" u2="f" k="8" />
<hkern u1="&#x15e;" u2="]" k="27" />
<hkern u1="&#x15e;" u2="\" k="18" />
<hkern u1="&#x15e;" u2="V" k="25" />
<hkern u1="&#x15e;" u2="&#x29;" k="35" />
<hkern u1="&#x15f;" u2="&#x2122;" k="43" />
<hkern u1="&#x15f;" u2="&#x7d;" k="57" />
<hkern u1="&#x15f;" u2="&#x7c;" k="43" />
<hkern u1="&#x15f;" u2="v" k="12" />
<hkern u1="&#x15f;" u2="]" k="55" />
<hkern u1="&#x15f;" u2="\" k="59" />
<hkern u1="&#x15f;" u2="V" k="49" />
<hkern u1="&#x15f;" u2="&#x3f;" k="29" />
<hkern u1="&#x15f;" u2="&#x2a;" k="12" />
<hkern u1="&#x15f;" u2="&#x29;" k="35" />
<hkern u1="&#x160;" u2="&#x135;" k="-18" />
<hkern u1="&#x160;" u2="&#x129;" k="-35" />
<hkern u1="&#x160;" u2="&#xef;" k="-16" />
<hkern u1="&#x160;" u2="&#xee;" k="-35" />
<hkern u1="&#x160;" u2="&#xec;" k="-55" />
<hkern u1="&#x160;" u2="&#xae;" k="6" />
<hkern u1="&#x160;" u2="&#x7d;" k="27" />
<hkern u1="&#x160;" u2="&#x7c;" k="27" />
<hkern u1="&#x160;" u2="v" k="12" />
<hkern u1="&#x160;" u2="f" k="8" />
<hkern u1="&#x160;" u2="]" k="27" />
<hkern u1="&#x160;" u2="\" k="18" />
<hkern u1="&#x160;" u2="V" k="25" />
<hkern u1="&#x160;" u2="&#x29;" k="35" />
<hkern u1="&#x161;" u2="&#x2122;" k="43" />
<hkern u1="&#x161;" u2="&#x7d;" k="57" />
<hkern u1="&#x161;" u2="&#x7c;" k="43" />
<hkern u1="&#x161;" u2="v" k="12" />
<hkern u1="&#x161;" u2="]" k="55" />
<hkern u1="&#x161;" u2="\" k="59" />
<hkern u1="&#x161;" u2="V" k="49" />
<hkern u1="&#x161;" u2="&#x3f;" k="29" />
<hkern u1="&#x161;" u2="&#x2a;" k="12" />
<hkern u1="&#x161;" u2="&#x29;" k="35" />
<hkern u1="&#x164;" u2="&#x15d;" k="98" />
<hkern u1="&#x164;" u2="&#x159;" k="76" />
<hkern u1="&#x164;" u2="&#x155;" k="49" />
<hkern u1="&#x164;" u2="&#x151;" k="104" />
<hkern u1="&#x164;" u2="&#x135;" k="-100" />
<hkern u1="&#x164;" u2="&#x131;" k="115" />
<hkern u1="&#x164;" u2="&#x12d;" k="-113" />
<hkern u1="&#x164;" u2="&#x12b;" k="-113" />
<hkern u1="&#x164;" u2="&#x129;" k="-150" />
<hkern u1="&#x164;" u2="&#x127;" k="-16" />
<hkern u1="&#x164;" u2="&#x11f;" k="129" />
<hkern u1="&#x164;" u2="&#x11d;" k="104" />
<hkern u1="&#x164;" u2="&#x109;" k="125" />
<hkern u1="&#x164;" u2="&#xf5;" k="131" />
<hkern u1="&#x164;" u2="&#xef;" k="-119" />
<hkern u1="&#x164;" u2="&#xee;" k="-117" />
<hkern u1="&#x164;" u2="&#xec;" k="-193" />
<hkern u1="&#x164;" u2="&#xea;" k="109" />
<hkern u1="&#x164;" u2="&#xe8;" k="82" />
<hkern u1="&#x164;" u2="x" k="92" />
<hkern u1="&#x164;" u2="v" k="96" />
<hkern u1="&#x164;" u2="f" k="20" />
<hkern u1="&#x164;" u2="&#x40;" k="16" />
<hkern u1="&#x164;" u2="&#x2f;" k="94" />
<hkern u1="&#x164;" u2="&#x2a;" k="-33" />
<hkern u1="&#x164;" u2="&#x26;" k="29" />
<hkern u1="&#x165;" u2="&#x203a;" k="18" />
<hkern u1="&#x165;" u2="&#x2039;" k="94" />
<hkern u1="&#x165;" u2="&#x2026;" k="72" />
<hkern u1="&#x165;" u2="&#x201e;" k="72" />
<hkern u1="&#x165;" u2="&#x201d;" k="-29" />
<hkern u1="&#x165;" u2="&#x201c;" k="-27" />
<hkern u1="&#x165;" u2="&#x201a;" k="72" />
<hkern u1="&#x165;" u2="&#x2019;" k="-29" />
<hkern u1="&#x165;" u2="&#x2018;" k="-27" />
<hkern u1="&#x165;" u2="&#x2014;" k="82" />
<hkern u1="&#x165;" u2="&#x2013;" k="82" />
<hkern u1="&#x165;" u2="&#x21b;" k="-20" />
<hkern u1="&#x165;" u2="&#x1ff;" k="23" />
<hkern u1="&#x165;" u2="&#x1fd;" k="23" />
<hkern u1="&#x165;" u2="&#x1fb;" k="23" />
<hkern u1="&#x165;" u2="&#x177;" k="-31" />
<hkern u1="&#x165;" u2="&#x175;" k="-16" />
<hkern u1="&#x165;" u2="&#x167;" k="-20" />
<hkern u1="&#x165;" u2="&#x165;" k="-20" />
<hkern u1="&#x165;" u2="&#x153;" k="23" />
<hkern u1="&#x165;" u2="&#x151;" k="23" />
<hkern u1="&#x165;" u2="&#x14f;" k="23" />
<hkern u1="&#x165;" u2="&#x14d;" k="23" />
<hkern u1="&#x165;" u2="&#x142;" k="-14" />
<hkern u1="&#x165;" u2="&#x13e;" k="-14" />
<hkern u1="&#x165;" u2="&#x13c;" k="-14" />
<hkern u1="&#x165;" u2="&#x13a;" k="-14" />
<hkern u1="&#x165;" u2="&#x137;" k="-14" />
<hkern u1="&#x165;" u2="&#x135;" k="-14" />
<hkern u1="&#x165;" u2="&#x131;" k="-14" />
<hkern u1="&#x165;" u2="&#x12f;" k="-14" />
<hkern u1="&#x165;" u2="&#x12d;" k="-14" />
<hkern u1="&#x165;" u2="&#x12b;" k="-14" />
<hkern u1="&#x165;" u2="&#x129;" k="-14" />
<hkern u1="&#x165;" u2="&#x127;" k="-14" />
<hkern u1="&#x165;" u2="&#x125;" k="-14" />
<hkern u1="&#x165;" u2="&#x11b;" k="23" />
<hkern u1="&#x165;" u2="&#x119;" k="23" />
<hkern u1="&#x165;" u2="&#x117;" k="23" />
<hkern u1="&#x165;" u2="&#x115;" k="23" />
<hkern u1="&#x165;" u2="&#x113;" k="23" />
<hkern u1="&#x165;" u2="&#x111;" k="23" />
<hkern u1="&#x165;" u2="&#x10f;" k="23" />
<hkern u1="&#x165;" u2="&#x10d;" k="23" />
<hkern u1="&#x165;" u2="&#x10b;" k="23" />
<hkern u1="&#x165;" u2="&#x109;" k="23" />
<hkern u1="&#x165;" u2="&#x107;" k="23" />
<hkern u1="&#x165;" u2="&#x105;" k="23" />
<hkern u1="&#x165;" u2="&#x103;" k="23" />
<hkern u1="&#x165;" u2="&#x101;" k="23" />
<hkern u1="&#x165;" u2="&#xff;" k="-31" />
<hkern u1="&#x165;" u2="&#xfd;" k="-31" />
<hkern u1="&#x165;" u2="&#xf8;" k="23" />
<hkern u1="&#x165;" u2="&#xf6;" k="23" />
<hkern u1="&#x165;" u2="&#xf5;" k="23" />
<hkern u1="&#x165;" u2="&#xf4;" k="23" />
<hkern u1="&#x165;" u2="&#xf3;" k="23" />
<hkern u1="&#x165;" u2="&#xf2;" k="23" />
<hkern u1="&#x165;" u2="&#xef;" k="-14" />
<hkern u1="&#x165;" u2="&#xee;" k="-14" />
<hkern u1="&#x165;" u2="&#xed;" k="-14" />
<hkern u1="&#x165;" u2="&#xec;" k="-14" />
<hkern u1="&#x165;" u2="&#xeb;" k="23" />
<hkern u1="&#x165;" u2="&#xea;" k="23" />
<hkern u1="&#x165;" u2="&#xe9;" k="23" />
<hkern u1="&#x165;" u2="&#xe8;" k="23" />
<hkern u1="&#x165;" u2="&#xe7;" k="23" />
<hkern u1="&#x165;" u2="&#xe6;" k="23" />
<hkern u1="&#x165;" u2="&#xe5;" k="23" />
<hkern u1="&#x165;" u2="&#xe4;" k="23" />
<hkern u1="&#x165;" u2="&#xe3;" k="23" />
<hkern u1="&#x165;" u2="&#xe2;" k="23" />
<hkern u1="&#x165;" u2="&#xe1;" k="23" />
<hkern u1="&#x165;" u2="&#xe0;" k="23" />
<hkern u1="&#x165;" u2="&#xdf;" k="-14" />
<hkern u1="&#x165;" u2="&#xbb;" k="18" />
<hkern u1="&#x165;" u2="&#xab;" k="94" />
<hkern u1="&#x165;" u2="&#x7d;" k="-20" />
<hkern u1="&#x165;" u2="&#x7c;" k="-31" />
<hkern u1="&#x165;" u2="y" k="-31" />
<hkern u1="&#x165;" u2="x" k="-33" />
<hkern u1="&#x165;" u2="w" k="-16" />
<hkern u1="&#x165;" u2="v" k="-31" />
<hkern u1="&#x165;" u2="t" k="-20" />
<hkern u1="&#x165;" u2="q" k="23" />
<hkern u1="&#x165;" u2="o" k="23" />
<hkern u1="&#x165;" u2="l" k="-14" />
<hkern u1="&#x165;" u2="k" k="-14" />
<hkern u1="&#x165;" u2="j" k="-14" />
<hkern u1="&#x165;" u2="i" k="-14" />
<hkern u1="&#x165;" u2="h" k="-14" />
<hkern u1="&#x165;" u2="e" k="23" />
<hkern u1="&#x165;" u2="d" k="23" />
<hkern u1="&#x165;" u2="c" k="23" />
<hkern u1="&#x165;" u2="b" k="-16" />
<hkern u1="&#x165;" u2="a" k="23" />
<hkern u1="&#x165;" u2="]" k="-20" />
<hkern u1="&#x165;" u2="&#x3f;" k="-14" />
<hkern u1="&#x165;" u2="&#x3b;" k="-8" />
<hkern u1="&#x165;" u2="&#x3a;" k="-8" />
<hkern u1="&#x165;" u2="&#x2f;" k="49" />
<hkern u1="&#x165;" u2="&#x2e;" k="72" />
<hkern u1="&#x165;" u2="&#x2d;" k="82" />
<hkern u1="&#x165;" u2="&#x2c;" k="72" />
<hkern u1="&#x165;" u2="&#x2a;" k="-55" />
<hkern u1="&#x165;" u2="&#x27;" k="-18" />
<hkern u1="&#x165;" u2="&#x22;" k="-18" />
<hkern u1="&#x165;" u2="&#x21;" k="-14" />
<hkern u1="&#x166;" u2="&#x2039;" k="86" />
<hkern u1="&#x166;" u2="&#x2014;" k="74" />
<hkern u1="&#x166;" u2="&#x2013;" k="74" />
<hkern u1="&#x166;" u2="&#x1ff;" k="133" />
<hkern u1="&#x166;" u2="&#x15d;" k="98" />
<hkern u1="&#x166;" u2="&#x159;" k="76" />
<hkern u1="&#x166;" u2="&#x155;" k="49" />
<hkern u1="&#x166;" u2="&#x153;" k="133" />
<hkern u1="&#x166;" u2="&#x151;" k="104" />
<hkern u1="&#x166;" u2="&#x14f;" k="133" />
<hkern u1="&#x166;" u2="&#x14d;" k="133" />
<hkern u1="&#x166;" u2="&#x141;" k="-12" />
<hkern u1="&#x166;" u2="&#x135;" k="-100" />
<hkern u1="&#x166;" u2="&#x131;" k="115" />
<hkern u1="&#x166;" u2="&#x12d;" k="-113" />
<hkern u1="&#x166;" u2="&#x12b;" k="-113" />
<hkern u1="&#x166;" u2="&#x129;" k="-150" />
<hkern u1="&#x166;" u2="&#x127;" k="-16" />
<hkern u1="&#x166;" u2="&#x11f;" k="129" />
<hkern u1="&#x166;" u2="&#x11d;" k="104" />
<hkern u1="&#x166;" u2="&#x11b;" k="133" />
<hkern u1="&#x166;" u2="&#x119;" k="133" />
<hkern u1="&#x166;" u2="&#x117;" k="133" />
<hkern u1="&#x166;" u2="&#x115;" k="133" />
<hkern u1="&#x166;" u2="&#x113;" k="133" />
<hkern u1="&#x166;" u2="&#x10d;" k="133" />
<hkern u1="&#x166;" u2="&#x10b;" k="133" />
<hkern u1="&#x166;" u2="&#x109;" k="125" />
<hkern u1="&#x166;" u2="&#x107;" k="133" />
<hkern u1="&#x166;" u2="&#xf8;" k="133" />
<hkern u1="&#x166;" u2="&#xf6;" k="133" />
<hkern u1="&#x166;" u2="&#xf5;" k="131" />
<hkern u1="&#x166;" u2="&#xf4;" k="133" />
<hkern u1="&#x166;" u2="&#xf3;" k="133" />
<hkern u1="&#x166;" u2="&#xf2;" k="133" />
<hkern u1="&#x166;" u2="&#xef;" k="-119" />
<hkern u1="&#x166;" u2="&#xee;" k="-117" />
<hkern u1="&#x166;" u2="&#xec;" k="-193" />
<hkern u1="&#x166;" u2="&#xeb;" k="133" />
<hkern u1="&#x166;" u2="&#xea;" k="109" />
<hkern u1="&#x166;" u2="&#xe9;" k="133" />
<hkern u1="&#x166;" u2="&#xe8;" k="82" />
<hkern u1="&#x166;" u2="&#xe7;" k="133" />
<hkern u1="&#x166;" u2="&#xab;" k="86" />
<hkern u1="&#x166;" u2="x" k="92" />
<hkern u1="&#x166;" u2="v" k="96" />
<hkern u1="&#x166;" u2="o" k="133" />
<hkern u1="&#x166;" u2="f" k="20" />
<hkern u1="&#x166;" u2="e" k="133" />
<hkern u1="&#x166;" u2="c" k="133" />
<hkern u1="&#x166;" u2="&#x40;" k="16" />
<hkern u1="&#x166;" u2="&#x2f;" k="94" />
<hkern u1="&#x166;" u2="&#x2d;" k="84" />
<hkern u1="&#x166;" u2="&#x2a;" k="-33" />
<hkern u1="&#x166;" u2="&#x26;" k="29" />
<hkern u1="&#x167;" u2="&#x2122;" k="16" />
<hkern u1="&#x167;" u2="&#x7d;" k="20" />
<hkern u1="&#x167;" u2="&#x7c;" k="27" />
<hkern u1="&#x167;" u2="]" k="8" />
<hkern u1="&#x167;" u2="\" k="27" />
<hkern u1="&#x167;" u2="V" k="8" />
<hkern u1="&#x167;" u2="&#x3f;" k="6" />
<hkern u1="&#x167;" u2="&#x29;" k="16" />
<hkern u1="&#x168;" u2="&#x135;" k="-23" />
<hkern u1="&#x168;" u2="&#x12d;" k="-14" />
<hkern u1="&#x168;" u2="&#x12b;" k="-10" />
<hkern u1="&#x168;" u2="&#x129;" k="-41" />
<hkern u1="&#x168;" u2="&#xef;" k="-20" />
<hkern u1="&#x168;" u2="&#xee;" k="-39" />
<hkern u1="&#x168;" u2="&#xec;" k="-78" />
<hkern u1="&#x168;" u2="&#x7d;" k="18" />
<hkern u1="&#x168;" u2="&#x7c;" k="14" />
<hkern u1="&#x168;" u2="f" k="8" />
<hkern u1="&#x168;" u2="]" k="23" />
<hkern u1="&#x168;" u2="&#x2f;" k="29" />
<hkern u1="&#x168;" u2="&#x29;" k="31" />
<hkern u1="&#x169;" u2="&#x2122;" k="39" />
<hkern u1="&#x169;" u2="&#x7d;" k="51" />
<hkern u1="&#x169;" u2="&#x7c;" k="45" />
<hkern u1="&#x169;" u2="]" k="39" />
<hkern u1="&#x169;" u2="\" k="55" />
<hkern u1="&#x169;" u2="V" k="51" />
<hkern u1="&#x169;" u2="&#x3f;" k="31" />
<hkern u1="&#x169;" u2="&#x29;" k="35" />
<hkern u1="&#x16a;" u2="&#x135;" k="-23" />
<hkern u1="&#x16a;" u2="&#x12d;" k="-14" />
<hkern u1="&#x16a;" u2="&#x12b;" k="-10" />
<hkern u1="&#x16a;" u2="&#x129;" k="-41" />
<hkern u1="&#x16a;" u2="&#xef;" k="-20" />
<hkern u1="&#x16a;" u2="&#xee;" k="-39" />
<hkern u1="&#x16a;" u2="&#xec;" k="-78" />
<hkern u1="&#x16a;" u2="&#x7d;" k="18" />
<hkern u1="&#x16a;" u2="&#x7c;" k="14" />
<hkern u1="&#x16a;" u2="f" k="8" />
<hkern u1="&#x16a;" u2="]" k="23" />
<hkern u1="&#x16a;" u2="&#x2f;" k="29" />
<hkern u1="&#x16a;" u2="&#x29;" k="31" />
<hkern u1="&#x16b;" u2="&#x2122;" k="39" />
<hkern u1="&#x16b;" u2="&#x7d;" k="51" />
<hkern u1="&#x16b;" u2="&#x7c;" k="45" />
<hkern u1="&#x16b;" u2="]" k="39" />
<hkern u1="&#x16b;" u2="\" k="55" />
<hkern u1="&#x16b;" u2="V" k="51" />
<hkern u1="&#x16b;" u2="&#x3f;" k="31" />
<hkern u1="&#x16b;" u2="&#x29;" k="35" />
<hkern u1="&#x16c;" u2="&#x135;" k="-23" />
<hkern u1="&#x16c;" u2="&#x12d;" k="-14" />
<hkern u1="&#x16c;" u2="&#x12b;" k="-10" />
<hkern u1="&#x16c;" u2="&#x129;" k="-41" />
<hkern u1="&#x16c;" u2="&#xef;" k="-20" />
<hkern u1="&#x16c;" u2="&#xee;" k="-39" />
<hkern u1="&#x16c;" u2="&#xec;" k="-78" />
<hkern u1="&#x16c;" u2="&#x7d;" k="18" />
<hkern u1="&#x16c;" u2="&#x7c;" k="14" />
<hkern u1="&#x16c;" u2="f" k="8" />
<hkern u1="&#x16c;" u2="]" k="23" />
<hkern u1="&#x16c;" u2="&#x2f;" k="29" />
<hkern u1="&#x16c;" u2="&#x29;" k="31" />
<hkern u1="&#x16d;" u2="&#x2122;" k="39" />
<hkern u1="&#x16d;" u2="&#x7d;" k="51" />
<hkern u1="&#x16d;" u2="&#x7c;" k="45" />
<hkern u1="&#x16d;" u2="]" k="39" />
<hkern u1="&#x16d;" u2="\" k="55" />
<hkern u1="&#x16d;" u2="V" k="51" />
<hkern u1="&#x16d;" u2="&#x3f;" k="31" />
<hkern u1="&#x16d;" u2="&#x29;" k="35" />
<hkern u1="&#x16e;" u2="&#x135;" k="-23" />
<hkern u1="&#x16e;" u2="&#x12d;" k="-14" />
<hkern u1="&#x16e;" u2="&#x12b;" k="-10" />
<hkern u1="&#x16e;" u2="&#x129;" k="-41" />
<hkern u1="&#x16e;" u2="&#xef;" k="-20" />
<hkern u1="&#x16e;" u2="&#xee;" k="-39" />
<hkern u1="&#x16e;" u2="&#xec;" k="-78" />
<hkern u1="&#x16e;" u2="&#x7d;" k="18" />
<hkern u1="&#x16e;" u2="&#x7c;" k="14" />
<hkern u1="&#x16e;" u2="f" k="8" />
<hkern u1="&#x16e;" u2="]" k="23" />
<hkern u1="&#x16e;" u2="&#x2f;" k="29" />
<hkern u1="&#x16e;" u2="&#x29;" k="31" />
<hkern u1="&#x16f;" u2="&#x2122;" k="39" />
<hkern u1="&#x16f;" u2="&#x7d;" k="51" />
<hkern u1="&#x16f;" u2="&#x7c;" k="45" />
<hkern u1="&#x16f;" u2="]" k="39" />
<hkern u1="&#x16f;" u2="\" k="55" />
<hkern u1="&#x16f;" u2="V" k="51" />
<hkern u1="&#x16f;" u2="&#x3f;" k="31" />
<hkern u1="&#x16f;" u2="&#x29;" k="35" />
<hkern u1="&#x170;" u2="&#x135;" k="-23" />
<hkern u1="&#x170;" u2="&#x12d;" k="-14" />
<hkern u1="&#x170;" u2="&#x12b;" k="-10" />
<hkern u1="&#x170;" u2="&#x129;" k="-41" />
<hkern u1="&#x170;" u2="&#xef;" k="-20" />
<hkern u1="&#x170;" u2="&#xee;" k="-39" />
<hkern u1="&#x170;" u2="&#xec;" k="-78" />
<hkern u1="&#x170;" u2="&#x7d;" k="18" />
<hkern u1="&#x170;" u2="&#x7c;" k="14" />
<hkern u1="&#x170;" u2="f" k="8" />
<hkern u1="&#x170;" u2="]" k="23" />
<hkern u1="&#x170;" u2="&#x2f;" k="29" />
<hkern u1="&#x170;" u2="&#x29;" k="31" />
<hkern u1="&#x171;" u2="&#x2122;" k="39" />
<hkern u1="&#x171;" u2="&#x7d;" k="51" />
<hkern u1="&#x171;" u2="&#x7c;" k="45" />
<hkern u1="&#x171;" u2="]" k="39" />
<hkern u1="&#x171;" u2="\" k="55" />
<hkern u1="&#x171;" u2="V" k="51" />
<hkern u1="&#x171;" u2="&#x3f;" k="31" />
<hkern u1="&#x171;" u2="&#x29;" k="35" />
<hkern u1="&#x172;" u2="&#x135;" k="-23" />
<hkern u1="&#x172;" u2="&#x12d;" k="-14" />
<hkern u1="&#x172;" u2="&#x12b;" k="-10" />
<hkern u1="&#x172;" u2="&#x129;" k="-41" />
<hkern u1="&#x172;" u2="&#xef;" k="-20" />
<hkern u1="&#x172;" u2="&#xee;" k="-39" />
<hkern u1="&#x172;" u2="&#xec;" k="-78" />
<hkern u1="&#x172;" u2="&#x7d;" k="18" />
<hkern u1="&#x172;" u2="&#x7c;" k="14" />
<hkern u1="&#x172;" u2="f" k="8" />
<hkern u1="&#x172;" u2="]" k="23" />
<hkern u1="&#x172;" u2="&#x2f;" k="29" />
<hkern u1="&#x172;" u2="&#x29;" k="31" />
<hkern u1="&#x173;" u2="&#x2122;" k="39" />
<hkern u1="&#x173;" u2="&#x201e;" k="-16" />
<hkern u1="&#x173;" u2="&#x7d;" k="51" />
<hkern u1="&#x173;" u2="&#x7c;" k="45" />
<hkern u1="&#x173;" u2="j" k="-14" />
<hkern u1="&#x173;" u2="]" k="39" />
<hkern u1="&#x173;" u2="\" k="55" />
<hkern u1="&#x173;" u2="V" k="51" />
<hkern u1="&#x173;" u2="&#x3f;" k="31" />
<hkern u1="&#x173;" u2="&#x29;" k="35" />
<hkern u1="&#x174;" u2="&#x159;" k="20" />
<hkern u1="&#x174;" u2="&#x155;" k="20" />
<hkern u1="&#x174;" u2="&#x135;" k="-59" />
<hkern u1="&#x174;" u2="&#x131;" k="23" />
<hkern u1="&#x174;" u2="&#x12d;" k="-86" />
<hkern u1="&#x174;" u2="&#x12b;" k="-82" />
<hkern u1="&#x174;" u2="&#x129;" k="-123" />
<hkern u1="&#x174;" u2="&#xef;" k="-90" />
<hkern u1="&#x174;" u2="&#xee;" k="-76" />
<hkern u1="&#x174;" u2="&#xec;" k="-145" />
<hkern u1="&#x174;" u2="&#xae;" k="8" />
<hkern u1="&#x174;" u2="x" k="10" />
<hkern u1="&#x174;" u2="v" k="12" />
<hkern u1="&#x174;" u2="&#x40;" k="33" />
<hkern u1="&#x174;" u2="&#x2f;" k="78" />
<hkern u1="&#x174;" u2="&#x2a;" k="-27" />
<hkern u1="&#x174;" u2="&#x29;" k="18" />
<hkern u1="&#x174;" u2="&#x26;" k="39" />
<hkern u1="&#x175;" u2="&#x2122;" k="27" />
<hkern u1="&#x175;" u2="&#x7d;" k="57" />
<hkern u1="&#x175;" u2="&#x7c;" k="25" />
<hkern u1="&#x175;" u2="]" k="66" />
<hkern u1="&#x175;" u2="\" k="37" />
<hkern u1="&#x175;" u2="X" k="29" />
<hkern u1="&#x175;" u2="V" k="18" />
<hkern u1="&#x175;" u2="&#x3f;" k="10" />
<hkern u1="&#x175;" u2="&#x2f;" k="27" />
<hkern u1="&#x175;" u2="&#x29;" k="45" />
<hkern u1="&#x176;" u2="&#x17a;" k="84" />
<hkern u1="&#x176;" u2="&#x177;" k="51" />
<hkern u1="&#x176;" u2="&#x171;" k="94" />
<hkern u1="&#x176;" u2="&#x16b;" k="94" />
<hkern u1="&#x176;" u2="&#x169;" k="88" />
<hkern u1="&#x176;" u2="&#x159;" k="53" />
<hkern u1="&#x176;" u2="&#x155;" k="14" />
<hkern u1="&#x176;" u2="&#x151;" k="96" />
<hkern u1="&#x176;" u2="&#x14f;" k="100" />
<hkern u1="&#x176;" u2="&#x14d;" k="121" />
<hkern u1="&#x176;" u2="&#x135;" k="-43" />
<hkern u1="&#x176;" u2="&#x131;" k="113" />
<hkern u1="&#x176;" u2="&#x12d;" k="-125" />
<hkern u1="&#x176;" u2="&#x12b;" k="-117" />
<hkern u1="&#x176;" u2="&#x129;" k="-156" />
<hkern u1="&#x176;" u2="&#x127;" k="-14" />
<hkern u1="&#x176;" u2="&#x11f;" k="104" />
<hkern u1="&#x176;" u2="&#x113;" k="111" />
<hkern u1="&#x176;" u2="&#xff;" k="43" />
<hkern u1="&#x176;" u2="&#xfc;" k="90" />
<hkern u1="&#x176;" u2="&#xf5;" k="100" />
<hkern u1="&#x176;" u2="&#xf2;" k="117" />
<hkern u1="&#x176;" u2="&#xf1;" k="111" />
<hkern u1="&#x176;" u2="&#xef;" k="-129" />
<hkern u1="&#x176;" u2="&#xee;" k="-57" />
<hkern u1="&#x176;" u2="&#xed;" k="-10" />
<hkern u1="&#x176;" u2="&#xec;" k="-172" />
<hkern u1="&#x176;" u2="&#xeb;" k="109" />
<hkern u1="&#x176;" u2="&#xea;" k="125" />
<hkern u1="&#x176;" u2="&#xe8;" k="72" />
<hkern u1="&#x176;" u2="&#xae;" k="39" />
<hkern u1="&#x176;" u2="&#x7d;" k="-8" />
<hkern u1="&#x176;" u2="&#x7c;" k="-18" />
<hkern u1="&#x176;" u2="x" k="70" />
<hkern u1="&#x176;" u2="v" k="72" />
<hkern u1="&#x176;" u2="f" k="31" />
<hkern u1="&#x176;" u2="]" k="-8" />
<hkern u1="&#x176;" u2="&#x40;" k="59" />
<hkern u1="&#x176;" u2="&#x2f;" k="135" />
<hkern u1="&#x176;" u2="&#x2a;" k="-33" />
<hkern u1="&#x176;" u2="&#x26;" k="70" />
<hkern u1="&#x177;" u2="&#x2122;" k="25" />
<hkern u1="&#x177;" u2="&#x7d;" k="55" />
<hkern u1="&#x177;" u2="&#x7c;" k="27" />
<hkern u1="&#x177;" u2="]" k="66" />
<hkern u1="&#x177;" u2="\" k="41" />
<hkern u1="&#x177;" u2="X" k="31" />
<hkern u1="&#x177;" u2="V" k="16" />
<hkern u1="&#x177;" u2="&#x3f;" k="10" />
<hkern u1="&#x177;" u2="&#x2f;" k="39" />
<hkern u1="&#x177;" u2="&#x29;" k="39" />
<hkern u1="&#x178;" u2="&#x17a;" k="84" />
<hkern u1="&#x178;" u2="&#x177;" k="51" />
<hkern u1="&#x178;" u2="&#x171;" k="94" />
<hkern u1="&#x178;" u2="&#x16b;" k="94" />
<hkern u1="&#x178;" u2="&#x169;" k="88" />
<hkern u1="&#x178;" u2="&#x159;" k="53" />
<hkern u1="&#x178;" u2="&#x155;" k="14" />
<hkern u1="&#x178;" u2="&#x151;" k="96" />
<hkern u1="&#x178;" u2="&#x14f;" k="100" />
<hkern u1="&#x178;" u2="&#x14d;" k="121" />
<hkern u1="&#x178;" u2="&#x135;" k="-43" />
<hkern u1="&#x178;" u2="&#x131;" k="113" />
<hkern u1="&#x178;" u2="&#x12d;" k="-125" />
<hkern u1="&#x178;" u2="&#x12b;" k="-117" />
<hkern u1="&#x178;" u2="&#x129;" k="-156" />
<hkern u1="&#x178;" u2="&#x127;" k="-14" />
<hkern u1="&#x178;" u2="&#x11f;" k="104" />
<hkern u1="&#x178;" u2="&#x113;" k="111" />
<hkern u1="&#x178;" u2="&#xff;" k="43" />
<hkern u1="&#x178;" u2="&#xfc;" k="90" />
<hkern u1="&#x178;" u2="&#xf5;" k="100" />
<hkern u1="&#x178;" u2="&#xf2;" k="117" />
<hkern u1="&#x178;" u2="&#xf1;" k="111" />
<hkern u1="&#x178;" u2="&#xef;" k="-129" />
<hkern u1="&#x178;" u2="&#xee;" k="-57" />
<hkern u1="&#x178;" u2="&#xed;" k="-10" />
<hkern u1="&#x178;" u2="&#xec;" k="-172" />
<hkern u1="&#x178;" u2="&#xeb;" k="109" />
<hkern u1="&#x178;" u2="&#xea;" k="125" />
<hkern u1="&#x178;" u2="&#xe8;" k="72" />
<hkern u1="&#x178;" u2="&#xae;" k="39" />
<hkern u1="&#x178;" u2="&#x7d;" k="-8" />
<hkern u1="&#x178;" u2="&#x7c;" k="-18" />
<hkern u1="&#x178;" u2="x" k="70" />
<hkern u1="&#x178;" u2="v" k="72" />
<hkern u1="&#x178;" u2="f" k="31" />
<hkern u1="&#x178;" u2="]" k="-8" />
<hkern u1="&#x178;" u2="&#x40;" k="59" />
<hkern u1="&#x178;" u2="&#x2f;" k="135" />
<hkern u1="&#x178;" u2="&#x2a;" k="-33" />
<hkern u1="&#x178;" u2="&#x26;" k="70" />
<hkern u1="&#x179;" u2="&#x135;" k="-66" />
<hkern u1="&#x179;" u2="&#x12d;" k="-57" />
<hkern u1="&#x179;" u2="&#x12b;" k="-57" />
<hkern u1="&#x179;" u2="&#x129;" k="-98" />
<hkern u1="&#x179;" u2="&#xef;" k="-61" />
<hkern u1="&#x179;" u2="&#xee;" k="-84" />
<hkern u1="&#x179;" u2="&#xec;" k="-133" />
<hkern u1="&#x179;" u2="&#x29;" k="18" />
<hkern u1="&#x17a;" u2="&#x2122;" k="33" />
<hkern u1="&#x17a;" u2="&#x7d;" k="41" />
<hkern u1="&#x17a;" u2="&#x7c;" k="33" />
<hkern u1="&#x17a;" u2="]" k="14" />
<hkern u1="&#x17a;" u2="\" k="43" />
<hkern u1="&#x17a;" u2="V" k="27" />
<hkern u1="&#x17a;" u2="&#x3f;" k="8" />
<hkern u1="&#x17a;" u2="&#x29;" k="29" />
<hkern u1="&#x17b;" u2="&#x135;" k="-66" />
<hkern u1="&#x17b;" u2="&#x12d;" k="-57" />
<hkern u1="&#x17b;" u2="&#x12b;" k="-57" />
<hkern u1="&#x17b;" u2="&#x129;" k="-98" />
<hkern u1="&#x17b;" u2="&#xef;" k="-61" />
<hkern u1="&#x17b;" u2="&#xee;" k="-84" />
<hkern u1="&#x17b;" u2="&#xec;" k="-133" />
<hkern u1="&#x17b;" u2="&#x29;" k="18" />
<hkern u1="&#x17c;" u2="&#x2122;" k="33" />
<hkern u1="&#x17c;" u2="&#x7d;" k="41" />
<hkern u1="&#x17c;" u2="&#x7c;" k="33" />
<hkern u1="&#x17c;" u2="]" k="14" />
<hkern u1="&#x17c;" u2="\" k="43" />
<hkern u1="&#x17c;" u2="V" k="27" />
<hkern u1="&#x17c;" u2="&#x3f;" k="8" />
<hkern u1="&#x17c;" u2="&#x29;" k="29" />
<hkern u1="&#x17d;" u2="&#x135;" k="-66" />
<hkern u1="&#x17d;" u2="&#x12d;" k="-57" />
<hkern u1="&#x17d;" u2="&#x12b;" k="-57" />
<hkern u1="&#x17d;" u2="&#x129;" k="-98" />
<hkern u1="&#x17d;" u2="&#xef;" k="-61" />
<hkern u1="&#x17d;" u2="&#xee;" k="-84" />
<hkern u1="&#x17d;" u2="&#xec;" k="-133" />
<hkern u1="&#x17d;" u2="&#x29;" k="18" />
<hkern u1="&#x17e;" u2="&#x2122;" k="33" />
<hkern u1="&#x17e;" u2="&#x12b;" k="-20" />
<hkern u1="&#x17e;" u2="&#x7d;" k="41" />
<hkern u1="&#x17e;" u2="&#x7c;" k="33" />
<hkern u1="&#x17e;" u2="]" k="14" />
<hkern u1="&#x17e;" u2="\" k="43" />
<hkern u1="&#x17e;" u2="V" k="27" />
<hkern u1="&#x17e;" u2="&#x3f;" k="8" />
<hkern u1="&#x17e;" u2="&#x29;" k="29" />
<hkern u1="&#x1fa;" u2="&#x2122;" k="78" />
<hkern u1="&#x1fa;" u2="&#xae;" k="66" />
<hkern u1="&#x1fa;" u2="&#x7d;" k="35" />
<hkern u1="&#x1fa;" u2="&#x7c;" k="88" />
<hkern u1="&#x1fa;" u2="v" k="37" />
<hkern u1="&#x1fa;" u2="f" k="14" />
<hkern u1="&#x1fa;" u2="]" k="16" />
<hkern u1="&#x1fa;" u2="\" k="92" />
<hkern u1="&#x1fa;" u2="V" k="66" />
<hkern u1="&#x1fa;" u2="&#x40;" k="6" />
<hkern u1="&#x1fa;" u2="&#x3f;" k="43" />
<hkern u1="&#x1fa;" u2="&#x2a;" k="72" />
<hkern u1="&#x1fa;" u2="&#x29;" k="25" />
<hkern u1="&#x1fa;" u2="&#x26;" k="6" />
<hkern u1="&#x1fb;" u2="&#x2122;" k="45" />
<hkern u1="&#x1fb;" u2="&#x7d;" k="49" />
<hkern u1="&#x1fb;" u2="&#x7c;" k="47" />
<hkern u1="&#x1fb;" u2="]" k="43" />
<hkern u1="&#x1fb;" u2="\" k="61" />
<hkern u1="&#x1fb;" u2="V" k="55" />
<hkern u1="&#x1fb;" u2="&#x3f;" k="33" />
<hkern u1="&#x1fb;" u2="&#x2a;" k="16" />
<hkern u1="&#x1fb;" u2="&#x29;" k="33" />
<hkern u1="&#x1fc;" u2="&#x135;" k="-61" />
<hkern u1="&#x1fc;" u2="&#x12d;" k="-57" />
<hkern u1="&#x1fc;" u2="&#x12b;" k="-55" />
<hkern u1="&#x1fc;" u2="&#x129;" k="-96" />
<hkern u1="&#x1fc;" u2="&#xef;" k="-61" />
<hkern u1="&#x1fc;" u2="&#xee;" k="-78" />
<hkern u1="&#x1fc;" u2="&#xec;" k="-133" />
<hkern u1="&#x1fc;" u2="v" k="14" />
<hkern u1="&#x1fc;" u2="&#x29;" k="14" />
<hkern u1="&#x1fd;" u2="&#x2122;" k="51" />
<hkern u1="&#x1fd;" u2="&#x142;" k="-20" />
<hkern u1="&#x1fd;" u2="&#x7d;" k="53" />
<hkern u1="&#x1fd;" u2="&#x7c;" k="51" />
<hkern u1="&#x1fd;" u2="v" k="12" />
<hkern u1="&#x1fd;" u2="]" k="47" />
<hkern u1="&#x1fd;" u2="\" k="72" />
<hkern u1="&#x1fd;" u2="V" k="53" />
<hkern u1="&#x1fd;" u2="&#x3f;" k="35" />
<hkern u1="&#x1fd;" u2="&#x2a;" k="27" />
<hkern u1="&#x1fd;" u2="&#x29;" k="35" />
<hkern u1="&#x1fe;" u2="&#x2122;" k="27" />
<hkern u1="&#x1fe;" u2="&#x7d;" k="63" />
<hkern u1="&#x1fe;" u2="&#x7c;" k="43" />
<hkern u1="&#x1fe;" u2="x" k="10" />
<hkern u1="&#x1fe;" u2="]" k="72" />
<hkern u1="&#x1fe;" u2="\" k="45" />
<hkern u1="&#x1fe;" u2="X" k="35" />
<hkern u1="&#x1fe;" u2="V" k="31" />
<hkern u1="&#x1fe;" u2="&#x3f;" k="10" />
<hkern u1="&#x1fe;" u2="&#x2f;" k="33" />
<hkern u1="&#x1fe;" u2="&#x29;" k="55" />
<hkern u1="&#x1ff;" u2="&#x2122;" k="66" />
<hkern u1="&#x1ff;" u2="&#xae;" k="37" />
<hkern u1="&#x1ff;" u2="&#x7d;" k="63" />
<hkern u1="&#x1ff;" u2="&#x7c;" k="63" />
<hkern u1="&#x1ff;" u2="x" k="27" />
<hkern u1="&#x1ff;" u2="v" k="20" />
<hkern u1="&#x1ff;" u2="f" k="10" />
<hkern u1="&#x1ff;" u2="]" k="70" />
<hkern u1="&#x1ff;" u2="\" k="92" />
<hkern u1="&#x1ff;" u2="X" k="20" />
<hkern u1="&#x1ff;" u2="V" k="74" />
<hkern u1="&#x1ff;" u2="&#x3f;" k="43" />
<hkern u1="&#x1ff;" u2="&#x2a;" k="43" />
<hkern u1="&#x1ff;" u2="&#x29;" k="51" />
<hkern u1="&#x218;" u2="&#x135;" k="-18" />
<hkern u1="&#x218;" u2="&#x129;" k="-35" />
<hkern u1="&#x218;" u2="&#xef;" k="-16" />
<hkern u1="&#x218;" u2="&#xee;" k="-35" />
<hkern u1="&#x218;" u2="&#xec;" k="-55" />
<hkern u1="&#x218;" u2="&#xae;" k="6" />
<hkern u1="&#x218;" u2="&#x7d;" k="27" />
<hkern u1="&#x218;" u2="&#x7c;" k="27" />
<hkern u1="&#x218;" u2="v" k="12" />
<hkern u1="&#x218;" u2="f" k="8" />
<hkern u1="&#x218;" u2="]" k="27" />
<hkern u1="&#x218;" u2="\" k="18" />
<hkern u1="&#x218;" u2="V" k="25" />
<hkern u1="&#x218;" u2="&#x29;" k="35" />
<hkern u1="&#x219;" u2="&#x2122;" k="43" />
<hkern u1="&#x219;" u2="&#x7d;" k="57" />
<hkern u1="&#x219;" u2="&#x7c;" k="43" />
<hkern u1="&#x219;" u2="v" k="12" />
<hkern u1="&#x219;" u2="]" k="55" />
<hkern u1="&#x219;" u2="\" k="59" />
<hkern u1="&#x219;" u2="V" k="49" />
<hkern u1="&#x219;" u2="&#x3f;" k="29" />
<hkern u1="&#x219;" u2="&#x2a;" k="12" />
<hkern u1="&#x219;" u2="&#x29;" k="35" />
<hkern u1="&#x21a;" u2="&#x15d;" k="98" />
<hkern u1="&#x21a;" u2="&#x159;" k="76" />
<hkern u1="&#x21a;" u2="&#x155;" k="49" />
<hkern u1="&#x21a;" u2="&#x151;" k="104" />
<hkern u1="&#x21a;" u2="&#x135;" k="-100" />
<hkern u1="&#x21a;" u2="&#x131;" k="115" />
<hkern u1="&#x21a;" u2="&#x12d;" k="-113" />
<hkern u1="&#x21a;" u2="&#x12b;" k="-113" />
<hkern u1="&#x21a;" u2="&#x129;" k="-150" />
<hkern u1="&#x21a;" u2="&#x127;" k="-16" />
<hkern u1="&#x21a;" u2="&#x11f;" k="129" />
<hkern u1="&#x21a;" u2="&#x11d;" k="104" />
<hkern u1="&#x21a;" u2="&#x109;" k="125" />
<hkern u1="&#x21a;" u2="&#xf5;" k="131" />
<hkern u1="&#x21a;" u2="&#xef;" k="-119" />
<hkern u1="&#x21a;" u2="&#xee;" k="-117" />
<hkern u1="&#x21a;" u2="&#xec;" k="-193" />
<hkern u1="&#x21a;" u2="&#xea;" k="109" />
<hkern u1="&#x21a;" u2="&#xe8;" k="82" />
<hkern u1="&#x21a;" u2="x" k="92" />
<hkern u1="&#x21a;" u2="v" k="96" />
<hkern u1="&#x21a;" u2="f" k="20" />
<hkern u1="&#x21a;" u2="&#x40;" k="16" />
<hkern u1="&#x21a;" u2="&#x2f;" k="94" />
<hkern u1="&#x21a;" u2="&#x2a;" k="-33" />
<hkern u1="&#x21a;" u2="&#x26;" k="29" />
<hkern u1="&#x21b;" u2="&#x2122;" k="16" />
<hkern u1="&#x21b;" u2="&#x7d;" k="20" />
<hkern u1="&#x21b;" u2="&#x7c;" k="27" />
<hkern u1="&#x21b;" u2="]" k="8" />
<hkern u1="&#x21b;" u2="\" k="27" />
<hkern u1="&#x21b;" u2="V" k="8" />
<hkern u1="&#x21b;" u2="&#x3f;" k="6" />
<hkern u1="&#x21b;" u2="&#x29;" k="16" />
<hkern u1="&#x2013;" u2="&#x166;" k="86" />
<hkern u1="&#x2013;" u2="&#x142;" k="-12" />
<hkern u1="&#x2013;" u2="&#x141;" k="-20" />
<hkern u1="&#x2013;" u2="x" k="70" />
<hkern u1="&#x2013;" u2="v" k="33" />
<hkern u1="&#x2013;" u2="f" k="33" />
<hkern u1="&#x2013;" u2="X" k="51" />
<hkern u1="&#x2013;" u2="V" k="70" />
<hkern u1="&#x2014;" u2="&#x166;" k="86" />
<hkern u1="&#x2014;" u2="&#x142;" k="-12" />
<hkern u1="&#x2014;" u2="&#x141;" k="-20" />
<hkern u1="&#x2014;" u2="x" k="70" />
<hkern u1="&#x2014;" u2="v" k="33" />
<hkern u1="&#x2014;" u2="f" k="33" />
<hkern u1="&#x2014;" u2="X" k="51" />
<hkern u1="&#x2014;" u2="V" k="70" />
<hkern u1="&#x2018;" u2="&#x135;" k="-70" />
<hkern u1="&#x2018;" u2="&#x12d;" k="-84" />
<hkern u1="&#x2018;" u2="&#x12b;" k="-84" />
<hkern u1="&#x2018;" u2="&#x129;" k="-125" />
<hkern u1="&#x2018;" u2="&#xef;" k="-94" />
<hkern u1="&#x2018;" u2="&#xee;" k="-86" />
<hkern u1="&#x2018;" u2="&#xec;" k="-164" />
<hkern u1="&#x2019;" u2="&#x155;" k="12" />
<hkern u1="&#x2019;" u2="&#x135;" k="-49" />
<hkern u1="&#x2019;" u2="&#x12d;" k="-90" />
<hkern u1="&#x2019;" u2="&#x12b;" k="-88" />
<hkern u1="&#x2019;" u2="&#x129;" k="-127" />
<hkern u1="&#x2019;" u2="&#xef;" k="-94" />
<hkern u1="&#x2019;" u2="&#xee;" k="-66" />
<hkern u1="&#x2019;" u2="&#xec;" k="-162" />
<hkern u1="&#x2019;" u2="&#xae;" k="20" />
<hkern u1="&#x2019;" u2="&#x7c;" k="-10" />
<hkern u1="&#x2019;" u2="x" k="12" />
<hkern u1="&#x2019;" u2="v" k="14" />
<hkern u1="&#x2019;" u2="f" k="20" />
<hkern u1="&#x2019;" u2="&#x40;" k="53" />
<hkern u1="&#x2019;" u2="&#x2f;" k="152" />
<hkern u1="&#x2019;" u2="&#x26;" k="76" />
<hkern u1="&#x201a;" u2="v" k="70" />
<hkern u1="&#x201a;" u2="f" k="29" />
<hkern u1="&#x201a;" u2="V" k="96" />
<hkern u1="&#x201c;" u2="&#x135;" k="-68" />
<hkern u1="&#x201c;" u2="&#x12d;" k="-84" />
<hkern u1="&#x201c;" u2="&#x12b;" k="-84" />
<hkern u1="&#x201c;" u2="&#x129;" k="-125" />
<hkern u1="&#x201c;" u2="&#xef;" k="-94" />
<hkern u1="&#x201c;" u2="&#xee;" k="-84" />
<hkern u1="&#x201c;" u2="&#xec;" k="-160" />
<hkern u1="&#x201d;" u2="&#x155;" k="12" />
<hkern u1="&#x201d;" u2="&#x135;" k="-49" />
<hkern u1="&#x201d;" u2="&#x12d;" k="-90" />
<hkern u1="&#x201d;" u2="&#x12b;" k="-88" />
<hkern u1="&#x201d;" u2="&#x129;" k="-127" />
<hkern u1="&#x201d;" u2="&#xef;" k="-94" />
<hkern u1="&#x201d;" u2="&#xee;" k="-66" />
<hkern u1="&#x201d;" u2="&#xec;" k="-162" />
<hkern u1="&#x201d;" u2="&#xae;" k="20" />
<hkern u1="&#x201d;" u2="&#x7c;" k="-10" />
<hkern u1="&#x201d;" u2="x" k="12" />
<hkern u1="&#x201d;" u2="v" k="14" />
<hkern u1="&#x201d;" u2="f" k="20" />
<hkern u1="&#x201d;" u2="&#x40;" k="53" />
<hkern u1="&#x201d;" u2="&#x2f;" k="152" />
<hkern u1="&#x201d;" u2="&#x26;" k="76" />
<hkern u1="&#x201e;" u2="&#x135;" k="-20" />
<hkern u1="&#x201e;" u2="v" k="70" />
<hkern u1="&#x201e;" u2="j" k="-20" />
<hkern u1="&#x201e;" u2="f" k="29" />
<hkern u1="&#x201e;" u2="V" k="96" />
<hkern u1="&#x2039;" u2="V" k="47" />
<hkern u1="&#x203a;" u2="&#x166;" k="82" />
<hkern u1="&#x203a;" u2="&#x142;" k="-10" />
<hkern u1="&#x203a;" u2="&#x141;" k="-20" />
<hkern u1="&#x203a;" u2="x" k="66" />
<hkern u1="&#x203a;" u2="v" k="35" />
<hkern u1="&#x203a;" u2="f" k="29" />
<hkern u1="&#x203a;" u2="X" k="35" />
<hkern u1="&#x203a;" u2="V" k="72" />
<hkern u1="&#x2122;" u2="&#x21a;" k="14" />
<hkern u1="&#x2122;" u2="&#x1fc;" k="47" />
<hkern u1="&#x2122;" u2="&#x1fa;" k="47" />
<hkern u1="&#x2122;" u2="&#x17d;" k="31" />
<hkern u1="&#x2122;" u2="&#x17b;" k="31" />
<hkern u1="&#x2122;" u2="&#x179;" k="31" />
<hkern u1="&#x2122;" u2="&#x178;" k="51" />
<hkern u1="&#x2122;" u2="&#x176;" k="51" />
<hkern u1="&#x2122;" u2="&#x166;" k="14" />
<hkern u1="&#x2122;" u2="&#x164;" k="14" />
<hkern u1="&#x2122;" u2="&#x135;" k="-14" />
<hkern u1="&#x2122;" u2="&#x134;" k="27" />
<hkern u1="&#x2122;" u2="&#x104;" k="47" />
<hkern u1="&#x2122;" u2="&#x102;" k="47" />
<hkern u1="&#x2122;" u2="&#x100;" k="47" />
<hkern u1="&#x2122;" u2="&#xee;" k="-31" />
<hkern u1="&#x2122;" u2="&#xec;" k="-29" />
<hkern u1="&#x2122;" u2="&#xdd;" k="51" />
<hkern u1="&#x2122;" u2="&#xc6;" k="47" />
<hkern u1="&#x2122;" u2="&#xc5;" k="47" />
<hkern u1="&#x2122;" u2="&#xc4;" k="47" />
<hkern u1="&#x2122;" u2="&#xc3;" k="47" />
<hkern u1="&#x2122;" u2="&#xc2;" k="47" />
<hkern u1="&#x2122;" u2="&#xc1;" k="47" />
<hkern u1="&#x2122;" u2="&#xc0;" k="47" />
<hkern u1="&#x2122;" u2="Z" k="31" />
<hkern u1="&#x2122;" u2="Y" k="51" />
<hkern u1="&#x2122;" u2="X" k="35" />
<hkern u1="&#x2122;" u2="V" k="8" />
<hkern u1="&#x2122;" u2="T" k="14" />
<hkern u1="&#x2122;" u2="J" k="27" />
<hkern u1="&#x2122;" u2="A" k="47" />
<hkern u1="&#x2122;" u2="&#x27;" k="14" />
<hkern u1="&#x2122;" u2="&#x22;" k="14" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="20" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="25" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="18" />
<hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron" 	g2="w,wcircumflex" 	k="10" />
<hkern g1="D,Dcaron,Dcroat" 	g2="T,Tcaron,Tbar,uni021A" 	k="39" />
<hkern g1="D,Dcaron,Dcroat" 	g2="W,Wcircumflex" 	k="20" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="76" />
<hkern g1="D,Dcaron,Dcroat" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="D,Dcaron,Dcroat" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="35" />
<hkern g1="D,Dcaron,Dcroat" 	g2="J,Jcircumflex" 	k="18" />
<hkern g1="D,Dcaron,Dcroat" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="12" />
<hkern g1="D,Dcaron,Dcroat" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="39" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="guillemotleft,guilsinglleft" 	k="45" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="quoteleft,quotedblleft" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="w,wcircumflex" 	k="25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="W,Wcircumflex" 	k="10" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="33" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="quoteright,quotedblright" 	k="23" />
<hkern g1="G,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="J,Jcircumflex" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="J,Jcircumflex" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="J,Jcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="J,Jcircumflex" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="J,Jcircumflex" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="J,Jcircumflex" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="J,Jcircumflex" 	g2="w,wcircumflex" 	k="8" />
<hkern g1="J,Jcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="K,Kcommaaccent" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="25" />
<hkern g1="K,Kcommaaccent" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="41" />
<hkern g1="K,Kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="33" />
<hkern g1="K,Kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="57" />
<hkern g1="K,Kcommaaccent" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="K,Kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="45" />
<hkern g1="K,Kcommaaccent" 	g2="quoteleft,quotedblleft" 	k="31" />
<hkern g1="K,Kcommaaccent" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="K,Kcommaaccent" 	g2="t,tcaron,tbar,uni021B" 	k="10" />
<hkern g1="K,Kcommaaccent" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="35" />
<hkern g1="K,Kcommaaccent" 	g2="w,wcircumflex" 	k="29" />
<hkern g1="K,Kcommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="27" />
<hkern g1="K,Kcommaaccent" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="8" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="41" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="T,Tcaron,Tbar,uni021A" 	k="133" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="25" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="W,Wcircumflex" 	k="102" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="162" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="14" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="39" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="hyphen,endash,emdash" 	k="111" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="18" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteleft,quotedblleft" 	k="143" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quoteright,quotedblright" 	k="143" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="quotedbl,quotesingle" 	k="141" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="t,tcaron,tbar,uni021B" 	k="29" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="8" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="w,wcircumflex" 	k="45" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="80" />
<hkern g1="L,Lacute,Lcommaaccent,Lcaron,Lslash" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="41" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="W,Wcircumflex" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="74" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="quoteleft,quotedblleft" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="33" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Omacron,Obreve,Ohungarumlaut,Oslashacute" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="33" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="25" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="W,Wcircumflex" 	k="18" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="59" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="23" />
<hkern g1="R,Racute,Rcommaaccent,Rcaron" 	g2="J,Jcircumflex" 	k="6" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="W,Wcircumflex" 	k="12" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="37" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="w,wcircumflex" 	k="14" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="12" />
<hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="14" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="133" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="143" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotleft,guilsinglleft" 	k="102" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="hyphen,endash,emdash" 	k="102" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="133" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="t,tcaron,tbar,uni021B" 	k="27" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="117" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="w,wcircumflex" 	k="96" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="y,yacute,ydieresis,ycircumflex" 	k="96" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="76" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="100" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="115" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="guillemotright,guilsinglright" 	k="92" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="colon,semicolon" 	k="82" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="145" />
<hkern g1="T,Tcaron,Tbar,uni021A" 	g2="z,zacute,zdotaccent,zcaron" 	k="127" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="hyphen,endash,emdash" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="quoteleft,quotedblleft" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="27" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="10" />
<hkern g1="W,Wcircumflex" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="31" />
<hkern g1="W,Wcircumflex" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="16" />
<hkern g1="W,Wcircumflex" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="45" />
<hkern g1="W,Wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="37" />
<hkern g1="W,Wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="W,Wcircumflex" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="W,Wcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="45" />
<hkern g1="W,Wcircumflex" 	g2="quoteleft,quotedblleft" 	k="25" />
<hkern g1="W,Wcircumflex" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="W,Wcircumflex" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="25" />
<hkern g1="W,Wcircumflex" 	g2="w,wcircumflex" 	k="16" />
<hkern g1="W,Wcircumflex" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="W,Wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="55" />
<hkern g1="W,Wcircumflex" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="W,Wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="90" />
<hkern g1="W,Wcircumflex" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="29" />
<hkern g1="W,Wcircumflex" 	g2="guillemotright,guilsinglright" 	k="51" />
<hkern g1="W,Wcircumflex" 	g2="colon,semicolon" 	k="39" />
<hkern g1="W,Wcircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="33" />
<hkern g1="W,Wcircumflex" 	g2="z,zacute,zdotaccent,zcaron" 	k="20" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="55" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="33" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="133" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="123" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="137" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="hyphen,endash,emdash" 	k="135" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="135" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="t,tcaron,tbar,uni021B" 	k="29" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="106" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="w,wcircumflex" 	k="84" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="y,yacute,ydieresis,ycircumflex" 	k="72" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="100" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="J,Jcircumflex" 	k="41" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="139" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="113" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="106" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="colon,semicolon" 	k="92" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="127" />
<hkern g1="Y,Yacute,Ycircumflex,Ydieresis" 	g2="z,zacute,zdotaccent,zcaron" 	k="94" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="guillemotleft,guilsinglleft" 	k="51" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="quoteright,quotedblright" 	k="12" />
<hkern g1="Z,Zacute,Zdotaccent,Zcaron" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="129" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="W,Wcircumflex" 	k="41" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="127" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,amacron,abreve,aogonek,aringacute" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="8" />
<hkern g1="b,p" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="6" />
<hkern g1="b,p" 	g2="T,Tcaron,Tbar,uni021A" 	k="131" />
<hkern g1="b,p" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="b,p" 	g2="W,Wcircumflex" 	k="53" />
<hkern g1="b,p" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="141" />
<hkern g1="b,p" 	g2="quoteleft,quotedblleft" 	k="33" />
<hkern g1="b,p" 	g2="quoteright,quotedblright" 	k="35" />
<hkern g1="b,p" 	g2="quotedbl,quotesingle" 	k="41" />
<hkern g1="b,p" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="b,p" 	g2="y,yacute,ydieresis,ycircumflex" 	k="16" />
<hkern g1="b,p" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="b,p" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="b,p" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="b,p" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="168" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="W,Wcircumflex" 	k="18" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="98" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="10" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotleft,guilsinglleft" 	k="68" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="hyphen,endash,emdash" 	k="68" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="12" />
<hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="colon,semicolon" 	g2="T,Tcaron,Tbar,uni021A" 	k="92" />
<hkern g1="colon,semicolon" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="colon,semicolon" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="90" />
<hkern g1="d,dcroat" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="150" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="154" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="quotedbl,quotesingle" 	k="25" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="J,Jcircumflex" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="129" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="W,Wcircumflex" 	k="8" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="70" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	g2="hyphen,endash,emdash" 	k="29" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="96" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="96" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t,tcaron,tbar,uni021B" 	k="6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J,Jcircumflex" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="47" />
<hkern g1="guillemotright,guilsinglright" 	g2="T,Tcaron,Tbar,uni021A" 	k="100" />
<hkern g1="guillemotright,guilsinglright" 	g2="W,Wcircumflex" 	k="57" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="131" />
<hkern g1="guillemotright,guilsinglright" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="109" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="117" />
<hkern g1="guillemotright,guilsinglright" 	g2="t,tcaron,tbar,uni021B" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="w,wcircumflex" 	k="29" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis,ycircumflex" 	k="37" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="J,Jcircumflex" 	k="47" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="27" />
<hkern g1="guillemotright,guilsinglright" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent,zcaron" 	k="59" />
<hkern g1="guillemotright,guilsinglright" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="16" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="57" />
<hkern g1="hyphen,endash,emdash" 	g2="T,Tcaron,Tbar,uni021A" 	k="100" />
<hkern g1="hyphen,endash,emdash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="hyphen,endash,emdash" 	g2="W,Wcircumflex" 	k="55" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="131" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="117" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="127" />
<hkern g1="hyphen,endash,emdash" 	g2="t,tcaron,tbar,uni021B" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="w,wcircumflex" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis,ycircumflex" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="J,Jcircumflex" 	k="49" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="33" />
<hkern g1="hyphen,endash,emdash" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent,zcaron" 	k="70" />
<hkern g1="hyphen,endash,emdash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="14" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="k,kcommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="125" />
<hkern g1="k,kcommaaccent" 	g2="W,Wcircumflex" 	k="8" />
<hkern g1="k,kcommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="70" />
<hkern g1="k,kcommaaccent" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="23" />
<hkern g1="k,kcommaaccent" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="6" />
<hkern g1="k,kcommaaccent" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="k,kcommaaccent" 	g2="hyphen,endash,emdash" 	k="68" />
<hkern g1="k,kcommaaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="25" />
<hkern g1="l,lacute,lcommaaccent,lslash" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="dcaron,lcaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="14" />
<hkern g1="dcaron,lcaron" 	g2="guillemotleft,guilsinglleft" 	k="72" />
<hkern g1="dcaron,lcaron" 	g2="hyphen,endash,emdash" 	k="63" />
<hkern g1="dcaron,lcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="14" />
<hkern g1="dcaron,lcaron" 	g2="quoteleft,quotedblleft" 	k="-27" />
<hkern g1="dcaron,lcaron" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="dcaron,lcaron" 	g2="quotedbl,quotesingle" 	k="-18" />
<hkern g1="dcaron,lcaron" 	g2="t,tcaron,tbar,uni021B" 	k="-23" />
<hkern g1="dcaron,lcaron" 	g2="w,wcircumflex" 	k="-16" />
<hkern g1="dcaron,lcaron" 	g2="y,yacute,ydieresis,ycircumflex" 	k="-31" />
<hkern g1="dcaron,lcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="86" />
<hkern g1="dcaron,lcaron" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="dcaron,lcaron" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="dcaron,lcaron" 	g2="b" 	k="-16" />
<hkern g1="dcaron,lcaron" 	g2="h,k,germandbls,hcircumflex,hbar,kcommaaccent" 	k="-12" />
<hkern g1="dcaron,lcaron" 	g2="i,j,igrave,iacute,icircumflex,idieresis,itilde,imacron,ibreve,iogonek,dotlessi,jcircumflex" 	k="-12" />
<hkern g1="dcaron,lcaron" 	g2="l,lacute,lcommaaccent,lcaron,lslash" 	k="-12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="8" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="T,Tcaron,Tbar,uni021A" 	k="133" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="18" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="W,Wcircumflex" 	k="51" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="147" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="quotedbl,quotesingle" 	k="35" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="w,wcircumflex" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,eng" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="T,Tcaron,Tbar,uni021A" 	k="133" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="W,Wcircumflex" 	k="55" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="150" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteleft,quotedblleft" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quoteright,quotedblright" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="quotedbl,quotesingle" 	k="45" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="w,wcircumflex" 	k="14" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="y,yacute,ydieresis,ycircumflex" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="J,Jcircumflex" 	k="25" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,oslashacute" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="49" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T,Tcaron,Tbar,uni021A" 	k="98" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W,Wcircumflex" 	k="80" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="131" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="25" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="250" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="254" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="262" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t,tcaron,tbar,uni021B" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="23" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w,wcircumflex" 	k="55" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis,ycircumflex" 	k="70" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="25" />
<hkern g1="quoteleft,quotedblleft" 	g2="T,Tcaron,Tbar,uni021A" 	k="-8" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="-18" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="29" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="104" />
<hkern g1="quoteleft,quotedblleft" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="186" />
<hkern g1="quoteleft,quotedblleft" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="18" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="29" />
<hkern g1="quoteleft,quotedblleft" 	g2="z,zacute,zdotaccent,zcaron" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="12" />
<hkern g1="quoteright,quotedblright" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="-20" />
<hkern g1="quoteright,quotedblright" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="43" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="137" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="119" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="53" />
<hkern g1="quoteright,quotedblright" 	g2="quoteleft,quotedblleft" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="t,tcaron,tbar,uni021B" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	k="27" />
<hkern g1="quoteright,quotedblright" 	g2="w,wcircumflex" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="y,yacute,ydieresis,ycircumflex" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="113" />
<hkern g1="quoteright,quotedblright" 	g2="J,Jcircumflex" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="203" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron" 	k="29" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="70" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="35" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="39" />
<hkern g1="quoteright,quotedblright" 	g2="z,zacute,zdotaccent,zcaron" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron" 	k="14" />
<hkern g1="quotedbl,quotesingle" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Oslashacute" 	k="12" />
<hkern g1="quotedbl,quotesingle" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="-10" />
<hkern g1="quotedbl,quotesingle" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="31" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="109" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="90" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="96" />
<hkern g1="quotedbl,quotesingle" 	g2="J,Jcircumflex" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="174" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="quotedbl,quotesingle" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="14" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="106" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="45" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="12" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotleft,guilsinglleft" 	k="74" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="hyphen,endash,emdash" 	k="66" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="10" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="49" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="J,Jcircumflex" 	k="31" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="31" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="88" />
<hkern g1="r,racute,rcommaaccent,rcaron" 	g2="guillemotright,guilsinglright" 	k="18" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="T,Tcaron,Tbar,uni021A" 	k="143" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="10" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="W,Wcircumflex" 	k="35" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="117" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="y,yacute,ydieresis,ycircumflex" 	k="12" />
<hkern g1="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="t,tbar,uni021B" 	g2="T,Tcaron,Tbar,uni021A" 	k="102" />
<hkern g1="t,tbar,uni021B" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="57" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotleft,guilsinglleft" 	k="70" />
<hkern g1="t,tbar,uni021B" 	g2="hyphen,endash,emdash" 	k="59" />
<hkern g1="t,tbar,uni021B" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="T,Tcaron,Tbar,uni021A" 	k="123" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="W,Wcircumflex" 	k="37" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="121" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="J,Jcircumflex" 	k="10" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="8" />
<hkern g1="w,wcircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="121" />
<hkern g1="w,wcircumflex" 	g2="W,Wcircumflex" 	k="12" />
<hkern g1="w,wcircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="78" />
<hkern g1="w,wcircumflex" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="6" />
<hkern g1="w,wcircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="6" />
<hkern g1="w,wcircumflex" 	g2="guillemotleft,guilsinglleft" 	k="29" />
<hkern g1="w,wcircumflex" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="w,wcircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="6" />
<hkern g1="w,wcircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="18" />
<hkern g1="w,wcircumflex" 	g2="J,Jcircumflex" 	k="27" />
<hkern g1="w,wcircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="16" />
<hkern g1="w,wcircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="53" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="T,Tcaron,Tbar,uni021A" 	k="121" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="W,Wcircumflex" 	k="10" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="70" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="a,d,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,dcaron,dcroat,aringacute,aeacute" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent" 	k="16" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="hyphen,endash,emdash" 	k="41" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,oslashacute" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,Aringacute,AEacute" 	k="23" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="J,Jcircumflex" 	k="29" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="Z,Zacute,Zdotaccent,Zcaron" 	k="18" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="78" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="guillemotright,guilsinglright" 	k="25" />
<hkern g1="y,yacute,ydieresis,ycircumflex" 	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent" 	k="18" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="T,Tcaron,Tbar,uni021A" 	k="143" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="W,Wcircumflex" 	k="16" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="Y,Yacute,Ycircumflex,Ydieresis" 	k="94" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="hyphen,endash,emdash" 	k="59" />
<hkern g1="z,zacute,zdotaccent,zcaron" 	g2="guillemotright,guilsinglright" 	k="6" />
</font>
</defs></svg> 