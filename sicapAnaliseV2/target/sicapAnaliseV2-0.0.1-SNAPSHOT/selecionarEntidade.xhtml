<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui"
	xmlns:pe="http://primefaces.org/ui/extensions" xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jsp/jstl/core" template="/resources/template/template.xhtml">

<!-- 	<ui:define name="scripts"> -->
<!-- 		<script type="text/javascript"> -->
// 				var tempoSessao = 1500;
// 				var timer = setInterval(function() {
// 					if( tempoSessao === 0 ) {
// 						document.getElementById('session-countdown').innerHTML = "00min:00s";
// 			    		clearInterval( timer );			    	
// 			  		}
// 					var segundos = tempoSessao % 60;
// 					var minutos = parseInt(tempoSessao / 60);
// 					segundos = (segundos + "").replace(/^(\d)$/, '0$1');
// 					minutos = (minutos + "").replace(/^(\d)$/, '0$1');
// 			   		document.getElementById('session-countdown').innerHTML = minutos + "min:" + segundos + "s";   
// 			   		tempoSessao--;
// 				}, 1000);
<!-- 		</script> -->
<!-- 	</ui:define> -->

<!-- 	<ui:define name="menubar" /> -->

	<ui:define name="content">
		<h:form id="FrmEntidades" prependId="false" acceptcharset="ISO-8859-1">
			<p:dataTable paginator="true" paginatorPosition="bottom" emptyMessage="Nenhuma entidade encontrada." value="#{loginBean.listaEntidades}"
				var="entidade" rows="10" rowsPerPageTemplate="10,20,30,40,50" selectionMode="single" selection="#{loginBean.entidade}"
				rowKey="#{entidade.idEntidadeCjur}">
				<p:ajax event="rowSelect" listener="#{loginBean.selecaoEntidade()}" />

				<f:facet name="header">
					<h:outputText value="Selecione uma entidade para analisar" />
				</f:facet>

				<p:column headerText="Nome" filterBy="#{entidade.nome}#{utilBean.removeAcentos(entidade.nome)}" filterMatchMode="contains"
					filterStyle="width: 100% !important;">
					<h:outputText value="#{entidade.nome}" />
				</p:column>

				<p:column headerText="Ente" filterBy="#{entidade.ente}" filterMatchMode="contains" filterStyle="width: 100% !important;">
					<h:outputText value="#{entidade.ente}" />
				</p:column>

				<p:column headerText="Poder">
					<h:outputText value="#{entidade.poder}" />
				</p:column>

				<p:column headerText="Classificacao Administrativa">
					<h:outputText value="#{entidade.classificacaoAdministrativa}" />
				</p:column>
			</p:dataTable>
		</h:form>
	</ui:define>

</ui:composition>