<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets" xmlns:p="http://primefaces.org/ui" xmlns:pe="http://primefaces.org/ui/extensions"
	template="/resources/template/template.xhtml">

	<ui:define name="content">
		<p:fieldset legend="Classe de Cargos">
			<h:form prependId="false">
				<p:growl id="msg" autoUpdate="true" />

				<p:fieldset legend="Ações">
					<h:panelGrid columns="2">
						<p:commandButton icon="fa fa-fw fa-list white" value="Listar" />
						<p:commandButton icon="fa fa-fw fa-plus white" value="Novo" />
					</h:panelGrid>
				</p:fieldset>
				<p:spacer />
				<p:spacer />

				<h:panelGrid columns="2" style="margin-bottom:10px;" cellpadding="5">
					<h:outputText value="Entidade: " />
					<p:selectOneMenu value="#{classeCargosBean.entidade.idEntidadeCjur}" autoWidth="false" style="width: 200px!important;" filter="true"
						filterMatchMode="contains">
						<f:selectItem itemLabel="Escolha..." itemValue="#{0}" noSelectionOption="true" />
						<f:selectItems value="#{classeCargosBean.listaEntidade}" var="entidade" itemLabel="#{entidade.nome}" itemValue="#{entidade.idEntidadeCjur}" />
					</p:selectOneMenu>
				</h:panelGrid>

				<p:fieldset legend="Cadastro de Classe de Cargos">
					<p:wizard flowListener="#{classeCargosBean.onFlowProcess()}">
						<p:tab id="classeCargo" title="Classe">
							<p:panel header="Dados da Classe">
								<h:panelGrid columns="2">
									<h:outputText value="Nome da Classe: " />
									<p:inputText value="#{classeCargosBean.classeCargos.nome}" maxlength="50" required="true"
										requiredMessage="O nome da classe deve ser informado." size="53" />
									<h:outputText value="Descrição: " />
									<p:inputTextarea value="#{classeCargosBean.classeCargos.descricao}" maxlength="255" counter="displayDescricaoClasse"
										counterTemplate="{0} caracteres restantes." />
									<p:spacer />
									<h:outputText id="displayDescricaoClasse" />
									<h:outputText value="Permitir que outros usuários façam alteraçõs: " />
									<p:selectOneRadio value="#{classeCargosBean.classeCargos.permitirAlteracao}">
										<f:selectItem itemLabel="Sim" itemValue="true" />
										<f:selectItem itemLabel="Não" itemValue="false" />
									</p:selectOneRadio>
									<h:outputText value="Possui subClasses: " />
									<p:selectOneRadio value="#{classeCargosBean.possuiSubClasse}">
										<f:selectItem itemLabel="Sim" itemValue="true" />
										<f:selectItem itemLabel="Não" itemValue="false" />
									</p:selectOneRadio>

								</h:panelGrid>
							</p:panel>
						</p:tab>

						<p:tab id="subClasseCargo" title="SubClasse">
							<p:panel id="panelDadosSubClasse" header="Dados das SubClasses">
								<p:fieldset id="cadSubClasse" rendered="#{classeCargosBean.possuiSubClasse}">
									<h:panelGrid columns="2">
										<h:outputText value="Nome da subClasse: " />
										<p:inputText value="#{classeCargosBean.subClasseCargos.nome}" maxlength="50" size="53" />
										<h:outputText value="Descrição: " />
										<p:inputTextarea value="#{classeCargosBean.subClasseCargos.descricao}" maxlength="255" counter="displayDescricaoSubClasse"
											counterTemplate="{0} caracteres restantes." />
										<p:spacer />
										<h:outputText id="displayDescricaoSubClasse" />
										<p:spacer />
										<p:commandButton value="Adicionar" actionListener="#{classeCargosBean.adicionarSubClasse()}"
											update="msg tblListaSubClasseCargoInsert panelDadosSubClasse" />
										<p:spacer />
										<p:separator />
										<p:spacer />
										<p:dataTable id="tblListaSubClasseCargoInsert" value="#{classeCargosBean.listaSubClasseCargoInsert}" var="subClasse">
											<f:facet name="header">
												<h:outputText value="SubClasses" />
											</f:facet>
											<p:column headerText="Nome">
												<h:outputText value="#{subClasse.nome}" />
											</p:column>
											<p:column headerText="Descrição">
												<h:outputText value="#{subClasse.descricao}" />
											</p:column>
											<p:column headerText="Ações" width="10%">
												<p:commandLink actionListener="#{classeCargosBean.editarSubClasse(subClasse)}"
													update="msg tblListaSubClasseCargoInsert panelDadosSubClasse">
													<h:outputText class="icon-edit" title="Editar SubClasse" />
												</p:commandLink>
												<p:commandLink actionListener="#{classeCargosBean.removerSubClasse(subClasse)}"
													update="msg tblListaSubClasseCargoInsert panelDadosSubClasse">
													<h:outputText class="fa fa-fw fa-remove" title="Remover SubClasse" />
												</p:commandLink>
											</p:column>
											<f:facet name="footer">
												<h:outputText value="Total de subClasses #{classeCargosBean.listaSubClasseCargoInsert.size()}" />
											</f:facet>
										</p:dataTable>
									</h:panelGrid>
								</p:fieldset>
							</p:panel>
						</p:tab>

						<p:tab id="tabListaCargos" title="Cargos" >
							<p:panel header="Dados dos Cargos">
							</p:panel>
						</p:tab>

					</p:wizard>
				</p:fieldset>







				<pe:masterDetail id="masterDetail" level="#{classeCargosBean.currentLevel}" showAllBreadcrumbItems="true">

					<f:facet name="header">
						<p:messages showDetail="false" showSummary="true" />
					</f:facet>

					<pe:masterDetailLevel id="detailInicio" level="1">
						<f:facet name="label">
							<h:outputText value="Lista" />
						</f:facet>
						<h:outputText value="teste" />
					</pe:masterDetailLevel>
				</pe:masterDetail>
			</h:form>
		</p:fieldset>
	</ui:define>
</ui:composition>